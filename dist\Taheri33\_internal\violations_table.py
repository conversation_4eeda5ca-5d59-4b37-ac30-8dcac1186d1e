from PyQt5.QtWidgets import QTableView
from PyQt5.QtCore import Qt

class ViolationsTableView(QTableView):
    """
    A custom table view for displaying violations.
    This is a basic implementation to resolve import errors.
    """
    
    def __init__(self, parent=None):
        super().__init__(parent)
        
        # Configure the table view
        self.setAlternatingRowColors(True)
        self.setSelectionBehavior(QTableView.SelectRows)
        self.setSelectionMode(QTableView.SingleSelection)
        self.setSortingEnabled(True)
        self.setEditTriggers(QTableView.NoEditTriggers)  # Read-only
        
        # Set style
        self.setStyleSheet("""
            QTableView {
                background-color: white;
                alternate-background-color: #f9f9f9;
                border: 1px solid #dcdcdc;
                border-radius: 4px;
                padding: 2px;
                gridline-color: #e0e0e0;
            }
            QTableView::item {
                border: none;
                padding: 5px;
                border-radius: 2px;
                color: black;
                font-weight: bold;
            }
            QTableView::item:selected {
                background-color: #3498db;
                color: white;
            }
            QTableView::item:hover {
                background-color: #edf3fe;
            }
        """)
    
    def configure_columns(self, widths):
        """Configure column widths"""
        for i, width in enumerate(widths):
            if i < self.model().columnCount():
                self.setColumnWidth(i, width)

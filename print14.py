import os
import sys
import sqlite3
import traceback
from datetime import datetime
import subprocess

# تثبيت المكتبات اللازمة
def ensure_libraries_installed():
    required_libraries = ["fpdf2", "arabic-reshaper", "python-bidi"]
    for library in required_libraries:
        try:
            __import__(library.replace('-', '_').replace('fpdf2', 'fpdf'))
            print(f"✓ مكتبة {library} متوفرة")
        except ImportError:
            print(f"جاري تثبيت مكتبة {library}...")
            try:
                # استخدام pip لتثبيت المكتبة مع إيقاف رسائل التحديث
                subprocess.check_call(
                    [sys.executable, "-m", "pip", "install", "--disable-pip-version-check", library],
                    stderr=subprocess.DEVNULL if sys.platform == 'win32' else subprocess.STDOUT
                )
                print(f"✓ تم تثبيت مكتبة {library} بنجاح")
            except Exception as e:
                print(f"✗ فشل في تثبيت مكتبة {library}: {str(e)}")

# تشغيل دالة تثبيت المكتبات عند تشغيل الملف مباشرة
if __name__ == '__main__':
    ensure_libraries_installed()

# استيراد المكتبات اللازمة بعد التأكد من تثبيتها
try:
    from fpdf import FPDF
    import arabic_reshaper
    from bidi.algorithm import get_display
except ImportError:
    subprocess.check_call([sys.executable, "-m", "pip", "install", "fpdf2", "arabic-reshaper", "python-bidi"])
    from fpdf import FPDF
    import arabic_reshaper
    from bidi.algorithm import get_display

# ================= إعدادات عامة =================
# الجدول الأول: 4 أعمدة، 4 صفوف
COL_WIDTHS_TABLE1 = [35, 15, 35, 12.5]  # نصف عرض الأعمدة الأربعة للجدول الأول (لأن الصفحة أفقية)

# إعدادات للهوامش (بالملليمتر)
PAGE_MARGIN_TOP = 5     # الهامش العلوي للصفحة
PAGE_MARGIN_BOTTOM = 5  # الهامش السفلي للصفحة
PAGE_MARGIN_LEFT = 10   # الهامش الأيسر للصفحة
PAGE_MARGIN_RIGHT = 10  # الهامش الأيمن للصفحة

# تحويل النقاط إلى ملليمتر
PT_TO_MM = 0.3528

# إعدادات أحجام المربعات النصية
LOGO_W_PT = 150  # عرض الشعار
LOGO_H_PT = 60   # ارتفاع الشعار

# تحويل الأحجام من نقاط إلى ملليمتر
LOGO_W = LOGO_W_PT * PT_TO_MM
LOGO_H = LOGO_H_PT * PT_TO_MM

# ارتفاع الصفوف
ROW_HEIGHT_TABLE1 = 8  # ارتفاع صفوف الجدول الأول

class ArabicHorizontalPDF(FPDF):
    """
    فئة PDF مخصصة للطباعة بالعربية في صفحات أفقية
    مع دعم لعرض استدعائين عموديين في كل صفحة أفقية
    """
    def __init__(self):
        # إنشاء PDF أفقي (Landscape)
        super().__init__(orientation='L', unit='mm', format='A4')
        # تعيين الهوامش
        self.set_margins(PAGE_MARGIN_LEFT, PAGE_MARGIN_TOP, PAGE_MARGIN_RIGHT)
        self.set_auto_page_break(auto=True, margin=PAGE_MARGIN_BOTTOM)
        
        # إضافة الخطوط العربية
        fonts_dir = os.path.join(os.path.dirname(__file__), 'fonts')
        self.add_font('Arial', '', os.path.join(fonts_dir, 'arial.ttf'))
        self.add_font('Arial', 'B', os.path.join(fonts_dir, 'arialbd.ttf'))
        
        try:
            # محاولة إضافة خط Calibri
            calibri_b_path = os.path.join(fonts_dir, 'calibrib.ttf')
            calibri_path = os.path.join(fonts_dir, 'calibri.ttf')
            
            if os.path.exists(calibri_path) and os.path.exists(calibri_b_path):
                self.add_font('Calibri', '', calibri_path)
                self.add_font('Calibri', 'B', calibri_b_path)
        except Exception as e:
            print(f"تحذير: تعذر إضافة خط Calibri: {str(e)}")
        
        self.set_font('Arial', '', 12)
        # تعيين سمك الخط الافتراضي
        self.set_line_width(0.3)

    def ar_text(self, txt: str) -> str:
        """
        تحويل النص العربي ليتم عرضه بشكل صحيح
        """
        reshaped = arabic_reshaper.reshape(str(txt))
        return get_display(reshaped)

    def multi_line_ar_text(self, txt: str, cell_width: float, font_size: int = 12) -> list:
        """
        تقسيم النص إلى سطور لتتناسب مع عرض الخلية
        """
        lines = []
        words = txt.split(' ')
        current_line = ""

        for word in words:
            test_line = current_line + " " + word if current_line else word
            ar_test_line = self.ar_text(test_line)

            self.set_font('Arial', '', font_size)
            width = self.get_string_width(ar_test_line)

            if width > cell_width and current_line:
                lines.append(current_line)
                current_line = word
            else:
                current_line = test_line

        if current_line:
            lines.append(current_line)

        return lines


def fetch_records(db_path: str, filter_criteria=None):
    """
    جلب السجلات من قاعدة البيانات مع تطبيق معايير التصفية المحددة
    """
    conn = sqlite3.connect(db_path)
    cur = conn.cursor()
    # جلب شعار المؤسسة
    cur.execute("SELECT ImagePath1 FROM بيانات_المؤسسة LIMIT 1")
    logo_row = cur.fetchone()
    logo_path = logo_row[0] if logo_row and os.path.exists(logo_row[0]) else None

    # إعداد الاستعلام مع مراعاة معايير التصفية
    query = '''
    SELECT رقم_الامتحان, الاسم_الكامل, الرمز, مركز_الامتحان, المستوى, القسم, القاعة, المؤسسة_الأصلية
    FROM امتحانات
    '''

    # إضافة شروط التصفية
    if filter_criteria:
        conditions = []
        params = []

        for key, value in filter_criteria.items():
            if value:
                conditions.append(f"{key} = ?")
                params.append(value)

        if conditions:
            query += " WHERE " + " AND ".join(conditions)

    # إضافة ترتيب السجلات
    query += " ORDER BY CAST(رقم_الامتحان AS INTEGER), CAST(القاعة AS INTEGER)"

    # تنفيذ الاستعلام
    if filter_criteria and params:
        cur.execute(query, params)
    else:
        cur.execute(query)

    cols = [c[0] for c in cur.description]
    recs = [dict(zip(cols, row)) for row in cur.fetchall()]
    conn.close()
    return logo_path, recs


def get_report_title(db_path):
    """الحصول على عنوان التقرير من قاعدة البيانات"""
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        cursor.execute("SELECT العنوان2 FROM جدول_الامتحان LIMIT 1")
        result = cursor.fetchone()
        conn.close()
        return result[0] if result and result[0] else "استدعاء المترشحين"
    except Exception as e:
        print(f"خطأ في استرجاع عنوان التقرير: {str(e)}")
        return "استدعاء المترشحين"


def process_invitation_horizontal(pdf, rec, logo_path, report_title, x_start, width, y_start, margin=5):
    """
    معالجة استدعاء فردي داخل صفحة أفقية
    
    المعاملات:
        pdf: كائن PDF
        rec: سجل المترشح
        logo_path: مسار الشعار
        report_title: عنوان التقرير
        x_start: نقطة البداية الأفقية للاستدعاء
        width: عرض الاستدعاء
        y_start: نقطة البداية الرأسية للاستدعاء
        margin: الهامش الداخلي
    """
    # تعيين عرض وارتفاع منطقة الاستدعاء
    invitation_width = width - (2 * margin)
    
    # إعداد موضع البداية
    x = x_start + margin
    y = y_start + margin
    
    # إضافة الشعار
    if logo_path and os.path.exists(logo_path):
        logo_width = LOGO_W * 0.6  # تصغير الشعار
        logo_height = LOGO_H * 0.6
        # وضع الشعار في المنتصف
        x_logo = x + (invitation_width - logo_width) / 2
        pdf.image(logo_path, x=x_logo, y=y, w=logo_width, h=logo_height)
        y += logo_height + 2
    
    # مربع العنوان الرئيسي
    pdf.set_draw_color(0, 0, 255)  # لون أزرق للإطار
    pdf.set_line_width(0.4)
    
    # استخدام عنوان التقرير المخصص
    title_text = report_title
    
    # حساب عرض مربع العنوان
    title_box_width = invitation_width
    box_height = 8  # ارتفاع مربع العنوان
    
    pdf.set_xy(x, y)
    pdf.set_font('Arial', 'B', 10)
    
    # تقسيم النص إلى سطرين إذا كان طويلاً
    lines = pdf.multi_line_ar_text(title_text, title_box_width - 5, 10)
    
    if len(lines) > 1:
        # رسم خلية فارغة أولاً
        pdf.cell(title_box_width, box_height, '', border=1, align='C')
        
        # ثم رسم كل سطر داخلها
        line_height = box_height / len(lines)
        for i, line in enumerate(lines[:2]):
            pdf.set_xy(x, y + i * line_height)
            pdf.cell(title_box_width, line_height, pdf.ar_text(line), border=0, align='C')
    else:
        # إذا كان النص قصيراً نرسمه كالمعتاد
        pdf.cell(title_box_width, box_height, pdf.ar_text(title_text), border=1, align='C')
    
    pdf.set_font('Arial', '', 8)
    y += box_height + 2
    
    # جدول البيانات: 4 أعمدة، 4 صفوف
    # تصغير عرض الأعمدة ليناسب عرض الاستدعاء
    scale_factor = invitation_width / sum(COL_WIDTHS_TABLE1)
    cols = [c * scale_factor for c in COL_WIDTHS_TABLE1]
    
    # محاولة الحصول على قيمة المؤسسة الأصلية
    institution_value = rec.get('المؤسسة_الأصلية', '')
    if not institution_value:
        institution_value = rec.get('المؤسسة-الأصلية', '')
    
    table_data = [
        [rec.get('الاسم_الكامل',''), 'الاسم الكامل', rec.get('مركز_الامتحان',''), 'مركز الامتحان'],
        [rec.get('الرمز',''), 'رقم المترشح (مسار)', institution_value, 'المؤسسة الأصلية'],
        [rec.get('المستوى',''), 'المستوى', rec.get('القاعة',''), 'قاعة الامتحان'],
        [rec.get('القسم',''), 'القسم', rec.get('رقم_الامتحان',''), 'رقم الامتحان']
    ]
    
    pdf.set_font('Arial', 'B', 7)  # تعديل هنا: تغيير حجم الخط من 8 إلى 7
    pdf.set_fill_color(230, 230, 230)
    
    # رسم جدول البيانات
    for row_idx, row_data in enumerate(table_data):
        x_cell = x
        for col_idx, cell in enumerate(row_data):
            is_header = col_idx == 1 or col_idx == 3
            pdf.set_xy(x_cell, y)
            pdf.cell(cols[col_idx], ROW_HEIGHT_TABLE1, pdf.ar_text(cell),
                   border=1, align='C', fill=is_header)
            x_cell += cols[col_idx]
        y += ROW_HEIGHT_TABLE1
    
    # إضافة جدول الامتحانات
    y += 3  # مسافة بين الجدولين
    y = draw_exam_schedule_table(pdf, x, invitation_width, y)
    
    return y  # نعيد موضع Y النهائي بعد الانتهاء من معالجة الاستدعاء


def draw_exam_schedule_table(pdf, x_start, table_width, y_start):
    """
    رسم جدول الامتحانات
    
    المعاملات:
        pdf: كائن PDF
        x_start: نقطة البداية الأفقية للجدول
        table_width: عرض الجدول
        y_start: نقطة البداية الرأسية للجدول
        
    العوائد:
        y: موضع Y النهائي بعد رسم الجدول
    """
    # تحديد عرض الأعمدة للجدول (5 أعمدة)
    col_widths = [table_width*0.2, table_width*0.2, table_width*0.2, table_width*0.2, table_width*0.2]
    
    # تعريف ارتفاع الصفوف
    row_height = 7  # ارتفاع الصفوف العادية
    header_height = 7  # ارتفاع صفوف الترويسة
    
    # تحديد بداية الرسم
    x = x_start
    y = y_start
    
    # الحصول على بيانات جدول الامتحانات
    exam_schedule_data = fetch_exam_schedule()
    first_record_day_date = ("", "")
    if exam_schedule_data and len(exam_schedule_data) > 0:
        first_record = exam_schedule_data[0]
        first_record_day_date = (first_record[0] or "", first_record[1] or "")
    
    # عنوان الجدول
    pdf.set_font('Arial', 'B', 8)  # تعديل هنا: تغيير حجم الخط من 9 إلى 8
    pdf.set_text_color(0, 0, 0)
    pdf.set_fill_color(200, 200, 200)  # لون رمادي فاتح للخلفية
    
    # عرض العنوان الرئيسي
    pdf.set_xy(x, y)
    pdf.cell(table_width, header_height, pdf.ar_text("جدول الامتحانات"), border=1, align='C', fill=True)
    y += header_height
    
    # دمج العمود الأول والثاني معًا والعمود الثالث والرابع معًا في رأس الجدول
    col1_2_width = col_widths[0] + col_widths[1]  # عرض العمود الأول + الثاني
    col3_4_width = col_widths[2] + col_widths[3]  # عرض العمود الثالث + الرابع
    
    x_cell = x
    # 1. رسم الخلية المدمجة الأولى (العمود 1+2)
    pdf.set_xy(x_cell, y)
    pdf.cell(col1_2_width, header_height, pdf.ar_text('بعد الزوال'), border=1, align='C', fill=True)
    x_cell += col1_2_width
    
    # 2. رسم الخلية المدمجة الثانية (العمود 3+4)
    pdf.set_xy(x_cell, y)
    pdf.cell(col3_4_width, header_height, pdf.ar_text('صباحا'), border=1, align='C', fill=True)
    x_cell += col3_4_width
    
    # 3. رسم الخلية الخامسة (بدون دمج)
    pdf.set_xy(x_cell, y)
    pdf.cell(col_widths[4], header_height, pdf.ar_text('اليوم'), border=1, align='C', fill=True)
    
    y += header_height
    
    # رسم صف العناوين الفرعية
    headers = ['الحصة الرابعة', 'الحصة الثالثة', 'الحصة الثانية', 'الحصة الأولى', 'التاريخ']
    
    x_cell = x
    for i in range(4):  # أول 4 عناوين
        pdf.set_xy(x_cell, y)
        pdf.cell(col_widths[i], header_height, pdf.ar_text(headers[i]), border=1, align='C', fill=True)
        x_cell += col_widths[i]
    
    # إضافة العمود الخامس
    pdf.set_xy(x_cell, y)
    pdf.cell(col_widths[4], header_height, pdf.ar_text(headers[4]), border=1, align='C', fill=True)
    
    y += header_height
    
    # رسم صفوف البيانات
    pdf.set_font('Arial', 'B', 6)  # تعديل هنا: تغيير حجم الخط من 8 إلى 6 (نقصان بمقدار نقطتين)
    pdf.set_fill_color(255, 255, 255)
    
    # تحديد عدد الصفوف (بحد أقصى 3 صفوف)
    max_rows = min(3, len(exam_schedule_data))
    
    # رسم صفوف البيانات
    for row_idx in range(max_rows):
        if row_idx < len(exam_schedule_data):
            row_data = exam_schedule_data[row_idx]
            
            # تحديد اليوم والتاريخ
            if row_idx == 2 and first_record_day_date[0]:
                day = first_record_day_date[0]
                date = first_record_day_date[1]
            else:
                day = row_data[0] or ""
                date = row_data[1] or ""
            
            # بيانات الحصص
            subjects = [
                (row_data[2] or "", row_data[3] or ""),  # الحصة1 والتوقيت1
                (row_data[4] or "", row_data[5] or ""),  # الحصة2 والتوقيت2
                (row_data[6] or "", row_data[7] or ""),  # الحصة3 والتوقيت3
                (row_data[8] or "", row_data[9] or "")   # الحصة4 والتوقيت4
            ]
            
            # رسم خلايا الحصص (من اليمين إلى اليسار)
            x_cell = x
            for col_idx in range(4):
                subject, time = subjects[3 - col_idx]  # عكس الترتيب
                cell_text = f"{subject}\n({time})" if subject and time else ""
                
                pdf.set_xy(x_cell, y)
                
                # تقسيم النص إلى سطرين
                if "\n" in cell_text:
                    # رسم خلية فارغة أولاً
                    pdf.cell(col_widths[col_idx], row_height, '', border=1, align='C')
                    
                    # ثم رسم كل سطر
                    parts = cell_text.split("\n")
                    line_height = row_height / 2
                    
                    # السطر الأول (المادة)
                    pdf.set_xy(x_cell, y)
                    pdf.cell(col_widths[col_idx], line_height, pdf.ar_text(parts[0]), border=0, align='C')
                    
                    # السطر الثاني (التوقيت)
                    pdf.set_xy(x_cell, y + line_height)
                    pdf.cell(col_widths[col_idx], line_height, pdf.ar_text(parts[1]), border=0, align='C')
                else:
                    # إذا كان النص قصيراً
                    pdf.cell(col_widths[col_idx], row_height, pdf.ar_text(cell_text), border=1, align='C')
                
                x_cell += col_widths[col_idx]
            
            # رسم خلية اليوم والتاريخ
            pdf.set_xy(x_cell, y)
            day_date_text = f"{day}\n{date}" if day and date else day or date
            
            if "\n" in day_date_text:
                # رسم خلية فارغة أولاً
                pdf.cell(col_widths[4], row_height, '', border=1, align='C')
                
                # ثم رسم كل سطر
                parts = day_date_text.split("\n")
                line_height = row_height / 2
                
                # السطر الأول (اليوم)
                pdf.set_xy(x_cell, y)
                pdf.cell(col_widths[4], line_height, pdf.ar_text(parts[0]), border=0, align='C')
                
                # السطر الثاني (التاريخ)
                pdf.set_xy(x_cell, y + line_height)
                pdf.cell(col_widths[4], line_height, pdf.ar_text(parts[1]), border=0, align='C')
            else:
                # إذا كان النص قصيراً
                pdf.cell(col_widths[4], row_height, pdf.ar_text(day_date_text), border=1, align='C')
        else:
            # رسم صفوف فارغة
            x_cell = x
            for col_idx in range(5):
                pdf.set_xy(x_cell, y)
                pdf.cell(col_widths[col_idx], row_height, '', border=1)
                x_cell += col_widths[col_idx]
        
        y += row_height
    
    # إضافة صفوف فارغة إذا كان العدد أقل من 3
    for row_idx in range(max_rows, 3):
        x_cell = x
        for col_idx in range(5):
            # إذا كان هذا هو الصف الثالث والعمود الخامس، نعرض بيانات السجل الأول
            if row_idx == 2 and col_idx == 4 and first_record_day_date[0]:
                day = first_record_day_date[0]
                date = first_record_day_date[1]
                day_date_text = f"{day}\n{date}" if day and date else day or date
                
                if "\n" in day_date_text:
                    # رسم خلية فارغة أولاً
                    pdf.set_xy(x_cell, y)
                    pdf.cell(col_widths[col_idx], row_height, '', border=1)
                    
                    # ثم رسم كل سطر
                    parts = day_date_text.split("\n")
                    line_height = row_height / 2
                    
                    pdf.set_xy(x_cell, y)
                    pdf.cell(col_widths[col_idx], line_height, pdf.ar_text(parts[0]), border=0, align='C')
                    
                    pdf.set_xy(x_cell, y + line_height)
                    pdf.cell(col_widths[col_idx], line_height, pdf.ar_text(parts[1]), border=0, align='C')
                else:
                    pdf.set_xy(x_cell, y)
                    pdf.cell(col_widths[col_idx], row_height, pdf.ar_text(day_date_text), border=1, align='C')
            else:
                pdf.set_xy(x_cell, y)
                pdf.cell(col_widths[col_idx], row_height, '', border=1)
            
            x_cell += col_widths[col_idx]
        
        y += row_height
    
    # إضافة رسالة تذكيرية صغيرة أسفل الجدول
    # تغيير الخط من مائل إلى عادي لتجنب استخدام helveticaI وتأكد من استخدام خط Arial
    pdf.set_font('Arial', '', 7)  # استخدام خط عادي بدلاً من مائل (I)
    pdf.set_xy(x, y + 1)
    
    # تحديث طريقة استدعاء cell للتوافق مع الإصدارات الحديثة من FPDF
    reminder_text = "* يرجى الحضور قبل 15 دقيقة من بداية كل امتحان وإحضار بطاقة التعريف الوطنية"
    pdf.cell(table_width, 5, pdf.ar_text(reminder_text), border=0, align='R')
    
    # إضافة مسافة بعد الرسالة التذكيرية
    y += 6
    
    # محاولة عرض صورة التوجيهات إذا كانت موجودة
    desktop_path = os.path.join(os.path.expanduser("~"), "Desktop")
    app_folder = os.path.join(desktop_path, "تقارير برنامج المعين في الحراسة العامة")
    images_folder = os.path.join(app_folder, "صور التوجيهات")
    image_path = os.path.join(images_folder, "توجيهات_المترشح.png")

    # التحقق من وجود الصورة
    if os.path.exists(image_path):
        # حساب عرض الصورة ليكون مساوياً لعرض الجدول
        image_width = table_width
        
        # تقدير ارتفاع مناسب للصورة مع الحفاظ على النسبة
        try:
            # استخدام PIL لقراءة أبعاد الصورة الفعلية
            from PIL import Image
            with Image.open(image_path) as img:
                w_orig, h_orig = img.size
                # حساب النسبة المئوية للارتفاع مقارنة بالعرض
                aspect_ratio = h_orig / w_orig
                # تحديد ارتفاع الصورة بناءً على العرض المحدد
                # تعديل هنا: زيادة معامل تصغير الصورة من 0.7 إلى 0.735 (زيادة 5%)
                image_height = image_width * aspect_ratio * 0.735
                # التأكد من أن الارتفاع لا يتجاوز حداً أقصى
                max_height = 42  # تعديل هنا: زيادة الحد الأقصى من 40 إلى 42
                image_height = min(image_height, max_height)
        except Exception:
            # إذا لم تتمكن من قراءة أبعاد الصورة، استخدم قيمة ثابتة
            image_height = 37  # تعديل هنا: زيادة ارتفاع الصورة الثابت من 35 إلى 37
        
        # إعداد إطار الصورة بلون أزرق وسمك 0.3
        pdf.set_draw_color(0, 0, 255)  # لون أزرق للإطار
        pdf.set_line_width(0.3)  # سمك الإطار
        
        # رسم إطار للصورة
        pdf.rect(x, y, image_width, image_height)
        
        # إضافة الصورة
        pdf.image(image_path, x=x, y=y, w=image_width, h=image_height)
        
        # تحديث موضع Y بعد إضافة الصورة
        y += image_height + 3
    else:
        # إذا لم تكن الصورة موجودة، عرض رسالة بسيطة
        pdf.set_font('Arial', 'B', 8)  # خط غامق للعنوان
        pdf.set_xy(x, y)
        pdf.cell(table_width, 5, pdf.ar_text("توجيهات هامة للمترشح:"), 0, 1, 'R')
        
        # استرجاع ملاحظات من جدول جدولة_الامتحان
        try:
            db_path = os.path.join(os.path.dirname(__file__), 'data.db')
            conn = sqlite3.connect(db_path)
            cursor = conn.cursor()
            
            # استعلام عن الملاحظات
            cursor.execute("SELECT ملاحظات FROM جدولة_الامتحان WHERE id = 1")
            result = cursor.fetchone()
            
            if result and result[0]:
                # عرض نص بسيط
                pdf.set_font('Arial', '', 7)
                pdf.set_xy(x, y + 5)
                pdf.multi_cell(table_width, 3, pdf.ar_text("يرجى الالتزام بالتعليمات العامة للامتحان ومواعيد الحصص."), 0, 'R')
                
            conn.close()
        except Exception as e:
            print(f"خطأ في استرجاع الملاحظات: {str(e)}")
        
        # تحديث موضع Y
        y += 10
    
    return y  # نعيد موضع Y النهائي بعد رسم الجدول والصورة


def fetch_exam_schedule():
    """
    استرجاع جدول الامتحانات من قاعدة البيانات
    
    العوائد:
        قائمة بسجلات جدول الامتحانات
    """
    try:
        db_path = os.path.join(os.path.dirname(__file__), 'data.db')
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # الحصول على السنة الدراسية والأسدس الحاليين
        cursor.execute("SELECT السنة_الدراسية, الأسدس FROM بيانات_المؤسسة LIMIT 1")
        result = cursor.fetchone()
        academic_year = result[0] if result and result[0] else ""
        semester = result[1] if result and result[1] else ""
        
        # استعلام عن جدول الامتحانات
        cursor.execute("""
            SELECT اليوم, التاريخ,
                   الحصة1, التوقيت1,
                   الحصة2, التوقيت2,
                   الحصة3, التوقيت3,
                   الحصة4, التوقيت4
            FROM جدولة_الامتحان
            WHERE السنة_الدراسية = ? AND الأسدس = ?
            ORDER BY id
        """, (academic_year, semester))
        
        exam_schedule_data = cursor.fetchall()
        conn.close()
        return exam_schedule_data
    
    except Exception as e:
        print(f"خطأ في استرجاع جدول الامتحانات: {str(e)}")
        return []

def generate_horizontal_report(logo_path, records, output_path, report_title=None):
    """
    إنشاء تقرير المترشحين في صفحات أفقية مع استدعائين في كل صفحة
    
    المعاملات:
        logo_path: مسار شعار المؤسسة
        records: سجلات المترشحين
        output_path: مسار ملف الخرج
        report_title: عنوان التقرير (اختياري)
    """
    pdf = ArabicHorizontalPDF()
    
    # ترتيب السجلات حسب رقم الامتحان
    def get_exam_number(record):
        exam_number = record.get('رقم_الامتحان', '0')
        if isinstance(exam_number, int):
            return exam_number
        elif isinstance(exam_number, str) and exam_number.isdigit():
            return int(exam_number)
        else:
            return 0

    records.sort(key=get_exam_number)
    
    # معالجة السجلات في أزواج
    for i in range(0, len(records), 2):
        pdf.add_page()
        
        # حساب عرض الصفحة المتاح (بعد طرح الهوامش)
        available_width = pdf.w - PAGE_MARGIN_LEFT - PAGE_MARGIN_RIGHT
        
        # معدل تباعد بين الاستدعاءين
        spacing = 10
        
        # حساب عرض كل استدعاء مع ضمان هوامش متساوية
        # المعادلة: (العرض المتاح - التباعد) / 2 * معامل التكبير
        invitation_width = ((available_width - spacing) / 2) * 1.05
        
        # حساب المواقع الأفقية للاستدعاءين بحيث تكون الهوامش متساوية
        # الاستدعاء الأول: من الهامش الأيسر
        x_left = PAGE_MARGIN_LEFT
        
        # الاستدعاء الثاني: بعد الهامش الأيسر + عرض الاستدعاء الأول + التباعد
        x_right = PAGE_MARGIN_LEFT + invitation_width + spacing
        
        # رسم الاستدعاء الأول (دائماً موجود)
        process_invitation_horizontal(
            pdf, records[i], logo_path, report_title, 
            x_left, invitation_width, PAGE_MARGIN_TOP
        )
        
        # رسم الاستدعاء الثاني (إذا كان موجوداً)
        if i + 1 < len(records):
            process_invitation_horizontal(
                pdf, records[i+1], logo_path, report_title, 
                x_right, invitation_width, PAGE_MARGIN_TOP
            )
            
            # رسم خط فاصل بين الاستدعائين
            pdf.set_draw_color(128, 128, 128)  # لون رمادي للخط الفاصل
            pdf.set_line_width(0.3)
            # ضبط موضع الخط الفاصل ليكون في المنتصف تماماً
            separator_x = x_left + invitation_width + (spacing / 2)
            pdf.line(
                separator_x, PAGE_MARGIN_TOP, 
                separator_x, pdf.h - PAGE_MARGIN_BOTTOM
            )

    # إنشاء المجلد إذا لم يكن موجوداً
    os.makedirs(os.path.dirname(output_path), exist_ok=True)
    pdf.output(output_path)
    print(f"تم إنشاء التقرير بنجاح: {output_path}")
    return output_path


def print_horizontal_invitations(parent=None, level=None, report_title=None, filter_criteria=None, output_dir=None):
    """
    دالة لإنشاء استدعاءات المترشحين في وضع أفقي، يمكن استدعاؤها من واجهة المستخدم
    
    المعاملات:
        parent: كائن النافذة الأم (لعرض رسائل)
        level: المستوى لتصفية البيانات (اختياري)
        report_title: عنوان التقرير (اختياري)
        filter_criteria: معايير التصفية الإضافية (اختياري)
        output_dir: مجلد حفظ التقرير (اختياري)

    العوائد:
        (success, output_path, message): ثلاثية تحدد نجاح العملية ومسار الملف ورسالة النتيجة
    """
    try:
        # تحديد مسار قاعدة البيانات
        db_path = os.path.join(os.path.dirname(__file__), 'data.db')

        # إعداد معايير التصفية
        criteria = filter_criteria or {}

        # تحديد مجلد حفظ التقرير
        if output_dir and os.path.exists(output_dir):
            reports_dir = output_dir
        else:
            # إنشاء مجلد التقارير الافتراضي
            reports_dir = os.path.join(os.path.expanduser('~'), 'Desktop', 'تقارير الامتحانات')
            os.makedirs(reports_dir, exist_ok=True)

        # إضافة المستوى لمعايير التصفية إذا تم تحديده
        if level:
            criteria['المستوى'] = level

        # جلب السجلات
        logo_path, records = fetch_records(db_path, criteria)

        # التحقق من وجود سجلات
        if not records:
            return False, None, "لم يتم العثور على سجلات مطابقة."

        # تحديد عنوان التقرير
        if not report_title:
            report_title = get_report_title(db_path)

        # تحديد اسم الملف بناءً على المعايير
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        
        # إضافة لاحقة للاسم بناءً على معايير التصفية
        suffix_parts = []
        
        if level:
            suffix_parts.append(f"{level}")
        
        if criteria.get('المؤسسة_الأصلية'):
            suffix_parts.append(f"{criteria['المؤسسة_الأصلية']}")
        
        suffix_parts.append("افقي_استدعائين")
        
        file_suffix = "_".join(suffix_parts)
        if file_suffix:
            file_suffix = f"_{file_suffix}"

        # إنشاء اسم الملف
        output_path = os.path.join(reports_dir, f"استدعاءات_المترشحين{file_suffix}_{timestamp}.pdf")

        # إنشاء التقرير
        generate_horizontal_report(logo_path, records, output_path, report_title)

        # فتح الملف بعد إنشائه
        try:
            if sys.platform == 'win32':
                os.startfile(output_path)
            else:
                import subprocess
                if sys.platform == 'darwin':  # macOS
                    subprocess.call(['open', output_path])
                else:  # Linux
                    subprocess.call(['xdg-open', output_path])
        except Exception as e:
            return True, output_path, f"تم إنشاء التقرير بنجاح ولكن تعذر فتح الملف: {str(e)}"

        return True, output_path, "تم إنشاء التقرير بنجاح."
    
    except Exception as e:
        traceback.print_exc()
        return False, None, f"حدث خطأ: {str(e)}"


if __name__ == '__main__':
    try:
        # اختبار إنشاء تقرير
        db = os.path.join(os.path.dirname(__file__), 'data.db')
        logo, recs = fetch_records(db)

        if not recs:
            print("لم يتم العثور على سجلات في قاعدة البيانات.")
            sys.exit(1)

        # الحصول على عنوان التقرير من قاعدة البيانات
        report_title = get_report_title(db)

        out = os.path.join(os.path.expanduser('~'), 'Desktop', 'تقارير الامتحانات', f"استدعاءات_افقي_{datetime.now().strftime('%Y%m%d_%H%M%S')}.pdf")
        
        print("جاري إنشاء استدعاءات المترشحين في وضع أفقي (استدعائين في كل صفحة)...")
        generate_horizontal_report(logo, recs, out, report_title)
        
        # فتح الملف بعد إنشائه
        if sys.platform == 'win32':
            os.startfile(out)
        else:
            if sys.platform == 'darwin':  # macOS
                subprocess.call(['open', out])
            else:  # Linux
                subprocess.call(['xdg-open', out])
                
        print("تم إنشاء التقرير بنجاح.")
        
    except Exception as e:
        print(f"خطأ: {str(e)}")
        traceback.print_exc()

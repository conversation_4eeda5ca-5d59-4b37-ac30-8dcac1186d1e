from PyQt5.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QLabel, QCheckBox, QPushButton,
    QDialogButtonBox, QMessageBox
)
from PyQt5.QtGui import QFont
from PyQt5.QtCore import Qt

class SelectLevelsDialog(QDialog):
    """نافذة اختيار وترتيب المستويات"""
    
    def __init__(self, parent=None, all_levels=None):
        super().__init__(parent)
        self.all_levels = all_levels or []
        self.selected_levels_list = []
        self.available_levels_list = []
        self.initUI()
        
    def initUI(self):
        """تهيئة واجهة المستخدم"""
        self.setWindowTitle("اختيار وترتيب المستويات")
        self.setFixedSize(800, 600)
        self.setStyleSheet("background-color: #33CCCC;")
        
        layout = QVBoxLayout(self)
        layout.setSpacing(10)  # تقليل المسافة بين العناصر
        
        # إضافة عنوان
        title_label = QLabel("اختر المستويات المطلوبة وحدد ترتيبها:")
        title_label.setFont(QFont("Calibri", 14, QFont.Bold))
        title_label.setStyleSheet("color: blue; border: 1.5px solid darkblue; padding: 5px; background-color: white;")
        title_label.setFixedHeight(35)
        layout.addWidget(title_label)
        
        # إنشاء قائمة للمستويات المحددة
        selected_levels_layout = QVBoxLayout()
        selected_levels_layout.setSpacing(5)
        
        # إنشاء قائمة للمستويات المتاحة
        available_levels_layout = QVBoxLayout()
        available_levels_layout.setSpacing(5)
        
        # إضافة عنوان للمستويات المحددة
        selected_title = QLabel("المستويات المحددة:")
        selected_title.setFont(QFont("Calibri", 14, QFont.Bold))
        selected_title.setStyleSheet("color: blue; border: 1.5px solid darkblue; padding: 5px; background-color: white;")
        selected_title.setFixedHeight(35)
        selected_levels_layout.addWidget(selected_title)
        
        # إضافة عنوان للمستويات المتاحة
        available_title = QLabel("المستويات المتاحة:")
        available_title.setFont(QFont("Calibri", 14, QFont.Bold))
        available_title.setStyleSheet("color: blue; border: 1.5px solid darkblue; padding: 5px; background-color: white;")
        available_title.setFixedHeight(35)
        available_levels_layout.addWidget(available_title)
        
        # إضافة المستويات المتاحة
        for level in self.all_levels:
            checkbox = QCheckBox(level)
            checkbox.setFont(QFont("Calibri", 13, QFont.Bold))
            checkbox.setStyleSheet("color: black; background-color: white;")
            checkbox.setFixedHeight(35)
            available_levels_layout.addWidget(checkbox)
            self.available_levels_list.append(checkbox)
        
        # إضافة زر لإضافة المستويات المحددة
        add_button = QPushButton("إضافة المستويات المحددة >>")
        add_button.setFont(QFont("Calibri", 13, QFont.Bold))
        add_button.setStyleSheet("""
            QPushButton {
                background-color: #33CCCC;
                color: black;
                border: 1.5px solid darkblue;
                border-radius: 5px;
            }
            QPushButton:hover {
                background-color: #00FFFF;
            }
        """)
        add_button.setFixedHeight(35)
        
        # إضافة زر لإعادة ترتيب المستويات المحددة
        move_up_button = QPushButton("نقل لأعلى")
        move_up_button.setFont(QFont("Calibri", 13, QFont.Bold))
        move_up_button.setStyleSheet("""
            QPushButton {
                background-color: #33CCCC;
                color: black;
                border: 1.5px solid darkblue;
                border-radius: 5px;
            }
            QPushButton:hover {
                background-color: #00FFFF;
            }
        """)
        move_up_button.setFixedHeight(35)
        
        move_down_button = QPushButton("نقل لأسفل")
        move_down_button.setFont(QFont("Calibri", 13, QFont.Bold))
        move_down_button.setStyleSheet("""
            QPushButton {
                background-color: #33CCCC;
                color: black;
                border: 1.5px solid darkblue;
                border-radius: 5px;
            }
            QPushButton:hover {
                background-color: #00FFFF;
            }
        """)
        move_down_button.setFixedHeight(35)
        
        # إضافة زر لحذف المستويات المحددة
        remove_button = QPushButton("<< حذف المستويات المحددة")
        remove_button.setFont(QFont("Calibri", 13, QFont.Bold))
        remove_button.setStyleSheet("""
            QPushButton {
                background-color: #33CCCC;
                color: black;
                border: 1.5px solid darkblue;
                border-radius: 5px;
            }
            QPushButton:hover {
                background-color: #00FFFF;
            }
        """)
        remove_button.setFixedHeight(35)
        
        # إنشاء تخطيط أفقي للأزرار
        buttons_layout = QHBoxLayout()
        buttons_layout.setSpacing(10)
        buttons_layout.addWidget(add_button)
        buttons_layout.addWidget(remove_button)
        
        # إنشاء تخطيط أفقي للأعمدة
        levels_layout = QHBoxLayout()
        levels_layout.setSpacing(20)
        levels_layout.addLayout(available_levels_layout, 1)
        
        # إنشاء تخطيط عمودي للمستويات المحددة وأزرار الترتيب
        selected_with_buttons_layout = QVBoxLayout()
        selected_with_buttons_layout.setSpacing(10)
        selected_with_buttons_layout.addLayout(selected_levels_layout)
        
        # إضافة أزرار الترتيب
        order_buttons_layout = QHBoxLayout()
        order_buttons_layout.setSpacing(10)
        order_buttons_layout.addWidget(move_up_button)
        order_buttons_layout.addWidget(move_down_button)
        selected_with_buttons_layout.addLayout(order_buttons_layout)
        
        levels_layout.addLayout(selected_with_buttons_layout, 1)
        
        layout.addLayout(levels_layout)
        layout.addLayout(buttons_layout)
        
        # إضافة أزرار موافق وإلغاء
        button_box = QDialogButtonBox(QDialogButtonBox.Ok | QDialogButtonBox.Cancel)
        button_box.accepted.connect(self.accept)
        button_box.rejected.connect(self.reject)
        
        # تنسيق أزرار موافق وإلغاء
        ok_button = button_box.button(QDialogButtonBox.Ok)
        ok_button.setText("موافق")
        ok_button.setFont(QFont("Calibri", 13, QFont.Bold))
        ok_button.setStyleSheet("""
            QPushButton {
                background-color: #33CCCC;
                color: black;
                border: 1.5px solid darkblue;
                border-radius: 5px;
                min-width: 100px;
            }
            QPushButton:hover {
                background-color: #00FFFF;
            }
        """)
        ok_button.setFixedHeight(35)
        
        cancel_button = button_box.button(QDialogButtonBox.Cancel)
        cancel_button.setText("إلغاء")
        cancel_button.setFont(QFont("Calibri", 13, QFont.Bold))
        cancel_button.setStyleSheet("""
            QPushButton {
                background-color: #33CCCC;
                color: black;
                border: 1.5px solid darkblue;
                border-radius: 5px;
                min-width: 100px;
            }
            QPushButton:hover {
                background-color: #00FFFF;
            }
        """)
        cancel_button.setFixedHeight(35)
        
        layout.addWidget(button_box)
        
        # تعريف الدوال للأزرار
        def add_selected_levels():
            for checkbox in self.available_levels_list:
                if checkbox.isChecked():
                    # إنشاء زر للمستوى المحدد
                    level_button = QPushButton(checkbox.text())
                    level_button.setFont(QFont("Calibri", 13, QFont.Bold))
                    level_button.setStyleSheet("""
                        QPushButton {
                            background-color: white;
                            color: black;
                            border: 1.5px solid darkblue;
                            border-radius: 5px;
                            text-align: right;
                        }
                        QPushButton:checked {
                            background-color: #00FFFF;
                        }
                    """)
                    level_button.setCheckable(True)
                    level_button.setFixedHeight(35)
                    selected_levels_layout.addWidget(level_button)
                    self.selected_levels_list.append(level_button)
                    checkbox.setChecked(False)
        
        def remove_selected_levels():
            for button in self.selected_levels_list[:]:
                if button.isChecked():
                    selected_levels_layout.removeWidget(button)
                    self.selected_levels_list.remove(button)
                    button.deleteLater()
        
        def move_up():
            for i, button in enumerate(self.selected_levels_list):
                if button.isChecked() and i > 0:
                    # تبديل الأزرار
                    self.selected_levels_list[i], self.selected_levels_list[i-1] = self.selected_levels_list[i-1], self.selected_levels_list[i]
                    
                    # إعادة ترتيب الأزرار في التخطيط
                    for j, btn in enumerate(self.selected_levels_list):
                        selected_levels_layout.removeWidget(btn)
                    
                    for j, btn in enumerate(self.selected_levels_list):
                        selected_levels_layout.insertWidget(j + 1, btn)  # +1 للعنوان
                    
                    break
        
        def move_down():
            for i, button in enumerate(self.selected_levels_list):
                if button.isChecked() and i < len(self.selected_levels_list) - 1:
                    # تبديل الأزرار
                    self.selected_levels_list[i], self.selected_levels_list[i+1] = self.selected_levels_list[i+1], self.selected_levels_list[i]
                    
                    # إعادة ترتيب الأزرار في التخطيط
                    for j, btn in enumerate(self.selected_levels_list):
                        selected_levels_layout.removeWidget(btn)
                    
                    for j, btn in enumerate(self.selected_levels_list):
                        selected_levels_layout.insertWidget(j + 1, btn)  # +1 للعنوان
                    
                    break
        
        # ربط الدوال بالأزرار
        add_button.clicked.connect(add_selected_levels)
        remove_button.clicked.connect(remove_selected_levels)
        move_up_button.clicked.connect(move_up)
        move_down_button.clicked.connect(move_down)
    
    def get_selected_levels(self):
        """الحصول على المستويات المحددة"""
        return [button.text() for button in self.selected_levels_list]

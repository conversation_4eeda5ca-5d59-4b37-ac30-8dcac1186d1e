#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
وحدة طباعة تقرير سجل مخالفات التلميذ بتنسيق محسن
تستخدم نفس الخطوط والتنسيق المستخدم في ملف print_violation_report.py
"""

import os
import traceback
import sqlite3
from datetime import datetime
import webbrowser

# استيراد مكتبات ReportLab لإنشاء ملفات PDF
try:
    from reportlab.lib.pagesizes import A4, landscape
    from reportlab.pdfgen import canvas
    from reportlab.lib.units import cm
    from reportlab.pdfbase import pdfmetrics
    from reportlab.pdfbase.ttfonts import TTFont
    from reportlab.platypus import Table, TableStyle
    from reportlab.lib import colors
    REPORTLAB_AVAILABLE = True
except ImportError:
    REPORTLAB_AVAILABLE = False
    print("تنبيه: مكتبة ReportLab غير متوفرة. قم بتثبيت: pip install reportlab")

# استيراد مكتبات معالجة النصوص العربية
try:
    import arabic_reshaper
    from bidi.algorithm import get_display
    ARABIC_SUPPORT = True
    print("تم تحميل مكتبات معالجة النصوص العربية بنجاح.")
except ImportError:
    ARABIC_SUPPORT = False
    print("تنبيه: مكتبات معالجة النصوص العربية غير متوفرة. قم بتثبيت: pip install arabic-reshaper python-bidi")

def fix_arabic_text(text):
    """معالجة النص العربي وتجهيزه للعرض في PDF"""
    if not text:
        return ""
    text = str(text).strip()
    if not text:
        return ""
    try:
        if ARABIC_SUPPORT:
            # إعادة تشكيل النص العربي
            reshaped_text = arabic_reshaper.reshape(text)
            # تطبيق خوارزمية BIDI لعرض النص بشكل صحيح
            bidi_text = get_display(reshaped_text)
            return bidi_text
        else:
            return text
    except Exception as e:
        print(f"خطأ في معالجة النص العربي '{text[:20]}...': {e}")
        return text

def register_fonts():
    """تسجيل الخطوط العربية المطلوبة"""
    script_dir = os.path.dirname(os.path.abspath(__file__))

    # قائمة بالخطوط المطلوبة ومساراتها المحتملة
    fonts_to_register = [
        {
            'name': 'Arabic',
            'paths': [
                'arial.ttf',
                os.path.join(script_dir, 'arial.ttf'),
                os.path.join(script_dir, 'fonts', 'arial.ttf'),
                os.path.join('C:\\Windows\\Fonts', 'arial.ttf')
            ]
        },
        {
            'name': 'Amiri',
            'paths': [
                'Amiri-Regular.ttf',
                os.path.join(script_dir, 'Amiri-Regular.ttf'),
                os.path.join(script_dir, 'fonts', 'Amiri-Regular.ttf')
            ]
        },
        {
            'name': 'Amiri-Bold',
            'paths': [
                'Amiri-Bold.ttf',
                os.path.join(script_dir, 'Amiri-Bold.ttf'),
                os.path.join(script_dir, 'fonts', 'Amiri-Bold.ttf')
            ]
        },
        {
            'name': 'Calibri',
            'paths': [
                'calibri.ttf',
                os.path.join(script_dir, 'calibri.ttf'),
                os.path.join(script_dir, 'fonts', 'calibri.ttf'),
                os.path.join('C:\\Windows\\Fonts', 'calibri.ttf')
            ]
        },
        {
            'name': 'Calibri-Bold',
            'paths': [
                'calibrib.ttf',
                os.path.join(script_dir, 'calibrib.ttf'),
                os.path.join(script_dir, 'fonts', 'calibrib.ttf'),
                os.path.join('C:\\Windows\\Fonts', 'calibrib.ttf')
            ]
        }
    ]

    # تسجيل كل خط من القائمة
    for font in fonts_to_register:
        font_registered = False
        for path in font['paths']:
            try:
                if os.path.exists(path):
                    pdfmetrics.registerFont(TTFont(font['name'], path))
                    print(f"تم تسجيل خط {font['name']} من المسار: {path}")
                    font_registered = True
                    break
            except Exception as font_error:
                print(f"خطأ في تسجيل خط {font['name']} من المسار {path}: {font_error}")

        if not font_registered:
            print(f"تحذير: لم يتم تسجيل خط {font['name']} من أي مسار")

    # إذا لم يتم تسجيل خط Amiri-Bold، استخدم Amiri العادي كبديل
    try:
        if 'Amiri-Bold' not in pdfmetrics.getRegisteredFontNames() and 'Amiri' in pdfmetrics.getRegisteredFontNames():
            # استخدام نفس مسار ملف Amiri العادي لتسجيل Amiri-Bold
            amiri_path = None
            for path in fonts_to_register[1]['paths']:
                if os.path.exists(path):
                    amiri_path = path
                    break

            if amiri_path:
                pdfmetrics.registerFont(TTFont('Amiri-Bold', amiri_path))
                print(f"تم استخدام خط Amiri العادي كبديل لـ Amiri-Bold من المسار: {amiri_path}")
    except Exception as fallback_error:
        print(f"خطأ في استخدام خط Amiri كبديل لـ Amiri-Bold: {fallback_error}")

    # طباعة قائمة الخطوط المسجلة للتشخيص
    print(f"الخطوط المسجلة: {pdfmetrics.getRegisteredFontNames()}")

def get_institution_info(db_path=None):
    """الحصول على معلومات المؤسسة من قاعدة البيانات"""
    try:
        if db_path is None:
            db_path = "data.db"

        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()

        # محاولة قراءة من جدول بيانات_المؤسسة
        try:
            cursor.execute("SELECT المؤسسة, السنة_الدراسية, ImagePath1 FROM بيانات_المؤسسة LIMIT 1")
            institution_row = cursor.fetchone()
            if institution_row:
                institution_data = {
                    'name': institution_row[0],
                    'school_year': institution_row[1],
                    'logo_path': institution_row[2] if institution_row[2] and os.path.exists(institution_row[2]) else None
                }
                return institution_data
        except Exception as e:
            print(f"خطأ في قراءة بيانات المؤسسة: {e}")

        conn.close()
    except Exception as db_error:
        print(f"خطأ في الاتصال بقاعدة البيانات: {db_error}")

    # إرجاع قاموس فارغ في حالة حدوث خطأ
    return {}

def create_student_violations_record_pdf(student_info, violations_records, file_path, db_path=None):
    """إنشاء ملف PDF لسجل مخالفات التلميذ بتصميم محسن

    Args:
        student_info (dict): قاموس يحتوي على معلومات التلميذ
        violations_records (list): قائمة من قواميس تحتوي على معلومات المخالفات
        file_path (str): مسار حفظ ملف PDF
        db_path (str, optional): مسار قاعدة البيانات. Defaults to None.

    Returns:
        bool: True إذا تم إنشاء الملف بنجاح، False في حالة حدوث خطأ
    """
    if not REPORTLAB_AVAILABLE:
        print("مكتبة ReportLab غير متوفرة. لا يمكن إنشاء ملف PDF.")
        return False

    try:
        # تسجيل الخطوط العربية
        register_fonts()

        # الحصول على بيانات المؤسسة
        institution_data = get_institution_info(db_path)

        # إنشاء ملف PDF - استخدام الاتجاه الأفقي (Landscape)
        c = canvas.Canvas(file_path, pagesize=landscape(A4))
        width, height = landscape(A4)  # تبديل العرض والارتفاع للاتجاه الأفقي

        # تعريف الهوامش
        margin = 2 * cm
        right_x = width - margin
        left_x = margin
        center_x = width / 2

        # إضافة الشعار في أعلى الصفحة
        logo_path = institution_data.get('logo_path')
        logo_height = 70  # ارتفاع الشعار بالبكسل
        if logo_path and os.path.exists(logo_path):
            try:
                # ترك هامش أعلى بمقدار 0.3 سم ثم وضع الشعار
                logo_width = 300  # عرض الشعار بالبكسل
                logo_y_position = height - (0.3*cm + logo_height)  # هامش 0.3 سم من أعلى الصفحة
                c.drawImage(logo_path, center_x - (logo_width/2), logo_y_position, width=logo_width, height=logo_height, preserveAspectRatio=True)
                print(f"تم رسم الشعار بنجاح من: {logo_path}")
            except Exception as e:
                print(f"خطأ في رسم الشعار: {e}")
                logo_height = 0  # تعيين ارتفاع الشعار إلى صفر في حالة حدوث خطأ
        else:
            logo_height = 0  # تعيين ارتفاع الشعار إلى صفر إذا لم يكن متوفراً

        # إضافة معلومات المؤسسة في وسط الصفحة بعد مسافة 0.7 سم من الشعار
        y_position = height - (0.3*cm + logo_height + 0.7*cm)

        # اختيار الخط المناسب للعناوين الرئيسية
        try:
            c.setFont('Amiri-Bold', 18)
            print("استخدام خط Amiri-Bold للعنوان الرئيسي")
        except:
            try:
                c.setFont('Amiri', 18)
                print("استخدام خط Amiri للعنوان الرئيسي")
            except:
                c.setFont('Arabic', 18)
                print("استخدام خط Arabic للعنوان الرئيسي")

        # اسم المؤسسة
        institution_name = institution_data.get('name', '')
        c.drawCentredString(center_x, y_position, fix_arabic_text(institution_name))

        # السنة الدراسية
        y_position -= 0.7*cm  # تعديل المسافة إلى 0.7 سم
        # اختيار الخط المناسب للعناوين الفرعية
        try:
            c.setFont('Amiri-Bold', 14)
            print("استخدام خط Amiri-Bold للعنوان الفرعي")
        except:
            try:
                c.setFont('Amiri', 14)
                print("استخدام خط Amiri للعنوان الفرعي")
            except:
                c.setFont('Arabic', 14)
                print("استخدام خط Arabic للعنوان الفرعي")

        school_year = institution_data.get('school_year', '')
        c.drawCentredString(center_x, y_position, fix_arabic_text(f"السنة الدراسية: {school_year}"))

        # عنوان التقرير
        y_position -= 0.7*cm  # تعديل المسافة إلى 0.7 سم
        # اختيار الخط المناسب لعنوان التقرير
        try:
            c.setFont('Amiri-Bold', 16)
            print("استخدام خط Amiri-Bold لعنوان التقرير")
        except:
            try:
                c.setFont('Amiri', 16)
                print("استخدام خط Amiri لعنوان التقرير")
            except:
                c.setFont('Arabic', 16)
                print("استخدام خط Arabic لعنوان التقرير")

        # إضافة لون أزرق غامق للعنوان
        c.setFillColorRGB(0.2, 0.4, 0.6)  # لون أزرق غامق للعنوان

        c.drawCentredString(center_x, y_position, fix_arabic_text("سجل مخالفات التلميذ"))
        c.setFillColorRGB(0, 0, 0)  # إعادة لون النص إلى الأسود

        # إضافة معلومات التلميذ
        y_position -= 0.7*cm  # تعديل المسافة إلى 0.7 سم

        # جدول معلومات التلميذ
        student_info_width = 16*cm
        student_info_height = 2*cm
        student_info_x = center_x - (student_info_width / 2)

        # رسم إطار جدول معلومات التلميذ
        c.setStrokeColorRGB(0.2, 0.4, 0.6)  # لون أزرق غامق للإطار
        c.setLineWidth(1.5)  # سمك الخط
        c.rect(student_info_x, y_position - student_info_height, student_info_width, student_info_height)

        # رسم الخطوط الداخلية للجدول
        c.line(student_info_x + student_info_width/2, y_position, student_info_x + student_info_width/2, y_position - student_info_height)
        c.line(student_info_x, y_position - student_info_height/2, student_info_x + student_info_width, y_position - student_info_height/2)

        # إعادة سمك الخط إلى القيمة الافتراضية
        c.setLineWidth(1)
        c.setStrokeColorRGB(0, 0, 0)  # إعادة لون الخط إلى الأسود

        # كتابة معلومات التلميذ
        # اختيار الخط المناسب لمعلومات التلميذ
        try:
            c.setFont('Calibri-Bold', 14)
            print("استخدام خط Calibri-Bold لمعلومات التلميذ")
        except:
            try:
                c.setFont('Calibri', 14)
                print("استخدام خط Calibri لمعلومات التلميذ")
            except:
                try:
                    c.setFont('Amiri-Bold', 14)
                    print("استخدام خط Amiri-Bold لمعلومات التلميذ")
                except:
                    c.setFont('Arabic', 14)
                    print("استخدام خط Arabic لمعلومات التلميذ")

        # تحسين تنسيق النص داخل الجدول
        c.drawRightString(student_info_x + student_info_width - 0.3*cm, y_position - 0.6*cm, fix_arabic_text("اسم التلميذ: " + student_info.get('name', '')))
        c.drawRightString(student_info_x + student_info_width/2 - 0.3*cm, y_position - 0.6*cm, fix_arabic_text("رمز التلميذ: " + student_info.get('code', '')))
        c.drawRightString(student_info_x + student_info_width - 0.3*cm, y_position - 1.6*cm, fix_arabic_text("المستوى: " + student_info.get('level', '')))
        c.drawRightString(student_info_x + student_info_width/2 - 0.3*cm, y_position - 1.6*cm, fix_arabic_text("القسم: " + student_info.get('section', '')))

        y_position -= student_info_height + 0.7*cm  # تعديل المسافة إلى 0.7 سم

        # عنوان قسم سجل المخالفات
        # اختيار الخط المناسب لعنوان قسم سجل المخالفات
        try:
            c.setFont('Amiri-Bold', 14)
            print("استخدام خط Amiri-Bold لعنوان قسم سجل المخالفات")
        except:
            try:
                c.setFont('Amiri', 14)
                print("استخدام خط Amiri لعنوان قسم سجل المخالفات")
            except:
                c.setFont('Arabic', 14)
                print("استخدام خط Arabic لعنوان قسم سجل المخالفات")

        # إضافة لون أزرق غامق للعنوان
        c.setFillColorRGB(0.1, 0.3, 0.6)  # لون أزرق غامق للخلفية

        # رسم مستطيل خلفية للعنوان
        title_width = 12*cm  # عرض العنوان
        title_height = 0.9*cm  # ارتفاع العنوان
        c.rect(center_x - title_width/2, y_position - 0.7*cm, title_width, title_height, fill=1, stroke=1)  # إضافة إطار

        # كتابة العنوان بلون أبيض
        c.setFillColorRGB(1, 1, 1)  # لون أبيض للنص
        c.drawCentredString(center_x, y_position - 0.45*cm, fix_arabic_text("سجل المخالفات"))

        # إعادة لون النص إلى الأسود
        c.setFillColorRGB(0, 0, 0)
        y_position -= 0.7*cm  # تعديل المسافة إلى 0.7 سم

        # التحقق من وجود مخالفات
        if not violations_records:
            # اختيار الخط المناسب للرسالة
            try:
                c.setFont('Calibri-Bold', 14)
            except:
                try:
                    c.setFont('Amiri-Bold', 14)
                except:
                    c.setFont('Arabic', 14)

            # رسالة عدم وجود مخالفات
            c.drawCentredString(center_x, y_position, fix_arabic_text("لا توجد مخالفات مسجلة لهذا التلميذ"))

            # حفظ الملف
            c.save()
            print(f"تم حفظ ملف PDF بنجاح: {file_path}")
            return True

        # إنشاء جدول المخالفات
        # تحضير بيانات الجدول
        table_data = [
            [
                fix_arabic_text("رقم المخالفة"),
                fix_arabic_text("التاريخ"),
                fix_arabic_text("المادة"),
                fix_arabic_text("الأستاذ(ة)"),
                fix_arabic_text("وصف المخالفة"),
                fix_arabic_text("الإجراء المتخذ")
            ]
        ]

        # إضافة بيانات المخالفات
        for violation in violations_records:
            # تقسيم النصوص الطويلة إلى أسطر متعددة باستخدام \n لتحسين العرض
            notes = violation.get('notes', '')
            procedures = violation.get('procedures', '')

            # إضافة مسافات بين العناصر المتعددة في وصف المخالفة والإجراء المتخذ
            if ' - ' in notes:
                notes = notes.replace(' - ', '\n- ')

            if ' - ' in procedures:
                procedures = procedures.replace(' - ', '\n- ')

            table_data.append([
                fix_arabic_text(str(violation.get('id', ''))),
                fix_arabic_text(violation.get('date', '')),
                fix_arabic_text(violation.get('subject', '')),
                fix_arabic_text(violation.get('teacher', '')),
                fix_arabic_text(notes),
                fix_arabic_text(procedures)
            ])

        # تحديد عرض الأعمدة - تعديل العرض لتناسب النص بداخلها
        # يمكنك تعديل هذه القيم يدويًا حسب احتياجاتك
        # الترتيب: [رقم المخالفة, التاريخ, المادة, الأستاذ(ة), وصف المخالفة, الإجراء المتخذ]
        col_widths = [1.5*cm, 2*cm, 2.5*cm, 3*cm, 10*cm, 8*cm]

        # تحديد ارتفاع الصفوف - زيادة المسافة بين الأسطر
        row_heights = [40]  # ارتفاع صف العنوان
        for _ in range(len(violations_records)):
            row_heights.append(80)  # زيادة ارتفاع صفوف البيانات أكثر لاستيعاب النص بشكل كامل

        # إنشاء الجدول
        table = Table(
            table_data,
            colWidths=col_widths,
            rowHeights=row_heights
        )

        # تحديد نمط الجدول
        table_style = TableStyle([
            # الحدود الخارجية
            ('BOX', (0, 0), (-1, -1), 1.5, colors.navy),

            # خطوط داخلية
            ('GRID', (0, 0), (-1, -1), 1, colors.navy),

            # تنسيق العناوين
            ('BACKGROUND', (0, 0), (-1, 0), colors.lightgrey),  # خلفية رمادية فاتحة لصف العنوان

            # تنسيق الخطوط - استخدام خط Calibri-Bold بحجم 13 داخل الجدول
            ('FONTNAME', (0, 0), (-1, -1), 'Calibri-Bold'),  # استخدام خط Calibri-Bold للجدول بأكمله

            # حجم الخط
            ('FONTSIZE', (0, 0), (-1, 0), 13),  # حجم الخط 13 لصف العنوان
            ('FONTSIZE', (0, 1), (-1, -1), 13),  # حجم الخط 13 لباقي الجدول

            # محاذاة النص
            ('ALIGNMENT', (0, 0), (-1, -1), 'RIGHT'),  # محاذاة يمين لكل الجدول
            ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),  # محاذاة وسط عمودية

            # المسافات الداخلية - تعديل المسافات لتناسب النص
            ('TOPPADDING', (0, 0), (-1, -1), 10),  # تقليل المسافة العلوية
            ('BOTTOMPADDING', (0, 0), (-1, -1), 10),  # تقليل المسافة السفلية
            ('RIGHTPADDING', (0, 0), (-1, -1), 15),  # تقليل المسافة من اليمين
            ('LEFTPADDING', (0, 0), (-1, -1), 15),  # تقليل المسافة من اليسار

            # تفعيل التفاف النص (word wrapping) لجميع الأعمدة في الجدول
            ('WORDWRAP', (0, 0), (-1, -1), True),
        ])

        # تطبيق النمط على الجدول
        try:
            table.setStyle(table_style)
        except Exception as style_error:
            print(f"خطأ في تطبيق نمط الجدول: {style_error}")
            # استخدام نمط بسيط بدون خطوط مخصصة
            simple_style = TableStyle([
                ('BOX', (0, 0), (-1, -1), 1, colors.black),
                ('GRID', (0, 0), (-1, -1), 0.5, colors.black),
                ('ALIGNMENT', (0, 0), (-1, -1), 'RIGHT'),
                ('FONTNAME', (0, 0), (-1, -1), 'Calibri-Bold'),
                ('WORDWRAP', (0, 0), (-1, -1), True),  # تفعيل التفاف النص لجميع الأعمدة
            ])
            table.setStyle(simple_style)

        # حساب ارتفاع الجدول
        table_height = sum(row_heights)

        # التحقق من تجاوز الجدول لحدود الصفحة
        available_height = y_position - margin
        if table_height > available_height:
            # إنشاء صفحة جديدة إذا كان الجدول كبيرًا جدًا
            c.showPage()
            y_position = height - margin

            # إضافة عنوان في الصفحة الجديدة
            c.setFont('Amiri-Bold', 16)
            c.setFillColorRGB(0.2, 0.4, 0.6)
            c.drawCentredString(center_x, y_position, fix_arabic_text("تابع سجل مخالفات التلميذ"))
            c.setFillColorRGB(0, 0, 0)
            y_position -= 0.7*cm  # تعديل المسافة إلى 0.7 سم

        # رسم الجدول مع محاذاة إلى اليمين
        # حساب الموضع الأفقي لوضع الجدول محاذيًا إلى اليمين
        table_width = sum(col_widths)
        table_x = width - margin - table_width  # محاذاة إلى اليمين

        # رسم الجدول
        table.wrapOn(c, width - 2*margin, table_height)
        table.drawOn(c, table_x, y_position - table_height)

        # تحديث موضع y بعد رسم الجدول
        y_position -= (table_height + 0.5*cm)

        # إضافة تاريخ الطباعة في أسفل الصفحة
        c.setFont('Calibri', 10)
        today = datetime.now().strftime("%Y-%m-%d")
        c.drawCentredString(center_x, margin/2, fix_arabic_text(f"تاريخ الطباعة: {today}"))

        # حفظ الملف
        c.save()
        print(f"تم حفظ ملف PDF بنجاح: {file_path}")
        return True

    except Exception as e:
        print(f"خطأ في إنشاء ملف PDF لسجل مخالفات التلميذ: {str(e)}")
        traceback.print_exc()
        return False

def open_pdf(file_path):
    """فتح ملف PDF في المتصفح الافتراضي"""
    try:
        print(f"محاولة فتح الملف: {file_path}")

        # التحقق من وجود الملف
        if os.path.exists(file_path):
            print(f"الملف موجود، حجمه: {os.path.getsize(file_path)} بايت")

            # التحقق من امتداد الملف
            if file_path.lower().endswith('.pdf'):
                print("امتداد الملف صحيح: PDF")
            else:
                print(f"تحذير: امتداد الملف غير معتاد: {os.path.splitext(file_path)[1]}")

            # محاولة فتح الملف باستخدام webbrowser
            try:
                absolute_path = os.path.abspath(file_path)
                file_url = f'file:///{absolute_path}'
                print(f"محاولة فتح الملف باستخدام webbrowser: {file_url}")

                # استخدام webbrowser لفتح الملف
                result = webbrowser.open(file_url)

                if result:
                    print(f"تم فتح الملف بنجاح: {file_path}")
                    return True
                else:
                    print(f"فشل في فتح الملف باستخدام webbrowser")

                    # محاولة بديلة باستخدام os.startfile
                    try:
                        if os.name == 'nt':  # Windows
                            print("محاولة فتح الملف باستخدام os.startfile")
                            os.startfile(absolute_path)
                            print("تم فتح الملف بنجاح باستخدام os.startfile")
                            return True
                    except Exception as startfile_error:
                        print(f"فشل في فتح الملف باستخدام os.startfile: {startfile_error}")

                    return False
            except Exception as browser_error:
                print(f"خطأ في فتح الملف باستخدام webbrowser: {browser_error}")
                return False
        else:
            print(f"خطأ: الملف غير موجود: {file_path}")
            return False
    except Exception as e:
        print(f"خطأ في فتح ملف PDF: {str(e)}")
        traceback.print_exc()
        return False

def print_student_violations_record(student_info, violations_records, db_path=None, auto_open=True):
    """طباعة سجل مخالفات التلميذ بتنسيق PDF محسن

    Args:
        student_info (dict): قاموس يحتوي على معلومات التلميذ
        violations_records (list): قائمة من قواميس تحتوي على معلومات المخالفات
        db_path (str, optional): مسار قاعدة البيانات. Defaults to None.
        auto_open (bool, optional): فتح الملف تلقائياً. Defaults to True.

    Returns:
        tuple: (bool, str) - نجاح العملية ومسار الملف
    """
    try:
        # إنشاء مجلد تقارير سجلات المخالفات داخل مجلد البرنامج
        program_dir = os.path.dirname(os.path.abspath(__file__))
        reports_dir = os.path.join(program_dir, "تقارير_سجلات_المخالفات")
        os.makedirs(reports_dir, exist_ok=True)

        # تحديد اسم ملف الإخراج
        student_name = student_info.get('name', 'بدون_اسم').replace(' ', '_')
        student_code = student_info.get('code', 'بدون_رمز')
        current_date = datetime.now().strftime("%Y%m%d")

        # إنشاء اسم الملف باستخدام اسم التلميذ ورمزه وتاريخ الطباعة
        filename = os.path.join(reports_dir, f"سجل_مخالفات_{student_name}_{student_code}_{current_date}.pdf")

        # إنشاء ملف PDF
        if create_student_violations_record_pdf(student_info, violations_records, filename, db_path):
            # فتح الملف إذا كان مطلوباً
            if auto_open:
                if open_pdf(filename):
                    return True, filename
                else:
                    print("فشل في فتح ملف PDF")
                    return False, filename
            else:
                # إرجاع نجاح العملية ومسار الملف
                return True, filename
        else:
            print("فشل في إنشاء ملف PDF لسجل مخالفات التلميذ")
            return False, ""

    except Exception as e:
        print(f"خطأ في طباعة سجل مخالفات التلميذ: {str(e)}")
        traceback.print_exc()
        return False, ""

# قسم اختباري
if __name__ == "__main__":
    # بيانات تجريبية للاختبار
    student_info_test = {
        "name": "أحمد المنصوري",
        "code": "A123456",
        "section": "1APIC-1",
        "level": "الأولى إعدادي"
    }

    violations_records_test = [
        {
            "id": 1,
            "date": "2024-01-15",
            "subject": "الرياضيات",
            "teacher": "محمد العلوي",
            "notes": "تأخر عن الحصة بدون مبرر",
            "procedures": "إنذار شفوي"
        },
        {
            "id": 2,
            "date": "2024-02-20",
            "subject": "اللغة العربية",
            "teacher": "فاطمة الزهراء",
            "notes": "عدم إنجاز الواجبات المنزلية",
            "procedures": "إخبار ولي الأمر"
        },
        {
            "id": 3,
            "date": "2024-03-10",
            "subject": "الفيزياء",
            "teacher": "عبد الله الناصري",
            "notes": "سلوك غير لائق أثناء الحصة",
            "procedures": "استدعاء ولي الأمر"
        }
    ]

    # طباعة سجل المخالفات التجريبي
    print_student_violations_record(student_info_test, violations_records_test)

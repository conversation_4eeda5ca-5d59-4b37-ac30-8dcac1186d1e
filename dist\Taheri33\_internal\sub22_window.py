#!/usr/bin/env python
# -*- coding: utf-8 -*-

import sys
import os
import sqlite3
from PyQt5.QtWidgets import (
    QApplication, QMainWindow, QTableWidget, QTableWidgetItem, QVBoxLayout,
    QHBoxLayout, QWidget, QHeaderView, QLabel, QLineEdit, QPushButton,
    QComboBox, QFrame, QMessageBox, QSpinBox, QGridLayout, QCheckBox,
    QFileDialog, QInputDialog, QDialog, QDialogButtonBox, QRadioButton,
    QGroupBox, QFormLayout, QScrollArea, QProgressDialog
)
import math
from PyQt5.QtGui import QFont, QIcon, QColor
from PyQt5.QtCore import Qt, QSize
import pandas as pd
# استيراد نافذة إضافة أرقام الامتحان
from sub24_window import ExamNumbersDialog
# استيراد نافذة ترقيم القاعات
from sub25_window import RoomAssignmentDialog

class ExamsWindow(QMainWindow):
    """نافذة عرض بيانات الامتحانات"""

    def __init__(self, parent=None, db_path="data.db"):
        super().__init__(parent)
        self.db_path = db_path
        self.initUI()
        self.load_data()

    def initUI(self):
        # تعيين عنوان النافذة
        self.setWindowTitle("بيانات الامتحانات")
        self.setWindowIcon(QIcon("01.ico"))

        # الحصول على حجم الشاشة واستخدامه لضبط حجم النافذة
        screen_size = QApplication.desktop().screenGeometry()
        screen_width = screen_size.width()
        screen_height = screen_size.height()

        # تعيين حجم ومكان النافذة (كامل الشاشة)
        self.setGeometry(0, 0, screen_width, screen_height)
        self.setMinimumSize(980, 600)

        # جعل النافذة تأخذ كامل الشاشة عند الفتح
        self.showMaximized()

        # إنشاء ويدجت مركزي
        central_widget = QWidget(self)
        self.setCentralWidget(central_widget)

        # إنشاء التخطيط الرئيسي
        main_layout = QVBoxLayout(central_widget)
        main_layout.setContentsMargins(10, 10, 10, 10)
        main_layout.setSpacing(10)

        # إضافة إطار للكائنات في أعلى النافذة
        top_frame = QFrame()
        top_frame.setFrameShape(QFrame.StyledPanel)
        top_frame.setStyleSheet("background-color: white; border: 1px solid #d0d0d0; border-radius: 5px;")
        top_layout = QGridLayout(top_frame)

        # إنشاء الكائنات المطلوبة في أعلى النافذة
        # العنوان الرئيسي
        header_label = QLabel("بيانات الامتحانات")
        header_label.setFont(QFont("Calibri", 14, QFont.Bold))
        header_label.setStyleSheet("color: black;")
        header_label.setAlignment(Qt.AlignCenter)
        top_layout.addWidget(header_label, 0, 0, 1, 3)

        # إطار للأزرار
        buttons_frame = QFrame()
        buttons_frame.setStyleSheet("background-color: white; border: none;")
        buttons_layout = QHBoxLayout(buttons_frame)  # تغيير إلى تخطيط أفقي
        buttons_layout.setSpacing(10)  # المسافة بين الأزرار

        # إنشاء الأزرار مع ربطها بالوظائف المناسبة
        button_data = [
            {"text": "استيراد المترشحين", "function": self.import_candidates},
            {"text": "إضافة رقم الامتحان", "function": self.add_exam_numbers_advanced},
            {"text": "ترقيم القاعات", "function": self.assign_rooms},
            {"text": "جدولة الامتحانات", "function": self.open_exam_schedule},
            {"text": "محضر التوقيعات", "function": self.open_table_labels},
            {"text": "ملصقات الطاولات (PDF)", "function": self.open_table_labels_pdf},
            {"text": "حذف البيانات", "function": self.delete_all_data}
        ]

        for button_info in button_data:
            button = QPushButton(button_info["text"])
            button.setFont(QFont("Calibri", 13, QFont.Bold))
            button.setStyleSheet("""
                QPushButton {
                    background-color: #f0f0f0;
                    color: black;
                    border: 1px solid #cccccc;
                    border-radius: 5px;
                    padding: 8px;
                    margin: 5px;
                    min-width: 150px;
                }
                QPushButton:hover {
                    background-color: #e0e0e0;
                }
            """)
            button.clicked.connect(button_info["function"])
            buttons_layout.addWidget(button)

        top_layout.addWidget(buttons_frame, 1, 0, 1, 3)  # جعل الأزرار تمتد على كامل العرض

        # إضافة الإطار العلوي إلى التخطيط الرئيسي
        main_layout.addWidget(top_frame)

        # إنشاء متغيرات الحقول المطلوبة ولكن بدون إضافتها للواجهة
        self.level_combo = QComboBox()
        self.level_combo.currentIndexChanged.connect(self.filter_by_level)

        self.exam_number_spin = QSpinBox()
        self.exam_number_spin.setMinimum(1)
        self.exam_number_spin.setMaximum(999999)
        self.exam_number_spin.setValue(1)

        self.total_candidates_spin = QSpinBox()
        self.total_candidates_spin.setValue(0)
        self.total_candidates_spin.setReadOnly(True)

        self.max_students_spin = QSpinBox()
        self.max_students_spin.setValue(20)

        self.room_name_input = QLineEdit("قاعة")

        self.room_number_spin = QSpinBox()
        self.room_number_spin.setValue(1)

        # إضافة شريط البحث
        search_frame = QFrame()
        search_frame.setFrameShape(QFrame.StyledPanel)
        search_frame.setStyleSheet("background-color: white; border: 1px solid #d0d0d0; border-radius: 5px;")
        search_layout = QHBoxLayout(search_frame)

        # إضافة قائمة المستويات
        level_label = QLabel("المستوى:")
        level_label.setFont(QFont("Calibri", 13, QFont.Bold))
        search_layout.addWidget(level_label)

        # استخدام قائمة المستويات المنسدلة التي تم إنشاؤها مسبقًا
        self.level_combo.setFont(QFont("Calibri", 13))
        self.level_combo.setStyleSheet("background-color: white; border: 1px solid #d0d0d0; min-width: 200px;")
        search_layout.addWidget(self.level_combo)

        # إضافة مساحة مرنة
        search_layout.addStretch(1)

        # إضافة حقل البحث
        search_label = QLabel("بحث:")
        search_label.setFont(QFont("Calibri", 13, QFont.Bold))
        search_layout.addWidget(search_label)

        self.search_input = QLineEdit()
        self.search_input.setFont(QFont("Calibri", 13))
        self.search_input.setPlaceholderText("أدخل نص للبحث...")
        self.search_input.textChanged.connect(self.filter_by_name)
        self.search_input.setStyleSheet("background-color: white; border: 1px solid #d0d0d0; min-width: 200px;")
        search_layout.addWidget(self.search_input)

        # إضافة زر تحديث
        refresh_button = QPushButton("تحديث")
        refresh_button.setFont(QFont("Calibri", 13, QFont.Bold))
        refresh_button.setStyleSheet("""
            QPushButton {
                background-color: #4CAF50;
                color: white;
                border-radius: 5px;
                padding: 5px 10px;
            }
            QPushButton:hover {
                background-color: #45a049;
            }
        """)
        refresh_button.clicked.connect(self.refresh_data)
        search_layout.addWidget(refresh_button)

        main_layout.addWidget(search_frame)

        # إنشاء جدول لعرض البيانات
        self.table = QTableWidget()
        self.table.setFont(QFont("Calibri", 13))

        # تعيين خصائص الجدول
        self.table.setAlternatingRowColors(False)  # إلغاء تبديل ألوان الصفوف
        self.table.setSelectionBehavior(QTableWidget.SelectRows)
        self.table.setSelectionMode(QTableWidget.SingleSelection)
        self.table.setEditTriggers(QTableWidget.NoEditTriggers)  # منع التعديل على الجدول

        # تعيين خلفية الجدول باللون الأبيض
        self.table.setStyleSheet("""
            QTableWidget {
                background-color: white;
                gridline-color: #d0d0d0;
            }
            QTableWidget::item {
                background-color: white;
            }
            QTableWidget::item:selected {
                background-color: #e0e0ff;
            }
        """)

        # تنسيق رأس الجدول
        header_font = QFont("Calibri", 13, QFont.Bold)
        self.table.horizontalHeader().setFont(header_font)
        self.table.horizontalHeader().setStyleSheet("background-color: #4a86e8; color: white;")
        self.table.horizontalHeader().setDefaultSectionSize(150)
        self.table.horizontalHeader().setMinimumSectionSize(80)

        # تنسيق الصفوف
        self.table.verticalHeader().setDefaultSectionSize(35)  # ارتفاع الصف

        main_layout.addWidget(self.table)

        # تعيين اتجاه النص من اليمين إلى اليسار
        self.setLayoutDirection(Qt.RightToLeft)

    def connect_to_database(self):
        """الاتصال بقاعدة البيانات"""
        try:
            conn = sqlite3.connect(self.db_path)
            return conn
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل الاتصال بقاعدة البيانات: {e}")
            return None

    def load_data(self):
        """تحميل البيانات من جدول امتحانات"""
        conn = self.connect_to_database()
        if not conn:
            return

        try:
            cursor = conn.cursor()

            # الحصول على أسماء الأعمدة
            cursor.execute("PRAGMA table_info(امتحانات)")
            columns_info = cursor.fetchall()
            columns = [col[1] for col in columns_info]

            # تم إزالة تحديث قائمة البحث حسب العمود

            # تحميل المستويات المتاحة في قائمة المستويات المنسدلة
            self.level_combo.clear()
            self.level_combo.addItem("الرجاء تحديد المستوى المطلوب")

            try:
                # الحصول على المستويات الفريدة
                cursor.execute("SELECT DISTINCT المستوى FROM امتحانات WHERE المستوى IS NOT NULL AND المستوى != '' ORDER BY المستوى")
                levels = cursor.fetchall()

                # إضافة المستويات إلى القائمة المنسدلة
                for level in levels:
                    self.level_combo.addItem(level[0])

                # تحديث عدد المترشحين الإجمالي
                cursor.execute("SELECT COUNT(*) FROM امتحانات")
                total_count = cursor.fetchone()[0]
                self.total_candidates_spin.setValue(total_count)

            except Exception:
                pass

            # تحديد الأعمدة التي سيتم عرضها (استبعاد عمود المعرف وعمود مركز_الامتحان وعمود المستوى)
            excluded_columns = ["id", "مركز_الامتحان", "المستوى"]
            display_columns = [col for col in columns if col not in excluded_columns]

            # تعيين عدد الأعمدة وعناوينها
            self.table.setColumnCount(len(display_columns))
            self.table.setHorizontalHeaderLabels(display_columns)

            # الحصول على البيانات مرتبة حسب رقم الامتحان
            cursor.execute("""
                SELECT * FROM امتحانات
                ORDER BY CASE
                    WHEN رقم_الامتحان IS NULL OR رقم_الامتحان = '' THEN 9999999
                    ELSE CAST(رقم_الامتحان AS INTEGER)
                END
            """)
            data = cursor.fetchall()

            # تعيين عدد الصفوف
            self.table.setRowCount(len(data))

            # ملء الجدول بالبيانات
            for row_idx, row_data in enumerate(data):
                display_col_idx = 0
                for col_idx, value in enumerate(row_data):
                    if columns[col_idx] in excluded_columns:
                        continue

                    # إنشاء عنصر جديد
                    item = QTableWidgetItem(str(value) if value is not None else "")
                    item.setTextAlignment(Qt.AlignCenter)

                    # تعيين خط غامق للنص
                    font = QFont("Calibri", 13, QFont.Bold)
                    item.setFont(font)

                    # إذا كان العمود هو رقم_الامتحان، نقوم بتخزين القيمة الرقمية
                    if columns[col_idx] == "رقم_الامتحان" and value:
                        try:
                            # محاولة تحويل القيمة إلى رقم
                            num_value = int(value)
                            # تخزين القيمة الرقمية لضمان الترتيب الصحيح
                            item.setData(Qt.UserRole, num_value)
                        except (ValueError, TypeError):
                            pass

                    self.table.setItem(row_idx, display_col_idx, item)
                    display_col_idx += 1

            # تعيين ارتفاع رأس الجدول
            self.table.horizontalHeader().setFixedHeight(40)
            self.table.horizontalHeader().setStyleSheet("QHeaderView::section { background-color: #4a86e8; color: white; font-weight: bold; height: 40px; }")

            # تعيين ارتفاع الصفوف
            for row in range(self.table.rowCount()):
                self.table.setRowHeight(row, 30)

            # تعيين عرض ثابت للأعمدة
            column_widths = {
                "الرمز": 120,
                "رقم_الامتحان": 100,
                "الجنس": 80,
                "الاسم_الكامل": 250,
                "تاريخ_الازدياد": 120,
                "المؤسسة_الأصلية": 300,
                "القسم": 150,
                "القاعة": 150
            }

            # تطبيق العرض المخصص لكل عمود
            for col_idx, col_name in enumerate(display_columns):
                if col_name in column_widths:
                    self.table.setColumnWidth(col_idx, column_widths[col_name])
                else:
                    # عرض افتراضي للأعمدة غير المحددة
                    self.table.setColumnWidth(col_idx, 120)

            # تعيين خط الجدول
            self.table.setFont(QFont("Calibri", 13, QFont.Bold))

            # منع المستخدم من تغيير عرض الأعمدة
            self.table.horizontalHeader().setSectionResizeMode(QHeaderView.Fixed)

            conn.close()

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل تحميل البيانات: {e}")
            if conn:
                conn.close()

    def refresh_data(self):
        """تحديث البيانات في الجدول"""
        # حفظ موضع التمرير الحالي
        scrollbar_position = self.table.verticalScrollBar().value()

        # تحديث البيانات
        self.load_data()

        # استعادة موضع التمرير
        self.table.verticalScrollBar().setValue(scrollbar_position)

        # عرض رسالة تأكيد
        QMessageBox.information(self, "تحديث", "تم تحديث البيانات بنجاح.")

    def filter_data(self):
        """تصفية البيانات حسب نص البحث"""
        search_text = self.search_input.text().lower()

        if not search_text:
            # إذا كان حقل البحث فارغًا، أظهر جميع الصفوف
            for row in range(self.table.rowCount()):
                self.table.setRowHidden(row, False)
            return

        # البحث في جميع الأعمدة
        for row in range(self.table.rowCount()):
            row_visible = False

            # البحث في جميع الأعمدة
            for col in range(self.table.columnCount()):
                item = self.table.item(row, col)
                if item and search_text in item.text().lower():
                    row_visible = True
                    break

            self.table.setRowHidden(row, not row_visible)

    def filter_by_level(self):
        """تصفية البيانات حسب المستوى المحدد"""
        selected_level = self.level_combo.currentText()

        # إذا كان المستوى هو الخيار الافتراضي، أظهر جميع الصفوف
        if selected_level == "الرجاء تحديد المستوى المطلوب":
            for row in range(self.table.rowCount()):
                self.table.setRowHidden(row, False)

            # تحديث عدد المترشحين الإجمالي
            conn = self.connect_to_database()
            if conn:
                try:
                    cursor = conn.cursor()
                    cursor.execute("SELECT COUNT(*) FROM امتحانات")
                    total_count = cursor.fetchone()[0]
                    self.total_candidates_spin.setValue(total_count)
                    conn.close()
                except Exception:
                    if conn:
                        conn.close()
            return

        # بما أن عمود المستوى غير موجود في الجدول، سنستخدم قاعدة البيانات مباشرة للتصفية
        conn = self.connect_to_database()
        if not conn:
            return

        try:
            cursor = conn.cursor()

            # الحصول على معرفات الطلاب في المستوى المحدد
            cursor.execute("SELECT id FROM امتحانات WHERE المستوى = ?", (selected_level,))
            student_ids = [row[0] for row in cursor.fetchall()]

            # حساب عدد الطلاب في المستوى المحدد
            level_count = len(student_ids)

            # تحديث عدد المترشحين في المستوى المحدد
            self.total_candidates_spin.setValue(level_count)

            # إذا لم يكن هناك طلاب في المستوى المحدد، أخفِ جميع الصفوف
            if not student_ids:
                for row in range(self.table.rowCount()):
                    self.table.setRowHidden(row, True)
                conn.close()
                return

            # الحصول على بيانات الطلاب في المستوى المحدد مرتبة حسب رقم الامتحان
            placeholders = ', '.join(['?' for _ in student_ids])
            query = f"""
                SELECT * FROM امتحانات
                WHERE id IN ({placeholders})
                ORDER BY CASE
                    WHEN رقم_الامتحان IS NULL OR رقم_الامتحان = '' THEN 9999999
                    ELSE CAST(رقم_الامتحان AS INTEGER)
                END
            """
            cursor.execute(query, student_ids)
            students_data = cursor.fetchall()

            # تفريغ الجدول
            self.table.setRowCount(0)

            # الحصول على أسماء الأعمدة
            cursor.execute("PRAGMA table_info(امتحانات)")
            columns_info = cursor.fetchall()
            columns = [col[1] for col in columns_info]

            # تحديد الأعمدة التي سيتم عرضها
            excluded_columns = ["id", "مركز_الامتحان", "المستوى"]
            display_columns = [col for col in columns if col not in excluded_columns]

            # تعيين عدد الأعمدة وعناوينها
            self.table.setColumnCount(len(display_columns))
            self.table.setHorizontalHeaderLabels(display_columns)

            # تعيين عدد الصفوف
            self.table.setRowCount(len(students_data))

            # ملء الجدول بالبيانات
            for row_idx, row_data in enumerate(students_data):
                display_col_idx = 0
                for col_idx, value in enumerate(row_data):
                    if columns[col_idx] in excluded_columns:
                        continue

                    # إنشاء عنصر جديد
                    item = QTableWidgetItem(str(value) if value is not None else "")
                    item.setTextAlignment(Qt.AlignCenter)

                    # تعيين خط غامق للنص
                    font = QFont("Calibri", 13, QFont.Bold)
                    item.setFont(font)

                    # إذا كان العمود هو رقم_الامتحان، نقوم بتخزين القيمة الرقمية
                    if columns[col_idx] == "رقم_الامتحان" and value:
                        try:
                            # محاولة تحويل القيمة إلى رقم
                            num_value = int(value)
                            # تخزين القيمة الرقمية لضمان الترتيب الصحيح
                            item.setData(Qt.UserRole, num_value)
                        except (ValueError, TypeError):
                            pass

                    self.table.setItem(row_idx, display_col_idx, item)
                    display_col_idx += 1

            # تعيين ارتفاع الصفوف
            for row in range(self.table.rowCount()):
                self.table.setRowHeight(row, 30)

            # تعيين عرض ثابت للأعمدة
            column_widths = {
                "الرمز": 120,
                "رقم_الامتحان": 100,
                "الجنس": 80,
                "الاسم_الكامل": 250,
                "تاريخ_الازدياد": 120,
                "المؤسسة_الأصلية": 300,
                "القسم": 150,
                "القاعة": 150
            }

            # تطبيق العرض المخصص لكل عمود
            for col_idx, col_name in enumerate(display_columns):
                if col_name in column_widths:
                    self.table.setColumnWidth(col_idx, column_widths[col_name])
                else:
                    # عرض افتراضي للأعمدة غير المحددة
                    self.table.setColumnWidth(col_idx, 200)

            # منع المستخدم من تغيير عرض الأعمدة
            self.table.horizontalHeader().setSectionResizeMode(QHeaderView.Fixed)

            conn.close()

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في تصفية البيانات حسب المستوى: {e}")
            if conn:
                conn.close()

    def filter_by_name(self):
        """تصفية البيانات حسب الاسم"""
        search_text = self.search_input.text().lower()
        selected_level = self.level_combo.currentText()

        if not search_text:
            # إذا كان حقل البحث فارغًا، أعد تطبيق تصفية المستوى فقط
            self.filter_by_level()
            return

        # بما أن عمود المستوى غير موجود في الجدول، سنستخدم قاعدة البيانات مباشرة للتصفية
        conn = self.connect_to_database()
        if not conn:
            return

        try:
            cursor = conn.cursor()

            # إذا تم تحديد مستوى، ابحث في هذا المستوى فقط
            if selected_level != "الرجاء تحديد المستوى المطلوب":
                cursor.execute(
                    "SELECT * FROM امتحانات WHERE المستوى = ? AND الاسم_الكامل LIKE ?",
                    (selected_level, f"%{search_text}%")
                )
            else:
                # وإلا ابحث في جميع المستويات
                cursor.execute(
                    "SELECT * FROM امتحانات WHERE الاسم_الكامل LIKE ?",
                    (f"%{search_text}%",)
                )

            students_data = cursor.fetchall()

            # الحصول على أسماء الأعمدة
            cursor.execute("PRAGMA table_info(امتحانات)")
            columns_info = cursor.fetchall()
            columns = [col[1] for col in columns_info]

            # تحديد الأعمدة التي سيتم عرضها
            excluded_columns = ["id", "مركز_الامتحان", "المستوى"]
            display_columns = [col for col in columns if col not in excluded_columns]

            # تفريغ الجدول
            self.table.setRowCount(0)

            # تعيين عدد الأعمدة وعناوينها
            self.table.setColumnCount(len(display_columns))
            self.table.setHorizontalHeaderLabels(display_columns)

            # تعيين عدد الصفوف
            self.table.setRowCount(len(students_data))

            # ملء الجدول بالبيانات
            for row_idx, row_data in enumerate(students_data):
                display_col_idx = 0
                for col_idx, value in enumerate(row_data):
                    if columns[col_idx] in excluded_columns:
                        continue

                    # إنشاء عنصر جديد
                    item = QTableWidgetItem(str(value) if value is not None else "")
                    item.setTextAlignment(Qt.AlignCenter)

                    # تعيين خط غامق للنص
                    font = QFont("Calibri", 13, QFont.Bold)
                    item.setFont(font)

                    # إذا كان العمود هو رقم_الامتحان، نقوم بتخزين القيمة الرقمية
                    if columns[col_idx] == "رقم_الامتحان" and value:
                        try:
                            # محاولة تحويل القيمة إلى رقم
                            num_value = int(value)
                            # تخزين القيمة الرقمية لضمان الترتيب الصحيح
                            item.setData(Qt.UserRole, num_value)
                        except (ValueError, TypeError):
                            pass

                    self.table.setItem(row_idx, display_col_idx, item)
                    display_col_idx += 1

            # تعيين ارتفاع الصفوف
            for row in range(self.table.rowCount()):
                self.table.setRowHeight(row, 30)

            # تعيين عرض ثابت للأعمدة
            column_widths = {
                "الرمز": 120,
                "رقم_الامتحان": 100,
                "الجنس": 80,
                "الاسم_الكامل": 250,
                "تاريخ_الازدياد": 120,
                "المؤسسة_الأصلية": 300,
                "القسم": 150,
                "القاعة": 150
            }

            # تطبيق العرض المخصص لكل عمود
            for col_idx, col_name in enumerate(display_columns):
                if col_name in column_widths:
                    self.table.setColumnWidth(col_idx, column_widths[col_name])
                else:
                    # عرض افتراضي للأعمدة غير المحددة
                    self.table.setColumnWidth(col_idx, 120)

            # منع المستخدم من تغيير عرض الأعمدة
            self.table.horizontalHeader().setSectionResizeMode(QHeaderView.Fixed)

            # تحديث عدد المترشحين
            self.total_candidates_spin.setValue(len(students_data))

            conn.close()

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في البحث عن الاسم: {e}")
            if conn:
                conn.close()


    def select_and_order_levels(self):
        """عرض مربع حوار لاختيار وترتيب المستويات"""
        # الاتصال بقاعدة البيانات
        conn = self.connect_to_database()
        if not conn:
            return None

        try:
            cursor = conn.cursor()

            # الحصول على جميع المستويات المتاحة
            cursor.execute("SELECT DISTINCT المستوى FROM امتحانات ORDER BY المستوى")
            all_levels = [row[0] for row in cursor.fetchall()]

            conn.close()

            if not all_levels:
                QMessageBox.warning(self, "تنبيه", "لا توجد مستويات متاحة في قاعدة البيانات")
                return None

            # استخدام النافذة الجديدة لاختيار وترتيب المستويات
            from select_levels_dialog import SelectLevelsDialog
            levels_dialog = SelectLevelsDialog(self, all_levels)

            # عرض مربع الحوار
            if levels_dialog.exec_() != QDialog.Accepted:
                return None

            # الحصول على المستويات المحددة بالترتيب
            ordered_levels = levels_dialog.get_selected_levels()

            if not ordered_levels:
                QMessageBox.warning(self, "تنبيه", "الرجاء اختيار مستوى واحد على الأقل.")
                return None

            return ordered_levels

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في الحصول على المستويات: {e}")
            if conn:
                conn.close()
            return None

    def add_exam_numbers_advanced(self):
        """إضافة أرقام الامتحان للطلاب مع إمكانية اختيار طريقة الترتيب والمستويات المتعددة"""
        start_number = self.exam_number_spin.value()

        # عرض مربع حوار لاختيار وترتيب المستويات
        ordered_levels = self.select_and_order_levels()

        if not ordered_levels:
            return

        # قائمة الأعمدة المتاحة للترتيب
        available_columns = [
            "القسم",
            "ر.ت",
            "الجنس",
            "الاسم_الكامل",
            "تاريخ_الازدياد",
            "الرمز",
            "المؤسسة_الأصلية"
        ]

        # إنشاء نافذة إضافة أرقام الامتحان
        sort_dialog = ExamNumbersDialog(
            parent=self,
            start_number=start_number,
            available_columns=available_columns,
            ordered_levels=ordered_levels
        )

        # عرض مربع الحوار
        if sort_dialog.exec_() != QDialog.Accepted:
            return

        # الحصول على الأعمدة المحددة بالترتيب
        sort_columns = sort_dialog.get_selected_columns()

        if not sort_columns:
            QMessageBox.warning(self, "تنبيه", "الرجاء اختيار عمود واحد على الأقل للترتيب.")
            return

        # الحصول على رقم البداية من الحقل الجديد
        start_number = sort_dialog.get_start_number()

        # تأكيد العملية باستخدام النافذة الحوارية المخصصة
        columns_text = "\n".join([f"{i+1}. {col}" for i, col in enumerate(sort_columns)])
        levels_text = "\n".join([f"{i+1}. {level}" for i, level in enumerate(ordered_levels)])

        # استيراد النافذة الحوارية المخصصة
        from custom_message_dialog import CustomMessageDialog

        # إنشاء نص الرسالة
        message = f"هل تريد توليد أرقام امتحان للمستويات التالية بدءًا من الرقم {start_number}؟\n\n"
        message += f"المستويات المحددة:\n{levels_text}\n\n"
        message += f"سيتم ترتيب المترشحين حسب الأعمدة التالية:\n{columns_text}"

        # إنشاء النافذة الحوارية
        confirm_dialog = CustomMessageDialog(
            parent=self,
            title="تأكيد إضافة أرقام الامتحان",
            message=message,
            icon_type="question"
        )

        # عرض النافذة الحوارية
        reply = confirm_dialog.exec_()

        if reply != QDialog.Accepted:
            return

        # التحقق من نوع عمود رقم_الامتحان في قاعدة البيانات
        conn = self.connect_to_database()
        if not conn:
            return

        try:
            cursor = conn.cursor()
            cursor.execute("PRAGMA table_info(امتحانات)")
            columns_info = cursor.fetchall()

            # طباعة معلومات الأعمدة للتصحيح
            print("معلومات أعمدة جدول امتحانات:")
            for column in columns_info:
                print(f"العمود: {column[1]}, النوع: {column[2]}")

            # التحقق من وجود عمود رقم_الامتحان
            exam_number_column = None
            for column in columns_info:
                if column[1] == "رقم_الامتحان":
                    exam_number_column = column
                    break

            if exam_number_column:
                print(f"تم العثور على عمود رقم_الامتحان: {exam_number_column}")
                # التحقق من نوع العمود
                column_type = exam_number_column[2]
                print(f"نوع عمود رقم_الامتحان: {column_type}")

                # إذا كان نوع العمود غير مناسب، نقوم بتعديله
                if column_type.lower() != "integer" and column_type.lower() != "int":
                    try:
                        print("محاولة تعديل نوع عمود رقم_الامتحان إلى INTEGER...")
                        # إنشاء جدول مؤقت بالهيكل الصحيح
                        cursor.execute("CREATE TABLE امتحانات_temp AS SELECT * FROM امتحانات")
                        # تعديل نوع العمود في الجدول المؤقت
                        cursor.execute("ALTER TABLE امتحانات_temp ADD COLUMN رقم_الامتحان_new INTEGER")
                        # نقل البيانات من العمود القديم إلى العمود الجديد
                        cursor.execute("UPDATE امتحانات_temp SET رقم_الامتحان_new = CAST(رقم_الامتحان AS INTEGER)")
                        # حذف العمود القديم
                        cursor.execute("ALTER TABLE امتحانات_temp DROP COLUMN رقم_الامتحان")
                        # إعادة تسمية العمود الجديد
                        cursor.execute("ALTER TABLE امتحانات_temp RENAME COLUMN رقم_الامتحان_new TO رقم_الامتحان")
                        # حذف الجدول الأصلي
                        cursor.execute("DROP TABLE امتحانات")
                        # إعادة تسمية الجدول المؤقت
                        cursor.execute("ALTER TABLE امتحانات_temp RENAME TO امتحانات")
                        conn.commit()
                        print("تم تعديل نوع عمود رقم_الامتحان بنجاح")
                    except Exception as e:
                        print(f"فشل في تعديل نوع عمود رقم_الامتحان: {e}")
            else:
                print("لم يتم العثور على عمود رقم_الامتحان!")
                try:
                    print("محاولة إضافة عمود رقم_الامتحان...")
                    cursor.execute("ALTER TABLE امتحانات ADD COLUMN رقم_الامتحان INTEGER")
                    conn.commit()
                    print("تم إضافة عمود رقم_الامتحان بنجاح")
                except Exception as e:
                    print(f"فشل في إضافة عمود رقم_الامتحان: {e}")

            # التحقق من إمكانية التحديث
            try:
                print("اختبار تحديث عمود رقم_الامتحان...")
                cursor.execute("UPDATE امتحانات SET رقم_الامتحان = 1 WHERE 1=0")  # لن يؤثر على أي صف
                conn.commit()
                print("يمكن تحديث عمود رقم_الامتحان بنجاح")
            except Exception as e:
                print(f"فشل في اختبار تحديث عمود رقم_الامتحان: {e}")

            # بناء استعلام SQL للترتيب
            # استخدام الأسماء الصحيحة للأعمدة مع الاقتباسات المناسبة
            order_by_clause = []
            for col in sort_columns:
                # معالجة خاصة لعمود "ر.ت" لأنه يحتوي على نقطة
                if col == "ر.ت":
                    order_by_clause.append('"ر.ت"')
                elif col == "المؤسسة_الأصلية":
                    order_by_clause.append('"المؤسسة_الأصلية"')
                # معالجة خاصة لعمود "القسم" لمعاملته بشكل صحيح
                elif col == "القسم":
                    # استخدام SUBSTR و CAST للحصول على الرقم من نهاية القسم (مثل "3ASCG-1" -> "1")
                    order_by_clause.append('CASE WHEN "القسم" IS NULL OR "القسم" = "" THEN 9999 ELSE CAST(SUBSTR("القسم", INSTR("القسم", "-") + 1) AS INTEGER) END')
                # معالجة خاصة لعمود "رقم_الامتحان" لمعاملته كقيمة رقمية
                elif col == "رقم_الامتحان":
                    order_by_clause.append('CASE WHEN "رقم_الامتحان" IS NULL OR "رقم_الامتحان" = "" THEN 9999 ELSE CAST("رقم_الامتحان" AS INTEGER) END')
                else:
                    order_by_clause.append(f'"{col}"')

            order_by_sql = ", ".join(order_by_clause)

            # طباعة استعلام الترتيب للتصحيح
            print(f"استعلام الترتيب: ORDER BY {order_by_sql}")

            # إنشاء شريط التقدم
            progress_dialog = QProgressDialog("جاري إضافة أرقام الامتحان...", "إلغاء", 0, 100, self)
            progress_dialog.setWindowTitle("إضافة أرقام الامتحان")
            progress_dialog.setWindowModality(Qt.WindowModal)
            progress_dialog.setAutoClose(True)
            progress_dialog.setMinimumDuration(0)
            progress_dialog.setStyleSheet("""
                QProgressDialog {
                    background-color: #00FFFF;
                }
                QLabel {
                    font-family: 'Calibri';
                    font-size: 14pt;
                    font-weight: bold;
                    color: darkblue;
                    border: 1.5px solid blue;
                    padding: 5px;
                }
                QProgressBar {
                    border: 1.5px solid blue;
                    border-radius: 3px;
                    text-align: center;
                    background-color: #00FFFF;
                }
                QProgressBar::chunk {
                    background-color: darkblue;
                    width: 10px;
                }
                QPushButton {
                    font-family: 'Calibri';
                    font-size: 13pt;
                    font-weight: bold;
                    min-width: 80px;
                    padding: 5px;
                    background-color: #00FFFF;
                }
            """)
            progress_dialog.show()
            progress_dialog.setValue(5)
            QApplication.instance().processEvents()

            # حساب إجمالي عدد الطلاب في جميع المستويات
            total_students_count = 0
            level_students_count = {}

            for level in ordered_levels:
                # البحث عن الطلاب في المستوى المحدد
                cursor.execute("SELECT COUNT(*) FROM امتحانات WHERE المستوى = ?", (level,))
                count = cursor.fetchone()[0]
                level_students_count[level] = count
                total_students_count += count

            # توليد أرقام الامتحان لكل مستوى
            current_number = start_number
            total_updated_count = 0
            processed_students = 0

            progress_dialog.setValue(10)
            progress_dialog.setLabelText("جاري تحضير البيانات...")
            QApplication.instance().processEvents()

            for level_index, level in enumerate(ordered_levels):
                progress_dialog.setLabelText(f"جاري معالجة المستوى: {level}")
                QApplication.instance().processEvents()

                print(f"\nمعالجة المستوى: {level}")

                # البحث عن الطلاب في المستوى المحدد مع الترتيب المطلوب
                query = f'SELECT id, القسم, "ر.ت", الجنس, الاسم_الكامل, تاريخ_الازدياد, الرمز, "المؤسسة_الأصلية" FROM امتحانات WHERE المستوى = ? ORDER BY {order_by_sql}'
                print(f"الاستعلام الكامل: {query}")

                cursor.execute(query, (level,))
                students = cursor.fetchall()

                # طباعة الطلاب المرتبين للتصحيح
                print(f"الطلاب المرتبين حسب الأعمدة المحددة:")
                for i, student in enumerate(students[:10]):  # طباعة أول 10 طلاب فقط لتجنب الإطالة
                    print(f"{i+1}. معرف: {student[0]}, القسم: {student[1]}, ر.ت: {student[2]}, الجنس: {student[3]}, الاسم: {student[4]}")

                # تحليل القسم للتصحيح
                print("\nتحليل قيم القسم:")
                for i, student in enumerate(students[:5]):
                    section = student[1]
                    if section and "-" in section:
                        section_number = section.split("-")[1]
                        print(f"القسم: {section}, الرقم المستخرج: {section_number}")
                    else:
                        print(f"القسم: {section}, لا يمكن استخراج رقم")

                # استخراج معرفات الطلاب فقط للتحديث
                student_ids = [student[0] for student in students]

                if not students:
                    print(f"لا يوجد طلاب في المستوى '{level}'")
                    continue

                # توليد أرقام الامتحان وتحديث قاعدة البيانات
                level_updated_count = 0

                # طباعة عدد الطلاب الذين سيتم تحديثهم
                print(f"عدد الطلاب في المستوى {level}: {len(student_ids)}")

                # تحديث شريط التقدم - حساب نسبة التقدم للمستوى الحالي
                level_progress_end = 10 + ((level_index + 1) * 80 / len(ordered_levels))

                # استخدام executemany لتحديث جميع الطلاب في المستوى دفعة واحدة
                try:
                    # إنشاء قائمة من الأرقام المتسلسلة
                    exam_numbers = list(range(current_number, current_number + len(student_ids)))

                    # إنشاء قائمة من الأزواج (رقم_الامتحان، معرف_الطالب)
                    update_data = list(zip(exam_numbers, student_ids))

                    # تنفيذ التحديث بشكل جماعي
                    cursor.executemany(
                        "UPDATE امتحانات SET رقم_الامتحان = ? WHERE id = ?",
                        update_data
                    )

                    level_updated_count = len(student_ids)
                    current_number += level_updated_count

                    # تنفيذ التحديث مرة واحدة بعد تحديث جميع السجلات
                    conn.commit()

                    # تحديث شريط التقدم - اكتمال معالجة المستوى
                    progress_value = int(level_progress_end)
                    progress_dialog.setValue(progress_value)
                    progress_dialog.setLabelText(f"تم معالجة المستوى: {level} ({level_updated_count} طالب)")
                    QApplication.instance().processEvents()

                except Exception as e:
                    print(f"فشل في تحديث المستوى {level}: {e}")
                    conn.rollback()
                    level_updated_count = 0

                total_updated_count += level_updated_count
                processed_students += level_students_count[level]
                print(f"تم تحديث {level_updated_count} طالب في المستوى {level}")

            # تحديث شريط التقدم - اكتمال العملية
            progress_dialog.setValue(95)
            progress_dialog.setLabelText("جاري تحديث الجدول...")
            QApplication.instance().processEvents()

            # التأكد من تنفيذ التحديثات
            conn.commit()

            # إغلاق الاتصال بقاعدة البيانات
            conn.close()

            # تحديث شريط التقدم
            progress_dialog.setValue(98)
            progress_dialog.setLabelText("جاري تحديث الجدول...")
            QApplication.instance().processEvents()

            # تحديث الجدول
            self.load_data()

            # إعادة تطبيق التصفية حسب المستوى
            self.filter_by_level()

            # ترتيب الجدول حسب رقم الامتحان
            self.sort_table_by_exam_number()

            # تحديث شريط التقدم - اكتمال العملية
            progress_dialog.setValue(100)
            progress_dialog.setLabelText("تم الانتهاء من إضافة أرقام الامتحان بنجاح!")
            QApplication.instance().processEvents()

            # عرض رسالة نجاح باستخدام النافذة الحوارية المخصصة
            levels_str = ", ".join(ordered_levels)

            # استيراد النافذة الحوارية المخصصة
            from custom_message_dialog import CustomMessageDialog

            # إنشاء نص الرسالة
            success_message = f"تم توليد أرقام الامتحان لـ {total_updated_count} طالب في المستويات التالية:\n{levels_str}\n\n"
            success_message += f"تم ترتيب المترشحين حسب: {', '.join(sort_columns)}"

            # إنشاء النافذة الحوارية
            success_dialog = CustomMessageDialog(
                parent=self,
                title="تمت العملية بنجاح",
                message=success_message,
                icon_type="information"
            )

            # عرض النافذة الحوارية
            success_dialog.exec_()
        except Exception as e:
            # عرض رسالة خطأ باستخدام النافذة الحوارية المخصصة
            from custom_message_dialog import CustomMessageDialog

            error_dialog = CustomMessageDialog(
                parent=self,
                title="خطأ",
                message=f"فشل في توليد أرقام الامتحان: {e}",
                icon_type="error"
            )

            error_dialog.exec_()
            print(f"خطأ في التحقق من هيكل الجدول: {e}")
            if conn:
                conn.close()



    def assign_rooms(self):
        """تعيين القاعات للطلاب"""
        try:
            # استدعاء نافذة ترقيم القاعات
            from sub25_window import RoomAssignmentDialog
            room_dialog = RoomAssignmentDialog(parent=self, db_path=self.db_path)
            room_dialog.exec_()
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في فتح نافذة ترقيم القاعات: {e}")

    def clear_rooms(self):
        """مسح جميع القاعات من عمود القاعة في جدول امتحانات"""
        # تم تعطيل هذه الوظيفة مع الإبقاء على الزر
        QMessageBox.information(self, "تنبيه", "تم تعطيل هذه الوظيفة مؤقتاً.")

    def reopen_window(self):
        """إعادة فتح النافذة بعد الانتهاء من العمل بنجاح"""
        # إعادة تهيئة النافذة
        self.close()
        new_window = ExamsWindow()
        new_window.show()

    def sort_table_by_exam_number(self):
        """ترتيب الجدول حسب رقم الامتحان بشكل رقمي"""
        try:
            # الحصول على عدد الأعمدة
            column_count = self.table.columnCount()

            # البحث عن عمود رقم_الامتحان
            exam_number_column = -1

            for i in range(column_count):
                header_text = self.table.horizontalHeaderItem(i).text()
                if header_text == "رقم_الامتحان":
                    exam_number_column = i
                    break

            if exam_number_column == -1:
                return

            # إعادة تحميل البيانات من قاعدة البيانات مع ترتيبها حسب رقم الامتحان
            conn = self.connect_to_database()
            if not conn:
                return

            try:
                cursor = conn.cursor()

                # تحديد المستوى المحدد (إذا كان هناك)
                selected_level = self.level_combo.currentText()

                if selected_level != "الرجاء تحديد المستوى المطلوب":
                    # استعلام مع تصفية حسب المستوى
                    cursor.execute("""
                        SELECT * FROM امتحانات
                        WHERE المستوى = ?
                        ORDER BY CAST(رقم_الامتحان AS INTEGER)
                    """, (selected_level,))
                else:
                    # استعلام بدون تصفية
                    cursor.execute("""
                        SELECT * FROM امتحانات
                        ORDER BY CAST(رقم_الامتحان AS INTEGER)
                    """)

                # تحديث الجدول
                self.load_data()

                conn.close()

            except Exception as e:
                if conn:
                    conn.close()
                raise e

        except Exception:
            pass

    def generate_exam_schedule(self):
        """إنشاء جدولة الامتحان"""
        selected_level = self.level_combo.currentText()

        if selected_level == "الرجاء تحديد المستوى المطلوب":
            QMessageBox.warning(self, "تنبيه", "الرجاء تحديد المستوى أولاً")
            return

        # تأكيد العملية
        reply = QMessageBox.question(
            self,
            "تأكيد",
            f"هل تريد إنشاء جدولة الامتحان للمستوى '{selected_level}'؟",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

        if reply != QMessageBox.Yes:
            return

        # هنا يمكن إضافة الكود الخاص بإنشاء جدولة الامتحان
        # على سبيل المثال، يمكن إنشاء ملف Excel أو PDF يحتوي على جدولة الامتحان

        QMessageBox.information(
            self,
            "معلومات",
            "هذه الميزة قيد التطوير. سيتم تنفيذها في الإصدار القادم."
        )

    def import_candidates(self):
        """استيراد المترشحين من اللوائح أو من ملف إكسل"""
        # إنشاء مربع حوار للاختيار بين استيراد من قاعدة البيانات الحالية أو من ملف إكسل
        import_dialog = QDialog(self)
        import_dialog.setWindowTitle("استيراد المترشحين")
        import_dialog.setMinimumWidth(400)
        import_dialog.setMinimumHeight(200)
        import_dialog.setStyleSheet("background-color: #00FFFF;")

        layout = QVBoxLayout(import_dialog)

        # إضافة عنوان
        title_label = QLabel("اختر مصدر استيراد بيانات المترشحين:")
        title_label.setFont(QFont("Calibri", 14, QFont.Bold))
        title_label.setStyleSheet("color: darkblue; border: 1.5px solid blue; padding: 5px;")
        title_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(title_label)

        # إضافة خيارات الاستيراد
        db_button = QPushButton("استيراد من قاعدة البيانات الحالية")
        db_button.setFont(QFont("Calibri", 13, QFont.Bold))
        db_button.setMinimumHeight(40)
        db_button.setStyleSheet("""
            QPushButton {
                background-color: #0066cc;
                color: white;
                border-radius: 5px;
            }
            QPushButton:hover {
                background-color: #0055aa;
            }
        """)
        layout.addWidget(db_button)

        excel_button = QPushButton("استيراد من ملف إكسل")
        excel_button.setFont(QFont("Calibri", 13, QFont.Bold))
        excel_button.setMinimumHeight(40)
        excel_button.setStyleSheet("""
            QPushButton {
                background-color: #009900;
                color: white;
                border-radius: 5px;
            }
            QPushButton:hover {
                background-color: #008800;
            }
        """)
        layout.addWidget(excel_button)

        # إضافة زر إلغاء
        cancel_button = QPushButton("إلغاء")
        cancel_button.setFont(QFont("Calibri", 13, QFont.Bold))
        cancel_button.setMinimumHeight(40)
        cancel_button.setStyleSheet("""
            QPushButton {
                background-color: #cc0000;
                color: white;
                border-radius: 5px;
            }
            QPushButton:hover {
                background-color: #bb0000;
            }
        """)
        layout.addWidget(cancel_button)

        # ربط الأزرار بالوظائف
        db_button.clicked.connect(lambda: self.handle_import_choice("db", import_dialog))
        excel_button.clicked.connect(lambda: self.handle_import_choice("excel", import_dialog))
        cancel_button.clicked.connect(import_dialog.reject)

        # عرض مربع الحوار
        import_dialog.exec_()

    def handle_import_choice(self, choice, dialog):
        """معالجة اختيار مصدر الاستيراد"""
        dialog.accept()

        if choice == "db":
            # استيراد من قاعدة البيانات الحالية
            self.import_from_database()
        elif choice == "excel":
            # استيراد من ملف إكسل
            self.import_from_excel()

    def import_from_excel(self):
        """استيراد البيانات من ملف إكسل"""
        try:
            # إنشاء اتصال بقاعدة البيانات
            conn = self.connect_to_database()
            if not conn:
                QMessageBox.critical(self, "خطأ", "فشل الاتصال بقاعدة البيانات")
                return

            # استدعاء نافذة استيراد البيانات من إكسل
            from sub23_window import ExcelImportWindow
            excel_import_window = ExcelImportWindow(self, conn)
            excel_import_window.show()
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء فتح نافذة استيراد البيانات من إكسل:\n{str(e)}")

    def import_from_database(self):
        """استيراد المترشحين من اللوائح حسب السنة الدراسية والمستوى"""
        try:
            # تأكيد العملية - مربع حوار مخصص بعرض أكبر
            confirm_dialog = QDialog(self)
            confirm_dialog.setWindowTitle("تأكيد الاستيراد")
            confirm_dialog.setMinimumWidth(600)  # زيادة العرض ليناسب النص
            confirm_dialog.setMinimumHeight(200)
            confirm_dialog.setStyleSheet("background-color: #00FFFF;")

            # تخطيط عمودي للمربع
            layout = QVBoxLayout(confirm_dialog)

            # إضافة النص الرئيسي في إطار
            question_frame = QFrame()
            question_frame.setStyleSheet("""
                QFrame {
                    background-color: white;
                    border: 1.5px solid blue;
                    border-radius: 5px;
                    padding: 5px;
                }
            """)
            question_layout = QVBoxLayout(question_frame)

            # إضافة النص الرئيسي
            question_label = QLabel("هل تريد استيراد المترشحين من اللوائح؟")
            question_label.setFont(QFont("Calibri", 14, QFont.Bold))
            question_label.setStyleSheet("color: darkblue;")
            question_label.setAlignment(Qt.AlignCenter)
            question_label.setWordWrap(True)  # السماح بالتفاف النص
            question_layout.addWidget(question_label)

            # إضافة النص التوضيحي
            info_label = QLabel("سيتم حذف جميع البيانات الموجودة في جدول امتحانات.")
            info_label.setFont(QFont("Calibri", 13))
            info_label.setStyleSheet("color: black;")
            info_label.setAlignment(Qt.AlignCenter)
            info_label.setWordWrap(True)  # السماح بالتفاف النص
            question_layout.addWidget(info_label)

            # إضافة الإطار إلى التخطيط الرئيسي
            layout.addWidget(question_frame)

            # إضافة مساحة
            layout.addSpacing(10)

            # إضافة أزرار نعم ولا
            button_layout = QHBoxLayout()

            yes_button = QPushButton("نعم")
            yes_button.setFont(QFont("Calibri", 13, QFont.Bold))
            yes_button.setMinimumWidth(120)  # زيادة عرض الزر
            yes_button.setMinimumHeight(35)
            yes_button.setStyleSheet("""
                QPushButton {
                    background-color: #0066cc;
                    color: white;
                    border-radius: 5px;
                }
                QPushButton:hover {
                    background-color: #0055aa;
                }
            """)

            no_button = QPushButton("لا")
            no_button.setFont(QFont("Calibri", 13, QFont.Bold))
            no_button.setMinimumWidth(120)  # زيادة عرض الزر
            no_button.setMinimumHeight(35)
            no_button.setStyleSheet("""
                QPushButton {
                    background-color: #cc0000;
                    color: white;
                    border-radius: 5px;
                }
                QPushButton:hover {
                    background-color: #bb0000;
                }
            """)

            button_layout.addStretch()
            button_layout.addWidget(yes_button)
            button_layout.addWidget(no_button)
            button_layout.addStretch()

            layout.addLayout(button_layout)

            # ربط الأزرار بالإجراءات
            yes_button.clicked.connect(lambda: confirm_dialog.done(QMessageBox.Yes))
            no_button.clicked.connect(lambda: confirm_dialog.done(QMessageBox.No))

            # تعيين الزر الافتراضي (لا)
            no_button.setFocus()

            # عرض مربع الحوار وانتظار الرد
            reply = confirm_dialog.exec_()

            if reply != QMessageBox.Yes:
                return

            # الاتصال بقاعدة البيانات
            conn = self.connect_to_database()
            if not conn:
                return

            try:
                cursor = conn.cursor()

                # الحصول على السنوات الدراسية المتاحة
                cursor.execute("SELECT DISTINCT السنة_الدراسية FROM بيانات_المؤسسة ORDER BY السنة_الدراسية DESC")
                academic_years = [row[0] for row in cursor.fetchall()]

                if not academic_years:
                    error_dialog = QMessageBox(self)
                    error_dialog.setWindowTitle("تنبيه")
                    error_dialog.setText("لا توجد سنوات دراسية متاحة في جدول بيانات_المؤسسة.")
                    error_dialog.setIcon(QMessageBox.Warning)
                    error_dialog.setStyleSheet("""
                        QMessageBox {
                            background-color: #00FFFF;
                        }
                        QLabel {
                            font-family: 'Calibri';
                            font-size: 14pt;
                            font-weight: bold;
                            color: darkblue;
                        }
                        QPushButton {
                            font-family: 'Calibri';
                            font-size: 13pt;
                            font-weight: bold;
                            min-width: 100px;
                            padding: 5px;
                            background-color: #00FFFF;
                            border: 1.5px solid blue;
                            border-radius: 5px;
                        }
                    """)
                    error_dialog.exec_()
                    conn.close()
                    return

                # إنشاء مربع حوار أنيق لاختيار السنة الدراسية
                year_dialog = QDialog(self)
                year_dialog.setWindowTitle("اختيار السنة الدراسية")
                year_dialog.setMinimumWidth(400)
                year_dialog.setMinimumHeight(250)
                year_dialog.setStyleSheet("background-color: #00FFFF;")

                year_layout = QVBoxLayout(year_dialog)

                # إضافة عنوان
                title_label = QLabel("اختر السنة الدراسية:")
                title_label.setFont(QFont("Calibri", 14, QFont.Bold))
                title_label.setStyleSheet("color: darkblue; border: 1.5px solid blue; padding: 5px;")
                title_label.setAlignment(Qt.AlignCenter)
                year_layout.addWidget(title_label)

                # إنشاء قائمة منسدلة للسنوات الدراسية
                year_combo = QComboBox()
                year_combo.setFont(QFont("Calibri", 13, QFont.Bold))
                year_combo.setStyleSheet("""
                    QComboBox {
                        background-color: white;
                        border: 1.5px solid blue;
                        border-radius: 5px;
                        padding: 5px;
                        min-height: 35px;
                    }
                    QComboBox::drop-down {
                        border: none;
                        width: 30px;
                    }
                    QComboBox QAbstractItemView {
                        background-color: white;
                        selection-background-color: #33CCCC;
                        font-family: 'Calibri';
                        font-size: 13pt;
                        font-weight: bold;
                    }
                """)

                for year in academic_years:
                    year_combo.addItem(year)

                year_layout.addWidget(year_combo)

                # إضافة أزرار موافق وإلغاء
                button_layout = QHBoxLayout()

                ok_button = QPushButton("موافق")
                ok_button.setFont(QFont("Calibri", 13, QFont.Bold))
                ok_button.setStyleSheet("""
                    QPushButton {
                        background-color: #0066cc;
                        color: white;
                        border-radius: 5px;
                        min-height: 35px;
                        min-width: 100px;
                    }
                    QPushButton:hover {
                        background-color: #0055aa;
                    }
                """)
                ok_button.clicked.connect(year_dialog.accept)

                cancel_button = QPushButton("إلغاء")
                cancel_button.setFont(QFont("Calibri", 13, QFont.Bold))
                cancel_button.setStyleSheet("""
                    QPushButton {
                        background-color: #cc0000;
                        color: white;
                        border-radius: 5px;
                        min-height: 35px;
                        min-width: 100px;
                    }
                    QPushButton:hover {
                        background-color: #bb0000;
                    }
                """)
                cancel_button.clicked.connect(year_dialog.reject)

                button_layout.addWidget(ok_button)
                button_layout.addWidget(cancel_button)

                year_layout.addLayout(button_layout)

                # عرض مربع الحوار
                if year_dialog.exec_() != QDialog.Accepted:
                    conn.close()
                    return

                selected_year = year_combo.currentText()

                if not selected_year:
                    conn.close()
                    return

                # الحصول على المستويات المتاحة للسنة الدراسية المحددة
                cursor.execute(
                    "SELECT DISTINCT المستوى FROM اللوائح WHERE السنة_الدراسية = ? ORDER BY المستوى",
                    (selected_year,)
                )
                levels = [row[0] for row in cursor.fetchall()]

                if not levels:
                    error_dialog = QMessageBox(self)
                    error_dialog.setWindowTitle("تنبيه")
                    error_dialog.setText(f"لا توجد مستويات متاحة للسنة الدراسية {selected_year} في جدول اللوائح.")
                    error_dialog.setIcon(QMessageBox.Warning)
                    error_dialog.setStyleSheet("""
                        QMessageBox {
                            background-color: #00FFFF;
                        }
                        QLabel {
                            font-family: 'Calibri';
                            font-size: 14pt;
                            font-weight: bold;
                            color: darkblue;
                        }
                        QPushButton {
                            font-family: 'Calibri';
                            font-size: 13pt;
                            font-weight: bold;
                            min-width: 100px;
                            padding: 5px;
                            background-color: #00FFFF;
                            border: 1.5px solid blue;
                            border-radius: 5px;
                        }
                    """)
                    error_dialog.exec_()
                    conn.close()
                    return

                # إنشاء مربع حوار أنيق متعدد الاختيارات لاختيار المستويات
                level_dialog = QDialog(self)
                level_dialog.setWindowTitle("اختيار المستويات")
                level_dialog.setMinimumWidth(500)
                level_dialog.setMinimumHeight(400)
                level_dialog.setStyleSheet("background-color: #00FFFF;")

                layout = QVBoxLayout(level_dialog)

                # إضافة عنوان
                title_label = QLabel("اختر المستويات المطلوبة:")
                title_label.setFont(QFont("Calibri", 14, QFont.Bold))
                title_label.setStyleSheet("color: darkblue; border: 1.5px solid blue; padding: 5px;")
                title_label.setAlignment(Qt.AlignCenter)
                layout.addWidget(title_label)

                # إضافة شرح
                info_label = QLabel(f"السنة الدراسية: {selected_year}")
                info_label.setFont(QFont("Calibri", 13, QFont.Bold))
                info_label.setStyleSheet("color: black; margin-bottom: 10px;")
                info_label.setAlignment(Qt.AlignCenter)
                layout.addWidget(info_label)

                # إنشاء منطقة قابلة للتمرير لمربعات الاختيار
                scroll_area = QScrollArea()
                scroll_area.setWidgetResizable(True)
                scroll_area.setStyleSheet("""
                    QScrollArea {
                        border: 1.5px solid blue;
                        border-radius: 5px;
                        background-color: white;
                    }
                    QScrollBar:vertical {
                        border: none;
                        background-color: #f0f0f0;
                        width: 12px;
                        margin: 0px;
                    }
                    QScrollBar::handle:vertical {
                        background-color: #33CCCC;
                        min-height: 20px;
                        border-radius: 6px;
                    }
                    QScrollBar::add-line:vertical, QScrollBar::sub-line:vertical {
                        height: 0px;
                    }
                """)

                scroll_widget = QWidget()
                scroll_layout = QVBoxLayout(scroll_widget)
                scroll_layout.setSpacing(5)

                # إنشاء مربعات اختيار للمستويات
                level_checkboxes = []
                for level in levels:
                    checkbox = QCheckBox(level)
                    checkbox.setFont(QFont("Calibri", 13, QFont.Bold))
                    checkbox.setStyleSheet("""
                        QCheckBox {
                            color: black;
                            spacing: 10px;
                            min-height: 30px;
                        }
                        QCheckBox::indicator {
                            width: 20px;
                            height: 20px;
                        }
                        QCheckBox::indicator:unchecked {
                            border: 1.5px solid blue;
                            border-radius: 3px;
                            background-color: white;
                        }
                        QCheckBox::indicator:checked {
                            border: 1.5px solid blue;
                            border-radius: 3px;
                            background-color: #33CCCC;
                        }
                    """)
                    scroll_layout.addWidget(checkbox)
                    level_checkboxes.append(checkbox)

                scroll_area.setWidget(scroll_widget)
                layout.addWidget(scroll_area)

                # إضافة أزرار تحديد الكل وإلغاء تحديد الكل
                select_buttons_layout = QHBoxLayout()

                select_all_button = QPushButton("تحديد الكل")
                select_all_button.setFont(QFont("Calibri", 13, QFont.Bold))
                select_all_button.setStyleSheet("""
                    QPushButton {
                        background-color: #0066cc;
                        color: white;
                        border-radius: 5px;
                        min-height: 35px;
                    }
                    QPushButton:hover {
                        background-color: #0055aa;
                    }
                """)

                deselect_all_button = QPushButton("إلغاء تحديد الكل")
                deselect_all_button.setFont(QFont("Calibri", 13, QFont.Bold))
                deselect_all_button.setStyleSheet("""
                    QPushButton {
                        background-color: #cc0000;
                        color: white;
                        border-radius: 5px;
                        min-height: 35px;
                    }
                    QPushButton:hover {
                        background-color: #bb0000;
                    }
                """)

                select_buttons_layout.addWidget(select_all_button)
                select_buttons_layout.addWidget(deselect_all_button)

                layout.addLayout(select_buttons_layout)

                # ربط وظائف تحديد الكل وإلغاء تحديد الكل
                select_all_button.clicked.connect(lambda: [checkbox.setChecked(True) for checkbox in level_checkboxes])
                deselect_all_button.clicked.connect(lambda: [checkbox.setChecked(False) for checkbox in level_checkboxes])

                # أزرار موافق وإلغاء
                button_layout = QHBoxLayout()

                ok_button = QPushButton("استيراد")
                ok_button.setFont(QFont("Calibri", 13, QFont.Bold))
                ok_button.setStyleSheet("""
                    QPushButton {
                        background-color: #009900;
                        color: white;
                        border-radius: 5px;
                        min-height: 35px;
                        min-width: 100px;
                    }
                    QPushButton:hover {
                        background-color: #008800;
                    }
                """)
                ok_button.clicked.connect(level_dialog.accept)

                cancel_button = QPushButton("إلغاء")
                cancel_button.setFont(QFont("Calibri", 13, QFont.Bold))
                cancel_button.setStyleSheet("""
                    QPushButton {
                        background-color: #cc0000;
                        color: white;
                        border-radius: 5px;
                        min-height: 35px;
                        min-width: 100px;
                    }
                    QPushButton:hover {
                        background-color: #bb0000;
                    }
                """)
                cancel_button.clicked.connect(level_dialog.reject)

                button_layout.addWidget(ok_button)
                button_layout.addWidget(cancel_button)

                layout.addLayout(button_layout)

                # عرض مربع الحوار
                if level_dialog.exec_() != QDialog.Accepted:
                    conn.close()
                    return

                # الحصول على المستويات المحددة
                selected_levels = []
                for i, checkbox in enumerate(level_checkboxes):
                    if checkbox.isChecked():
                        selected_levels.append(levels[i])

                if not selected_levels:
                    error_dialog = QMessageBox(self)
                    error_dialog.setWindowTitle("تنبيه")
                    error_dialog.setText("لم يتم اختيار أي مستوى.")
                    error_dialog.setIcon(QMessageBox.Warning)
                    error_dialog.setStyleSheet("""
                        QMessageBox {
                            background-color: #00FFFF;
                        }
                        QLabel {
                            font-family: 'Calibri';
                            font-size: 14pt;
                            font-weight: bold;
                            color: darkblue;
                        }
                        QPushButton {
                            font-family: 'Calibri';
                            font-size: 13pt;
                            font-weight: bold;
                            min-width: 100px;
                            padding: 5px;
                            background-color: #00FFFF;
                            border: 1.5px solid blue;
                            border-radius: 5px;
                        }
                    """)
                    error_dialog.exec_()
                    conn.close()
                    return

                # إظهار شريط التقدم
                progress_dialog = QProgressDialog("جاري استيراد البيانات...", "إلغاء", 0, 100, self)
                progress_dialog.setWindowTitle("استيراد البيانات")
                progress_dialog.setWindowModality(Qt.WindowModal)
                progress_dialog.setMinimumDuration(0)
                progress_dialog.setStyleSheet("""
                    QProgressDialog {
                        background-color: #00FFFF;
                    }
                    QLabel {
                        font-family: 'Calibri';
                        font-size: 14pt;
                        font-weight: bold;
                        color: darkblue;
                        border: 1.5px solid blue;
                        padding: 5px;
                    }
                    QProgressBar {
                        border: 1.5px solid blue;
                        border-radius: 3px;
                        text-align: center;
                        background-color: white;
                        min-height: 20px;
                    }
                    QProgressBar::chunk {
                        background-color: #33CCCC;
                        width: 10px;
                    }
                    QPushButton {
                        font-family: 'Calibri';
                        font-size: 13pt;
                        font-weight: bold;
                        min-width: 100px;
                        padding: 5px;
                        background-color: #00FFFF;
                        border: 1.5px solid blue;
                        border-radius: 5px;
                    }
                """)
                progress_dialog.show()
                progress_dialog.setValue(10)

                # حذف جميع البيانات من جدول امتحانات
                cursor.execute("DELETE FROM امتحانات")
                progress_dialog.setValue(20)

                # استيراد البيانات من الجداول المختلفة
                placeholders = ','.join(['?' for _ in selected_levels])

                # بناء استعلام SQL لاستيراد البيانات
                query = f"""
                INSERT INTO امتحانات (الرمز, الجنس, الاسم_الكامل, تاريخ_الازدياد, القسم, المستوى, مركز_الامتحان, "المؤسسة_الأصلية", "ر.ت")
                SELECT
                    sg.الرمز,
                    sg.النوع,
                    sg.الاسم_والنسب,
                    sg.تاريخ_الازدياد,
                    l.القسم,
                    l.المستوى,
                    i.المؤسسة,
                    i.المؤسسة,
                    l.رت
                FROM
                    السجل_العام sg
                JOIN
                    اللوائح l ON sg.الرمز = l.الرمز
                JOIN
                    بيانات_المؤسسة i ON l.السنة_الدراسية = i.السنة_الدراسية
                WHERE
                    l.السنة_الدراسية = ? AND l.المستوى IN ({placeholders})
                """

                progress_dialog.setValue(40)
                progress_dialog.setLabelText("جاري استيراد البيانات...")

                # تنفيذ الاستعلام
                params = [selected_year] + selected_levels
                cursor.execute(query, params)

                # الحصول على عدد السجلات المضافة
                imported_count = cursor.rowcount

                progress_dialog.setValue(70)
                progress_dialog.setLabelText("جاري حفظ البيانات...")

                # حفظ التغييرات وإغلاق الاتصال
                conn.commit()
                conn.close()

                progress_dialog.setValue(80)
                progress_dialog.setLabelText("جاري تحديث الجدول...")

                # تحديث الجدول
                self.load_data()

                progress_dialog.setValue(90)
                progress_dialog.setLabelText("جاري تحديث قائمة المستويات...")

                # تحديث قائمة المستويات
                self.level_combo.clear()
                self.level_combo.addItem("الرجاء تحديد المستوى المطلوب")
                for level in selected_levels:
                    self.level_combo.addItem(level)

                progress_dialog.setValue(100)
                progress_dialog.setLabelText("تم الاستيراد بنجاح!")

                # عرض رسالة نجاح باستخدام النافذة الحوارية المخصصة
                from custom_message_dialog import CustomMessageDialog

                # إنشاء نص الرسالة
                success_message = f"تم استيراد البيانات بنجاح!\n\n"
                success_message += f"تم استيراد {imported_count} سجل للسنة الدراسية {selected_year}.\n\n"
                success_message += f"المستويات المستوردة:\n{', '.join(selected_levels)}"

                # إنشاء النافذة الحوارية
                success_dialog = CustomMessageDialog(
                    parent=self,
                    title="تمت عملية الاستيراد بنجاح",
                    message=success_message,
                    icon_type="information"
                )

                # عرض النافذة الحوارية
                success_dialog.exec_()

            except Exception as e:
                # عرض رسالة خطأ باستخدام النافذة الحوارية المخصصة
                from custom_message_dialog import CustomMessageDialog

                error_dialog = CustomMessageDialog(
                    parent=self,
                    title="خطأ في الاستيراد",
                    message=f"فشل في استيراد البيانات: {e}",
                    icon_type="error"
                )

                error_dialog.exec_()
                if conn:
                    conn.close()

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في استيراد البيانات: {e}")

    def open_exam_schedule(self):
        """فتح نافذة جدولة الامتحان"""
        try:
            # استيراد نافذة جدولة الامتحان الاحترافية
            from sub26_window import ExamScheduleProfessional

            # إنشاء نافذة جدولة الامتحان الاحترافية
            schedule_dialog = ExamScheduleProfessional(parent=self)

            # عرض النافذة
            schedule_dialog.exec_()

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في فتح نافذة جدولة الامتحان: {e}")

    def open_table_labels(self):
        """فتح نافذة محضر التوقيعات"""
        # تم إلغاء هذه الوظيفة
        QMessageBox.information(self, "معلومات", "هذه الوظيفة غير متاحة حالياً.")

    def open_table_labels_pdf(self):
        """فتح نافذة ملصقات الطاولات للطباعة على PDF"""
        try:
            # استيراد نافذة ملصقات الطاولات للطباعة على PDF
            from sub27_window import TableLabelsPDF

            # إنشاء نافذة ملصقات الطاولات للطباعة على PDF
            labels_dialog = TableLabelsPDF(parent=self, db_path=self.db_path)

            # عرض النافذة
            labels_dialog.exec_()

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في فتح نافذة ملصقات الطاولات للطباعة على PDF: {e}")

    def delete_all_data(self):
        """حذف جميع بيانات جدول امتحانات"""
        # إنشاء مربع حوار للتأكيد
        confirm_dialog = QDialog(self)
        confirm_dialog.setWindowTitle("تأكيد الحذف")
        confirm_dialog.setMinimumWidth(500)
        confirm_dialog.setMinimumHeight(200)
        confirm_dialog.setStyleSheet("background-color: #ffeeee;")  # خلفية حمراء فاتحة للتحذير

        # تخطيط عمودي للمربع
        layout = QVBoxLayout(confirm_dialog)

        # إضافة أيقونة تحذير
        warning_label = QLabel("⚠️ تحذير")
        warning_label.setFont(QFont("Calibri", 16, QFont.Bold))
        warning_label.setStyleSheet("color: #cc0000;")
        warning_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(warning_label)

        # إضافة النص الرئيسي في إطار
        question_frame = QFrame()
        question_frame.setStyleSheet("""
            QFrame {
                background-color: white;
                border: 2px solid #cc0000;
                border-radius: 5px;
                padding: 10px;
            }
        """)
        question_layout = QVBoxLayout(question_frame)

        question_text = QLabel("هل أنت متأكد من حذف جميع بيانات جدول امتحانات؟\n\nهذا الإجراء لا يمكن التراجع عنه!")
        question_text.setFont(QFont("Calibri", 14))
        question_text.setStyleSheet("color: #cc0000; font-weight: bold;")
        question_text.setAlignment(Qt.AlignCenter)
        question_layout.addWidget(question_text)

        layout.addWidget(question_frame)

        # إضافة أزرار نعم/لا
        button_layout = QHBoxLayout()

        yes_button = QPushButton("نعم، حذف جميع البيانات")
        yes_button.setFont(QFont("Calibri", 13, QFont.Bold))
        yes_button.setMinimumHeight(40)
        yes_button.setStyleSheet("""
            QPushButton {
                background-color: #cc0000;
                color: white;
                border-radius: 5px;
            }
            QPushButton:hover {
                background-color: #bb0000;
            }
        """)

        no_button = QPushButton("لا، إلغاء العملية")
        no_button.setFont(QFont("Calibri", 13, QFont.Bold))
        no_button.setMinimumHeight(40)
        no_button.setStyleSheet("""
            QPushButton {
                background-color: #444444;
                color: white;
                border-radius: 5px;
            }
            QPushButton:hover {
                background-color: #333333;
            }
        """)

        button_layout.addStretch()
        button_layout.addWidget(yes_button)
        button_layout.addWidget(no_button)
        button_layout.addStretch()

        layout.addLayout(button_layout)

        # ربط الأزرار بالإجراءات
        yes_button.clicked.connect(lambda: confirm_dialog.done(QMessageBox.Yes))
        no_button.clicked.connect(lambda: confirm_dialog.done(QMessageBox.No))

        # تعيين الزر الافتراضي (لا)
        no_button.setFocus()

        # عرض مربع الحوار وانتظار الرد
        reply = confirm_dialog.exec_()

        if reply != QMessageBox.Yes:
            return

        # إنشاء مربع تقدم العملية
        progress_dialog = QProgressDialog("جاري حذف البيانات...", "إلغاء", 0, 100, self)
        progress_dialog.setWindowTitle("حذف البيانات")
        progress_dialog.setMinimumWidth(400)
        progress_dialog.setWindowModality(Qt.WindowModal)
        progress_dialog.setAutoClose(True)
        progress_dialog.setAutoReset(True)
        progress_dialog.setValue(0)
        progress_dialog.show()

        try:
            # الاتصال بقاعدة البيانات
            conn = self.connect_to_database()
            if not conn:
                return

            progress_dialog.setValue(20)

            try:
                cursor = conn.cursor()

                # حذف جميع البيانات من جدول امتحانات
                cursor.execute("DELETE FROM امتحانات")

                progress_dialog.setValue(60)

                # حفظ التغييرات
                conn.commit()

                progress_dialog.setValue(80)

                # إغلاق الاتصال
                conn.close()

                progress_dialog.setValue(90)

                # تحديث الجدول
                self.load_data()

                progress_dialog.setValue(100)

                # عرض رسالة نجاح
                QMessageBox.information(
                    self,
                    "تم الحذف",
                    "تم حذف جميع بيانات جدول امتحانات بنجاح."
                )

            except Exception as e:
                if conn:
                    conn.rollback()
                    conn.close()
                raise e

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في حذف البيانات: {e}")


# تشغيل التطبيق كنافذة مستقلة للاختبار
if __name__ == "__main__":
    app = QApplication(sys.argv)
    app.setLayoutDirection(Qt.RightToLeft)  # تعيين اتجاه التخطيط من اليمين إلى اليسار
    window = ExamsWindow()
    window.show()
    sys.exit(app.exec_())

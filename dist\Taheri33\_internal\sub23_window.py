import sys
import sqlite3
import datetime
import os
from PyQt5.QtWidgets import (QApplication, QWidget, QVBoxLayout, QHBoxLayout,
                            QPushButton, QFrame, QLabel, QComboBox, QLineEdit,
                            QMessageBox, QFileDialog, QGridLayout, QDialog,
                            QTableWidget, QTableWidgetItem, QHeaderView, QTabWidget,
                            QProgressBar)
from PyQt5.QtCore import Qt, QDate, QTimer
from PyQt5.QtGui import QFont, QColor, QIcon
from PyQt5.QtSql import QSqlQuery

# استيراد فئة معالج تقارير الغياب
from attendance_report import AttendanceReport
from split_attendance_report import SplitAttendanceReport  # استيراد الفئة الجديدة
from sub100_window import ConfirmationDialogs  # استيراد فئة رسائل التأكيد المخصصة

class PrintListsWindow(QWidget):
    def __init__(self, parent=None, db=None, academic_year=None):
        super().__init__(parent)

        # حفظ السنة الدراسية
        self.academic_year = academic_year
        self.parent_window = parent  # حفظ النافذة الأم
        self.is_embedded = parent is not None  # تحديد ما إذا كانت النافذة مدمجة

        # إذا كانت النافذة مدمجة، نقوم بإزالة زر الإغلاق من شريط العنوان
        if self.is_embedded:
            self.setWindowFlags(Qt.Window | Qt.CustomizeWindowHint | Qt.WindowTitleHint | Qt.WindowMinimizeButtonHint | Qt.WindowMaximizeButtonHint)

        # التعامل مع قاعدة البيانات
        self.db = None
        self.db_type = None

        # التحقق من نوع قاعدة البيانات المستخدمة
        if db is not None:
            if str(type(db)).find('QSqlDatabase') != -1:
                # إذا كان الاتصال من نوع QSqlDatabase، نحفظه ونحدد النوع
                self.db = db
                self.db_type = 'QSqlDatabase'
                print("تم اكتشاف اتصال من نوع QSqlDatabase")
            elif str(type(db)).find('sqlite3.Connection') != -1:
                # إذا كان الاتصال من نوع sqlite3.Connection، نحفظه ونحدد النوع
                self.db = db
                self.db_type = 'sqlite3'
                print("تم اكتشاف اتصال من نوع sqlite3.Connection")
            else:
                # إذا كان نوع الاتصال غير معروف، نطبع معلومات تشخيصية
                print(f"نوع اتصال قاعدة البيانات غير معروف: {type(db)}")

        # إنشاء اتصال جديد إذا لم يتم تمرير اتصال
        if self.db is None:
            try:
                self.db = sqlite3.connect("data.db")
                self.db_type = 'sqlite3'
                print("تم إنشاء اتصال جديد من نوع sqlite3.Connection")
            except Exception as e:
                print(f"فشل إنشاء اتصال جديد بقاعدة البيانات: {e}")

        # طباعة معلومات تشخيصية
        print(f"INFO: تم إنشاء PrintListsWindow مع db_type={self.db_type}, academic_year={academic_year}, is_embedded={self.is_embedded}")

        # تعيين خصائص النافذة
        self.setWindowModality(Qt.NonModal)
        # لا نقوم بتعيين windowFlags هنا لأننا قمنا بتعيينها بالفعل في حالة النافذة المدمجة
        if not self.is_embedded:
            self.setWindowFlags(Qt.Window)
        self.setWindowTitle("طباعة اللوائح")
        self.setGeometry(300, 30, 700, 650)

        # تعيين أيقونة النافذة
        try:
            self.setWindowIcon(QIcon("01.ico"))
        except Exception as e:
            print(f"خطأ في تحميل أيقونة البرنامج: {e}")

        # Set window direction to right-to-left for Arabic
        self.setLayoutDirection(Qt.RightToLeft)

        # تعيين مؤشر اليد للنافذة الرئيسية
        self.setCursor(Qt.ArrowCursor)  # المؤشر الافتراضي للخلفية

        # إنشاء نسخة من فئة تقرير الغياب
        self.report_generator = AttendanceReport()
        self.split_report_generator = SplitAttendanceReport()  # إنشاء كائن من الفئة الجديدة

        # إنشاء اتصال بقاعدة البيانات إذا لم يتم تمريره
        if self.db is None:
            try:
                self.db = sqlite3.connect("data.db")
                print("INFO: تم إنشاء اتصال جديد بقاعدة البيانات في PrintListsWindow")
            except Exception as e:
                print(f"ERROR: فشل الاتصال بقاعدة البيانات في PrintListsWindow: {e}")

        # Initialize UI
        self.initUI()

        # تحميل رموز الحصص المحفوظة مسبقا
        self.load_period_settings()

        # تحديد رقم الحراسة الافتراضي (1) وتحميل الأقسام المسندة
        # نستخدم QTimer لتأخير التحميل قليلاً بعد إنشاء واجهة المستخدم
        QTimer.singleShot(100, self.set_default_supervision)

    def initUI(self):
        # Main vertical layout
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(5, 5, 5, 5)
        main_layout.setSpacing(1)

        # Window title
        title_label = QLabel("طباعة اللوائح")
        title_label.setFont(QFont("Calibri", 20, QFont.Bold))
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("color: black;")
        main_layout.addWidget(title_label)

        # Content frame with light blue background - تبسيط الإطار
        content_frame = QFrame()
        content_frame.setStyleSheet("""
            QFrame {
                background-color: white;
                border: 1px solid #ddd;
                padding: 15px;
            }
        """)

        # Content layout
        content_layout = QVBoxLayout(content_frame)
        content_layout.setContentsMargins(15, 15, 15, 15)
        content_layout.setSpacing(15)

        # تنسيق العناوين لتكون واضحة
        label_style = """
            QLabel {
                font-family: 'Calibri';
                font-size: 13pt;
                font-weight: bold;
                color: black;
                background-color: transparent;
                padding: 5px;
            }
        """

        # إضافة اختيار رقم الحراسة - في صف واحد أفقياً
        supervision_row = QHBoxLayout()
        supervision_row.setSpacing(10)

        supervision_label = QLabel("اختر رقم الحراسة:")
        supervision_label.setFont(QFont("Calibri", 13, QFont.Bold))
        supervision_label.setStyleSheet(label_style)
        supervision_label.setFixedWidth(150)  # عرض ثابت للعنوان
        supervision_row.addWidget(supervision_label)

        self.supervision_combo = QComboBox()
        self.supervision_combo.setFont(QFont("Calibri", 13))
        self.supervision_combo.setFixedWidth(200)  # تعيين عرض ثابت 200 بكسل
        self.supervision_combo.setCursor(Qt.PointingHandCursor)  # تغيير شكل المؤشر إلى يد
        # ربط تغيير قيمة مربع الحراسة بتحديث الأقسام المسندة
        self.supervision_combo.currentIndexChanged.connect(self.on_supervision_changed)

        # إضافة خيارات الحراسة (من 1 إلى 5)
        for i in range(1, 6):
            self.supervision_combo.addItem(f"حراسة رقم {i}")

        # لا نقوم بتحديد أي حراسة افتراضياً، نترك مربع التحرير والسرد فارغاً
        # حتى يقوم المستخدم بتحديد رقم الحراسة بنفسه
        self.supervision_combo.setCurrentIndex(-1)
        supervision_row.addWidget(self.supervision_combo)
        supervision_row.addStretch(1)  # إضافة مساحة مرنة في نهاية الصف

        content_layout.addLayout(supervision_row)

        # Class selection - في صف واحد أفقياً
        class_row = QHBoxLayout()
        class_row.setSpacing(10)

        class_label = QLabel("اختر القسم:")
        class_label.setFont(QFont("Calibri", 13, QFont.Bold))
        class_label.setStyleSheet(label_style)
        class_label.setFixedWidth(150)  # عرض ثابت للعنوان
        class_row.addWidget(class_label)

        self.class_combo = QComboBox()
        self.class_combo.setFont(QFont("Calibri", 13))
        self.class_combo.setFixedWidth(200)  # تعيين عرض ثابت 200 بكسل
        self.class_combo.setCursor(Qt.PointingHandCursor)  # تغيير شكل المؤشر إلى يد
        class_row.addWidget(self.class_combo)
        class_row.addStretch(1)  # إضافة مساحة مرنة في نهاية الصف

        content_layout.addLayout(class_row)

        # تحميل الأقسام المسندة من قاعدة البيانات حسب رقم الحراسة 1 (الافتراضي)
        # سيتم تحميل الأقسام تلقائياً عند تعيين الحراسة رقم 1 كقيمة افتراضية
        # لذلك لا داعي لاستدعاء الدالة هنا

        # إضافة اختيار الأسبوع - في صف واحد أفقياً
        week_row = QHBoxLayout()
        week_row.setSpacing(10)

        week_label = QLabel("اختر الأسبوع:")
        week_label.setFont(QFont("Calibri", 13, QFont.Bold))
        week_label.setStyleSheet(label_style)
        week_label.setFixedWidth(150)  # عرض ثابت للعنوان
        week_row.addWidget(week_label)

        self.week_combo = QComboBox()
        self.week_combo.setFont(QFont("Calibri", 13))
        self.week_combo.setFixedWidth(200)  # تعيين عرض ثابت 200 بكسل
        self.week_combo.setCursor(Qt.PointingHandCursor)  # تغيير شكل المؤشر إلى يد
        self.week_combo.addItem("الأسبوع الحالي")
        self.week_combo.addItem("الأسبوع القادم")
        self.week_combo.addItem("الأسبوع الثالث")
        week_row.addWidget(self.week_combo)
        week_row.addStretch(1)  # إضافة مساحة مرنة في نهاية الصف

        content_layout.addLayout(week_row)

        # إضافة عنوان لمربعات الحصص
        periods_label = QLabel("رموز الحصص في التقرير:")
        periods_label.setFont(QFont("Calibri", 13, QFont.Bold))
        periods_label.setStyleSheet(label_style)
        content_layout.addWidget(periods_label)

        # إنشاء إطار لاحتواء مربعات الحصص
        periods_frame = QFrame()
        periods_frame.setStyleSheet("""
            QFrame {
                background-color: #f9f9f9;
                border: 1px solid #ddd;
                border-radius: 5px;
                padding: 8px;
            }
        """)
        periods_layout = QHBoxLayout(periods_frame)
        periods_layout.setSpacing(10)
        periods_layout.setAlignment(Qt.AlignCenter)

        # إنشاء قائمة لتخزين مربعات النصوص
        self.period_inputs = []

        # إضافة 8 مربعات نصية للحصص بدون عناوين
        for i in range(8):
            period_input = QLineEdit(str(i + 1))  # القيمة الافتراضية من 1 إلى 8
            period_input.setFont(QFont("Calibri", 12))
            period_input.setFixedSize(30, 30)
            period_input.setAlignment(Qt.AlignCenter)
            period_input.setMaxLength(2)
            period_input.setCursor(Qt.PointingHandCursor)  # تغيير شكل المؤشر إلى يد

            period_input.setStyleSheet("""
                QLineEdit {
                    border: 1px solid #bbb;
                    border-radius: 4px;
                    background-color: white;
                }
                QLineEdit:focus {
                    border: 1px solid #3498db;
                }
            """)

            self.period_inputs.append(period_input)
            periods_layout.addWidget(period_input)

        # إضافة شرح مختصر
        note_label = QLabel("ملاحظة: يمكن تعديل رموز الحصص حسب جدول المؤسسة (8 حصص لكل يوم)")
        note_label.setFont(QFont("Calibri", 10))
        note_label.setStyleSheet("color: #555;")
        note_label.setAlignment(Qt.AlignCenter)

        # إضافة زر حفظ التعديلات للحصص
        save_periods_btn = QPushButton("حفظ التعديلات")
        save_periods_btn.setFont(QFont("Calibri", 11, QFont.Bold))
        save_periods_btn.setCursor(Qt.PointingHandCursor)  # تغيير شكل المؤشر إلى يد
        save_periods_btn.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                color: white;
                border-radius: 5px;
                padding: 8px 15px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #219a52;
            }
            QPushButton:pressed {
                background-color: #1e8449;
            }
        """)
        save_periods_btn.clicked.connect(self.save_period_settings)

        # إضافة العناصر إلى التخطيط
        periods_note_layout = QVBoxLayout()
        periods_note_layout.addWidget(periods_frame)
        periods_note_layout.addWidget(note_label)
        periods_note_layout.addWidget(save_periods_btn)  # إضافة زر الحفظ
        periods_note_layout.setAlignment(save_periods_btn, Qt.AlignCenter)  # محاذاة الزر للوسط

        content_layout.addLayout(periods_note_layout)

        # Add spacer
        content_layout.addStretch()

        # تعديل الأزرار الثلاثة بتغيير التخطيط والأبعاد
        # إنشاء تخطيط شبكي بدلاً من التخطيط الأفقي
        buttons_grid = QGridLayout()
        buttons_grid.setSpacing(10)

        # Create button style
        button_style = """
            QPushButton {
                background-color: #3498db;
                color: white;
                border-radius: 5px;
                padding: 10px 15px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
            QPushButton:pressed {
                background-color: #1c6ea4;
            }
        """

        # الزر الأول: لائحة الغياب الأسبوعي - عرض 200
        self.print_btn1 = QPushButton("لائحة الغياب الأسبوعي")
        self.print_btn1.setFont(QFont("Calibri", 13, QFont.Bold))
        self.print_btn1.setCursor(Qt.PointingHandCursor)  # تغيير شكل المؤشر إلى يد
        self.print_btn1.setStyleSheet(button_style)
        self.print_btn1.setFixedWidth(200)  # تعيين العرض إلى 200
        self.print_btn1.clicked.connect(self.show_loading_and_print_report)

        # الزر الثاني: لائحة الغياب نصف الأسبوعي الاثنين - عرض 200
        self.print_btn2 = QPushButton("لائحة الغياب نصف الأسبوعي الاثنين")
        self.print_btn2.setFont(QFont("Calibri", 13, QFont.Bold))
        self.print_btn2.setCursor(Qt.PointingHandCursor)  # تغيير شكل المؤشر إلى يد
        self.print_btn2.setStyleSheet(button_style)
        self.print_btn2.setFixedWidth(300)  # تعيين العرض إلى 200
        self.print_btn2.clicked.connect(self.print_first_half_report)

        # الزر الثالث: لائحة الغياب نصف الأسبوعي الأربعاء - عرض 200 وتحت الزر الثاني
        self.print_btn3 = QPushButton("لائحة الغياب نصف الأسبوعي الأربعاء")
        self.print_btn3.setFont(QFont("Calibri", 13, QFont.Bold))
        self.print_btn3.setCursor(Qt.PointingHandCursor)  # تغيير شكل المؤشر إلى يد
        self.print_btn3.setStyleSheet(button_style)
        self.print_btn3.setFixedWidth(300)  # تعيين العرض إلى 200
        self.print_btn3.clicked.connect(self.print_second_half_report)

        # إضافة الأزرار في التخطيط الشبكي
        buttons_grid.addWidget(self.print_btn1, 0, 0, 1, 1, Qt.AlignCenter)  # الصف 0، العمود 0
        buttons_grid.addWidget(self.print_btn2, 0, 1, 1, 1, Qt.AlignCenter)  # الصف 0، العمود 1
        buttons_grid.addWidget(self.print_btn3, 1, 1, 1, 1, Qt.AlignCenter)  # الصف 1، العمود 1 (تحت الزر الثاني)

        # تخصيص نسبة عرض الأعمدة في الشبكة
        buttons_grid.setColumnStretch(0, 1)  # تخصيص نسبة للعمود 0
        buttons_grid.setColumnStretch(1, 1)  # تخصيص نسبة للعمود 1
        buttons_grid.setColumnStretch(2, 1)  # تخصيص نسبة للعمود 2 (مساحة فارغة)

        # إضافة تخطيط الأزرار إلى التخطيط الرئيسي
        content_layout.addLayout(buttons_grid)

        # Add content frame to main layout
        main_layout.addWidget(content_frame)

        # لا نضيف زر إغلاق للنوافذ المدمجة

    def save_period_settings(self):
        """حفظ إعدادات رموز الحصص في ملف JSON"""
        try:
            import json

            # الحصول على قيم الحصص من مربعات النصوص
            periods = [input_field.text() for input_field in self.period_inputs]

            # التحقق من صحة القيم المدخلة
            for idx, period in enumerate(periods):
                if not period.strip():
                    periods[idx] = str(idx + 1)  # استعادة القيمة الافتراضية في حالة تركها فارغة

            # إنشاء قاموس لتخزين الإعدادات
            settings = {
                "period_codes": periods,
                "last_updated": datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            }

            # اسم الملف للإعدادات
            settings_file = "print_settings.json"

            # حفظ الإعدادات في ملف JSON
            with open(settings_file, 'w', encoding='utf-8') as f:
                json.dump(settings, f, ensure_ascii=False, indent=4)

            # عرض رسالة نجاح مخصصة
            ConfirmationDialogs.show_custom_success_message(self, "تم حفظ إعدادات رموز الحصص بنجاح", "تم الحفظ")
            print("تم حفظ إعدادات رموز الحصص بنجاح")

        except Exception as e:
            # عرض رسالة خطأ مخصصة
            ConfirmationDialogs.show_custom_error_message(self, f"حدث خطأ أثناء حفظ الإعدادات:\n{str(e)}", "خطأ")
            print(f"خطأ أثناء حفظ الإعدادات: {str(e)}")

    def load_period_settings(self):
        """تحميل إعدادات رموز الحصص من ملف JSON"""
        try:
            import json

            # اسم الملف للإعدادات
            settings_file = "print_settings.json"

            # التحقق من وجود الملف
            if not os.path.exists(settings_file):
                return  # إذا لم يكن الملف موجوداً، لا تفعل شيئاً

            # قراءة الإعدادات من ملف JSON
            with open(settings_file, 'r', encoding='utf-8') as f:
                settings = json.load(f)

            # تحديث مربعات النصوص بالقيم المحفوظة
            periods = settings.get("period_codes", [])
            for idx, period in enumerate(periods):
                if idx < len(self.period_inputs):
                    self.period_inputs[idx].setText(period)

        except Exception as e:
            print(f"Error loading period settings: {str(e)}")
            # Usando un enfoque silencioso para no molestar al usuario con mensajes de error
            # si hay problemas al cargar las configuraciones

    def show_loading_and_print_report(self):
        """عرض شريط التحميل ثم طباعة تقرير الغياب"""
        # إنشاء نافذة حوار للتحميل
        self.loading_dialog = QDialog(self)
        self.loading_dialog.setWindowTitle("جاري إنشاء التقرير...")
        self.loading_dialog.setFixedSize(400, 100)
        self.loading_dialog.setWindowFlags(Qt.Dialog | Qt.CustomizeWindowHint | Qt.WindowTitleHint)

        # إنشاء تخطيط للنافذة
        layout = QVBoxLayout(self.loading_dialog)

        # إضافة نص
        label = QLabel("جاري إنشاء تقرير الغياب الأسبوعي، يرجى الانتظار...")
        label.setFont(QFont("Calibri", 12))
        label.setAlignment(Qt.AlignCenter)
        layout.addWidget(label)

        # إضافة شريط التحميل
        self.progress_bar = QProgressBar()
        self.progress_bar.setRange(0, 0)  # شريط تحميل غير محدد
        self.progress_bar.setTextVisible(False)
        layout.addWidget(self.progress_bar)

        # عرض نافذة التحميل
        self.loading_dialog.show()

        # استخدام مؤقت لبدء عملية الطباعة بعد عرض نافذة التحميل
        QTimer.singleShot(100, lambda: self.print_attendance_report(1))

    def print_attendance_report(self, report_type):
        """Generate and print the attendance report PDF"""
        try:
            # الحصول على قيم الحصص من مربعات النصوص - فقط 8 مربعات
            periods = [input_field.text() for input_field in self.period_inputs]

            # Get selected class and institution data
            selected_class = self.class_combo.currentText()
            institution_data = self.get_institution_data()

            # حساب تاريخ بداية الأسبوع (الاثنين) بناءً على اختيار الأسبوع
            selected_date = self.get_week_start_date()

            # Create appropriate filename based on report type
            report_type_names = {
                1: "تقرير_الغياب",
                2: "نموذج_فارغ",
                3: "نموذج_مختصر"
            }
            report_type_name = report_type_names.get(report_type, "تقرير_الغياب")

            # التحقق مما إذا كان المستخدم قد اختار "جميع الأقسام المسندة"
            if selected_class == "جميع الأقسام المسندة":
                # طباعة جميع الأقسام في ملف واحد
                # جمع جميع الأقسام من مربع التحرير والسرد (تخطي العنصر الأول "جميع الأقسام المسندة")
                classes_to_print = [self.class_combo.itemText(i) for i in range(1, self.class_combo.count())]

                if not classes_to_print:
                    ConfirmationDialogs.show_custom_warning_message(self, "لا توجد أقسام مسندة للطباعة.", "تنبيه")
                    return

                # إنشاء اسم الملف لجميع الأقسام مع التاريخ والوقت
                current_datetime = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
                file_name = f"{report_type_name}_جميع_الأقسام_{current_datetime}.pdf"

                # إنشاء مجلد رئيسي على سطح المكتب
                desktop_path = os.path.join(os.path.expanduser("~"), "Desktop")
                main_reports_dir = os.path.join(desktop_path, "تقارير برنامج المعين في الحراسة العامة")
                if not os.path.exists(main_reports_dir):
                    os.makedirs(main_reports_dir)
                    print(f"تم إنشاء المجلد الرئيسي: {main_reports_dir}")

                # إنشاء مجلد فرعي لأوراق الغياب الأسبوعية والنصف الأسبوعية
                reports_dir = os.path.join(main_reports_dir, "تقارير لوائح الغياب الأسبوعية والنصف الأسبوعية")
                if not os.path.exists(reports_dir):
                    os.makedirs(reports_dir)
                    print(f"تم إنشاء المجلد الفرعي: {reports_dir}")

                # تحديد مسار الملف الكامل
                file_path = os.path.join(reports_dir, file_name)

                # إعداد بيانات كل قسم
                class_data_list = []
                for class_name in classes_to_print:
                    # الحصول على بيانات الطلاب حسب نوع التقرير
                    if report_type == 1:  # تقرير الغياب الأسبوعي (كامل)
                        students = self.get_student_data(class_name)
                    elif report_type == 2:  # نموذج فارغ
                        # Create empty template with placeholder for 30 students
                        students = [{"id": i, "name": "", "code": ""} for i in range(1, 31)]
                    elif report_type == 3:  # نموذج مختصر
                        # Get students but limit to 15 or add placeholders if less
                        students = self.get_student_data(class_name)
                        if len(students) < 15:
                            # Add placeholders to reach 15 students
                            for i in range(len(students) + 1, 16):
                                students.append({"id": i, "name": "", "code": ""})
                        else:
                            # Limit to first 15 students
                            students = students[:15]

                    # إضافة البيانات لقائمة الأقسام
                    class_data_list.append({
                        'class_name': class_name,
                        'students': students
                    })

                # إنشاء التقرير بكل الأقسام في ملف واحد
                success = self.report_generator.generate_multi_class_pdf(
                    file_path,
                    class_data_list,
                    institution_data,
                    selected_date,
                    report_type,
                    periods  # إرسال قيم الحصص كمعامل إضافي
                )

                # إغلاق نافذة التحميل
                if hasattr(self, 'loading_dialog') and self.loading_dialog.isVisible():
                    self.loading_dialog.close()

                if success:
                    # فتح الملف بعد الإنشاء - سيتم عرض رسالة النجاح في دالة open_pdf_file
                    self.open_pdf_file(file_path)
                else:
                    ConfirmationDialogs.show_custom_warning_message(self, "حدث خطأ أثناء إنشاء التقرير", "تنبيه")

                return

            # في حالة اختيار قسم محدد (وليس "جميع الأقسام المسندة")
            # إنشاء اسم الملف مع التاريخ والوقت
            current_datetime = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
            file_name = f"{report_type_name}_{selected_class}_{current_datetime}.pdf"

            # إنشاء مجلد رئيسي على سطح المكتب
            desktop_path = os.path.join(os.path.expanduser("~"), "Desktop")
            main_reports_dir = os.path.join(desktop_path, "تقارير برنامج المعين في الحراسة العامة")
            if not os.path.exists(main_reports_dir):
                os.makedirs(main_reports_dir)
                print(f"تم إنشاء المجلد الرئيسي: {main_reports_dir}")

            # إنشاء مجلد فرعي لأوراق الغياب الأسبوعية والنصف الأسبوعية
            reports_dir = os.path.join(main_reports_dir, "تقارير لوائح الغياب الأسبوعية والنصف الأسبوعية")
            if not os.path.exists(reports_dir):
                os.makedirs(reports_dir)
                print(f"تم إنشاء المجلد الفرعي: {reports_dir}")

            # تحديد مسار الملف الكامل
            file_path = os.path.join(reports_dir, file_name)

            # طباعة تقرير لقسم واحد
            self._print_single_class_report(selected_class, file_path, report_type, selected_date, institution_data, periods)

        except Exception as e:
            # إغلاق نافذة التحميل في حالة حدوث خطأ
            if hasattr(self, 'loading_dialog') and self.loading_dialog.isVisible():
                self.loading_dialog.close()
            ConfirmationDialogs.show_custom_error_message(self, f"حدث خطأ أثناء إنشاء التقرير: {str(e)}", "خطأ")

    def _print_single_class_report(self, class_name, file_path, report_type, selected_date, institution_data, periods):
        """طباعة تقرير لقسم واحد محدد"""
        # Retrieve student data from database based on report type
        students = []
        if report_type == 1:  # تقرير الغياب الأسبوعي (كامل)
            students = self.get_student_data(class_name)
        elif report_type == 2:  # نموذج فارغ
            # Create empty template with placeholder for 30 students
            for i in range(1, 31):
                students.append({"id": i, "name": "", "code": ""})
        elif report_type == 3:  # نموذج مختصر
            # Get students but limit to 15 or add placeholders if less
            students = self.get_student_data(class_name)
            if len(students) < 15:
                # Add placeholders to reach 15 students
                for i in range(len(students) + 1, 16):
                    students.append({"id": i, "name": "", "code": ""})
            else:
                # Limit to first 15 students
                students = students[:15]

        # استخدام كائن معالج التقارير لإنشاء ملف PDF
        success = self.report_generator.generate_pdf(
            file_path,
            students,
            class_name,
            institution_data,
            selected_date,
            report_type,
            periods  # إرسال قيم الحصص كمعامل إضافي
        )

        # إغلاق نافذة التحميل
        if hasattr(self, 'loading_dialog') and self.loading_dialog.isVisible():
            self.loading_dialog.close()

        if success:
            # فتح الملف بعد الإنشاء
            self.open_pdf_file(file_path)
        else:
            ConfirmationDialogs.show_custom_warning_message(self, "حدث خطأ أثناء إنشاء التقرير", "تنبيه")

    def print_first_half_report(self):
        """طباعة تقرير النصف الأول من الأسبوع (الاثنين والثلاثاء)"""
        # إنشاء نافذة حوار للتحميل
        self.loading_dialog = QDialog(self)
        self.loading_dialog.setWindowTitle("جاري إنشاء التقرير...")
        self.loading_dialog.setFixedSize(400, 100)
        self.loading_dialog.setWindowFlags(Qt.Dialog | Qt.CustomizeWindowHint | Qt.WindowTitleHint)

        # إنشاء تخطيط للنافذة
        layout = QVBoxLayout(self.loading_dialog)

        # إضافة نص
        label = QLabel("جاري إنشاء تقرير الغياب نصف الأسبوعي، يرجى الانتظار...")
        label.setFont(QFont("Calibri", 12))
        label.setAlignment(Qt.AlignCenter)
        layout.addWidget(label)

        # إضافة شريط التحميل
        self.progress_bar = QProgressBar()
        self.progress_bar.setRange(0, 0)  # شريط تحميل غير محدد
        self.progress_bar.setTextVisible(False)
        layout.addWidget(self.progress_bar)

        # عرض نافذة التحميل
        self.loading_dialog.show()

        # استخدام مؤقت لبدء عملية الطباعة بعد عرض نافذة التحميل
        QTimer.singleShot(100, self._print_first_half_report_process)

    def _print_first_half_report_process(self):
        """العملية الفعلية لطباعة تقرير النصف الأول من الأسبوع"""
        try:
            # الحصول على قيم الحصص من مربعات النصوص
            periods = [input_field.text() for input_field in self.period_inputs]

            # Get selected class and institution data
            selected_class = self.class_combo.currentText()
            institution_data = self.get_institution_data()

            # حساب تاريخ بداية الأسبوع (الاثنين) بناءً على اختيار الأسبوع
            selected_date = self.get_week_start_date()

            # التحقق مما إذا كان المستخدم قد اختار "جميع الأقسام المسندة"
            if selected_class == "جميع الأقسام المسندة":
                # طباعة جميع الأقسام في ملف واحد
                # جمع جميع الأقسام من مربع التحرير والسرد (تخطي العنصر الأول "جميع الأقسام المسندة")
                classes_to_print = [self.class_combo.itemText(i) for i in range(1, self.class_combo.count())]

                if not classes_to_print:
                    ConfirmationDialogs.show_custom_warning_message(self, "لا توجد أقسام مسندة للطباعة.", "تنبيه")
                    return

                # إنشاء اسم الملف لجميع الأقسام مع التاريخ والوقت
                current_datetime = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
                file_name = f"تقرير_الغياب_النصف_الأول_جميع_الأقسام_{current_datetime}.pdf"

                # إنشاء مجلد رئيسي على سطح المكتب
                desktop_path = os.path.join(os.path.expanduser("~"), "Desktop")
                main_reports_dir = os.path.join(desktop_path, "تقارير برنامج المعين في الحراسة العامة")
                if not os.path.exists(main_reports_dir):
                    os.makedirs(main_reports_dir)
                    print(f"تم إنشاء المجلد الرئيسي: {main_reports_dir}")

                # إنشاء مجلد فرعي لأوراق الغياب الأسبوعية والنصف الأسبوعية
                reports_dir = os.path.join(main_reports_dir, "تقارير لوائح الغياب الأسبوعية والنصف الأسبوعية")
                if not os.path.exists(reports_dir):
                    os.makedirs(reports_dir)
                    print(f"تم إنشاء المجلد الفرعي: {reports_dir}")

                # تحديد مسار الملف الكامل
                file_path = os.path.join(reports_dir, file_name)

                # إعداد بيانات كل قسم
                class_data_list = []
                for class_name in classes_to_print:
                    # الحصول على بيانات الطلاب
                    students = self.get_student_data(class_name)

                    # إضافة البيانات لقائمة الأقسام
                    class_data_list.append({
                        'class_name': class_name,
                        'students': students
                    })

                # إنشاء التقرير بكل الأقسام في ملف واحد
                success = self.split_report_generator.generate_first_half_multi_class(
                    file_path,
                    class_data_list,
                    institution_data,
                    selected_date,
                    1,  # نوع التقرير
                    periods  # إرسال قيم الحصص كمعامل إضافي
                )

                # إغلاق نافذة التحميل
                if hasattr(self, 'loading_dialog') and self.loading_dialog.isVisible():
                    self.loading_dialog.close()

                if success:
                    # فتح الملف بعد الإنشاء
                    self.open_pdf_file(file_path)
                else:
                    ConfirmationDialogs.show_custom_warning_message(self, "حدث خطأ أثناء إنشاء التقرير", "تنبيه")

                return

            # في حالة اختيار قسم محدد (وليس "جميع الأقسام المسندة")
            # إنشاء اسم الملف مع التاريخ والوقت
            current_datetime = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
            file_name = f"تقرير_الغياب_النصف_الأول_{selected_class}_{current_datetime}.pdf"

            # إنشاء مجلد رئيسي على سطح المكتب
            desktop_path = os.path.join(os.path.expanduser("~"), "Desktop")
            main_reports_dir = os.path.join(desktop_path, "تقارير برنامج المعين في الحراسة العامة")
            if not os.path.exists(main_reports_dir):
                os.makedirs(main_reports_dir)
                print(f"تم إنشاء المجلد الرئيسي: {main_reports_dir}")

            # إنشاء مجلد فرعي لأوراق الغياب الأسبوعية والنصف الأسبوعية
            reports_dir = os.path.join(main_reports_dir, "تقارير لوائح الغياب الأسبوعية والنصف الأسبوعية")
            if not os.path.exists(reports_dir):
                os.makedirs(reports_dir)
                print(f"تم إنشاء المجلد الفرعي: {reports_dir}")

            # تحديد مسار الملف الكامل
            file_path = os.path.join(reports_dir, file_name)

            # الحصول على بيانات الطلاب
            students = self.get_student_data(selected_class)

            # استخدام كائن معالج التقارير لإنشاء ملف PDF
            success = self.split_report_generator.generate_first_half(
                file_path,
                students,
                selected_class,
                institution_data,
                selected_date,
                1,  # نوع التقرير
                periods  # إرسال قيم الحصص كمعامل إضافي
            )

            # إغلاق نافذة التحميل
            if hasattr(self, 'loading_dialog') and self.loading_dialog.isVisible():
                self.loading_dialog.close()

            if success:
                # فتح الملف بعد الإنشاء
                self.open_pdf_file(file_path)
            else:
                ConfirmationDialogs.show_custom_warning_message(self, "حدث خطأ أثناء إنشاء التقرير", "تنبيه")

        except Exception as e:
            # إغلاق نافذة التحميل في حالة حدوث خطأ
            if hasattr(self, 'loading_dialog') and self.loading_dialog.isVisible():
                self.loading_dialog.close()
            ConfirmationDialogs.show_custom_error_message(self, f"حدث خطأ أثناء إنشاء تقرير النصف الأول: {str(e)}", "خطأ")

    def print_second_half_report(self):
        """طباعة تقرير النصف الثاني من الأسبوع (الأربعاء والخميس والجمعة)"""
        # إنشاء نافذة حوار للتحميل
        self.loading_dialog = QDialog(self)
        self.loading_dialog.setWindowTitle("جاري إنشاء التقرير...")
        self.loading_dialog.setFixedSize(400, 100)
        self.loading_dialog.setWindowFlags(Qt.Dialog | Qt.CustomizeWindowHint | Qt.WindowTitleHint)

        # إنشاء تخطيط للنافذة
        layout = QVBoxLayout(self.loading_dialog)

        # إضافة نص
        label = QLabel("جاري إنشاء تقرير الغياب نصف الأسبوعي، يرجى الانتظار...")
        label.setFont(QFont("Calibri", 12))
        label.setAlignment(Qt.AlignCenter)
        layout.addWidget(label)

        # إضافة شريط التحميل
        self.progress_bar = QProgressBar()
        self.progress_bar.setRange(0, 0)  # شريط تحميل غير محدد
        self.progress_bar.setTextVisible(False)
        layout.addWidget(self.progress_bar)

        # عرض نافذة التحميل
        self.loading_dialog.show()

        # استخدام مؤقت لبدء عملية الطباعة بعد عرض نافذة التحميل
        QTimer.singleShot(100, self._print_second_half_report_process)

    def _print_second_half_report_process(self):
        """العملية الفعلية لطباعة تقرير النصف الثاني من الأسبوع"""
        try:
            # الحصول على قيم الحصص من مربعات النصوص
            periods = [input_field.text() for input_field in self.period_inputs]

            # Get selected class and institution data
            selected_class = self.class_combo.currentText()
            institution_data = self.get_institution_data()

            # حساب تاريخ بداية الأسبوع (الاثنين) بناءً على اختيار الأسبوع
            selected_date = self.get_week_start_date()

            # التحقق مما إذا كان المستخدم قد اختار "جميع الأقسام المسندة"
            if selected_class == "جميع الأقسام المسندة":
                # طباعة جميع الأقسام في ملف واحد
                # جمع جميع الأقسام من مربع التحرير والسرد (تخطي العنصر الأول "جميع الأقسام المسندة")
                classes_to_print = [self.class_combo.itemText(i) for i in range(1, self.class_combo.count())]

                if not classes_to_print:
                    ConfirmationDialogs.show_custom_warning_message(self, "لا توجد أقسام مسندة للطباعة.", "تنبيه")
                    return

                # إنشاء اسم الملف لجميع الأقسام مع التاريخ والوقت
                current_datetime = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
                file_name = f"تقرير_الغياب_النصف_الثاني_جميع_الأقسام_{current_datetime}.pdf"

                # إنشاء مجلد رئيسي على سطح المكتب
                desktop_path = os.path.join(os.path.expanduser("~"), "Desktop")
                main_reports_dir = os.path.join(desktop_path, "تقارير برنامج المعين في الحراسة العامة")
                if not os.path.exists(main_reports_dir):
                    os.makedirs(main_reports_dir)
                    print(f"تم إنشاء المجلد الرئيسي: {main_reports_dir}")

                # إنشاء مجلد فرعي لأوراق الغياب الأسبوعية والنصف الأسبوعية
                reports_dir = os.path.join(main_reports_dir, "تقارير لوائح الغياب الأسبوعية والنصف الأسبوعية")
                if not os.path.exists(reports_dir):
                    os.makedirs(reports_dir)
                    print(f"تم إنشاء المجلد الفرعي: {reports_dir}")

                # تحديد مسار الملف الكامل
                file_path = os.path.join(reports_dir, file_name)

                # إعداد بيانات كل قسم
                class_data_list = []
                for class_name in classes_to_print:
                    # الحصول على بيانات الطلاب
                    students = self.get_student_data(class_name)

                    # إضافة البيانات لقائمة الأقسام
                    class_data_list.append({
                        'class_name': class_name,
                        'students': students
                    })

                # إنشاء التقرير بكل الأقسام في ملف واحد
                success = self.split_report_generator.generate_second_half_multi_class(
                    file_path,
                    class_data_list,
                    institution_data,
                    selected_date,
                    1,  # نوع التقرير
                    periods  # إرسال قيم الحصص كمعامل إضافي
                )

                # إغلاق نافذة التحميل
                if hasattr(self, 'loading_dialog') and self.loading_dialog.isVisible():
                    self.loading_dialog.close()

                if success:
                    # فتح الملف بعد الإنشاء
                    self.open_pdf_file(file_path)
                else:
                    ConfirmationDialogs.show_custom_warning_message(self, "حدث خطأ أثناء إنشاء التقرير", "تنبيه")

                return

            # في حالة اختيار قسم محدد (وليس "جميع الأقسام المسندة")
            # إنشاء اسم الملف مع التاريخ والوقت
            current_datetime = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
            file_name = f"تقرير_الغياب_النصف_الثاني_{selected_class}_{current_datetime}.pdf"

            # إنشاء مجلد رئيسي على سطح المكتب
            desktop_path = os.path.join(os.path.expanduser("~"), "Desktop")
            main_reports_dir = os.path.join(desktop_path, "تقارير برنامج المعين في الحراسة العامة")
            if not os.path.exists(main_reports_dir):
                os.makedirs(main_reports_dir)
                print(f"تم إنشاء المجلد الرئيسي: {main_reports_dir}")

            # إنشاء مجلد فرعي لأوراق الغياب الأسبوعية والنصف الأسبوعية
            reports_dir = os.path.join(main_reports_dir, "تقارير لوائح الغياب الأسبوعية والنصف الأسبوعية")
            if not os.path.exists(reports_dir):
                os.makedirs(reports_dir)
                print(f"تم إنشاء المجلد الفرعي: {reports_dir}")

            # تحديد مسار الملف الكامل
            file_path = os.path.join(reports_dir, file_name)

            # الحصول على بيانات الطلاب
            students = self.get_student_data(selected_class)

            # استخدام كائن معالج التقارير لإنشاء ملف PDF
            success = self.split_report_generator.generate_second_half(
                file_path,
                students,
                selected_class,
                institution_data,
                selected_date,
                1,  # نوع التقرير
                periods  # إرسال قيم الحصص كمعامل إضافي
            )

            # إغلاق نافذة التحميل
            if hasattr(self, 'loading_dialog') and self.loading_dialog.isVisible():
                self.loading_dialog.close()

            if success:
                # فتح الملف بعد الإنشاء
                self.open_pdf_file(file_path)
            else:
                ConfirmationDialogs.show_custom_warning_message(self, "حدث خطأ أثناء إنشاء التقرير", "تنبيه")

        except Exception as e:
            # إغلاق نافذة التحميل في حالة حدوث خطأ
            if hasattr(self, 'loading_dialog') and self.loading_dialog.isVisible():
                self.loading_dialog.close()
            ConfirmationDialogs.show_custom_error_message(self, f"حدث خطأ أثناء إنشاء تقرير النصف الثاني: {str(e)}", "خطأ")

    def get_week_start_date(self):
        """حساب تاريخ بداية الأسبوع (الاثنين) بناءً على اختيار الأسبوع"""
        today = datetime.date.today()

        # حساب عدد الأيام حتى الاثنين القادم (0 = الاثنين، 1 = الثلاثاء، ..., 6 = الأحد)
        days_until_monday = (0 - today.weekday()) % 7
        if days_until_monday == 0:  # إذا كان اليوم هو الاثنين
            next_monday = today
        else:
            next_monday = today + datetime.timedelta(days=days_until_monday)

        # تحديد تاريخ البداية بناءً على اختيار الأسبوع
        selected_week = self.week_combo.currentText()
        if selected_week == "الأسبوع الحالي":
            # إذا كان اليوم هو الاثنين أو بعده، استخدم الاثنين الماضي
            if today.weekday() >= 0:  # 0 = الاثنين
                days_since_monday = today.weekday()
                start_date = today - datetime.timedelta(days=days_since_monday)
            else:
                # هذا لن يحدث أبداً لأن weekday() يعطي دائماً قيمة بين 0 و 6
                start_date = today
        elif selected_week == "الأسبوع القادم":
            start_date = next_monday
        elif selected_week == "الأسبوع الثالث":
            start_date = next_monday + datetime.timedelta(days=7)
        else:
            # استخدم الأسبوع الحالي كقيمة افتراضية
            days_since_monday = today.weekday()
            start_date = today - datetime.timedelta(days=days_since_monday)

        return start_date

    def get_institution_data(self):
        """الحصول على بيانات المؤسسة من قاعدة البيانات"""
        try:
            # التحقق من وجود اتصال بقاعدة البيانات
            if self.db is None:
                print("خطأ: لا يوجد اتصال بقاعدة البيانات في get_institution_data")
                return {
                    "institution": "المؤسسة التعليمية",
                    "academy": "",
                    "directorate": "",
                    "academic_year": "",
                    "ImagePath1": ""
                }

            # التعامل مع أنواع مختلفة من اتصالات قاعدة البيانات
            if self.db_type == 'sqlite3':
                # استخدام واجهة sqlite3
                return self._get_institution_data_sqlite3()
            elif self.db_type == 'QSqlDatabase':
                # استخدام واجهة QSqlDatabase
                return self._get_institution_data_qsql()
            else:
                print(f"نوع اتصال قاعدة البيانات غير معروف: {self.db_type}")
                # إرجاع بيانات افتراضية في حالة عدم معرفة نوع الاتصال
                return {
                    "institution": "المؤسسة التعليمية",
                    "academy": "",
                    "directorate": "",
                    "academic_year": "",
                    "ImagePath1": ""
                }

        except Exception as e:
            print(f"خطأ في الحصول على بيانات المؤسسة: {str(e)}")
            return {
                "institution": "المؤسسة التعليمية",
                "academy": "",
                "directorate": "",
                "academic_year": "",
                "ImagePath1": ""
            }

    def _get_institution_data_sqlite3(self):
        """الحصول على بيانات المؤسسة باستخدام واجهة sqlite3"""
        try:
            # استخدام اتصال قاعدة البيانات المخزن
            cursor = self.db.cursor()

            # استعلام للحصول على بيانات المؤسسة
            # التحقق من أسماء الأعمدة في جدول بيانات_المؤسسة
            cursor.execute("PRAGMA table_info(بيانات_المؤسسة)")
            columns = cursor.fetchall()
            print(f"أعمدة جدول بيانات_المؤسسة: {columns}")

            # استخدام استعلام أكثر أماناً
            try:
                cursor.execute("SELECT * FROM بيانات_المؤسسة LIMIT 1")
                result = cursor.fetchone()
                column_names = [description[0] for description in cursor.description]
                print(f"أسماء الأعمدة في جدول بيانات_المؤسسة: {column_names}")

                # البحث عن الأعمدة المطلوبة
                institution_name = ""
                academy = ""
                direction = ""
                academic_year = ""
                image_path1 = ""

                # البحث عن عمود المؤسسة
                institution_columns = [col for col in column_names if 'المؤسسة' == col]
                if institution_columns:
                    institution_index = column_names.index(institution_columns[0])
                    institution_name = result[institution_index] if result else ""
                else:
                    # البحث عن عمود اسم المؤسسة كبديل
                    name_columns = [col for col in column_names if 'اسم' in col.lower() and 'مؤسسة' in col.lower()]
                    if name_columns:
                        name_index = column_names.index(name_columns[0])
                        institution_name = result[name_index] if result else ""

                # البحث عن عمود الأكاديمية
                academy_columns = [col for col in column_names if 'أكاديمية' in col.lower() or 'اكاديمية' in col.lower()]
                if academy_columns:
                    academy_index = column_names.index(academy_columns[0])
                    academy = result[academy_index] if result else ""

                # البحث عن عمود المديرية
                direction_columns = [col for col in column_names if 'مديرية' in col.lower() or 'اقليم' in col.lower()]
                if direction_columns:
                    direction_index = column_names.index(direction_columns[0])
                    direction = result[direction_index] if result else ""

                # البحث عن عمود السنة الدراسية
                year_columns = [col for col in column_names if 'سنة' in col.lower() and 'دراسية' in col.lower()]
                if year_columns:
                    year_index = column_names.index(year_columns[0])
                    academic_year = result[year_index] if result else ""

                # البحث عن عمود مسار الشعار
                if 'ImagePath1' in column_names:
                    image_index = column_names.index('ImagePath1')
                    image_path1 = result[image_index] if result else ""

                # إنشاء نتيجة مخصصة
                result = (institution_name, academy, direction, academic_year, image_path1)
                print(f"تم استخراج بيانات المؤسسة: {result}")
            except Exception as e:
                print(f"خطأ في استعلام بيانات المؤسسة: {e}")
                result = None

            if result:
                return {
                    "institution": result[0] if result[0] else "المؤسسة التعليمية",
                    "academy": result[1] if result[1] else "",
                    "directorate": result[2] if result[2] else "",
                    "academic_year": result[3] if result[3] else "",
                    "ImagePath1": result[4] if len(result) > 4 and result[4] else ""
                }
            else:
                return {
                    "institution": "المؤسسة التعليمية",
                    "academy": "",
                    "directorate": "",
                    "academic_year": "",
                    "ImagePath1": ""
                }
        except Exception as e:
            print(f"خطأ في الحصول على بيانات المؤسسة باستخدام sqlite3: {str(e)}")
            return {
                "institution": "المؤسسة التعليمية",
                "academy": "",
                "directorate": "",
                "academic_year": "",
                "ImagePath1": ""
            }

    def _get_institution_data_qsql(self):
        """الحصول على بيانات المؤسسة باستخدام واجهة QSqlDatabase"""
        try:
            # إنشاء استعلام SQL
            query = QSqlQuery(self.db)

            # استعلام للحصول على بيانات المؤسسة
            sql_query = "SELECT * FROM بيانات_المؤسسة LIMIT 1"
            print(f"تنفيذ استعلام QSqlQuery: {sql_query}")

            if not query.exec_(sql_query):
                print(f"خطأ في تنفيذ الاستعلام: {query.lastError().text()}")
                return {
                    "institution": "المؤسسة التعليمية",
                    "academy": "",
                    "directorate": "",
                    "academic_year": "",
                    "ImagePath1": ""
                }

            # الحصول على أسماء الأعمدة
            record = query.record()
            column_count = record.count()
            column_names = [record.fieldName(i) for i in range(column_count)]
            print(f"أسماء الأعمدة في جدول بيانات_المؤسسة: {column_names}")

            # التحقق من وجود نتائج
            if not query.next():
                print("لم يتم العثور على بيانات المؤسسة")
                return {
                    "institution": "المؤسسة التعليمية",
                    "academy": "",
                    "directorate": "",
                    "academic_year": "",
                    "ImagePath1": ""
                }

            # البحث عن الأعمدة المطلوبة
            institution_name = ""
            academy = ""
            direction = ""
            academic_year = ""
            image_path1 = ""

            # البحث عن عمود المؤسسة
            institution_columns = [col for col in column_names if 'المؤسسة' == col]
            if institution_columns:
                institution_index = column_names.index(institution_columns[0])
                institution_name = query.value(institution_index) if query.value(institution_index) else ""
            else:
                # البحث عن عمود اسم المؤسسة كبديل
                name_columns = [col for col in column_names if 'اسم' in col.lower() and 'مؤسسة' in col.lower()]
                if name_columns:
                    name_index = column_names.index(name_columns[0])
                    institution_name = query.value(name_index) if query.value(name_index) else ""

            # البحث عن عمود الأكاديمية
            academy_columns = [col for col in column_names if 'أكاديمية' in col.lower() or 'اكاديمية' in col.lower()]
            if academy_columns:
                academy_index = column_names.index(academy_columns[0])
                academy = query.value(academy_index) if query.value(academy_index) else ""

            # البحث عن عمود المديرية
            direction_columns = [col for col in column_names if 'مديرية' in col.lower() or 'اقليم' in col.lower()]
            if direction_columns:
                direction_index = column_names.index(direction_columns[0])
                direction = query.value(direction_index) if query.value(direction_index) else ""

            # البحث عن عمود السنة الدراسية
            year_columns = [col for col in column_names if 'سنة' in col.lower() and 'دراسية' in col.lower()]
            if year_columns:
                year_index = column_names.index(year_columns[0])
                academic_year = query.value(year_index) if query.value(year_index) else ""

            # البحث عن عمود مسار الشعار
            if 'ImagePath1' in column_names:
                image_index = column_names.index('ImagePath1')
                image_path1 = query.value(image_index) if query.value(image_index) else ""

            # إنشاء نتيجة مخصصة
            result = (institution_name, academy, direction, academic_year, image_path1)
            print(f"تم استخراج بيانات المؤسسة: {result}")

            return {
                "institution": result[0] if result[0] else "المؤسسة التعليمية",
                "academy": result[1] if result[1] else "",
                "directorate": result[2] if result[2] else "",
                "academic_year": result[3] if result[3] else "",
                "ImagePath1": result[4] if len(result) > 4 and result[4] else ""
            }
        except Exception as e:
            print(f"خطأ في الحصول على بيانات المؤسسة باستخدام QSqlDatabase: {str(e)}")
            return {
                "institution": "المؤسسة التعليمية",
                "academy": "",
                "directorate": "",
                "academic_year": "",
                "ImagePath1": ""
            }

    def on_supervision_changed(self, index):
        """معالجة تغيير قيمة مربع الحراسة"""
        print(f"تم تغيير رقم الحراسة إلى: {index}")

        # إذا كان المؤشر -1 (لا يوجد اختيار)، لا تقم بتحميل الأقسام
        if index == -1:
            print("لم يتم تحديد رقم حراسة. تخطي تحميل الأقسام المسندة.")
            return

        # حساب رقم الحراسة الفعلي (1-5)
        supervision_number = index + 1
        print(f"رقم الحراسة الفعلي: {supervision_number}")

        # التحقق من وجود class_combo قبل محاولة تحميل الأقسام المسندة
        if hasattr(self, 'class_combo'):
            print(f"class_combo موجود. عدد العناصر الحالية: {self.class_combo.count()}")

            # طباعة العناصر الحالية في مربع التحرير والسرد
            for i in range(self.class_combo.count()):
                print(f"العنصر الحالي {i}: {self.class_combo.itemText(i)}")

            # مسح القائمة الحالية
            self.class_combo.blockSignals(True)  # منع إرسال إشارات أثناء التحديث
            self.class_combo.clear()
            self.class_combo.blockSignals(False)  # إعادة تفعيل الإشارات

            # استدعاء دالة تحميل الأقسام المسندة
            print("جاري استدعاء دالة load_assigned_classes...")

            # إذا كانت النافذة مدمجة، نستخدم طريقة مختلفة لتحميل الأقسام
            if hasattr(self, 'is_embedded') and self.is_embedded:
                print("النافذة مدمجة، استخدام طريقة خاصة لتحميل الأقسام المسندة...")
                # استخدام QTimer لتأخير تحميل الأقسام قليلاً
                QTimer.singleShot(50, self.load_assigned_classes)
            else:
                # تحميل الأقسام مباشرة إذا كانت النافذة مستقلة
                self.load_assigned_classes()

            # التحقق من نتيجة تحميل الأقسام المسندة
            print(f"تم الانتهاء من تحميل الأقسام المسندة. عدد العناصر الآن: {self.class_combo.count()}")

            # طباعة العناصر بعد التحميل
            for i in range(self.class_combo.count()):
                print(f"العنصر بعد التحميل {i}: {self.class_combo.itemText(i)}")

            # تحديث واجهة المستخدم
            QApplication.processEvents()
        else:
            print("تحذير: لم يتم العثور على class_combo. تخطي تحميل الأقسام المسندة.")

    def load_assigned_classes(self):
        """تحميل الأقسام المسندة من قاعدة البيانات حسب رقم الحراسة"""
        try:
            # التحقق من وجود supervision_combo و class_combo
            if not hasattr(self, 'supervision_combo') or not hasattr(self, 'class_combo'):
                print("تحذير: لم يتم العثور على supervision_combo أو class_combo. تخطي تحميل الأقسام المسندة.")
                return

            # الحصول على رقم الحراسة المحدد (1-5)
            supervision_index = self.supervision_combo.currentIndex()

            # التحقق من أن المؤشر صالح
            if supervision_index == -1:
                print("تحذير: لم يتم تحديد رقم حراسة. تخطي تحميل الأقسام المسندة.")
                return

            supervision_number = supervision_index + 1  # تحويل المؤشر إلى رقم الحراسة (1-5)

            print(f"جاري تحميل الأقسام المسندة للحراسة رقم {supervision_number}...")

            # مسح القائمة الحالية
            self.class_combo.blockSignals(True)  # منع إرسال إشارات أثناء التحديث
            self.class_combo.clear()

            # إضافة خيار "جميع الأقسام المسندة" كأول عنصر
            self.class_combo.addItem("جميع الأقسام المسندة")

            # التحقق من وجود اتصال بقاعدة البيانات
            if self.db is None:
                print("خطأ: لا يوجد اتصال بقاعدة البيانات")
                # محاولة إنشاء اتصال جديد
                try:
                    self.db = sqlite3.connect("data.db")
                    self.db_type = 'sqlite3'
                    print("تم إنشاء اتصال جديد بقاعدة البيانات من نوع sqlite3")
                except Exception as e:
                    print(f"فشل إنشاء اتصال جديد بقاعدة البيانات: {e}")
                    self.class_combo.blockSignals(False)  # إعادة تفعيل الإشارات
                    return

            # استخدام اتصال قاعدة البيانات المخزن
            try:
                # التعامل مع أنواع مختلفة من اتصالات قاعدة البيانات
                if self.db_type == 'sqlite3':
                    # استخدام واجهة sqlite3
                    self._load_assigned_classes_sqlite3(supervision_number)
                elif self.db_type == 'QSqlDatabase':
                    # استخدام واجهة QSqlDatabase
                    self._load_assigned_classes_qsql(supervision_number)
                else:
                    print(f"نوع اتصال قاعدة البيانات غير معروف: {self.db_type}")
                    # إضافة أقسام افتراضية في حالة عدم معرفة نوع الاتصال
                    self._add_default_classes()
            except Exception as e:
                print(f"خطأ في تنفيذ استعلام قاعدة البيانات: {e}")
                self.class_combo.blockSignals(False)  # إعادة تفعيل الإشارات
                # إضافة أقسام افتراضية في حالة حدوث خطأ
                self._add_default_classes()
                return
        except Exception as e:
            print(f"خطأ عام في تحميل الأقسام المسندة: {e}")
            # إضافة أقسام افتراضية في حالة حدوث خطأ
            self._add_default_classes()

    def _add_default_classes(self):
        """إضافة أقسام افتراضية في حالة حدوث خطأ"""
        try:
            # التأكد من وجود "جميع الأقسام المسندة" كأول عنصر
            if self.class_combo.count() == 0:
                self.class_combo.addItem("جميع الأقسام المسندة")

            # إضافة أقسام افتراضية
            default_classes = ["1/1", "1/2", "1/3", "2/1", "2/2"]
            for default_class in default_classes:
                self.class_combo.addItem(default_class)

            # إعادة تفعيل الإشارات
            self.class_combo.blockSignals(False)

            # تحديد العنصر الأول
            if self.class_combo.count() > 0:
                self.class_combo.setCurrentIndex(0)
        except Exception as e:
            print(f"خطأ في إضافة الأقسام الافتراضية: {e}")

    def _load_assigned_classes_sqlite3(self, supervision_number):
        """تحميل الأقسام المسندة باستخدام واجهة sqlite3"""
        try:
            cursor = self.db.cursor()

            # التحقق من وجود جدول البنية_التربوية
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='البنية_التربوية'")
            if not cursor.fetchone():
                print("خطأ: جدول 'البنية_التربوية' غير موجود في قاعدة البيانات")
                # طباعة قائمة الجداول الموجودة للتشخيص
                cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
                tables = cursor.fetchall()
                print(f"الجداول الموجودة في قاعدة البيانات: {tables}")
                self.class_combo.blockSignals(False)  # إعادة تفعيل الإشارات
                return

            # التحقق من وجود عمود السنة_الدراسية في جدول البنية_التربوية
            cursor.execute("PRAGMA table_info(البنية_التربوية)")
            structure_columns = cursor.fetchall()
            print(f"أعمدة جدول البنية_التربوية: {structure_columns}")

            has_year_column = any(col[1] == 'السنة_الدراسية' for col in structure_columns)
            has_assigned_column = any(col[1] == 'الأقسام_المسندة' for col in structure_columns)

            # استعلام للحصول على الأقسام المسندة حسب رقم الحراسة
            if has_year_column and has_assigned_column:
                # استعلام كامل مع شرط السنة الدراسية
                query = """
                SELECT DISTINCT القسم FROM البنية_التربوية
                WHERE السنة_الدراسية = (SELECT السنة_الدراسية FROM بيانات_المؤسسة LIMIT 1)
                AND الأقسام_المسندة LIKE ?
                ORDER BY القسم
                """
            elif has_assigned_column:
                # استعلام بدون شرط السنة الدراسية
                query = """
                SELECT DISTINCT القسم FROM البنية_التربوية
                WHERE الأقسام_المسندة LIKE ?
                ORDER BY القسم
                """
            else:
                # استعلام بسيط للحصول على جميع الأقسام
                query = """
                SELECT DISTINCT القسم FROM البنية_التربوية
                ORDER BY القسم
                """

            search_param = f"%{supervision_number}%"
            print(f"تنفيذ استعلام: {query} مع القيمة {search_param}")

            if "LIKE ?" in query:
                cursor.execute(query, (search_param,))
            else:
                cursor.execute(query)

            classes = cursor.fetchall()

            # طباعة نتائج الاستعلام للتشخيص
            print(f"نتائج الاستعلام: {classes}")

            # إذا لم يتم العثور على أي أقسام، جرب استعلامًا بديلاً
            if not classes:
                print("لم يتم العثور على أقسام. جاري تجربة استعلام بديل...")

                # استعلام بديل - الحصول على جميع الأقسام من البنية التربوية
                try:
                    # استعلام بسيط للحصول على جميع الأقسام
                    alt_query = "SELECT DISTINCT القسم FROM البنية_التربوية ORDER BY القسم"
                    print(f"تنفيذ استعلام بديل: {alt_query}")
                    cursor.execute(alt_query)
                    all_classes = cursor.fetchall()
                    print(f"جميع الأقسام في البنية التربوية: {all_classes}")

                    # محاولة التحقق من قيم الأقسام المسندة
                    try:
                        # التحقق من وجود عمود الأقسام_المسندة
                        if has_assigned_column:
                            cursor.execute("SELECT القسم, الأقسام_المسندة FROM البنية_التربوية LIMIT 10")
                            sample_assignments = cursor.fetchall()
                            print(f"عينة من الأقسام وقيم الأقسام المسندة: {sample_assignments}")
                    except Exception as e:
                        print(f"خطأ في استعلام عينة الأقسام المسندة: {e}")
                except Exception as e:
                    print(f"خطأ في الاستعلام البديل: {e}")
                    all_classes = []

                # استخدام جميع الأقسام إذا كانت متوفرة
                if all_classes:
                    classes = all_classes
                    print("تم استخدام جميع الأقسام من البنية التربوية للسنة الدراسية الحالية")

            # إضافة الأقسام إلى القائمة المنسدلة
            self._add_classes_to_combo(classes)

        except Exception as e:
            print(f"خطأ في تنفيذ استعلام قاعدة البيانات sqlite3: {e}")
            raise e

    def _load_assigned_classes_qsql(self, supervision_number):
        """تحميل الأقسام المسندة باستخدام واجهة QSqlDatabase"""
        try:
            # إنشاء استعلام SQL
            query = QSqlQuery(self.db)

            # التحقق من وجود جدول البنية_التربوية
            query.exec_("SELECT name FROM sqlite_master WHERE type='table' AND name='البنية_التربوية'")
            if not query.next():
                print("خطأ: جدول 'البنية_التربوية' غير موجود في قاعدة البيانات")
                # طباعة قائمة الجداول الموجودة للتشخيص
                query.exec_("SELECT name FROM sqlite_master WHERE type='table'")
                tables = []
                while query.next():
                    tables.append(query.value(0))
                print(f"الجداول الموجودة في قاعدة البيانات: {tables}")
                self.class_combo.blockSignals(False)  # إعادة تفعيل الإشارات
                return

            # استعلام للحصول على الأقسام المسندة حسب رقم الحراسة
            sql_query = f"""
            SELECT DISTINCT القسم FROM البنية_التربوية
            WHERE السنة_الدراسية = (SELECT السنة_الدراسية FROM بيانات_المؤسسة LIMIT 1)
            AND الأقسام_المسندة LIKE '%{supervision_number}%'
            ORDER BY القسم
            """

            print(f"تنفيذ استعلام QSqlQuery: {sql_query}")

            if not query.exec_(sql_query):
                print(f"خطأ في تنفيذ الاستعلام: {query.lastError().text()}")
                # جرب استعلامًا بديلًا بدون شرط السنة الدراسية
                alt_query = f"""
                SELECT DISTINCT القسم FROM البنية_التربوية
                WHERE الأقسام_المسندة LIKE '%{supervision_number}%'
                ORDER BY القسم
                """
                print(f"تنفيذ استعلام بديل: {alt_query}")

                if not query.exec_(alt_query):
                    print(f"خطأ في تنفيذ الاستعلام البديل: {query.lastError().text()}")
                    # جرب استعلامًا أبسط
                    simple_query = "SELECT DISTINCT القسم FROM البنية_التربوية ORDER BY القسم"
                    print(f"تنفيذ استعلام أبسط: {simple_query}")

                    if not query.exec_(simple_query):
                        print(f"خطأ في تنفيذ الاستعلام الأبسط: {query.lastError().text()}")
                        # إضافة أقسام افتراضية في حالة فشل جميع الاستعلامات
                        self._add_default_classes()
                        return

            # جمع نتائج الاستعلام
            classes = []
            while query.next():
                class_name = query.value(0)
                if class_name:
                    classes.append([class_name])

            # طباعة نتائج الاستعلام للتشخيص
            print(f"نتائج استعلام QSqlQuery: {classes}")

            # إضافة الأقسام إلى القائمة المنسدلة
            self._add_classes_to_combo(classes)

        except Exception as e:
            print(f"خطأ في تنفيذ استعلام قاعدة البيانات QSqlDatabase: {e}")
            raise e

    def _add_classes_to_combo(self, classes):
        """إضافة الأقسام إلى القائمة المنسدلة"""
        try:
            # الحصول على رقم الحراسة المحدد (1-5)
            supervision_index = self.supervision_combo.currentIndex()
            supervision_number = supervision_index + 1  # تحويل المؤشر إلى رقم الحراسة (1-5)

            # إضافة الأقسام إلى القائمة المنسدلة
            added_count = 0
            for class_tuple in classes:
                class_name = class_tuple[0]
                if class_name:  # التأكد من أن اسم القسم ليس فارغاً
                    self.class_combo.addItem(class_name)
                    added_count += 1
                    print(f"تمت إضافة القسم: {class_name}")

            # طباعة عدد الأقسام التي تم تحميلها
            print(f"تم تحميل {len(classes)} قسم مسند للحراسة رقم {supervision_number}")
            print(f"تمت إضافة {added_count} قسم إلى مربع التحرير والسرد")

            # التحقق من عدد العناصر في مربع التحرير والسرد
            total_items = self.class_combo.count()
            print(f"إجمالي العناصر في مربع التحرير والسرد: {total_items}")

            # طباعة جميع العناصر في مربع التحرير والسرد للتشخيص
            for i in range(total_items):
                print(f"العنصر {i}: {self.class_combo.itemText(i)}")

            # تحديد العنصر الأول افتراضياً
            if self.class_combo.count() > 0:
                self.class_combo.setCurrentIndex(0)
                print(f"تم تحديد العنصر الأول: {self.class_combo.currentText()}")

            # إضافة أقسام افتراضية للاختبار إذا لم يتم العثور على أي أقسام
            if added_count == 0:
                print("لم يتم العثور على أي أقسام. إضافة أقسام افتراضية للاختبار...")
                self._add_default_classes()

            self.class_combo.blockSignals(False)  # إعادة تفعيل الإشارات

            # تحديث واجهة المستخدم
            QApplication.processEvents()

        except Exception as e:
            print(f"خطأ في إضافة الأقسام إلى القائمة المنسدلة: {e}")
            # عرض رسالة خطأ للمستخدم
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء تحميل الأقسام المسندة:\n{str(e)}")

            # إضافة أقسام افتراضية في حالة حدوث خطأ
            print("إضافة أقسام افتراضية بسبب حدوث خطأ...")
            self.class_combo.clear()
            self.class_combo.addItem("جميع الأقسام المسندة")
            default_classes = ["1/1", "1/2", "1/3", "2/1", "2/2"]
            for default_class in default_classes:
                self.class_combo.addItem(default_class)

            self.class_combo.blockSignals(False)  # إعادة تفعيل الإشارات

    def get_student_data(self, class_name):
        """الحصول على بيانات الطلاب للقسم المحدد"""
        try:
            # التحقق من وجود اتصال بقاعدة البيانات
            if self.db is None:
                print("خطأ: لا يوجد اتصال بقاعدة البيانات في get_student_data")
                return []

            # التعامل مع أنواع مختلفة من اتصالات قاعدة البيانات
            if self.db_type == 'sqlite3':
                # استخدام واجهة sqlite3
                return self._get_student_data_sqlite3(class_name)
            elif self.db_type == 'QSqlDatabase':
                # استخدام واجهة QSqlDatabase
                return self._get_student_data_qsql(class_name)
            else:
                print(f"نوع اتصال قاعدة البيانات غير معروف: {self.db_type}")
                # إرجاع بيانات افتراضية في حالة عدم معرفة نوع الاتصال
                return self._get_default_students(class_name)

        except Exception as e:
            print(f"خطأ في الحصول على بيانات الطلاب: {str(e)}")
            # إرجاع قائمة فارغة في حالة حدوث خطأ
            return self._get_default_students(class_name)

    def _get_default_students(self, class_name):
        """إنشاء بيانات افتراضية للطلاب"""
        print(f"إنشاء بيانات افتراضية للطلاب في القسم {class_name}...")
        students = []
        for i in range(1, 21):  # 20 طالب افتراضي
            students.append({
                "id": i,
                "name": f"طالب {i} - {class_name}",
                "code": f"C{i}"
            })
        return students

    def _get_student_data_sqlite3(self, class_name):
        """الحصول على بيانات الطلاب باستخدام واجهة sqlite3"""
        try:
            # استخدام اتصال قاعدة البيانات المخزن
            cursor = self.db.cursor()

            # التحقق من الجداول الموجودة في قاعدة البيانات
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
            tables = [table[0] for table in cursor.fetchall()]
            print(f"الجداول الموجودة في قاعدة البيانات: {tables}")

            # تحديد الجدول المناسب للتلاميذ
            students_table = None
            if 'التلاميذ' in tables:
                students_table = 'التلاميذ'
                print("تم العثور على جدول التلاميذ")
            elif 'جدول_عام' in tables:
                students_table = 'جدول_عام'
                print("تم العثور على جدول_عام")
            elif 'اللوائح' in tables:
                students_table = 'اللوائح'
                print("تم العثور على جدول اللوائح")
            else:
                print("لم يتم العثور على جدول للتلاميذ")
                return []

            # التحقق من أعمدة الجدول المحدد
            cursor.execute(f"PRAGMA table_info({students_table})")
            columns = [col[1] for col in cursor.fetchall()]
            print(f"أعمدة جدول {students_table}: {columns}")

            # تحديد أسماء الأعمدة المناسبة
            id_column = None
            name_column = None
            code_column = None
            class_column = None
            year_column = None

            # البحث عن عمود معرف التلميذ
            id_candidates = ['رقم_التلميذ', 'الرمز', 'رقم_مسار', 'رت']
            for candidate in id_candidates:
                if candidate in columns:
                    id_column = candidate
                    break

            # البحث عن عمود اسم التلميذ
            name_candidates = ['الاسم_الكامل', 'الاسم_والنسب', 'الاسم']
            for candidate in name_candidates:
                if candidate in columns:
                    name_column = candidate
                    break

            # البحث عن عمود رمز التلميذ
            code_candidates = ['رمز_مسار', 'الرمز', 'رقم_مسار']
            for candidate in code_candidates:
                if candidate in columns:
                    code_column = candidate
                    break

            # البحث عن عمود القسم
            class_candidates = ['القسم', 'الفوج', 'الفصل']
            for candidate in class_candidates:
                if candidate in columns:
                    class_column = candidate
                    break

            # البحث عن عمود السنة الدراسية
            year_candidates = ['السنة_الدراسية', 'الموسم_الدراسي']
            for candidate in year_candidates:
                if candidate in columns:
                    year_column = candidate
                    break

            # التحقق من وجود الأعمدة الضرورية
            if not id_column or not name_column or not class_column:
                print(f"لم يتم العثور على الأعمدة الضرورية في جدول {students_table}")
                return []

            # بناء استعلام ديناميكي
            query = f"""
            SELECT {id_column}, {name_column}"""

            # إضافة عمود الرمز إذا وجد
            if code_column:
                query += f", {code_column}"

            query += f" FROM {students_table} WHERE {class_column} = ?"

            # إضافة شرط السنة الدراسية إذا وجد العمود
            if year_column:
                query += f" AND {year_column} = (SELECT السنة_الدراسية FROM بيانات_المؤسسة LIMIT 1)"

            query += f" ORDER BY {id_column}"

            # طباعة معلومات تشخيصية
            print(f"تنفيذ استعلام الطلاب: {query} للقسم {class_name}")

            cursor.execute(query, (class_name,))
            students_data = cursor.fetchall()

            # طباعة عدد الطلاب الذين تم العثور عليهم
            print(f"تم العثور على {len(students_data)} طالب في القسم {class_name}")

            # تحويل البيانات إلى قائمة من القواميس
            students = []
            for i, student in enumerate(students_data):
                student_id = i + 1  # ترقيم تسلسلي يبدأ من 1
                student_name = student[1] if len(student) > 1 and student[1] else ""
                student_code = student[2] if len(student) > 2 and student[2] else ""

                students.append({
                    "id": student_id,
                    "name": student_name,
                    "code": student_code
                })

            # إذا لم يتم العثور على أي طلاب، جرب استعلامًا بديلًا
            if not students and 'اللوائح' in tables and 'السجل_العام' in tables:
                print("جاري تجربة استعلام بديل باستخدام جدولي اللوائح والسجل_العام...")

                # التحقق من أعمدة جدول اللوائح
                cursor.execute("PRAGMA table_info(اللوائح)")
                list_columns = [col[1] for col in cursor.fetchall()]
                print(f"أعمدة جدول اللوائح: {list_columns}")

                # التحقق من أعمدة جدول السجل_العام
                cursor.execute("PRAGMA table_info(السجل_العام)")
                registry_columns = [col[1] for col in cursor.fetchall()]
                print(f"أعمدة جدول السجل_العام: {registry_columns}")

                # البحث عن الأعمدة المناسبة
                list_id_column = 'الرمز' if 'الرمز' in list_columns else None
                list_class_column = 'القسم' if 'القسم' in list_columns else None
                list_year_column = 'السنة_الدراسية' if 'السنة_الدراسية' in list_columns else None

                registry_id_column = 'الرمز' if 'الرمز' in registry_columns else None
                registry_name_column = 'الاسم_والنسب' if 'الاسم_والنسب' in registry_columns else ('الاسم_الكامل' if 'الاسم_الكامل' in registry_columns else None)

                # التحقق من وجود الأعمدة الضرورية
                if list_id_column and list_class_column and registry_id_column and registry_name_column:
                    # بناء استعلام ربط بين الجدولين
                    join_query = f"""
                    SELECT s.{registry_id_column}, s.{registry_name_column}
                    FROM السجل_العام s
                    JOIN اللوائح l ON s.{registry_id_column} = l.{list_id_column}
                    WHERE l.{list_class_column} = ?
                    """

                    # إضافة شرط السنة الدراسية إذا وجد العمود
                    if list_year_column:
                        join_query += f" AND l.{list_year_column} = (SELECT السنة_الدراسية FROM بيانات_المؤسسة LIMIT 1)"

                    join_query += f" ORDER BY s.{registry_id_column}"

                    print(f"تنفيذ استعلام الربط: {join_query} للقسم {class_name}")

                    cursor.execute(join_query, (class_name,))
                    join_data = cursor.fetchall()

                    print(f"تم العثور على {len(join_data)} طالب في القسم {class_name} باستخدام استعلام الربط")

                    # تحويل البيانات إلى قائمة من القواميس
                    for i, student in enumerate(join_data):
                        student_id = i + 1  # ترقيم تسلسلي يبدأ من 1
                        student_name = student[1] if len(student) > 1 and student[1] else ""
                        student_code = student[0] if student[0] else ""

                        students.append({
                            "id": student_id,
                            "name": student_name,
                            "code": student_code
                        })

            # إذا لم يتم العثور على أي طلاب، أضف بيانات افتراضية للاختبار
            if not students:
                return self._get_default_students(class_name)

            return students

        except Exception as e:
            print(f"خطأ في الحصول على بيانات الطلاب باستخدام sqlite3: {str(e)}")
            # إرجاع قائمة فارغة في حالة حدوث خطأ
            return self._get_default_students(class_name)

    def _get_student_data_qsql(self, class_name):
        """الحصول على بيانات الطلاب باستخدام واجهة QSqlDatabase"""
        try:
            # إنشاء استعلام SQL
            query = QSqlQuery(self.db)

            # التحقق من الجداول الموجودة في قاعدة البيانات
            query.exec_("SELECT name FROM sqlite_master WHERE type='table'")
            tables = []
            while query.next():
                tables.append(query.value(0))
            print(f"الجداول الموجودة في قاعدة البيانات: {tables}")

            # محاولة استخدام جدول_عام أولاً (الأولوية الأولى)
            if 'جدول_عام' in tables:
                print("محاولة استخدام جدول_عام للحصول على بيانات الطلاب...")
                students = self._get_students_from_main_table(class_name, query)
                if students and len(students) > 0:
                    print(f"تم العثور على {len(students)} طالب في جدول_عام للقسم {class_name}")
                    return students
                else:
                    print("لم يتم العثور على طلاب في جدول_عام، جاري تجربة الطريقة التالية...")

            # محاولة استخدام جدولي اللوائح والسجل_العام (الأولوية الثانية)
            if 'اللوائح' in tables and 'السجل_العام' in tables:
                print("محاولة استخدام جدولي اللوائح والسجل_العام للحصول على بيانات الطلاب...")
                students = self._get_students_from_join_tables(class_name, query)
                if students and len(students) > 0:
                    print(f"تم العثور على {len(students)} طالب من جدولي اللوائح والسجل_العام للقسم {class_name}")
                    return students
                else:
                    print("لم يتم العثور على طلاب في جدولي اللوائح والسجل_العام، جاري تجربة الطريقة التالية...")

            # محاولة استخدام جدول التلاميذ (الأولوية الثالثة)
            if 'التلاميذ' in tables:
                print("محاولة استخدام جدول التلاميذ للحصول على بيانات الطلاب...")
                # استعلام مباشر من جدول التلاميذ
                query_str = f"""
                SELECT الرمز, الاسم_والنسب, الرمز
                FROM التلاميذ
                WHERE القسم = '{class_name}'
                ORDER BY الرمز
                """
                if query.exec_(query_str):
                    students = []
                    i = 1
                    while query.next():
                        student_id = i
                        student_name = query.value(1) if query.value(1) else ""
                        student_code = query.value(0) if query.value(0) else ""

                        students.append({
                            "id": student_id,
                            "name": student_name,
                            "code": student_code
                        })
                        i += 1

                    if len(students) > 0:
                        print(f"تم العثور على {len(students)} طالب في جدول التلاميذ للقسم {class_name}")
                        return students
                    else:
                        print("لم يتم العثور على طلاب في جدول التلاميذ")
                else:
                    print(f"خطأ في تنفيذ استعلام جدول التلاميذ: {query.lastError().text()}")

            # إذا وصلنا إلى هنا، فلم نتمكن من العثور على أي طلاب
            print("لم يتم العثور على أي طلاب باستخدام جميع الطرق المتاحة")
            return self._get_default_students(class_name)

        except Exception as e:
            print(f"خطأ في الحصول على بيانات الطلاب باستخدام QSqlDatabase: {str(e)}")
            # إرجاع قائمة فارغة في حالة حدوث خطأ
            return self._get_default_students(class_name)

    def _get_students_from_main_table(self, class_name, query):
        """الحصول على بيانات الطلاب من جدول_عام"""
        try:
            # استعلام مباشر من جدول_عام
            query_str = f"""
            SELECT الرمز, الاسم_والنسب, الرمز
            FROM جدول_عام
            WHERE القسم = '{class_name}' AND السنة_الدراسية = (SELECT السنة_الدراسية FROM بيانات_المؤسسة LIMIT 1)
            ORDER BY CAST(COALESCE(رت, '0') AS INTEGER)
            """
            print(f"تنفيذ استعلام QSqlQuery للطلاب من جدول_عام: {query_str}")

            if not query.exec_(query_str):
                print(f"خطأ في تنفيذ استعلام جدول_عام: {query.lastError().text()}")
                # محاولة استعلام بدون شرط السنة الدراسية
                alt_query_str = f"""
                SELECT الرمز, الاسم_والنسب, الرمز
                FROM جدول_عام
                WHERE القسم = '{class_name}'
                ORDER BY CAST(COALESCE(رت, '0') AS INTEGER)
                """
                print(f"تنفيذ استعلام بديل من جدول_عام: {alt_query_str}")

                if not query.exec_(alt_query_str):
                    print(f"خطأ في تنفيذ الاستعلام البديل من جدول_عام: {query.lastError().text()}")
                    return []

            # جمع نتائج الاستعلام
            students = []
            i = 1
            while query.next():
                student_id = i
                student_name = query.value(1) if query.value(1) else ""
                student_code = query.value(0) if query.value(0) else ""

                students.append({
                    "id": student_id,
                    "name": student_name,
                    "code": student_code
                })
                i += 1

            print(f"تم العثور على {len(students)} طالب في جدول_عام باستخدام QSqlQuery")
            return students

        except Exception as e:
            print(f"خطأ في الحصول على بيانات الطلاب من جدول_عام: {str(e)}")
            return []

    def _get_students_from_join_tables(self, class_name, query):
        """الحصول على بيانات الطلاب من جدولي اللوائح والسجل_العام"""
        try:
            # استعلام ربط بين جدولي اللوائح والسجل_العام
            join_query_str = f"""
            SELECT s.الرمز, s.الاسم_والنسب, s.الرمز
            FROM السجل_العام s
            JOIN اللوائح l ON s.الرمز = l.الرمز
            WHERE l.القسم = '{class_name}' AND l.السنة_الدراسية = (SELECT السنة_الدراسية FROM بيانات_المؤسسة LIMIT 1)
            ORDER BY CAST(COALESCE(l.رت, '0') AS INTEGER)
            """
            print(f"تنفيذ استعلام الربط: {join_query_str}")

            if not query.exec_(join_query_str):
                print(f"خطأ في تنفيذ استعلام الربط: {query.lastError().text()}")
                # محاولة استعلام بدون شرط السنة الدراسية
                alt_join_query_str = f"""
                SELECT s.الرمز, s.الاسم_والنسب, s.الرمز
                FROM السجل_العام s
                JOIN اللوائح l ON s.الرمز = l.الرمز
                WHERE l.القسم = '{class_name}'
                ORDER BY CAST(COALESCE(l.رت, '0') AS INTEGER)
                """
                print(f"تنفيذ استعلام ربط بديل: {alt_join_query_str}")

                if not query.exec_(alt_join_query_str):
                    print(f"خطأ في تنفيذ استعلام الربط البديل: {query.lastError().text()}")
                    return []

            # جمع نتائج الاستعلام
            students = []
            i = 1
            while query.next():
                student_id = i
                student_name = query.value(1) if query.value(1) else ""
                student_code = query.value(0) if query.value(0) else ""

                students.append({
                    "id": student_id,
                    "name": student_name,
                    "code": student_code
                })
                i += 1

            print(f"تم العثور على {len(students)} طالب من جدولي اللوائح والسجل_العام باستخدام استعلام الربط")
            return students

        except Exception as e:
            print(f"خطأ في الحصول على بيانات الطلاب من جدولي اللوائح والسجل_العام: {str(e)}")
            return []

    def set_default_supervision(self):
        """تعيين رقم الحراسة الافتراضي وتحميل الأقسام المسندة"""
        try:
            print("تعيين رقم الحراسة الافتراضي...")

            # التحقق من وجود supervision_combo
            if hasattr(self, 'supervision_combo'):
                # تعيين رقم الحراسة الافتراضي إلى 1 (المؤشر 0)
                self.supervision_combo.blockSignals(True)  # منع إرسال إشارات أثناء التحديث
                self.supervision_combo.setCurrentIndex(0)  # تحديد الحراسة رقم 1
                self.supervision_combo.blockSignals(False)  # إعادة تفعيل الإشارات

                print(f"تم تعيين رقم الحراسة الافتراضي: {self.supervision_combo.currentText()}")

                # تحميل الأقسام المسندة
                # إذا كانت النافذة مدمجة، نستخدم طريقة مختلفة لتحميل الأقسام
                if hasattr(self, 'is_embedded') and self.is_embedded:
                    print("النافذة مدمجة، استخدام طريقة خاصة لتحميل الأقسام المسندة...")
                    # استخدام QTimer لتأخير تحميل الأقسام قليلاً
                    QTimer.singleShot(100, self.load_assigned_classes)
                else:
                    # تحميل الأقسام مباشرة إذا كانت النافذة مستقلة
                    self.load_assigned_classes()
            else:
                print("تحذير: لم يتم العثور على supervision_combo")
        except Exception as e:
            print(f"خطأ في تعيين رقم الحراسة الافتراضي: {str(e)}")

    def showEvent(self, event):
        """معالجة حدث عرض النافذة"""
        super().showEvent(event)

        # إذا كانت النافذة مدمجة، نقوم بإعادة تحميل الأقسام المسندة عند عرضها
        if hasattr(self, 'is_embedded') and self.is_embedded:
            print("تم عرض النافذة المدمجة، إعادة تحميل الأقسام المسندة...")
            # استخدام QTimer لتأخير إعادة تحميل الأقسام قليلاً
            QTimer.singleShot(200, self.reload_assigned_classes)

    def reload_assigned_classes(self):
        """إعادة تحميل الأقسام المسندة بناءً على رقم الحراسة الحالي"""
        try:
            # التحقق من وجود supervision_combo و class_combo
            if hasattr(self, 'supervision_combo') and hasattr(self, 'class_combo'):
                # الحصول على رقم الحراسة الحالي
                current_index = self.supervision_combo.currentIndex()

                # إذا كان هناك رقم حراسة محدد، نقوم بإعادة تحميل الأقسام المسندة
                if current_index >= 0:
                    print(f"إعادة تحميل الأقسام المسندة للحراسة رقم {current_index + 1}...")

                    # مسح القائمة الحالية
                    self.class_combo.blockSignals(True)  # منع إرسال إشارات أثناء التحديث
                    self.class_combo.clear()
                    self.class_combo.blockSignals(False)  # إعادة تفعيل الإشارات

                    # تحميل الأقسام المسندة
                    self.load_assigned_classes()
                else:
                    # إذا لم يكن هناك رقم حراسة محدد، نقوم بتعيين رقم الحراسة الافتراضي
                    print("لم يتم تحديد رقم حراسة، تعيين رقم الحراسة الافتراضي...")
                    self.set_default_supervision()
        except Exception as e:
            print(f"خطأ في إعادة تحميل الأقسام المسندة: {str(e)}")

    def open_pdf_file(self, file_path):
        """فتح ملف PDF بعد إنشائه"""
        try:
            # التحقق من وجود الملف
            if not os.path.exists(file_path):
                print(f"خطأ: الملف غير موجود: {file_path}")
                ConfirmationDialogs.show_custom_warning_message(
                    self,
                    "لم يتم العثور على الملف المطلوب.",
                    "تنبيه"
                )
                return False

            # استخدام نافذة النجاح المخصصة من ConfirmationDialogs
            should_open = ConfirmationDialogs.show_pdf_success_dialog(
                self,
                file_path,
                "تم إنشاء التقرير بنجاح"
            )

            # إذا اختار المستخدم فتح الملف
            if should_open:
                # فتح الملف باستخدام البرنامج الافتراضي
                try:
                    # على نظام Windows، استخدم os.startfile
                    os.startfile(file_path)
                    print(f"تم فتح الملف: {file_path}")
                except Exception as e:
                    print(f"خطأ في فتح الملف مباشرة: {str(e)}")
                    ConfirmationDialogs.show_custom_warning_message(
                        self,
                        f"تم إنشاء الملف بنجاح ولكن حدث خطأ أثناء فتحه تلقائياً.\nيمكنك فتح الملف يدوياً من المسار:\n{file_path}",
                        "تنبيه"
                    )

            return True

        except Exception as e:
            print(f"خطأ في فتح الملف: {str(e)}")
            ConfirmationDialogs.show_custom_error_message(
                self,
                f"حدث خطأ أثناء فتح الملف:\n{str(e)}",
                "خطأ"
            )
            return False

# إذا تم تشغيل هذا الملف مباشرة
if __name__ == "__main__":
    # إنشاء تطبيق PyQt
    app = QApplication(sys.argv)

    # إنشاء اتصال بقاعدة البيانات
    try:
        db = sqlite3.connect("data.db")
        print("تم الاتصال بقاعدة البيانات بنجاح")

        # الحصول على السنة الدراسية الحالية
        cursor = db.cursor()
        cursor.execute("SELECT السنة_الدراسية FROM بيانات_المؤسسة LIMIT 1")
        result = cursor.fetchone()
        academic_year = result[0] if result else ""
        print(f"السنة الدراسية الحالية: {academic_year}")

        # إنشاء النافذة وعرضها
        window = PrintListsWindow(db=db, academic_year=academic_year)
        window.show()

        # تشغيل حلقة الأحداث
        sys.exit(app.exec_())
    except Exception as e:
        print(f"خطأ في الاتصال بقاعدة البيانات: {e}")

        # إنشاء النافذة بدون اتصال بقاعدة البيانات
        window = PrintListsWindow()
        window.show()

        # تشغيل حلقة الأحداث
        sys.exit(app.exec_())
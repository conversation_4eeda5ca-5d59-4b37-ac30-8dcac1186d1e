# -*- coding: utf-8 -*-

import sys
import os
import sqlite3
from PyQt5.QtWidgets import (QApplication, QMainWindow, QWidget, QTabWidget,
                            QVBox<PERSON>ayout, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, QGraphicsDropShadowEffect,
                            QPushButton, QDialog)
from PyQt5.QtGui import QFont, QColor
from PyQt5.QtCore import Qt, QSize

# Initialize availability flags with default values
SUB6_CONTENT_AVAILABLE = False
SUB7_CONTENT_AVAILABLE = False
SUB8_CONTENT_AVAILABLE = False

# --- استيراد نوافذ التبويبات ---
try:
    from sub1_window import Sub1Window
    # from sub2_window import SubWindow as Sub2Window # Removed import
    from sub5_window import StatisticsWindow
    # تعديل الاستيرادات لاستخدام الكلاسات الصحيحة من الملفات الصحيحة
    from sub6_window import Sub6Window  # استيراد Sub6Window من sub6_window.py للتبويب الخامس
    # نستورد Sub7Window فقط عند الحاجة لتجنب الاستيراد المزدوج
    # تم تعليق استيراد Sub7Window هنا لأننا سنستورده مباشرة في دالة open_printer_settings_dialog
    from sub8_window import Sub8Window  # استيراد Sub8Window للإعدادات الافتراضية

    print("تم استيراد النوافذ الفرعية بنجاح")  # تعديل رسالة التأكيد
    SUB6_CONTENT_AVAILABLE = True
    SUB7_CONTENT_AVAILABLE = True
    SUB8_CONTENT_AVAILABLE = True
except ImportError as e:
    print(f"خطأ في استيراد أحد ملفات النوافذ الفرعية: {e}")
    print("تأكد من وجود ملفات sub1_window.py, sub5_window.py, sub6_window.py, sub7_window.py, sub8_window.py في نفس المجلد.") # Removed sub2_window.py
    if hasattr(e, 'name') and e.name == 'sub6_window':
        print("سيتم استخدام محتوى مؤقت للتبويب الخامس.")
        Sub6Window = None
    elif hasattr(e, 'name') and e.name == 'sub7_window':
        print("سيتم استخدام محتوى مؤقت للتبويب السادس.")
        # لا نحتاج لتعيين Sub7Window = None هنا لأننا سنستورده مباشرة في دالة open_printer_settings_dialog
    elif hasattr(e, 'name') and e.name == 'sub8_window':
        print("سيتم استخدام محتوى مؤقت للتبويب الرابع.")
        Sub8Window = None
    else:
        print(f"الملف '{e.name}.py' مفقود وهو مطلوب. يتم إنهاء البرنامج.")
        sys.exit(1)

class Sub0Window(QMainWindow):
    """نافذة رئيسية مع مربع تبويبات متعدد بمظهر محسن"""

    def __init__(self):
        super().__init__()
        self.setWindowTitle("تهيئة البرنامج")
        self.setFixedSize(1300, 700) # تم تعديل الارتفاع في الكود السابق
        self.setObjectName("MainWindow")  # إضافة اسم للنافذة لتسهيل العثور عليها

        self.setStyleSheet("""
            QMainWindow { background-color: #F0F2F5; }
        """)

        content_frame = QFrame(self)
        outer_margin = 20
        content_frame.setGeometry(
            outer_margin, outer_margin,
            self.width() - 2 * outer_margin, self.height() - 2 * outer_margin
        )
        content_frame.setStyleSheet("""
            QFrame { background-color: white; border-radius: 10px; }
        """)

        shadow = QGraphicsDropShadowEffect()
        shadow.setBlurRadius(30); shadow.setXOffset(2); shadow.setYOffset(4)
        shadow.setColor(QColor(0, 0, 0, 70))
        content_frame.setGraphicsEffect(shadow)

        content_layout = QVBoxLayout(content_frame)
        content_layout.setContentsMargins(10, 10, 10, 10) # تم تعديل الهوامش في الكود السابق
        content_layout.setSpacing(15)

        self.tab_widget = QTabWidget(content_frame)
        self.tab_widget.setLayoutDirection(Qt.RightToLeft)

        self.tab_widget.setStyleSheet("""
            QTabWidget::pane { border: none; background-color: #FFFFFF; border-radius: 10px; padding: 20px; }
            QTabBar { alignment: right; }
            QTabBar::tab {
                background-color: none; color: #666666;
                font-family: Calibri; font-size: 12pt; font-weight: bold;
                min-width: 150px; height: 40px;
                margin-left: 0px; margin-right: 5px; border: none;
                border-top-left-radius: 6px; border-top-right-radius: 6px;
                padding: 5px 15px;
            }
            QTabBar::tab:hover { background-color: #E3F2FD; color: #333333; }
            QTabBar::tab:selected {
                background-color: #FFFFFF; color: #1976d2;
                border-bottom: 3px solid #1976d2;
            }
            QTabBar::tab:focus { outline: none; }
        """)

        # تحديث قائمة عناوين التبويبات (إزالة "توزيع الأقسام")
        tab_titles = [
            "استيراد البيانات", "إحصائيات", # Removed "بيانات المؤسسة"
            "الإعدادات الافتراضية", "عناوين الأوراق والملاحظات", "إعدادات الطابعة"
        ]

        self.db_path = os.path.join(os.path.dirname(__file__), "data.db")
        if not os.path.exists(self.db_path):
            print(f"تحذير: ملف قاعدة البيانات '{self.db_path}' غير موجود.")

        self.sub1_window_instance = None
        # self.sub2_window_instance = None # Removed
        # إزالة متغير self.sub3_wizard_window_instance
        self.stats_window_instance = None  # إضافة متغير لتتبع نافذة الإحصائيات
        self.sub6_window_instance = None  # إضافة متغير لتتبع نافذة Sub6Window
        self.sub7_window_instance = None  # إضافة متغير لتتبع نافذة Sub7Window
        self.sub8_window_instance = None  # إضافة متغير لتتبع نافذة الإعدادات الافتراضية

        for i, title in enumerate(tab_titles):
            tab_container = QWidget()
            tab_layout = QVBoxLayout(tab_container)

            if i == 0:  # التبويب الأول: استيراد البيانات
                tab_layout.setContentsMargins(0, 0, 0, 0)
                tab_layout.setSpacing(0)

                if 'Sub1Window' in globals() and Sub1Window:
                    self.sub1_window_instance = Sub1Window(tab_container)
                    self.sub1_window_instance.setMinimumSize(1250, 600)

                    # Fix for QWIDGETSIZE_MAX error - use direct large values or better approach
                    try:
                        # Instead of using QWIDGETSIZE_MAX, we'll remove the fixed size constraint
                        # by setting maximum size to a very large value (Qt's internal max is 16777215)
                        self.sub1_window_instance.setMaximumSize(16777215, 16777215)
                        # Also clear any fixed size that might have been set
                        self.sub1_window_instance.setFixedSize(1250, 600)  # Set initial size
                    except Exception as e:
                        print(f"Note: Could not adjust window size constraints. Error: {e}")

                    tab_layout.addWidget(self.sub1_window_instance)
                else:
                    widget = self.create_placeholder(title, is_error=True, module_name="sub1_window")
                    tab_layout.addWidget(widget)

            # Removed elif block for "بيانات المؤسسة" (previously i == 1)

            elif i == 1:  # التبويب الثاني: إحصائيات (كان سابقاً الثالث, الآن الثاني)
                tab_layout.setContentsMargins(0, 0, 0, 0)  # هوامش صفرية لملء المساحة
                tab_layout.setSpacing(0)  # بدون مسافات بين العناصر

                if 'StatisticsWindow' in globals() and StatisticsWindow:
                    # التحقق من وجود قاعدة البيانات
                    if os.path.exists(self.db_path):
                        # إنشاء نسخة من نافذة الإحصائيات
                        self.stats_window_instance = StatisticsWindow(tab_container, db_path=self.db_path)

                        # تعديل حجم النافذة لتتناسب مع حجم التبويب
                        self.stats_window_instance.setMinimumSize(1250, 600)

                        # محاولة إزالة قيود الحجم الثابت
                        try:
                            # نستخدم قيمة عالية لإزالة قيود الحجم الثابت
                            self.stats_window_instance.setMaximumSize(16777215, 16777215)
                            # تعيين حجم مبدئي مناسب
                            self.stats_window_instance.setFixedSize(1250, 600)
                        except Exception as e:
                            print(f"تنبيه: لم نتمكن من ضبط قيود الحجم لـ StatisticsWindow. الخطأ: {e}")

                        # إضافة النافذة للتبويب
                        tab_layout.addWidget(self.stats_window_instance)
                    else:
                        widget = self.create_db_error_widget()
                        tab_layout.addWidget(widget)
                else:
                    widget = self.create_placeholder(title, is_error=True, module_name="sub5_window")
                    tab_layout.addWidget(widget)
            elif i == 2:  # التبويب الثالث: الإعدادات الافتراضية (كان سابقاً الرابع, الآن الثالث)
                tab_layout.setContentsMargins(0, 0, 0, 0)  # هوامش صفرية لملء المساحة
                tab_layout.setSpacing(0)  # بدون مسافات بين العناصر

                if 'Sub8Window' in globals() and Sub8Window and SUB8_CONTENT_AVAILABLE:
                    # إنشاء نسخة من نافذة الإعدادات الافتراضية
                    self.sub8_window_instance = Sub8Window(tab_container)

                    # تعديل حجم النافذة لتتناسب مع حجم التبويب
                    self.sub8_window_instance.setMinimumSize(1250, 600)

                    # محاولة إزالة قيود الحجم الثابت
                    try:
                        # نستخدم قيمة عالية لإزالة قيود الحجم الثابت
                        self.sub8_window_instance.setMaximumSize(16777215, 16777215)
                        # تعيين حجم مبدئي مناسب
                        self.sub8_window_instance.setFixedSize(1250, 600)

                        # تم إزالة تعديلات تصغير الأزرار لتظهر بحجمها الطبيعي

                        # تعديل حجم خط العنوان فقط إذا لزم الأمر
                        title_labels = self.sub8_window_instance.findChildren(QLabel)
                        for label in title_labels:
                            if "إعدادات البرنامج" in label.text():
                                label.setFont(QFont("Amiri", 18, QFont.Bold))  # تعديل حجم الخط

                    except Exception as e:
                        print(f"تنبيه: لم نتمكن من ضبط تنسيق الأزرار في Sub8Window. الخطأ: {e}")

                    # إضافة النافذة للتبويب
                    tab_layout.addWidget(self.sub8_window_instance)
                else:
                    widget = self.create_placeholder(title, is_error=not SUB8_CONTENT_AVAILABLE, module_name="sub8_window")
                    tab_layout.addWidget(widget)
            elif i == 3:  # التبويب الرابع: عناوين الأوراق والملاحظات (كان سابقاً الخامس, الآن الرابع)
                tab_layout.setContentsMargins(0, 0, 0, 0)
                tab_layout.setSpacing(0)

                if SUB6_CONTENT_AVAILABLE and Sub6Window is not None:
                    # إنشاء نسخة من نافذة Sub6Window (الأوراق والملاحظات)
                    self.sub6_window_instance = Sub6Window()

                    # تعديل حجم النافذة لتتناسب مع حجم التبويب
                    self.sub6_window_instance.setMinimumSize(1250, 600)

                    # محاولة إزالة قيود الحجم الثابت
                    try:
                        self.sub6_window_instance.setMaximumSize(16777215, 16777215)
                        self.sub6_window_instance.setFixedSize(1250, 600)
                    except Exception as e:
                        print(f"تنبيه: لم نتمكن من ضبط قيود الحجم لـ Sub6Window. الخطأ: {e}")

                    # إضافة النافذة للتبويب
                    tab_layout.addWidget(self.sub6_window_instance)
                else:
                    widget = self.create_placeholder(title, is_error=True, module_name="sub6_window")
                    tab_layout.addWidget(widget)

            elif i == 4:  # التبويب الخامس: إعدادات الطابعة (كان سابقاً السادس, الآن الخامس)
                # استخدام تخطيط بهوامش مناسبة للزر
                tab_layout.setContentsMargins(20, 20, 20, 20)
                tab_layout.setSpacing(10)
                tab_layout.setAlignment(Qt.AlignCenter)

                # إنشاء زر لفتح نافذة إعدادات الطابعة
                printer_settings_button = QPushButton("فتح إعدادات الطابعة")
                printer_settings_button.setMinimumSize(250, 50)
                printer_settings_button.setFont(QFont("Calibri", 14, QFont.Bold))
                printer_settings_button.setStyleSheet("""
                    QPushButton {
                        background-color: #1976d2;
                        color: white;
                        border-radius: 8px;
                        padding: 10px;
                    }
                    QPushButton:hover {
                        background-color: #1565c0;
                    }
                    QPushButton:pressed {
                        background-color: #0d47a1;
                    }
                """)

                # إضافة الزر إلى التخطيط
                tab_layout.addWidget(printer_settings_button)

                # تخزين مرجع للتبويب الحالي
                self.printer_settings_tab = tab_container

                # إنشاء متغير لتخزين نافذة إعدادات الطابعة (سيتم إنشاؤها عند الضغط على الزر)
                self.sub7_window_instance = None

                # تخزين مرجع للتخطيط الحالي للاستخدام في دالة فتح النافذة
                self.printer_tab_layout = tab_layout

                # ربط الزر بدالة فتح نافذة إعدادات الطابعة
                printer_settings_button.clicked.connect(self.open_printer_settings_dialog)

                # إضافة وصف توضيحي
                description_label = QLabel("انقر على الزر أعلاه لفتح نافذة إعدادات الطابعة")
                description_label.setAlignment(Qt.AlignCenter)
                description_label.setFont(QFont("Calibri", 12))
                description_label.setStyleSheet("color: #666666; margin-top: 10px;")
                tab_layout.addWidget(description_label)
            else:  # This case should ideally not be reached with the current tab_titles
                tab_layout.setContentsMargins(0, 0, 0, 0) # هوامش صفرية
                widget = self.create_placeholder(title)
                tab_layout.addWidget(widget)


            self.tab_widget.addTab(tab_container, title)

        content_layout.addWidget(self.tab_widget)
        self.tab_widget.currentChanged.connect(self.on_tab_changed)

    def create_placeholder(self, title, is_error=False, module_name=""):
        placeholder_widget = QWidget()
        placeholder_layout = QVBoxLayout(placeholder_widget)
        placeholder_layout.setAlignment(Qt.AlignCenter)
        if is_error:
            message = f"لا يمكن تحميل محتوى '{title}'.\nالملف '{module_name}.py' غير موجود أو به خطأ."
            color = "red"; font_size = 14; font_bold = False
        else:
            message = f"محتوى قيد الإنشاء لـ\n'{title}'"
            color = "#9E9E9E"; font_size = 16; font_bold = True
        content_label = QLabel(message)
        content_label.setAlignment(Qt.AlignCenter)
        font = QFont("Calibri", font_size); font.setBold(font_bold)
        content_label.setFont(font); content_label.setStyleSheet(f"color: {color};")
        placeholder_layout.addWidget(content_label)
        return placeholder_widget

    def create_db_error_widget(self):
        error_widget = QWidget()
        layout = QVBoxLayout(error_widget)
        layout.setAlignment(Qt.AlignCenter)
        error_label = QLabel(f"لا يمكن عرض الإحصائيات. ملف قاعدة البيانات غير موجود:\n{self.db_path}")
        error_label.setAlignment(Qt.AlignCenter); error_label.setStyleSheet("color: red; font-size: 14pt;")
        layout.addWidget(error_label)
        return error_widget

    def set_current_tab_by_index(self, index):
        if 0 <= index < self.tab_widget.count():
            self.tab_widget.setCurrentIndex(index)
            print(f"Sub0Window: تم تعيين التبويب إلى الفهرس {index}")
        else:
            print(f"تحذير (Sub0Window): فهرس التبويب {index} غير صالح.")

    def resizeEvent(self, event):
        super().resizeEvent(event)
        # تعديل حجم النوافذ المضمنة لتتناسب مع التبويبات عند تغيير الحجم
        current_tab = self.tab_widget.currentIndex()
        current_widget = self.tab_widget.currentWidget()

        if current_tab == 0 and self.sub1_window_instance:
            tab_size = current_widget.size()
            self.sub1_window_instance.setMinimumSize(tab_size)
        # Removed elif for current_tab == 1 (previously Sub2Window)
        elif current_tab == 1 and self.stats_window_instance: # Index adjusted
            tab_size = current_widget.size()
            self.stats_window_instance.setMinimumSize(tab_size)
        elif current_tab == 2 and self.sub8_window_instance: # Index adjusted
            tab_size = current_widget.size()
            self.sub8_window_instance.setMinimumSize(tab_size)
        elif current_tab == 3 and self.sub6_window_instance: # Index adjusted
            tab_size = current_widget.size()
            self.sub6_window_instance.setMinimumSize(tab_size)
        elif current_tab == 4 and self.sub7_window_instance: # Index adjusted
            # لا نقوم بتعديل حجم نافذة إعدادات الطابعة
            pass

    def refresh_all_tabs(self):
        """تحديث جميع التبويبات والنوافذ المدمجة"""
        print("جاري تحديث جميع التبويبات...")

        # إعادة فتح الاتصال بقاعدة البيانات إذا كان مغلقاً
        try:
            conn = sqlite3.connect(self.db_path)
            conn.close()
            print("تم التحقق من الاتصال بقاعدة البيانات")
        except Exception as e:
            print(f"خطأ في الاتصال بقاعدة البيانات: {e}")

        # تحديث نافذة استيراد البيانات
        if self.sub1_window_instance:
            try:
                print("تحديث نافذة استيراد البيانات...")
                if hasattr(self.sub1_window_instance, 'reload_local_data'):
                    self.sub1_window_instance.reload_local_data()
                elif hasattr(self.sub1_window_instance, 'display_database_statistics'):
                    self.sub1_window_instance.display_database_statistics()
            except Exception as e:
                print(f"خطأ في تحديث نافذة استيراد البيانات: {e}")

        # تحديث نافذة الإحصائيات
        if self.stats_window_instance:
            try:
                print("تحديث نافذة الإحصائيات...")
                self.stats_window_instance.update_statistics()
            except Exception as e:
                print(f"خطأ في تحديث نافذة الإحصائيات: {e}")

        # Removed refresh for sub2_window_instance

        # تحديث نافذة الإعدادات الافتراضية
        if self.sub8_window_instance:
            try:
                print("تحديث نافذة الإعدادات الافتراضية...")
                if hasattr(self.sub8_window_instance, 'load_settings'):
                    self.sub8_window_instance.load_settings()
            except Exception as e:
                print(f"خطأ في تحديث نافذة الإعدادات الافتراضية: {e}")

        # تحديث نافذة عناوين الأوراق والملاحظات
        if self.sub6_window_instance:
            try:
                print("تحديث نافذة عناوين الأوراق والملاحظات...")
                if hasattr(self.sub6_window_instance, 'load_data'):
                    self.sub6_window_instance.load_data()
                elif hasattr(self.sub6_window_instance, 'refresh_data'):
                    self.sub6_window_instance.refresh_data()
            except Exception as e:
                print(f"خطأ في تحديث نافذة عناوين الأوراق والملاحظات: {e}")

        # لا نحتاج لتحديث نافذة إعدادات الطابعة هنا
        # لأنها ستُنشأ وتُحدث عند فتحها من خلال الزر

        print("تم تحديث جميع التبويبات بنجاح")

    def refresh_current_tab(self):
        """تحديث التبويب الحالي فقط"""
        current_index = self.tab_widget.currentIndex()
        print(f"جاري تحديث التبويب الحالي: {current_index} - '{self.tab_widget.tabText(current_index)}'")

        # إعادة فتح الاتصال بقاعدة البيانات إذا كان مغلقاً
        try:
            conn = sqlite3.connect(self.db_path)
            conn.close()
            print("تم التحقق من الاتصال بقاعدة البيانات")
        except Exception as e:
            print(f"خطأ في الاتصال بقاعدة البيانات: {e}")

        if current_index == 0 and self.sub1_window_instance:
            # تحديث نافذة استيراد البيانات
            try:
                print("تحديث نافذة استيراد البيانات...")
                if hasattr(self.sub1_window_instance, 'reload_local_data'):
                    self.sub1_window_instance.reload_local_data()
                elif hasattr(self.sub1_window_instance, 'display_database_statistics'):
                    self.sub1_window_instance.display_database_statistics()
            except Exception as e:
                print(f"خطأ في تحديث نافذة استيراد البيانات: {e}")
        # Removed elif for current_index == 1 (previously Sub2Window)
        elif current_index == 1 and self.stats_window_instance: # Index adjusted
            # تحديث نافذة الإحصائيات
            try:
                print("تحديث نافذة الإحصائيات...")
                self.stats_window_instance.update_statistics()
            except Exception as e:
                print(f"خطأ في تحديث نافذة الإحصائيات: {e}")
        elif current_index == 2 and self.sub8_window_instance: # Index adjusted
            # تحديث نافذة الإعدادات الافتراضية
            try:
                print("تحديث نافذة الإعدادات الافتراضية...")
                if hasattr(self.sub8_window_instance, 'load_settings'):
                    self.sub8_window_instance.load_settings()
            except Exception as e:
                print(f"خطأ في تحديث نافذة الإعدادات الافتراضية: {e}")
        elif current_index == 3 and self.sub6_window_instance: # Index adjusted
            # تحديث نافذة عناوين الأوراق والملاحظات
            try:
                print("تحديث نافذة عناوين الأوراق والملاحظات...")
                if hasattr(self.sub6_window_instance, 'load_data'):
                    self.sub6_window_instance.load_data()
                elif hasattr(self.sub6_window_instance, 'refresh_data'):
                    self.sub6_window_instance.refresh_data()
            except Exception as e:
                print(f"خطأ في تحديث نافذة عناوين الأوراق والملاحظات: {e}")
        elif current_index == 4: # Index adjusted
            # لا نحتاج لتحديث شيء في تبويب إعدادات الطابعة
            # لأن النافذة ستُفتح فقط عند النقر على الزر
            print("تم تحديث تبويب إعدادات الطابعة")

        print(f"تم تحديث التبويب {current_index} بنجاح")

    def open_printer_settings_dialog(self):
        """فتح نافذة إعدادات الطابعة كنافذة منبثقة"""
        try:
            # استيراد Sub7Window مباشرة بدلاً من تشغيلها كعملية منفصلة
            try:
                from sub7_window import Sub7Window

                # إنشاء نافذة إعدادات الطابعة
                printer_settings_window = Sub7Window()

                # عرض النافذة كنافذة مشروطة (modal)
                printer_settings_window.exec_()

                print("تم فتح نافذة إعدادات الطابعة بنجاح")
            except ImportError:
                # في حالة عدم القدرة على استيراد Sub7Window، نحاول تشغيل الملف كعملية منفصلة
                import subprocess
                import sys
                import os

                # الحصول على مسار البايثون الحالي
                python_executable = sys.executable

                # الحصول على المسار الكامل لملف sub7_window.py
                script_path = os.path.join(os.path.dirname(__file__), "sub7_window.py")

                # التأكد من وجود الملف
                if not os.path.exists(script_path):
                    raise FileNotFoundError(f"ملف sub7_window.py غير موجود في المسار: {script_path}")

                # تشغيل الملف كعملية منفصلة
                subprocess.Popen([python_executable, script_path])

                print(f"تم تشغيل نافذة إعدادات الطابعة كعملية منفصلة: {script_path}")
        except Exception as e:
            print(f"خطأ في فتح نافذة إعدادات الطابعة: {e}")
            error_label = QLabel(f"تعذر فتح نافذة إعدادات الطابعة: {str(e)}")
            error_label.setStyleSheet("color: red;")
            error_label.setAlignment(Qt.AlignCenter)
            if hasattr(self, 'printer_tab_layout'):
                self.printer_tab_layout.addWidget(error_label)

    def on_tab_changed(self, index):
        """تنفيذ إجراءات عند تغيير التبويب"""
        print(f"تم الانتقال إلى التبويب: {index} - '{self.tab_widget.tabText(index)}'")

        # إضافة معالجة حجم النافذة المضمنة عند التغيير للتبويب
        current_widget = self.tab_widget.widget(index)

        # إعادة فتح الاتصال بقاعدة البيانات إذا كان مغلقاً
        try:
            conn = sqlite3.connect(self.db_path)
            conn.close()
        except Exception as e:
            print(f"خطأ في الاتصال بقاعدة البيانات: {e}")

        if index == 0 and self.sub1_window_instance:
            tab_size = current_widget.size()
            self.sub1_window_instance.setMinimumSize(tab_size)
            # تحديث بيانات استيراد البيانات عند الانتقال إلى التبويب
            try:
                if hasattr(self.sub1_window_instance, 'reload_local_data'):
                    self.sub1_window_instance.reload_local_data()
                elif hasattr(self.sub1_window_instance, 'display_database_statistics'):
                    self.sub1_window_instance.display_database_statistics()
            except Exception as e:
                print(f"خطأ في تحديث نافذة استيراد البيانات: {e}")
        elif index == 1 and self.stats_window_instance: # Index adjusted
            tab_size = current_widget.size()
            self.stats_window_instance.setMinimumSize(tab_size)
            # تحديث بيانات الإحصائيات عند الانتقال إلى التبويب
            try:
                self.stats_window_instance.update_statistics()
            except Exception as e:
                print(f"خطأ في تحديث نافذة الإحصائيات: {e}")
        elif index == 2 and self.sub8_window_instance: # Index adjusted
            tab_size = current_widget.size()
            self.sub8_window_instance.setMinimumSize(tab_size)
            # تحديث الإعدادات الافتراضية عند الانتقال إلى التبويب
            try:
                if hasattr(self.sub8_window_instance, 'load_settings'):
                    self.sub8_window_instance.load_settings()
            except Exception as e:
                print(f"خطأ في تحديث نافذة الإعدادات الافتراضية: {e}")
        elif index == 3 and self.sub6_window_instance: # Index adjusted
            tab_size = current_widget.size()
            self.sub6_window_instance.setMinimumSize(tab_size)
            # تحديث عناوين الأوراق والملاحظات عند الانتقال إلى التبويب
            try:
                if hasattr(self.sub6_window_instance, 'load_data'):
                    self.sub6_window_instance.load_data()
                elif hasattr(self.sub6_window_instance, 'refresh_data'):
                    self.sub6_window_instance.refresh_data()
            except Exception as e:
                print(f"خطأ في تحديث نافذة عناوين الأوراق والملاحظات: {e}")
        elif index == 4: # Index adjusted
            # لا نحتاج لتحديث شيء في تبويب إعدادات الطابعة
            # لأن النافذة ستُفتح فقط عند النقر على الزر
            print("تم الانتقال إلى تبويب إعدادات الطابعة")

        # بقية الكود الأصلي
        if index == 1:  # تم تعديل الرقم من 2 إلى 1 لأن تبويب الإحصائيات الآن هو التبويب رقم 1
            tab = self.tab_widget.widget(index)
            if 'StatisticsWindow' in locals() and StatisticsWindow:
                stats_widget = None
                for i in range(tab.layout().count()):
                    item = tab.layout().itemAt(i)
                    if isinstance(item.widget(), StatisticsWindow):
                        stats_widget = item.widget(); break
                if stats_widget:
                    print("تحديث الإحصائيات..."); stats_widget.update_statistics()
                else: print("لم يتم العثور على ويدجت الإحصائيات في التبويب.")

if __name__ == "__main__":
    app = QApplication(sys.argv)
    app.setLayoutDirection(Qt.RightToLeft)
    font = QFont("Calibri", 11)
    app.setFont(font)
    window = Sub0Window()
    window.show()
    sys.exit(app.exec_())

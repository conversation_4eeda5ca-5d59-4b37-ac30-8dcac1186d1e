# معالج إدارة الأقسام والحراسة

## نظرة عامة

تم تطوير معالج إدارة الأقسام والحراسة لتحسين واجهة المستخدم وتبسيط عملية تعيين الأقسام للحراسات المختلفة. يقوم المعالج بتوجيه المستخدم خلال عملية التعيين خطوة بخطوة، مما يجعل العملية أكثر سهولة ووضوحًا.

## الملفات

- `sub3_wizard_window.py`: يحتوي على تنفيذ معالج الخطوات الجديد.
- `sub3_window_replacement.py`: يحتوي على فئة `Sub3Window` الجديدة التي تفتح المعالج مباشرة.

## كيفية الاستخدام

لاستخدام المعالج الجديد، يمكنك استبدال ملف `sub3_window.py` الحالي بملف `sub3_window_replacement.py` أو تعديل ملف `main_window.py` لاستخدام فئة `Sub3WizardWindow` مباشرة.

### خطوات المعالج

يتكون المعالج من أربع خطوات رئيسية:

#### الخطوة الأولى: اختيار السنة الدراسية

في هذه الخطوة، يجب على المستخدم اختيار السنة الدراسية التي يريد إدارة أقسامها. لا يمكن الانتقال للخطوة التالية حتى يتم اختيار سنة دراسية.

#### الخطوة الثانية: ترتيب المستويات الدراسية

في هذه الخطوة، يمكن للمستخدم تعديل ترتيب المستويات الدراسية حسب الحاجة. يتم عرض المستويات في جدول يمكن تعديل قيم الترتيب فيه.

#### الخطوة الثالثة: تعيين الأقسام للحراسات

في هذه الخطوة، يتم عرض المستويات الدراسية واحدًا تلو الآخر، ويمكن للمستخدم تعيين كل قسم إلى حراسة محددة باستخدام قائمة منسدلة. يمكن التنقل بين المستويات باستخدام أزرار "المستوى السابق" و"المستوى التالي".

#### الخطوة الرابعة: الملخص والحفظ

في هذه الخطوة، يتم عرض ملخص للتعيينات التي قام بها المستخدم. يمكن للمستخدم مراجعة التعيينات قبل حفظها في قاعدة البيانات. كما يمكنه عرض الأقسام المسندة في نافذة منفصلة.

## المميزات

- واجهة مستخدم سهلة الاستخدام وبديهية.
- عملية تعيين منظمة ومقسمة إلى خطوات واضحة.
- إمكانية التنقل بين الخطوات باستخدام أزرار "التالي" و"السابق".
- عرض ملخص للتعيينات قبل الحفظ.
- إمكانية عرض الأقسام المسندة في نافذة منفصلة.

## متطلبات النظام

- Python 3.6 أو أحدث
- PyQt5
- قاعدة بيانات SQLite

## التثبيت

1. نسخ ملفي `sub3_wizard_window.py` و `sub3_window_replacement.py` إلى مجلد المشروع.
2. استبدال ملف `sub3_window.py` الحالي بملف `sub3_window_replacement.py` أو تعديل ملف `main_window.py` لاستخدام فئة `Sub3WizardWindow` مباشرة.

## الاستخدام المباشر

يمكن تشغيل المعالج مباشرة باستخدام الأمر التالي:

```
python sub3_wizard_window.py
```

أو يمكن استخدام واجهة التوجيه:

```
python sub3_window_replacement.py
```

import sqlite3
import sys
import os
import re
from PyQt5.QtWidgets import (QApplication, QWidget, QVBoxLayout, QTextEdit, QHBoxLayout, QPushButton,
                           QFileDialog, QMessageBox, QFrame, QGraphicsDropShadowEffect, QProgressDialog)
from PyQt5.QtGui import QFont, QColor, QIcon
from PyQt5.QtCore import Qt, QDateTime

# محاولة استيراد pandas مع معالجة الخطأ
try:
    import pandas as pd
    PANDAS_AVAILABLE = True
except ImportError:
    # إنشاء كائن بديل للتعامل مع الخطأ بشكل أفضل
    class PandasSubstitute:
        def __getattr__(self, name):
            raise ImportError(
                "مكتبة pandas غير متوفرة. الرجاء تثبيتها باستخدام الأمر:\n"
                "pip install pandas --only-binary=:all:\n\n"
                "أو تثبيت نسخة بديلة مجمعة مسبقاً:\n"
                "pip install pandas-only-binary"
            )
    pd = PandasSubstitute()
    PANDAS_AVAILABLE = False

class ArrivalsImporter:
    def __init__(self, log_text, main_window=None):
        """فئة مخصصة لاستيراد بيانات الوافدين والمغادرين من ملفات Excel إلى قاعدة البيانات"""
        self.log_text = log_text
        self.db_path = "data.db"
        self.main_window = main_window
        self.parent_widget = main_window

        # التحقق من توفر pandas وإضافة تحذير إذا لم تكن متوفرة
        if not PANDAS_AVAILABLE:
            self.log("تنبيه: مكتبة pandas غير متوفرة. لن تعمل وظائف استيراد البيانات من Excel.", "warning")
            self.log("لتثبيت pandas، استخدم الأمر: pip install pandas --only-binary=:all:", "info")

    def log(self, message, status="info"):
        """إضافة رسالة إلى سجل العمليات مع لون مميز"""
        timestamp = QDateTime.currentDateTime().toString("hh:mm:ss")

        # تحديد رمز الحالة
        status_icon = {
            "info": "ℹ️",
            "success": "✅",
            "error": "❌",
            "warning": "⚠️",
            "progress": "🔄"
        }.get(status, "ℹ️")

        self.log_text.append(f"{status_icon} {timestamp} - {message}")

    def create_arrivals_departures_table(self, cursor):
        """إنشاء جدول سجلات الوافدين والمغادرين بالهيكل المطلوب إذا لم يكن موجوداً"""
        try:
            # التحقق من وجود الجدول أولاً
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='سجلات_الوافدين_والمغادرين'")
            table_exists = cursor.fetchone()
            
            if table_exists:
                self.log("✅ جدول سجلات_الوافدين_والمغادرين موجود مسبقاً، سيتم إضافة البيانات الجديدة", "info")
                
                # التحقق من وجود جميع الأعمدة المطلوبة وإضافة المفقود منها
                cursor.execute("PRAGMA table_info(سجلات_الوافدين_والمغادرين)")
                existing_columns = [col[1] for col in cursor.fetchall()]
                
                required_columns = [
                    'رقم_التلميذ', 'النسب', 'الإسم', 'تاريخ_التحويل', 'نوع_التحويل',
                    'مؤسسة_الإستقبال', 'المؤسسة_الأصلية', 'المديرية_الإقليمية_الأصلية',
                    'الأكاديمية_الأصلية', 'المستوى', 'السنة_الدراسية', 'ملاحظات',
                    'اسم_الملف', 'تاريخ_الاستيراد'
                ]
                
                # إضافة الأعمدة المفقودة
                columns_added = 0
                for column in required_columns:
                    if column not in existing_columns:
                        try:
                            cursor.execute(f"ALTER TABLE سجلات_الوافدين_والمغادرين ADD COLUMN {column} TEXT")
                            self.log(f"تم إضافة العمود المفقود: {column}", "success")
                            columns_added += 1
                        except Exception as e:
                            self.log(f"خطأ في إضافة العمود {column}: {str(e)}", "warning")
                
                if columns_added > 0:
                    self.log(f"تم إضافة {columns_added} عمود مفقود للجدول", "success")
                else:
                    self.log("جميع الأعمدة المطلوبة موجودة", "info")
                
                # التحقق من وجود القيد الفريد وإضافته إذا لم يكن موجوداً
                try:
                    cursor.execute("""
                        SELECT name FROM sqlite_master 
                        WHERE type='index' AND name='unique_student_transfer'
                    """)
                    unique_constraint_exists = cursor.fetchone()
                    
                    if not unique_constraint_exists:
                        cursor.execute("""
                            CREATE UNIQUE INDEX unique_student_transfer 
                            ON سجلات_الوافدين_والمغادرين(رقم_التلميذ, تاريخ_التحويل, ملاحظات)
                        """)
                        self.log("تم إضافة القيد الفريد لمنع تكرار البيانات", "success")
                    else:
                        self.log("القيد الفريد موجود مسبقاً", "info")
                        
                except Exception as e:
                    self.log(f"خطأ في إضافة القيد الفريد: {str(e)}", "warning")
                
                return
            
            # إذا لم يكن الجدول موجوداً، قم بإنشائه
            self.log("إنشاء جدول سجلات_الوافدين_والمغادرين جديد...", "progress")
            
            # إنشاء الجدول الجديد بالهيكل الكامل مع القيد الفريد
            cursor.execute("""
                CREATE TABLE سجلات_الوافدين_والمغادرين (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    رقم_التلميذ TEXT,
                    النسب TEXT,
                    الإسم TEXT,
                    تاريخ_التحويل TEXT,
                    نوع_التحويل TEXT,
                    مؤسسة_الإستقبال TEXT,
                    المؤسسة_الأصلية TEXT,
                    المديرية_الإقليمية_الأصلية TEXT,
                    الأكاديمية_الأصلية TEXT,
                    المستوى TEXT,
                    السنة_الدراسية TEXT,
                    ملاحظات TEXT,
                    اسم_الملف TEXT,
                    تاريخ_الاستيراد TEXT,
                    UNIQUE(رقم_التلميذ, تاريخ_التحويل, ملاحظات)
                )
            """)
            
            # إنشاء فهارس للبحث السريع
            cursor.execute("""
                CREATE INDEX IF NOT EXISTS idx_arrivals_student_id 
                ON سجلات_الوافدين_والمغادرين(رقم_التلميذ)
            """)
            
            cursor.execute("""
                CREATE INDEX IF NOT EXISTS idx_arrivals_date 
                ON سجلات_الوافدين_والمغادرين(تاريخ_التحويل)
            """)
            
            cursor.execute("""
                CREATE INDEX IF NOT EXISTS idx_arrivals_type 
                ON سجلات_الوافدين_والمغادرين(نوع_التحويل)
            """)
            
            cursor.execute("""
                CREATE INDEX IF NOT EXISTS idx_arrivals_name 
                ON سجلات_الوافدين_والمغادرين(الإسم)
            """)
            
            cursor.execute("""
                CREATE INDEX IF NOT EXISTS idx_arrivals_year 
                ON سجلات_الوافدين_والمغادرين(السنة_الدراسية)
            """)
            
            cursor.execute("""
                CREATE INDEX IF NOT EXISTS idx_arrivals_level 
                ON سجلات_الوافدين_والمغادرين(المستوى)
            """)
            
            self.log("تم إنشاء جدول سجلات_الوافدين_والمغادرين الجديد بالقيد الفريد بنجاح", "success")
            
        except Exception as e:
            raise Exception(f"خطأ في إنشاء/تحديث جدول سجلات_الوافدين_والمغادرين: {str(e)}")

    def extract_special_fields(self, df):
        """استخراج السنة الدراسية والملاحظات والمستوى من مواقع محددة"""
        سنة_دراسية = ""
        ملاحظات = ""
        مستوى = ""
        
        try:
            # استخراج السنة الدراسية من العمود G الصف 7 (الفهرس 6)
            if len(df) > 6 and len(df.columns) > 6:
                سنة_دراسية_value = df.iloc[6, 6]  # الصف 7، العمود G
                if pd.notna(سنة_دراسية_value):
                    سنة_دراسية = str(سنة_دراسية_value).strip()
                    self.log(f"تم استخراج السنة الدراسية: {سنة_دراسية}", "success")
        except Exception as e:
            self.log(f"خطأ في استخراج السنة الدراسية: {str(e)}", "warning")
        
        try:
            # استخراج الملاحظات من العمود D الصف 2 (الفهرس 1)
            if len(df) > 1 and len(df.columns) > 3:
                ملاحظات_value = df.iloc[1, 3]  # الصف 2، العمود D
                if pd.notna(ملاحظات_value):
                    ملاحظات = str(ملاحظات_value).strip()
                    self.log(f"تم استخراج الملاحظات: {ملاحظات}", "success")
        except Exception as e:
            self.log(f"خطأ في استخراج الملاحظات: {str(e)}", "warning")
        
        try:
            # استخراج المستوى من العمود C الصف 7 (الفهرس 6، العمود 2)
            if len(df) > 6 and len(df.columns) > 2:
                مستوى_value = df.iloc[6, 2]  # الصف 7، العمود C
                if pd.notna(مستوى_value):
                    مستوى = str(مستوى_value).strip()
                    self.log(f"تم استخراج المستوى: {مستوى}", "success")
        except Exception as e:
            self.log(f"خطأ في استخراج المستوى: {str(e)}", "warning")
        
        return سنة_دراسية, ملاحظات, مستوى

    def identify_columns(self, columns, ملاحظات=""):
        """تحديد الأعمدة المطلوبة من أسماء الأعمدة الموجودة في ملف إكسل مع مراعاة نوع اللائحة"""
        self.log(f"الأعمدة الموجودة في الملف: {list(columns)}", "info")
        self.log(f"نوع اللائحة: {ملاحظات}", "info")
        
        available_columns = list(columns)
        if len(available_columns) == 0:
            self.log("لا توجد أعمدة في الملف", "error")
            return None
        
        # استخدام الأعمدة من A إلى I مباشرة (الفهارس 0-8)
        column_mapping = {}
        
        if len(available_columns) >= 9:
            self.log("استخدام تخطيط الأعمدة من A إلى I", "info")
            
            # تحديد ترتيب الأعمدة حسب نوع اللائحة
            if "المغادرون" in ملاحظات:
                # في حالة المغادرون: مؤسسة_الإستقبال = G، المؤسسة_الأصلية = F
                self.log("🔄 تطبيق ترتيب أعمدة لائحة المغادرون", "info")
                column_mapping = {
                    'رقم_التلميذ': available_columns[0],               # العمود A
                    'النسب': available_columns[1],                    # العمود B
                    'الإسم': available_columns[2],                    # العمود C
                    'تاريخ_التحويل': available_columns[3],            # العمود D
                    'نوع_التحويل': available_columns[4],             # العمود E
                    'المؤسسة_الأصلية': available_columns[5],          # العمود F
                    'مؤسسة_الإستقبال': available_columns[6],         # العمود G
                    'المديرية_الإقليمية_الأصلية': available_columns[7], # العمود H
                    'الأكاديمية_الأصلية': available_columns[8],       # العمود I
                }
            else:
                # في حالة الوافدون أو أي حالة أخرى: مؤسسة_الإستقبال = ف، المؤسسة_الأصلية = G
                self.log("🔄 تطبيق ترتيب أعمدة لائحة الوافدون", "info")
                column_mapping = {
                    'رقم_التلميذ': available_columns[0],               # العمود A
                    'النسب': available_columns[1],                    # العمود B
                    'الإسم': available_columns[2],                    # العمود C
                    'تاريخ_التحويل': available_columns[3],            # العمود D
                    'نوع_التحويل': available_columns[4],             # العمود E
                    'مؤسسة_الإستقبال': available_columns[5],         # العمود ف
                    'المؤسسة_الأصلية': available_columns[6],          # العمود G
                    'المديرية_الإقليمية_الأصلية': available_columns[7], # العمود H
                    'الأكاديمية_الأصلية': available_columns[8],       # العمود I
                }
        else:
            self.log(f"عدد الأعمدة غير كافي. المطلوب: 9 أعمدة، الموجود: {len(available_columns)}", "error")
            
            # محاولة البحث الذكي كما كان من قبل
            for col in available_columns:
                col_lower = str(col).lower().strip()
                
                # رقم التلميذ
                if not column_mapping.get('رقم_التلميذ'):
                    if any(keyword in col_lower for keyword in ['رقم التلميذ', 'رقم_التلميذ', 'id']):
                        column_mapping['رقم_التلميذ'] = col
                        
                # النسب
                if not column_mapping.get('النسب'):
                    if any(keyword in col_lower for keyword in ['النسب', 'نسب']):
                        column_mapping['النسب'] = col
                
                # الإسم
                if not column_mapping.get('الإسم'):
                    if any(keyword in col_lower for keyword in ['إسم', 'اسم', 'الإسم', 'الاسم', 'name']):
                        column_mapping['الإسم'] = col
                
                # تاريخ التحويل
                if not column_mapping.get('تاريخ_التحويل'):
                    if any(keyword in col_lower for keyword in ['تاريخ التحويل', 'تاريخ_التحويل', 'تاريخ', 'date']):
                        column_mapping['تاريخ_التحويل'] = col
                
                # نوع التحويل
                if not column_mapping.get('نوع_التحويل'):
                    if any(keyword in col_lower for keyword in ['نوع التحويل', 'نوع_التحويل', 'النوع', 'نوع', 'type']):
                        column_mapping['نوع_التحويل'] = col
                        
                # مؤسسة الإستقبال
                if not column_mapping.get('مؤسسة_الإستقبال'):
                    if any(keyword in col_lower for keyword in ['مؤسسة الإستقبال', 'مؤسسة_الإستقبال', 'مؤسسة الاستقبال']):
                        column_mapping['مؤسسة_الإستقبال'] = col
                        
                # المؤسسة الأصلية
                if not column_mapping.get('المؤسسة_الأصلية'):
                    if any(keyword in col_lower for keyword in ['المؤسسة الأصلية', 'المؤسسة_الأصلية', 'مؤسسة أصلية']):
                        column_mapping['المؤسسة_الأصلية'] = col
                        
                # المديرية الإقليمية الأصلية
                if not column_mapping.get('المديرية_الإقليمية_الأصلية'):
                    if any(keyword in col_lower for keyword in ['م. الإقليمية الأصلية', 'المديرية الإقليمية', 'مديرية إقليمية']):
                        column_mapping['المديرية_الإقليمية_الأصلية'] = col
                        
                # الأكاديمية الأصلية
                if not column_mapping.get('الأكاديمية_الأصلية'):
                    if any(keyword in col_lower for keyword in ['الأكاديمية الأصلية', 'الأكاديمية_الأصلية', 'أكاديمية']):
                        column_mapping['الأكاديمية_الأصلية'] = col

        self.log("تم تحديد الأعمدة كالتالي:", "info")
        excel_columns = ['A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I']
        field_names = ['رقم_التلميذ', 'النسب', 'الإسم', 'تاريخ_التحويل', 'نوع_التحويل', 
                      'مؤسسة_الإستقبال', 'المؤسسة_الأصلية', 'المديرية_الإقليمية_الأصلية', 'الأكاديمية_الأصلية']
        
        for key, value in column_mapping.items():
            # العثور على فهرس العمود لعرض حرف Excel المقابل
            try:
                col_index = available_columns.index(value)
                if col_index < len(excel_columns):
                    self.log(f"• العمود {excel_columns[col_index]} ({key}): {value}", "info")
            except:
                self.log(f"• {key}: {value}", "info")
        
        return column_mapping

    def prepare_records_data(self, df, column_mapping, file_name, سنة_دراسية, ملاحظات, مستوى):
        """تحضير البيانات للإدراج في قاعدة البيانات بدءاً من الصف 11"""
        records_data = []
        
        # البدء من الصف 11 (الفهرس 10 في pandas)
        start_row = 10
        df_subset = df.iloc[start_row:]
        
        self.log(f"عدد الصفوف الكلي في الملف: {len(df)}", "info")
        self.log(f"البدء من الصف {start_row + 1}، عدد الصفوف المعالجة: {len(df_subset)}", "info")
        
        for index, row in df_subset.iterrows():
            try:
                # استخراج البيانات من الأعمدة
                رقم_التلميذ = str(row[column_mapping.get('رقم_التلميذ', '')]).strip() if column_mapping.get('رقم_التلميذ') and pd.notna(row[column_mapping.get('رقم_التلميذ', '')]) else ""
                النسب = str(row[column_mapping.get('النسب', '')]).strip() if column_mapping.get('النسب') and pd.notna(row[column_mapping.get('النسب', '')]) else ""
                الإسم = str(row[column_mapping.get('الإسم', '')]).strip() if column_mapping.get('الإسم') and pd.notna(row[column_mapping.get('الإسم', '')]) else ""
                
                # معالجة تاريخ التحويل
                تاريخ_التحويل = ""
                if column_mapping.get('تاريخ_التحويل'):
                    date_value = row[column_mapping.get('تاريخ_التحويل')]
                    if pd.notna(date_value):
                        if isinstance(date_value, str):
                            تاريخ_التحويل = date_value.strip()
                        else:
                            try:
                                تاريخ_التحويل = pd.to_datetime(date_value).strftime('%Y-%m-%d')
                            except:
                                تاريخ_التحويل = str(date_value).strip()
                
                نوع_التحويل = str(row[column_mapping.get('نوع_التحويل', '')]).strip() if column_mapping.get('نوع_التحويل') and pd.notna(row[column_mapping.get('نوع_التحويل', '')]) else ""
                مؤسسة_الإستقبال = str(row[column_mapping.get('مؤسسة_الإستقبال', '')]).strip() if column_mapping.get('مؤسسة_الإستقبال') and pd.notna(row[column_mapping.get('مؤسسة_الإستقبال', '')]) else ""
                المؤسسة_الأصلية = str(row[column_mapping.get('المؤسسة_الأصلية', '')]).strip() if column_mapping.get('المؤسسة_الأصلية') and pd.notna(row[column_mapping.get('المؤسسة_الأصلية', '')]) else ""
                المديرية_الإقليمية_الأصلية = str(row[column_mapping.get('المديرية_الإقليمية_الأصلية', '')]).strip() if column_mapping.get('المديرية_الإقليمية_الأصلية') and pd.notna(row[column_mapping.get('المديرية_الإقليمية_الأصلية', '')]) else ""
                الأكاديمية_الأصلية = str(row[column_mapping.get('الأكاديمية_الأصلية', '')]).strip() if column_mapping.get('الأكاديمية_الأصلية') and pd.notna(row[column_mapping.get('الأكاديمية_الأصلية', '')]) else ""
                
                # استخدام المستوى المستخرج من العمود C الصف 7 (تم تمريره كمعامل)
                # مستوى = المستوى المستخرج مسبقاً من extract_special_fields
                
                # تنظيف البيانات
                رقم_التلميذ = re.sub(r'[^\w\d]', '', رقم_التلميذ)
                النسب = النسب.replace('\n', '').replace('\r', '').strip()
                الإسم = الإسم.replace('\n', '').replace('\r', '').strip()
                نوع_التحويل = نوع_التحويل.replace('\n', '').replace('\r', '').strip()
                
                # التحقق من وجود البيانات الأساسية على الأقل
                if الإسم and len(الإسم) > 1:
                    records_data.append((
                        رقم_التلميذ,
                        النسب,
                        الإسم,
                        تاريخ_التحويل,
                        نوع_التحويل,
                        مؤسسة_الإستقبال,
                        المؤسسة_الأصلية,
                        المديرية_الإقليمية_الأصلية,
                        الأكاديمية_الأصلية,
                        مستوى,
                        سنة_دراسية,
                        ملاحظات,
                        file_name
                    ))
                else:
                    # تسجيل السجلات المرفوضة
                    if index < start_row + 10:  # عرض أول 10 سجلات مرفوضة فقط لتجنب الإزعاج
                        self.log(f"تم تجاهل الصف {index + 1}: بيانات غير كافية", "warning")
            
            except Exception as row_error:
                if index < start_row + 10:  # عرض أول 10 أخطاء فقط
                    self.log(f"خطأ في معالجة الصف {index + 1}: {str(row_error)}", "warning")
                continue
        
        self.log(f"تم تحضير {len(records_data)} سجل صالح من أصل {len(df_subset)} صف", "success")
        return records_data

    def update_academies_data(self, cursor):
        """تحديث بيانات الأكاديميات بناءً على المديريات الإقليمية"""
        try:
            self.log("🔄 بدء عملية تحديث بيانات الأكاديميات...", "progress")
            
            # التحقق من وجود جدول الأكاديميات
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='الأكاديميات'")
            if not cursor.fetchone():
                self.log("⚠️ لم يتم العثور على جدول الأكاديميات. تم تخطي عملية التحديث.", "warning")
                return
            
            # التحقق من وجود الأعمدة المطلوبة في جدول الأكاديميات
            cursor.execute("PRAGMA table_info(الأكاديميات)")
            columns_info = cursor.fetchall()
            column_names = [col[1] for col in columns_info]
            
            if 'الأكاديمية' not in column_names or 'المديرية' not in column_names:
                self.log("❌ جدول الأكاديميات لا يحتوي على الأعمدة المطلوبة (الأكاديمية، المديرية)", "error")
                return
            
            # عرض إحصائيات قبل التحديث
            cursor.execute("""
                SELECT COUNT(*) FROM سجلات_الوافدين_والمغادرين 
                WHERE (الأكاديمية_الأصلية IS NULL OR الأكاديمية_الأصلية = '')
                AND (المديرية_الإقليمية_الأصلية IS NOT NULL AND المديرية_الإقليمية_الأصلية != '')
            """)
            records_to_update = cursor.fetchone()[0]
            
            if records_to_update == 0:
                self.log("ℹ️ لا توجد سجلات تحتاج لتحديث الأكاديمية", "info")
                return
            
            self.log(f"📊 عدد السجلات التي تحتاج لتحديث: {records_to_update}", "info")
            
            # تنفيذ عملية التحديث
            update_query = """
                UPDATE سجلات_الوافدين_والمغادرين 
                SET الأكاديمية_الأصلية = (
                    SELECT الأكاديمية 
                    FROM الأكاديميات 
                    WHERE الأكاديميات.المديرية = سجلات_الوافدين_والمغادرين.المديرية_الإقليمية_الأصلية
                    LIMIT 1
                )
                WHERE (الأكاديمية_الأصلية IS NULL OR الأكاديمية_الأصلية = '')
                AND (المديرية_الإقليمية_الأصلية IS NOT NULL AND المديرية_الإقليمية_الأصلية != '')
                AND EXISTS (
                    SELECT 1 FROM الأكاديميات 
                    WHERE الأكاديميات.المديرية = سجلات_الوافدين_والمغادرين.المديرية_الإقليمية_الأصلية
                )
            """
            
            cursor.execute(update_query)
            updated_rows = cursor.rowcount
            
            # عرض نتائج التحديث
            if updated_rows > 0:
                self.log(f"✅ تم تحديث {updated_rows} سجل بنجاح", "success")
                
                # عرض إحصائيات مفصلة
                cursor.execute("""
                    SELECT المديرية_الإقليمية_الأصلية, الأكاديمية_الأصلية, COUNT(*) as عدد_السجلات
                    FROM سجلات_الوافدين_والمغادرين 
                    WHERE الأكاديمية_الأصلية IS NOT NULL AND الأكاديمية_الأصلية != ''
                    GROUP BY المديرية_الإقليمية_الأصلية, الأكاديمية_الأصلية
                    ORDER BY عدد_السجلات DESC
                    LIMIT 10
                """)
                
                updated_stats = cursor.fetchall()
                if updated_stats:
                    self.log("📋 أهم التحديثات:", "info")
                    for مديرية, أكاديمية, عدد in updated_stats:
                        self.log(f"   • {مديرية} ← {أكاديمية}: {عدد} سجل", "info")
            else:
                self.log("⚠️ لم يتم تحديث أي سجل. تحقق من توافق أسماء المديريات", "warning")
                
                # عرض المديريات غير المطابقة
                cursor.execute("""
                    SELECT DISTINCT المديرية_الإقليمية_الأصلية
                    FROM سجلات_الوافدين_والمغادرين 
                    WHERE (الأكاديمية_الأصلية IS NULL OR الأكاديمية_الأصلية = '')
                    AND (المديرية_الإقليمية_الأصلية IS NOT NULL AND المديرية_الإقليمية_الأصلية != '')
                    AND المديرية_الإقليمية_الأصلية NOT IN (
                        SELECT المديرية FROM الأكاديميات WHERE المديرية IS NOT NULL
                    )
                    LIMIT 5
                """)
                
                unmatched_dirs = cursor.fetchall()
                if unmatched_dirs:
                    self.log("❌ مديريات غير موجودة في جدول الأكاديميات:", "warning")
                    for (مديرية,) in unmatched_dirs:
                        self.log(f"   • {مديرية}", "warning")
            
        except Exception as e:
            self.log(f"❌ خطأ في تحديث بيانات الأكاديميات: {str(e)}", "error")

class Sub001Window(QWidget):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("استيراد بيانات الوافدين والمغادرين")
        self.setFixedSize(1200, 600)
        self.setLayoutDirection(Qt.RightToLeft)

        # إضافة أيقونة البرنامج إلى النافذة
        icon_path = "01.ico"
        if os.path.exists(icon_path):
            self.setWindowIcon(QIcon(icon_path))

        # تعريف مسار قاعدة البيانات
        self.db_path = "data.db"
        self.pandas_available = PANDAS_AVAILABLE

        # التخطيط الرئيسي
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(20, 20, 20, 20)

        # إنشاء إطار رئيسي مع تأثير الظل
        content_frame = QFrame()
        content_frame.setFrameShape(QFrame.Box)
        content_frame.setFrameShadow(QFrame.Raised)
        content_frame.setStyleSheet("""
            QFrame {
                background-color: white;
                border-radius: 10px;
                border: 1px solid #bdc3c7;
            }
        """)

        # إضافة تأثير الظل
        shadow = QGraphicsDropShadowEffect()
        shadow.setBlurRadius(15)
        shadow.setXOffset(5)
        shadow.setYOffset(5)
        shadow.setColor(QColor(0, 0, 0, 150))
        content_frame.setGraphicsEffect(shadow)

        # تخطيط المحتوى داخل الإطار
        frame_layout = QVBoxLayout(content_frame)
        frame_layout.setContentsMargins(15, 15, 15, 15)
        frame_layout.setSpacing(15)

        # زر استيراد البيانات
        import_button = QPushButton("📁 استيراد بيانات الوافدين والمغادرين من ملفات Excel")
        import_button.setFont(QFont('Calibri', 14, QFont.Bold))
        import_button.setStyleSheet("""
            QPushButton {
                background-color: #3498db;
                color: white;
                border-radius: 8px;
                padding: 15px;
                min-height: 50px;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
            QPushButton:pressed {
                background-color: #21618c;
            }
        """)
        import_button.clicked.connect(self.import_arrivals_departures_data)
        frame_layout.addWidget(import_button)

        # مربع السجلات
        self.log_text = QTextEdit()
        self.log_text.setReadOnly(True)
        self.log_text.setFont(QFont('Calibri', 11))
        self.log_text.setMinimumHeight(450)
        self.log_text.setStyleSheet("""
            QTextEdit {
                background-color: #f8f9fa;
                border: 2px solid #dee2e6;
                border-radius: 5px;
                padding: 10px;
            }
        """)
        frame_layout.addWidget(self.log_text)

        # إنشاء تخطيط أفقي للأزرار في الأسفل
        bottom_buttons_layout = QHBoxLayout()
        bottom_buttons_layout.setSpacing(10)

        # زر تحديث البيانات
        refresh_button = QPushButton("🔄 تحديث البيانات")
        refresh_button.setFont(QFont('Calibri', 12, QFont.Bold))
        refresh_button.setStyleSheet("""
            QPushButton {
                background-color: #2ecc71;
                color: white;
                border-radius: 5px;
                padding: 8px;
                margin-top: 5px;
            }
            QPushButton:hover {
                background-color: #27ae60;
            }
        """)
        refresh_button.clicked.connect(self.manual_refresh)
        bottom_buttons_layout.addWidget(refresh_button)

        # زر إغلاق النافذة
        close_button = QPushButton("❌ إغلاق النافذة")
        close_button.setFont(QFont('Calibri', 12, QFont.Bold))
        close_button.setStyleSheet("""
            QPushButton {
                background-color: #e74c3c;
                color: white;
                border-radius: 5px;
                padding: 8px;
                margin-top: 5px;
            }
            QPushButton:hover {
                background-color: #c0392b;
            }
        """)
        close_button.clicked.connect(self.close)
        bottom_buttons_layout.addWidget(close_button)

        frame_layout.addLayout(bottom_buttons_layout)
        main_layout.addWidget(content_frame)

        # إنشاء كائن المستورد
        self.importer = ArrivalsImporter(self.log_text, self)

        # رسالة ترحيبية
        self.importer.log("أهلاً بك في نافذة استيراد بيانات الوافدين والمغادرين", "info")
        self.importer.log("سيتم استيراد البيانات من الصف 11 فما فوق", "info")
        self.importer.log("المستوى: العمود C الصف 7 | السنة الدراسية: العمود G الصف 7 | الملاحظات: العمود D الصف 2", "info")
        self.importer.log("اضغط على الزر أعلاه لاختيار ملفات Excel للاستيراد", "info")

    def import_arrivals_departures_data(self):
        """استيراد بيانات الوافدين والمغادرين من ملفات إكسل متعددة"""
        # التحقق من توفر pandas
        if not PANDAS_AVAILABLE:
            self.show_pandas_not_installed()
            return

        # مسح مربع السجلات قبل البدء بالاستيراد
        self.log_text.clear()
        self.importer.log("بدء عملية استيراد بيانات الوافدين والمغادرين", "info")
        self.importer.log("سيتم إضافة البيانات الجديدة للجدول الموجود (بدون حذف البيانات السابقة)", "info")
        self.importer.log("سيتم البدء من الصف 11 في كل ملف", "info")

        try:
            # فتح حوار اختيار ملفات متعددة
            file_paths, _ = QFileDialog.getOpenFileNames(
                self,
                "اختر ملفات Excel للوافدين والمغادرين (يمكن اختيار عدة ملفات)",
                os.path.expanduser("~"),
                "Excel Files (*.xlsx *.xls);;All Files (*.*)"
            )

            if not file_paths:
                self.importer.log("تم إلغاء عملية الاستيراد", "warning")
                return

            self.importer.log(f"تم اختيار {len(file_paths)} ملف للاستيراد", "success")

        except Exception as dialog_error:
            self.importer.log(f"خطأ في فتح متصفح الملفات: {str(dialog_error)}", "error")
            return

        conn = None
        try:
            # فتح اتصال بقاعدة البيانات
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # التحقق من الجدول وإنشاؤه إذا لزم الأمر (بدون حذف البيانات الموجودة)
            self.importer.create_arrivals_departures_table(cursor)

            # عرض إحصائيات الجدول قبل الاستيراد
            cursor.execute("SELECT COUNT(*) FROM سجلات_الوافدين_والمغادرين")
            records_before = cursor.fetchone()[0]
            if records_before > 0:
                self.importer.log(f"📊 عدد السجلات الموجودة في الجدول قبل الاستيراد: {records_before}", "info")

            # إنشاء نافذة شريط التقدم
            progress_dialog = QProgressDialog("جاري استيراد بيانات الوافدين والمغادرين...", None, 0, 100, self)
            progress_dialog.setWindowTitle("استيراد بيانات الوافدين والمغادرين")
            progress_dialog.setMinimumDuration(0)
            progress_dialog.setWindowModality(Qt.WindowModal)
            progress_dialog.setCancelButton(None)
            progress_dialog.show()

            # عداد إجمالي السجلات المضافة
            total_records_imported = 0
            successful_files = 0

            # معالجة كل ملف على حدة
            for file_index, file_path in enumerate(file_paths):
                try:
                    file_name = os.path.basename(file_path)
                    progress_percent = int((file_index / len(file_paths)) * 90)
                    progress_dialog.setValue(progress_percent)
                    progress_dialog.setLabelText(f"معالجة الملف {file_index + 1}/{len(file_paths)}: {file_name}")
                    QApplication.processEvents()

                    self.importer.log(f"جاري معالجة الملف: {file_name}", "progress")

                    # التحقق من عدم استيراد نفس الملف مسبقاً
                    cursor.execute("SELECT COUNT(*) FROM سجلات_الوافدين_والمغادرين WHERE اسم_الملف = ?", (file_name,))
                    existing_records = cursor.fetchone()[0]
                    
                    if existing_records > 0:
                        self.importer.log(f"⚠️ الملف {file_name} موجود مسبقاً ({existing_records} سجل). هل تريد المتابعة؟", "warning")
                        # يمكن إضافة نافذة تأكيد هنا إذا أردت

                    # قراءة ملف Excel
                    df = pd.read_excel(file_path, header=None)

                    if df.empty:
                        self.importer.log(f"الملف {file_name} فارغ. تم تخطيه.", "warning")
                        continue

                    # استخدام الصف 0 كعناوين للأعمدة أو إنشاء أسماء افتراضية
                    if len(df) > 0:
                        # إنشاء أسماء أعمدة افتراضية من A إلى I
                        excel_column_names = ['A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I']
                        if len(df.columns) >= 9:
                            df.columns = excel_column_names[:len(df.columns)]
                        else:
                            # إضافة أعمدة فارغة إذا كان العدد أقل من 9
                            for i in range(len(df.columns), 9):
                                df[excel_column_names[i]] = ""
                            df.columns = excel_column_names

                    self.importer.log(f"أعمدة الملف بعد التنسيق: {list(df.columns)}", "info")

                    # استخراج السنة الدراسية والملاحظات والمستوى
                    سنة_دراسية, ملاحظات, مستوى = self.importer.extract_special_fields(df)

                    # تحديد الأعمدة مع تمرير الملاحظات لتحديد نوع اللائحة
                    column_mapping = self.importer.identify_columns(df.columns, ملاحظات)

                    if not column_mapping:
                        self.importer.log(f"لا يمكن تحديد الأعمدة في الملف {file_name}. تم تخطيه.", "warning")
                        continue

                    # تحضير البيانات (بدءاً من الصف 11)
                    records_data = self.importer.prepare_records_data(df, column_mapping, file_name, سنة_دراسية, ملاحظات, مستوى)

                    # إدراج البيانات في قاعدة البيانات
                    if records_data:
                        # استخدام INSERT OR IGNORE لتجنب إدراج البيانات المكررة
                        inserted_count = 0
                        duplicate_count = 0
                        
                        for record in records_data:
                            try:
                                cursor.execute("""
                                    INSERT OR IGNORE INTO سجلات_الوافدين_والمغادرين 
                                    (رقم_التلميذ, النسب, الإسم, تاريخ_التحويل, نوع_التحويل, 
                                     مؤسسة_الإستقبال, المؤسسة_الأصلية, المديرية_الإقليمية_الأصلية, 
                                     الأكاديمية_الأصلية, المستوى, السنة_الدراسية, ملاحظات, اسم_الملف, تاريخ_الاستيراد)
                                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, datetime('now', 'localtime'))
                                """, record)
                                
                                if cursor.rowcount > 0:
                                    inserted_count += 1
                                else:
                                    duplicate_count += 1
                                    
                            except Exception as insert_error:
                                self.importer.log(f"خطأ في إدراج سجل: {str(insert_error)}", "warning")
                                duplicate_count += 1
                        
                        conn.commit()

                        total_records_imported += inserted_count
                        successful_files += 1
                        
                        if duplicate_count > 0:
                            self.importer.log(f"✅ تم استيراد {inserted_count} سجل جديد من الملف {file_name}", "success")
                            self.importer.log(f"⚠️ تم تجاهل {duplicate_count} سجل مكرر", "warning")
                        else:
                            self.importer.log(f"✅ تم استيراد {inserted_count} سجل من الملف {file_name}", "success")
                    else:
                        self.importer.log(f"❌ لا توجد بيانات صالحة في الملف {file_name}", "warning")

                except Exception as file_error:
                    self.importer.log(f"❌ خطأ في معالجة الملف {file_name}: {str(file_error)}", "error")

            # عرض إحصائيات الجدول بعد الاستيراد
            cursor.execute("SELECT COUNT(*) FROM سجلات_الوافدين_والمغادرين")
            records_after = cursor.fetchone()[0]
            
            # تحديث شريط التقدم - 100%
            progress_dialog.setValue(100)
            progress_dialog.setLabelText("تم الانتهاء من استيراد البيانات!")
            QApplication.processEvents()

            conn.close()
            conn = None

            # عرض ملخص العملية
            self.importer.log("="*60, "info")
            self.importer.log("📊 ملخص عملية الاستيراد:", "info")
            self.importer.log(f"• إجمالي الملفات المعالجة: {len(file_paths)}", "info")
            self.importer.log(f"• الملفات التي تم استيرادها بنجاح: {successful_files}", "info")
            self.importer.log(f"• السجلات المضافة في هذه العملية: {total_records_imported}", "info")
            if records_before > 0:
                self.importer.log(f"• السجلات الموجودة سابقاً: {records_before}", "info")
            self.importer.log(f"• إجمالي السجلات في الجدول الآن: {records_after}", "info")

            if successful_files > 0:
                self.importer.log("✅ تم استيراد بيانات الوافدين والمغادرين بنجاح!", "success")
                
                # تحديث بيانات الأكاديميات بعد الاستيراد
                self.importer.log("="*60, "info")
                self.importer.log("🔄 بدء مرحلة تحديث البيانات...", "progress")
                
                # إعادة فتح الاتصال للتحديث
                conn = sqlite3.connect(self.db_path)
                cursor = conn.cursor()
                
                # تحديث الأكاديميات
                self.importer.update_academies_data(cursor)
                
                # حفظ التغييرات
                conn.commit()
                conn.close()
                conn = None
                
                self.importer.log("="*60, "info")
                self.importer.log("🎉 تم الانتهاء من جميع العمليات بنجاح!", "success")
                
                self.show_message_box(
                    "اكتمل الاستيراد والتحديث",
                    f"تم استيراد {total_records_imported} سجل جديد بنجاح من {successful_files} ملف.\n"
                    f"إجمالي السجلات في الجدول الآن: {records_after}\n"
                    f"كما تم تحديث بيانات الأكاديميات المرتبطة بالمديريات الإقليمية.",
                    QMessageBox.Information
                )
            else:
                self.importer.log("❌ لم يتم استيراد أي بيانات", "error")

        except Exception as e:
            self.importer.log(f"حدث خطأ أثناء استيراد البيانات: {str(e)}", "error")
        finally:
            if conn:
                try:
                    conn.close()
                except:
                    pass

    def show_message_box(self, title, message, icon=QMessageBox.Information):
        """عرض رسالة موحدة بتنسيق محدد"""
        msg_box = QMessageBox(self)
        msg_box.setWindowTitle(title)
        msg_box.setText(message)
        msg_box.setIcon(icon)
        msg_box.exec_()

    def manual_refresh(self):
        """تحديث البيانات يدوياً عند الضغط على زر التحديث"""
        self.importer.log("جاري تحديث البيانات...", "progress")
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='سجلات_الوافدين_والمغادرين'")
            if cursor.fetchone():
                cursor.execute("SELECT COUNT(*) FROM سجلات_الوافدين_والمغادرين")
                total_records = cursor.fetchone()[0]
                
                cursor.execute("SELECT COUNT(DISTINCT اسم_الملف) FROM سجلات_الوافدين_والمغادرين")
                total_files = cursor.fetchone()[0]
                
                # إحصائيات الأكاديميات
                cursor.execute("""
                    SELECT COUNT(*) FROM سجلات_الوافدين_والمغادرين 
                    WHERE الأكاديمية_الأصلية IS NOT NULL AND الأكاديمية_الأصلية != ''
                """)
                records_with_academy = cursor.fetchone()[0]

                self.importer.log(f"📊 إجمالي السجلات: {total_records}", "info")
                self.importer.log(f"📁 عدد الملفات المستوردة: {total_files}", "info")
                self.importer.log(f"🏛️ السجلات التي لها أكاديمية: {records_with_academy}", "info")
                
                # تحديث الأكاديميات يدوياً إذا أراد المستخدم
                if total_records > 0:
                    self.importer.log("🔄 تحديث بيانات الأكاديميات...", "progress")
                    self.importer.update_academies_data(cursor)
                    conn.commit()
                    
            else:
                self.importer.log("⚠️ لم يتم العثور على جدول سجلات_الوافدين_والمغادرين", "warning")

            conn.close()
            self.importer.log("تم تحديث البيانات بنجاح ✨", "success")
            
        except Exception as e:
            self.importer.log(f"خطأ في تحديث البيانات: {str(e)}", "error")

    def show_pandas_not_installed(self):
        """عرض رسالة خطأ عند عدم توفر pandas"""
        self.importer.log("خطأ: مكتبة pandas غير متوفرة", "error")
        self.show_message_box(
            "خطأ: pandas غير متوفرة",
            "مكتبة pandas مطلوبة لاستيراد ملفات Excel.\n\nللتثبيت: pip install pandas",
            QMessageBox.Critical
        )

if __name__ == '__main__':
    app = QApplication(sys.argv)
    window = Sub001Window()
    window.show()
    sys.exit(app.exec_())

from PyQt5.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QFrame, QLabel, 
    QLineEdit, QTextEdit, QPushButton, QTabWidget, QGridLayout,
    QGraphicsDropShadowEffect, QTableWidget
)
from PyQt5.QtGui import QFont, QColor
from PyQt5.QtCore import Qt

STYLES = {
    "frame": """
        QFrame {
            background-color: white;
            border-radius: 10px;
            padding: 15px;
        }
    """,
    "title": """
        QLabel {
            color: #2c3e50;
            padding: 10px;
            background-color: white;
            border-radius: 8px;
        }
    """,
    "tab": """
        QTabWidget::pane {
            border: 1px solid #cccccc;
            border-radius: 8px;
            padding: 10px;
        }
        QTabBar::tab {
            background-color: #f0f0f0;
            color: #2c3e50;
            padding: 8px 20px;
            margin-right: 2px;
            border-top-left-radius: 8px;
            border-top-right-radius: 8px;
        }
        QTabBar::tab:selected {
            background-color: #2c3e50;
            color: white;
        }
    """,
    "input": """
        QLineEdit, QTextEdit {
            padding: 8px;
            border: 1px solid #cccccc;
            border-radius: 5px;
            background-color: #f8f9fa;
            min-height: 25px;
        }
        QLineEdit:read-only {
            background-color: #e9ecef;
        }
    """,
    "button": """
        QPushButton {
            background-color: #2c3e50;
            color: white;
            border: none;
            padding: 8px 20px;
            border-radius: 5px;
            font-weight: bold;
        }
        QPushButton:hover {
            background-color: #34495e;
        }
    """
}

class StudentCardUI(QWidget):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setup_ui()

    def setup_ui(self):
        self.setLayoutDirection(Qt.RightToLeft)
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(20, 20, 20, 20)
        main_layout.setSpacing(15)

        # إطار العنوان مع تنسيق محسن
        header_frame = QFrame()
        header_frame.setStyleSheet(STYLES["frame"])
        header_layout = QHBoxLayout(header_frame)
        
        title_label = QLabel("بطاقة تلميذ(ة)")
        title_label.setFont(QFont("Calibri", 25, QFont.Bold))
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet(STYLES["title"])
        
        # تحسين الظل
        shadow = QGraphicsDropShadowEffect()
        shadow.setBlurRadius(20)
        shadow.setXOffset(0)
        shadow.setYOffset(5)
        shadow.setColor(QColor(0, 0, 0, 50))
        header_frame.setGraphicsEffect(shadow)

        header_layout.addWidget(title_label)
        main_layout.addWidget(header_frame)

        # تحسين مظهر التبويبات
        self.tab_widget = QTabWidget()
        self.tab_widget.setFont(QFont("Calibri", 13))
        self.tab_widget.setStyleSheet(STYLES["tab"])
        self.tab_widget.setLayoutDirection(Qt.RightToLeft)

        # تبويب بيانات التلميذ
        self.tab_student_data = QWidget()
        tab_layout = QVBoxLayout(self.tab_student_data)

        # بيانات شخصية
        personal_group = QFrame()
        personal_layout = QGridLayout(personal_group)
        fields = [
            ("الرمز:", "code_display"),
            ("الاسم والنسب:", "name_display"),
            ("النوع:", "gender_display"),
            ("تاريخ الازدياد:", "birth_date_display"),
            ("مكان الازدياد:", "birth_place_display")
        ]
        for i, (label_text, name) in enumerate(fields):
            label = QLabel(label_text)
            label.setFont(QFont("Calibri", 13, QFont.Bold))
            field = QLineEdit()
            field.setFont(QFont("Calibri", 13))
            field.setReadOnly(True)
            field.setStyleSheet(STYLES["input"])
            setattr(self, name, field)
            personal_layout.addWidget(label, i, 0)
            personal_layout.addWidget(field, i, 1)

        tab_layout.addWidget(personal_group)

        # بيانات التمدرس
        school_group = QFrame()
        school_layout = QGridLayout(school_group)
        fields2 = [
            ("السنة الدراسية:", "school_year_display"),
            ("المستوى:", "level_display"),
            ("القسم:", "class_display"),
            ("رت:", "rt_display")
        ]
        for i, (label_text, name) in enumerate(fields2):
            label = QLabel(label_text)
            label.setFont(QFont("Calibri", 13, QFont.Bold))
            field = QLineEdit()
            field.setFont(QFont("Calibri", 13))
            field.setReadOnly(True)
            field.setStyleSheet(STYLES["input"])
            setattr(self, name, field)
            school_layout.addWidget(label, i, 0)
            school_layout.addWidget(field, i, 1)

        tab_layout.addWidget(school_group)

        self.tab_widget.addTab(self.tab_student_data, "بيانات التلميذ")

        # تبويب بيانات الاتصال
        self.tab_contact = QWidget()
        contact_layout = QGridLayout(self.tab_contact)

        self.phone1_field = QLineEdit()
        self.phone1_field.setStyleSheet(STYLES["input"])
        self.phone2_field = QLineEdit()
        self.phone2_field.setStyleSheet(STYLES["input"])
        self.notes_field = QTextEdit()
        self.notes_field.setStyleSheet(STYLES["input"])
        self.save_contact_btn = QPushButton("حفظ")
        self.save_contact_btn.setStyleSheet(STYLES["button"])

        contact_layout.addWidget(QLabel("الهاتف الأول:"), 0, 0)
        contact_layout.addWidget(self.phone1_field, 0, 1)
        contact_layout.addWidget(QLabel("الهاتف الثاني:"), 1, 0)
        contact_layout.addWidget(self.phone2_field, 1, 1)
        contact_layout.addWidget(QLabel("ملاحظات:"), 2, 0)
        contact_layout.addWidget(self.notes_field, 2, 1)
        contact_layout.addWidget(self.save_contact_btn, 3, 1)

        self.tab_widget.addTab(self.tab_contact, "بيانات الاتصال")

        # إضافة تبويب ورقة الدخول
        self.tab_entry_sheet = QWidget()
        entry_layout = QVBoxLayout(self.tab_entry_sheet)
        
        # إضافة جدول ورقة الدخول
        self.entry_table = QTableWidget()
        self.entry_table.setColumnCount(5)
        self.entry_table.setHorizontalHeaderLabels(["التاريخ", "الوقت", "نوع الورقة", "الملاحظات", "الحالة"])
        self.entry_table.horizontalHeader().setStretchLastSection(True)
        self.entry_table.setFont(QFont("Calibri", 13))
        entry_layout.addWidget(self.entry_table)
        
        # أزرار التحكم
        control_layout = QHBoxLayout()
        
        self.add_entry_btn = QPushButton("إضافة")
        self.delete_entry_btn = QPushButton("حذف")
        self.print_entry_btn = QPushButton("طباعة")
        
        for btn in [self.add_entry_btn, self.delete_entry_btn, self.print_entry_btn]:
            btn.setFont(QFont("Calibri", 13, QFont.Bold))
            btn.setMinimumWidth(100)
            control_layout.addWidget(btn)
        
        entry_layout.addLayout(control_layout)
        
        # إضافة التبويب إلى النافذة الرئيسية
        self.tab_widget.addTab(self.tab_entry_sheet, "ورقة الدخول")

        main_layout.addWidget(self.tab_widget)

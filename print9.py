"""
تقارير PDF مع دعم كامل للغة العربية وخطوط واضحة
"""

import os
import sys
import sqlite3
from datetime import datetime
import subprocess

try:
    from fpdf import FPDF
    from fpdf.enums import XPos, YPos  # Import XPos and YPos
    import arabic_reshaper
    from bidi.algorithm import get_display
except ImportError:
    print("المكتبات المطلوبة غير متوفرة. جاري تثبيتها...")
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "fpdf2", "arabic-reshaper", "python-bidi"])
        from fpdf import FPDF
        from fpdf.enums import XPos, YPos
        import arabic_reshaper
        from bidi.algorithm import get_display
        print("تم تثبيت المكتبات بنجاح!")
    except Exception as e:
        print(f"فشل تثبيت المكتبات: {e}")
        sys.exit(1)

class ArabicPDF(FPDF):
    """فئة مخصصة لإنشاء ملفات PDF مع دعم اللغة العربية"""

    def __init__(self, orientation='P', unit='mm', format='A4'):
        super().__init__(orientation, unit, format)
        self.add_page()
        self.set_auto_page_break(auto=True, margin=15)

        self.fonts_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), "fonts")

        if not os.path.exists(self.fonts_dir):
            os.makedirs(self.fonts_dir)
            print(f"تم إنشاء مجلد الخطوط: {self.fonts_dir}")

        arial_path = os.path.join(self.fonts_dir, "arial.ttf")
        arial_bold_path = os.path.join(self.fonts_dir, "arialbd.ttf")

        self.arial_regular_available = False
        self.arial_bold_available = False

        if os.path.exists(arial_path):
            self.add_font('Arial', '', arial_path)  # Removed uni=True
            self.arial_regular_available = True
        else:
            print(f"خطأ فادح: ملف الخط الأساسي {arial_path} غير موجود. لا يمكن متابعة إنشاء PDF بشكل صحيح.")

        if os.path.exists(arial_bold_path):
            self.add_font('Arial', 'B', arial_bold_path)  # Removed uni=True
            self.arial_bold_available = True
        else:
            print(f"تحذير: ملف الخط العريض {arial_bold_path} غير موجود. سيتم استخدام الخط العادي بدلاً من العريض.")

        if self.arial_regular_available:
            self.set_font('Arial', '', 12)
        else:
            print("تحذير: الخط Arial العادي غير متوفر، يتم الرجوع إلى خط أساسي قد لا يدعم العربية بشكل كامل.")
            self.set_font('Helvetica', '', 12)

    def write_arabic(self, x, y, text, size=12, bold=False):
        """كتابة النصوص العربية في ملف PDF"""
        reshaped_text = arabic_reshaper.reshape(text)
        bidi_text = get_display(reshaped_text)
        self.set_xy(x, y)

        current_style = ''
        if bold and self.arial_bold_available:
            current_style = 'B'
        elif bold and not self.arial_bold_available:
            print(f"تنبيه: طلب خط عريض ولكن ملف الخط العريض لـ Arial غير متوفر. يتم استخدام الخط العادي.")

        if self.arial_regular_available:
            self.set_font('Arial', current_style, size)
        else:
            self.set_font('Helvetica', current_style, size)

        self.cell(0, 10, bidi_text, new_x=XPos.LMARGIN, new_y=YPos.NEXT, align='R')  # Updated ln=True

def download_arabic_fonts():
    """تنزيل الخطوط العربية إذا لم تكن موجودة"""
    fonts_dir = os.path.join(os.path.dirname(__file__), "fonts")
    if not os.path.exists(fonts_dir):
        os.makedirs(fonts_dir)
    arial_path = os.path.join(fonts_dir, "arial.ttf")
    arial_bold_path = os.path.join(fonts_dir, "arialbd.ttf")
    if not os.path.exists(arial_path) or not os.path.exists(arial_bold_path):
        print("تنزيل الخطوط العربية...")
        print(f"يرجى وضع ملفات arial.ttf و arialbd.ttf في المجلد: {fonts_dir}")
        pass

def create_certificates_report(records=None, output_dir=None, file_name=None):
    """إنشاء تقرير الشهادات المدرسية باستخدام FPDF المحسنة"""
    download_arabic_fonts()

    arial_regular_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "fonts", "arial.ttf")
    if not os.path.exists(arial_regular_path):
        print(f"خطأ: ملف الخط الأساسي 'arial.ttf' مفقود في مجلد 'fonts'. لا يمكن إنشاء التقرير.")
        print(f"يرجى وضع ملف arial.ttf في المجلد: {os.path.join(os.path.dirname(os.path.abspath(__file__)), 'fonts')}")
        return None

    pdf = ArabicPDF()
    if not pdf.arial_regular_available:
        print("فشل تهيئة ArabicPDF بسبب عدم توفر الخط الأساسي.")
        return None

    pdf.set_font('Arial', '', 12)
    pdf.write_arabic(10, 10, "تقرير الشهادات المدرسية", bold=True)
    if records:
        for record in records:
            pdf.write_arabic(10, pdf.get_y() + 10, f"الاسم: {record['name']}, الرقم: {record['id']}")

    # استخدام المجلد المحدد إذا تم تمريره، وإلا استخدام مجلد البرنامج
    if output_dir and os.path.exists(output_dir):
        if file_name:
            output_path = os.path.join(output_dir, file_name)
        else:
            output_path = os.path.join(output_dir, "تقرير_الشهادات_المدرسية.pdf")
    else:
        output_path = os.path.join(os.path.dirname(__file__), "تقرير_الشهادات_المدرسية.pdf")

    pdf.output(output_path)
    print(f"تم إنشاء التقرير بنجاح: {output_path}")
    return output_path

def generate_test_data():
    """توليد بيانات تجريبية للاختبار"""
    return [{"id": i, "name": f"طالب {i}"} for i in range(1, 11)]

def print_certificates_requests(parent=None):
    """طباعة سجلات طلبات الشهادات المدرسية - وظيفة متوافقة مع الدالة الأصلية في print9.py"""
    try:
        # إنشاء المجلد الرئيسي على سطح المكتب
        desktop_path = os.path.join(os.path.expanduser("~"), "Desktop")
        main_folder = os.path.join(desktop_path, "تقارير برنامج المعين في الحراسة العامة")
        if not os.path.exists(main_folder):
            os.makedirs(main_folder)

        # إنشاء المجلد الفرعي لتقارير طلبات الشواهد المدرسية
        reports_dir = os.path.join(main_folder, "تقارير طلبات الشواهد المدرسية")
        if not os.path.exists(reports_dir):
            os.makedirs(reports_dir)

        # إنشاء مجلد احتياطي في حالة فشل الوصول إلى سطح المكتب
        backup_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), "طلبات_الشواهد_المدرسية")
        if not os.path.exists(backup_dir):
            os.makedirs(backup_dir)

        # التحقق من إمكانية الكتابة في المجلد على سطح المكتب
        try:
            test_file = os.path.join(reports_dir, "test.txt")
            with open(test_file, 'w') as f:
                f.write("test")
            os.remove(test_file)
        except Exception as e:
            print(f"تعذر الكتابة في مجلد سطح المكتب: {e}")
            reports_dir = backup_dir

        current_datetime = datetime.now().strftime("%Y%m%d_%H%M%S")
        file_path = os.path.join(reports_dir, f"طلبات_الشهادات_المدرسية_{current_datetime}.pdf")
        try:
            db_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "data.db")
            conn = sqlite3.connect(db_path)
            cursor = conn.cursor()

            cursor.execute("""
                SELECT الرقم, القسم, الرمز, الاسم_والنسب,
                       تاريخ_الطلب, تاريخ_التسليم, ملاحظات
                FROM الشهادة_المدرسية
                ORDER BY id ASC
            """)

            records = cursor.fetchall()
            conn.close()

            if not records:
                print("لا توجد سجلات للطباعة")
                return False, file_path, reports_dir
        except Exception as e:
            print(f"خطأ في قراءة قاعدة البيانات: {e}")
            return False, "", ""

        # استخدام المجلد المحدد وإنشاء اسم ملف مناسب
        file_name = f"طلبات_الشهادات_المدرسية_{current_datetime}.pdf"
        output_path = create_certificates_report(records, reports_dir, file_name)

        if output_path and os.path.exists(output_path):
            print(f"تمت طباعة {len(records)} سجل بنجاح")
            print(f"الملف تم حفظه في: {output_path}")

            if parent and hasattr(parent, '__module__') and 'sub19_window' in getattr(parent, '__module__', ''):
                # لا نعرض رسالة نجاح هنا لأن sub19_window سيعرض رسالة خاصة به
                pass
            elif parent:
                try:
                    from print9 import show_print_success_dialog
                    show_print_success_dialog(parent, output_path)
                except ImportError:
                    pass

            return True, output_path, reports_dir
        else:
            print("فشل في إنشاء التقرير")
            return False, "", ""
    except Exception as e:
        print(f"خطأ أثناء طباعة طلبات الشهادات المدرسية: {e}")
        if parent:
            try:
                from PyQt5.QtWidgets import QMessageBox
                QMessageBox.critical(parent, "خطأ", f"حدث خطأ أثناء طباعة طلبات الشهادات المدرسية:\n{str(e)}")
            except ImportError:
                pass
        return False, "", ""

def show_print_success_dialog(parent, output_path):
    """عرض رسالة النجاح بعد طباعة التقرير"""
    try:
        from PyQt5.QtWidgets import QMessageBox
        QMessageBox.information(parent, "نجاح", f"تم إنشاء التقرير بنجاح وحفظه في:\n{output_path}")
    except ImportError:
        pass

if __name__ == "__main__":
    print("===== إنشاء تقرير الشهادات المدرسية باستخدام FPDF المحسنة =====")
    create_certificates_report()

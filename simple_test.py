#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sqlite3

def run_simple_test():
    """
    دالة بسيطة للتحقق من بيانات الطالب H142106728
    """
    try:
        # الاتصال بقاعدة البيانات
        conn = sqlite3.connect("data.db")
        cur = conn.cursor()
        
        print("=== استعلامات بسيطة للطالب H142106728 ===")
        
        # 1. التحقق من القسم الذي ينتمي إليه الطالب
        cur.execute("""
            SELECT رت, الرمز, القسم, السنة_الدراسية
            FROM اللوائح
            WHERE الرمز = 'H142106728'
        """)
        student_section = cur.fetchall()
        print(f"القسم الذي ينتمي إليه الطالب H142106728: {student_section}")
        
        # 2. التحقق من وجود بيانات الغياب للطالب في جدول مسك_الغياب_الأسبوعي
        cur.execute("""
            SELECT id, السنة_الدراسية, الشهر, رمز_التلميذ, "1", "2", "3", "4", "5", مجموع_شهري
            FROM مسك_الغياب_الأسبوعي
            WHERE رمز_التلميذ = 'H142106728'
        """)
        student_absence = cur.fetchall()
        print(f"عدد سجلات الغياب للطالب H142106728 في جدول مسك_الغياب_الأسبوعي: {len(student_absence)}")
        if student_absence:
            print("سجلات الغياب:")
            for record in student_absence:
                print(f"  سجل: {record}")
        
        # إغلاق الاتصال بقاعدة البيانات
        conn.close()
        print("\nتم إغلاق الاتصال بقاعدة البيانات")
        
    except Exception as e:
        print(f"خطأ: {e}")
        import traceback
        traceback.print_exc()
        if 'conn' in locals() and conn:
            conn.close()

if __name__ == "__main__":
    run_simple_test()

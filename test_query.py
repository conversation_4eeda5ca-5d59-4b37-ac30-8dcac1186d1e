#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sqlite3
import os

def run_test_queries():
    """
    دالة لتنفيذ استعلامات تجريبية للتحقق من بيانات الطالب H142106728
    """
    try:
        # الاتصال بقاعدة البيانات
        conn = sqlite3.connect("data.db")
        cur = conn.cursor()
        
        print("=== استعلامات تجريبية للطالب H142106728 ===")
        
        # 1. التحقق من القسم الذي ينتمي إليه الطالب
        cur.execute("""
            SELECT رت, الرمز, القسم, السنة_الدراسية
            FROM اللوائح
            WHERE الرمز = 'H142106728'
        """)
        student_section = cur.fetchall()
        print(f"القسم الذي ينتمي إليه الطالب H142106728: {student_section}")
        
        # 2. التحقق من وجود بيانات الغياب للطالب في جدول مسك_الغياب_الأسبوعي
        cur.execute("""
            SELECT id, السنة_الدراسية, الشهر, رمز_التلميذ, "1", "2", "3", "4", "5", مجموع_شهري
            FROM مسك_الغياب_الأسبوعي
            WHERE رمز_التلميذ = 'H142106728'
        """)
        student_absence = cur.fetchall()
        print(f"عدد سجلات الغياب للطالب H142106728 في جدول مسك_الغياب_الأسبوعي: {len(student_absence)}")
        if student_absence:
            print("عينة من سجلات الغياب:")
            for i, record in enumerate(student_absence[:5]):  # عرض أول 5 سجلات فقط
                print(f"  سجل {i+1}: {record}")
                print(f"    معرف: {record[0]}")
                print(f"    السنة الدراسية: {record[1]}")
                print(f"    الشهر: {record[2]}")
                print(f"    رمز التلميذ: {record[3]}")
                print(f"    الأسبوع 1: {record[4]}")
                print(f"    الأسبوع 2: {record[5]}")
                print(f"    الأسبوع 3: {record[6]}")
                print(f"    الأسبوع 4: {record[7]}")
                print(f"    الأسبوع 5: {record[8]}")
                print(f"    المجموع الشهري: {record[9]}")
        
        # 3. التحقق من وجود بيانات الغياب للطالب في جدول مسك_الغياب_الأسبوعي لشهر شتنبر
        cur.execute("""
            SELECT id, السنة_الدراسية, الشهر, رمز_التلميذ, "1", "2", "3", "4", "5", مجموع_شهري
            FROM مسك_الغياب_الأسبوعي
            WHERE رمز_التلميذ = 'H142106728' AND الشهر = 'شتنبر'
        """)
        student_absence_september = cur.fetchall()
        print(f"عدد سجلات الغياب للطالب H142106728 في جدول مسك_الغياب_الأسبوعي لشهر شتنبر: {len(student_absence_september)}")
        if student_absence_september:
            for record in student_absence_september:
                print(f"  سجل غياب لشهر شتنبر: {record}")
        
        # 4. تجربة استعلام الانضمام المستخدم في دالة print_absence_report
        # الحصول على السنة الدراسية الحالية
        cur.execute("SELECT السنة_الدراسية FROM بيانات_المؤسسة LIMIT 1")
        school_year = cur.fetchone()[0]
        
        # الحصول على القسم من نتائج الاستعلام الأول
        if student_section:
            section = student_section[0][2]  # القسم في الموضع 2
            
            print(f"\n=== تجربة استعلام الانضمام للطالب H142106728 ===")
            print(f"السنة الدراسية: {school_year}, القسم: {section}")
            
            test_query = """
                SELECT l.رت, sg.الاسم_والنسب, l.الرمز,
                       mag."1", mag."2", mag."3", mag."4", mag."5",
                       mag.مجموع_شهري, mag.الغياب_المبرر, mag.ملاحظات, mag.بداية_الشهر
                FROM اللوائح l
                LEFT JOIN مسك_الغياب_الأسبوعي mag ON l.الرمز = mag.رمز_التلميذ
                    AND mag.الشهر = ?
                    AND mag.السنة_الدراسية = ?
                LEFT JOIN السجل_العام sg ON l.الرمز = sg.الرمز
                JOIN بيانات_المؤسسة inst ON l.السنة_الدراسية = inst.السنة_الدراسية
                WHERE l.الرمز = ? AND l.السنة_الدراسية = ?
            """
            
            # تنفيذ الاستعلام لشهر شتنبر
            cur.execute(test_query, ('شتنبر', school_year, 'H142106728', school_year))
            join_result_september = cur.fetchall()
            print(f"نتائج استعلام الانضمام للطالب H142106728 لشهر شتنبر: {len(join_result_september)}")
            if join_result_september:
                for idx, record in enumerate(join_result_september):
                    print(f"  نتيجة {idx+1}:")
                    print(f"    رت: {record[0]}")
                    print(f"    الاسم والنسب: {record[1]}")
                    print(f"    الرمز: {record[2]}")
                    print(f"    الأسبوع 1: {record[3]}, النوع: {type(record[3])}")
                    print(f"    الأسبوع 2: {record[4]}, النوع: {type(record[4])}")
                    print(f"    الأسبوع 3: {record[5]}, النوع: {type(record[5])}")
                    print(f"    الأسبوع 4: {record[6]}, النوع: {type(record[6])}")
                    print(f"    الأسبوع 5: {record[7]}, النوع: {type(record[7])}")
                    print(f"    المجموع الشهري: {record[8]}, النوع: {type(record[8])}")
                    print(f"    الغياب المبرر: {record[9]}, النوع: {type(record[9])}")
                    print(f"    ملاحظات: {record[10]}, النوع: {type(record[10])}")
                    print(f"    بداية الشهر: {record[11]}, النوع: {type(record[11])}")
            
            # تنفيذ الاستعلام لشهر أكتوبر
            cur.execute(test_query, ('أكتوبر', school_year, 'H142106728', school_year))
            join_result_october = cur.fetchall()
            print(f"نتائج استعلام الانضمام للطالب H142106728 لشهر أكتوبر: {len(join_result_october)}")
            if join_result_october:
                for idx, record in enumerate(join_result_october):
                    print(f"  نتيجة {idx+1}:")
                    print(f"    رت: {record[0]}")
                    print(f"    الاسم والنسب: {record[1]}")
                    print(f"    الرمز: {record[2]}")
                    print(f"    الأسبوع 1: {record[3]}, النوع: {type(record[3])}")
                    print(f"    الأسبوع 2: {record[4]}, النوع: {type(record[4])}")
                    print(f"    الأسبوع 3: {record[5]}, النوع: {type(record[5])}")
                    print(f"    الأسبوع 4: {record[6]}, النوع: {type(record[6])}")
                    print(f"    الأسبوع 5: {record[7]}, النوع: {type(record[7])}")
                    print(f"    المجموع الشهري: {record[8]}, النوع: {type(record[8])}")
                    print(f"    الغياب المبرر: {record[9]}, النوع: {type(record[9])}")
                    print(f"    ملاحظات: {record[10]}, النوع: {type(record[10])}")
                    print(f"    بداية الشهر: {record[11]}, النوع: {type(record[11])}")
        
        # إغلاق الاتصال بقاعدة البيانات
        conn.close()
        print("\nتم إغلاق الاتصال بقاعدة البيانات")
        
    except Exception as e:
        print(f"خطأ: {e}")
        import traceback
        traceback.print_exc()
        if conn:
            conn.close()

if __name__ == "__main__":
    run_test_queries()

import sys
import os
import traceback
import logging
import sqlite3
import platform
from datetime import datetime

# استيراد وحدة تهيئة قاعدة البيانات
try:
    from database_initializer import initialize_database
    logging.info("تم استيراد وحدة تهيئة قاعدة البيانات بنجاح")
except ImportError as e:
    logging.error(f"تحذير: لم يتم استيراد وحدة تهيئة قاعدة البيانات: {e}")
    if __debug__:
        print(f"تحذير: لم يتم استيراد وحدة تهيئة قاعدة البيانات: {e}")
from PyQt5.QtWidgets import (QApplication, QDialog, QProgressBar, QLabel,
                           QVBoxLayout, QWidget, QPushButton, QMessageBox,
                           QLineEdit, QGridLayout, QHBoxLayout)
from PyQt5.QtGui import QPixmap, QFont, QColor
from PyQt5.QtCore import Qt, QTimer

# إعداد سجل الأخطاء
log_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'logs')
if not os.path.exists(log_dir):
    try:
        os.makedirs(log_dir)
    except:
        log_dir = os.path.dirname(os.path.abspath(__file__))

log_file = os.path.join(log_dir, f'app_log_{datetime.now().strftime("%Y%m%d_%H%M%S")}.txt')
logging.basicConfig(
    filename=log_file,
    level=logging.DEBUG,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

# تسجيل معلومات النظام
logging.info(f"بدء تشغيل البرنامج")
logging.info(f"نظام التشغيل: {os.name}")
logging.info(f"المجلد الحالي: {os.getcwd()}")
logging.info(f"مسار البرنامج: {os.path.abspath(__file__)}")
logging.info(f"بايثون: {sys.version}")
logging.info(f"محزّم: {getattr(sys, 'frozen', False)}")
logging.info(f"بنية النظام: {sys.maxsize > 2**32 and '64-bit' or '32-bit'}")
if getattr(sys, 'frozen', False):
    logging.info(f"مسار التنفيذي: {sys.executable}")
    logging.info(f"مسار MEIPASS: {getattr(sys, '_MEIPASS', 'غير متوفر')}")

# استيراد وحدة تضمين النوافذ المستقلة
try:
    import embed_all_windows
    logging.info("تم استيراد وحدة تضمين النوافذ المستقلة بنجاح")
    if __debug__:
        print("تم استيراد وحدة تضمين النوافذ المستقلة بنجاح")
except ImportError as e:
    logging.error(f"تحذير: لم يتم استيراد embed_all_windows: {e}")
    if __debug__:
        print(f"تحذير: لم يتم استيراد embed_all_windows: {e}")

# استيراد وحدات المساعدة
try:
    import module_loader
    logging.info("تم استيراد module_loader بنجاح")
    if __debug__:
        print("تم استيراد module_loader بنجاح")
except ImportError as e:
    logging.error(f"تحذير: لم يتم استيراد module_loader: {e}")
    if __debug__:
        print(f"تحذير: لم يتم استيراد module_loader: {e}")

# استيراد معالج مشكلة عدم تطابق المعلمات
try:
    import fix_parameter_mismatch
    logging.info("تم استيراد fix_parameter_mismatch بنجاح")
    if __debug__:
        print("تم استيراد fix_parameter_mismatch بنجاح")
except ImportError as e:
    logging.error(f"تحذير: لم يتم استيراد fix_parameter_mismatch: {e}")
    if __debug__:
        print(f"تحذير: لم يتم استيراد fix_parameter_mismatch: {e}")

# استيراد وحدة النوافذ المستقلة لضمان تضمينها عند التحزيم
try:
    logging.warning("وحدة import_all غير متوفرة. تم تخطي استيرادها.")
    if __debug__:
        print("وحدة import_all غير متوفرة. تم تخطي استيرادها.")
except ImportError as e:
    logging.error(f"تحذير: فشل استيراد النوافذ المستقلة: {e}")
    if __debug__:
        print(f"تحذير: فشل استيراد النوافذ المستقلة: {e}")

try:
    from main_window import MainWindow
    logging.info("تم استيراد MainWindow بنجاح")
except ImportError as e:
    logging.critical(f"خطأ فادح: فشل استيراد MainWindow: {e}")
    print(f"خطأ فادح: فشل استيراد MainWindow: {e}")
    # طباعة رسالة خطأ مباشرة إذا كان البرنامج محزمًا
    if getattr(sys, 'frozen', False):
        import ctypes
        ctypes.windll.user32.MessageBoxW(0, f"خطأ فادح: فشل استيراد MainWindow\n{str(e)}", "خطأ في البرنامج", 0)
    sys.exit(1)

class RegistrationDialog(QDialog):
    """نافذة تسجيل كود التفعيل"""
    def __init__(self, school_data, parent=None):
        super().__init__(parent)
        self.setWindowTitle("تفعيل البرنامج")
        self.setFixedSize(500, 300)  # زيادة حجم النافذة
        self.setWindowFlags(self.windowFlags() & ~Qt.WindowContextHelpButtonHint)
        self.setLayoutDirection(Qt.RightToLeft)

        self.school_data = school_data
        self.activation_status = False

        layout = QVBoxLayout()

        # عرض معلومات المؤسسة
        info_layout = QGridLayout()

        # إنشاء تسميات بخط أكبر وغامق
        academy_label = QLabel("الأكاديمية:")
        academy_label.setFont(QFont("Calibri", 14, QFont.Bold))

        academy_value = QLabel(school_data.get('academy', 'غير متوفر'))
        academy_value.setFont(QFont("Calibri", 13))

        directorate_label = QLabel("المديرية:")
        directorate_label.setFont(QFont("Calibri", 14, QFont.Bold))

        directorate_value = QLabel(school_data.get('directorate', 'غير متوفر'))
        directorate_value.setFont(QFont("Calibri", 14))

        school_label = QLabel("المؤسسة:")
        school_label.setFont(QFont("Calibri", 14, QFont.Bold))

        school_value = QLabel(school_data.get('school', 'غير متوفر'))
        school_value.setFont(QFont("Calibri", 14))

        info_layout.addWidget(academy_label, 0, 0)
        info_layout.addWidget(academy_value, 0, 1)
        info_layout.addWidget(directorate_label, 1, 0)
        info_layout.addWidget(directorate_value, 1, 1)
        info_layout.addWidget(school_label, 2, 0)
        info_layout.addWidget(school_value, 2, 1)

        layout.addLayout(info_layout)
        layout.addSpacing(20)

        # إضافة حقل إدخال كود التفعيل بخط أكبر
        registration_label = QLabel("أدخل كود التفعيل:")
        registration_label.setFont(QFont("Calibri", 13, QFont.Bold))
        layout.addWidget(registration_label)

        self.registration_input = QLineEdit()
        self.registration_input.setPlaceholderText("أدخل كود التفعيل هنا...")
        self.registration_input.setFont(QFont("Calibri", 13))
        self.registration_input.setMinimumHeight(40)  # زيادة ارتفاع مربع النص
        layout.addWidget(self.registration_input)

        button_layout = QHBoxLayout()
        self.activate_button = QPushButton("تفعيل")
        self.activate_button.setFont(QFont("Calibri", 13, QFont.Bold))
        self.activate_button.setMinimumHeight(40)
        self.activate_button.setMinimumWidth(120)
        self.activate_button.clicked.connect(self.verify_registration)

        self.cancel_button = QPushButton("إلغاء")
        self.cancel_button.setFont(QFont("Calibri", 13, QFont.Bold))
        self.cancel_button.setMinimumHeight(40)
        self.cancel_button.setMinimumWidth(120)
        self.cancel_button.clicked.connect(self.reject)

        button_layout.addWidget(self.activate_button)
        button_layout.addWidget(self.cancel_button)

        layout.addSpacing(20)
        layout.addLayout(button_layout)

        self.setLayout(layout)

    def verify_registration(self):
        """التحقق من كود التفعيل المُدخل"""
        registration_input = self.registration_input.text().strip()

        if not registration_input:
            QMessageBox.warning(self, "خطأ", "الرجاء إدخال كود التفعيل")
            return

        if self.verify_activation(self.school_data, registration_input):
            self.activation_status = True
            QMessageBox.information(self, "تفعيل ناجح", "تم تفعيل البرنامج بنجاح!")
            self.accept()
        else:
            QMessageBox.critical(self, "خطأ في التفعيل", "كود التفعيل غير صحيح.\nالرجاء التأكد من الكود والمحاولة مرة أخرى.")

    def verify_activation(self, school_data, registration_number_input):
        """
        التحقق من كود التفعيل بناءً على بيانات المؤسسة ورقم التسجيل المدخل.

        school_data: dict يحتوي على 'academy', 'directorate', 'school'
        registration_number_input: رقم التسجيل المدخل من المستخدم (كـ str أو int)
        """
        try:
            academy = school_data.get('academy', '') or 'غير متوفر'
            directorate = school_data.get('directorate', '') or 'غير متوفر'
            school = school_data.get('school', '') or 'غير متوفر'

            # 1. توليد رمز المؤسسة الرقمي (10 أرقام)
            combined_text = f"{academy}-{directorate}-{school}"
            hash_value = 0
            for char in combined_text:
                hash_value = (hash_value * 31 + ord(char)) & 0xFFFFFFFF
            numeric_code = hash_value % 10000000000
            if numeric_code < 1000000000:
                numeric_code += 1000000000  # ضمان 10 أرقام
            school_code = f"{numeric_code:010d}"  # كنص

            logging.info(f"🔢 رمز المؤسسة المُولد: {school_code}")
            if __debug__:
                print(f"🔢 رمز المؤسسة المُولد: {school_code}")

            # 2. استخراج أول 3 أرقام وتطبيق معادلة التفعيل
            school_code_int = int(school_code)
            first_three_digits = int(school_code[:3])
            expected_registration = (school_code_int * 98) + (first_three_digits * 71)

            logging.info(f"✅ رقم التسجيل المتوقع: {expected_registration}")
            if __debug__:
                print(f"✅ رقم التسجيل المتوقع: {expected_registration}")

            # 3. التحقق من صحة المدخل
            if str(registration_number_input).isdigit() and int(registration_number_input) == expected_registration:
                return True
            else:
                return False

        except Exception as e:
            logging.error(f"❌ حدث خطأ أثناء التحقق: {e}")
            if __debug__:
                print(f"❌ حدث خطأ أثناء التحقق: {e}")
            return False

class WelcomeDialog(QDialog):
    """نافذة ترحيبية مع شريط تقدم وزر الدخول"""
    def __init__(self, parent=None):
        super().__init__(parent, Qt.WindowStaysOnTopHint | Qt.FramelessWindowHint)
        self.setModal(True)
        self.setFixedSize(600, 530)  # زيادة ارتفاع النافذة لاستيعاب الإطار وزر الإغلاق

        # إضافة أيقونة البرنامج إلى النافذة
        from PyQt5.QtGui import QIcon
        icon_path = "01.ico"
        if os.path.exists(icon_path):
            self.setWindowIcon(QIcon(icon_path))

        # تعيين خلفية النافذة
        self.setStyleSheet("""
            WelcomeDialog {
                background-color: #f0f2f5;
                border: 2px solid #004D40;
                border-radius: 10px;
            }
        """)

        # إنشاء مخطط رئيسي
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.setSpacing(0)

        # إضافة شريط العنوان
        title_bar = QWidget()
        title_bar.setFixedHeight(40)
        title_bar.setStyleSheet("""
            QWidget {
                background-color: #004D40;
                border-top-left-radius: 8px;
                border-top-right-radius: 8px;
            }
        """)
        title_bar_layout = QHBoxLayout(title_bar)
        title_bar_layout.setContentsMargins(10, 0, 10, 0)
        # عنوان النافذة
        window_title = QLabel("نسخة 2025-01")
        window_title.setFont(QFont("Calibri", 12, QFont.Bold))
        window_title.setStyleSheet("color: white;")

        # زر الإغلاق
        close_button = QPushButton("×")
        close_button.setFixedSize(30, 30)
        close_button.setFont(QFont("Arial", 16, QFont.Bold))
        close_button.setStyleSheet("""
            QPushButton {
                background-color: transparent;
                color: white;
                border: none;
                border-radius: 15px;
            }
            QPushButton:hover {
                background-color: #E53935;
            }
            QPushButton:pressed {
                background-color: #C62828;
            }
        """)
        close_button.clicked.connect(self.close_application)

        title_bar_layout.addWidget(window_title)
        title_bar_layout.addStretch()
        title_bar_layout.addWidget(close_button)

        main_layout.addWidget(title_bar)

        # إنشاء مخطط عمودي للمحتوى
        content_widget = QWidget()
        content_widget.setStyleSheet("background-color: #f0f2f5;")
        layout = QVBoxLayout(content_widget)

        # إضافة عنوان
        title_label = QLabel("برنامج المعين في الحراسة العامة")
        title_label.setFont(QFont("Calibri", 24, QFont.Bold))
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("color: #004D40;")
        layout.addSpacing(10)
        layout.addWidget(title_label)
        layout.addSpacing(10)

        # إضافة اسم المبرمج
        developer_label = QLabel("برمجة محمد الطاهري")
        developer_label.setFont(QFont("Calibri", 14, QFont.Bold))
        developer_label.setAlignment(Qt.AlignCenter)
        developer_label.setStyleSheet("color: #00796B;")
        layout.addWidget(developer_label)
        layout.addSpacing(20)

        # إضافة رسالة ترحيب
        welcome_label = QLabel("مرحبا بكم في البرنامج")
        welcome_label.setFont(QFont("Calibri", 16))
        welcome_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(welcome_label)
        layout.addSpacing(20)

        # إضافة رسالة انتظار
        self.wait_label = QLabel("الرجاء الانتظار قليلاً حتى يتم تحميل البرنامج...")
        self.wait_label.setFont(QFont("Calibri", 12))
        self.wait_label.setAlignment(Qt.AlignCenter)
        self.wait_label.setStyleSheet("color: #555;")
        layout.addWidget(self.wait_label)
        layout.addSpacing(20)

        # إضافة شريط التقدم
        self.progress_bar = QProgressBar()
        self.progress_bar.setRange(0, 100)
        self.progress_bar.setValue(0)
        self.progress_bar.setTextVisible(True)
        self.progress_bar.setAlignment(Qt.AlignCenter)
        self.progress_bar.setFixedHeight(25)
        self.progress_bar.setStyleSheet("""
            QProgressBar {
                border: 1px solid #CCC;
                border-radius: 5px;
                text-align: center;
                background-color: #FFF;
            }
            QProgressBar::chunk {
                background-color: #004D40;
                border-radius: 5px;
            }
        """)
        layout.addWidget(self.progress_bar)
        layout.addSpacing(20)

        # إضافة مجموعة رمز المؤسسة
        school_code_layout = QHBoxLayout()
        school_code_label = QLabel("رمز المؤسسة:")
        school_code_label.setFont(QFont("Calibri", 12, QFont.Bold))

        self.school_code_text = QLineEdit()
        self.school_code_text.setReadOnly(True)  # مربع نص للقراءة فقط (غير قابل للتعديل)
        self.school_code_text.setAlignment(Qt.AlignCenter)
        self.school_code_text.setFont(QFont("Calibri", 14, QFont.Bold))
        self.school_code_text.setFixedHeight(35)
        self.school_code_text.setStyleSheet("""
            QLineEdit {
                background-color: #E8F5E9;
                color: #004D40;
                border: 1px solid #A5D6A7;
                border-radius: 5px;
                padding: 5px;
            }
        """)
        self.school_code_text.setPlaceholderText("جاري استخراج رمز المؤسسة...")

        school_code_layout.addWidget(school_code_label)
        school_code_layout.addWidget(self.school_code_text)
        layout.addLayout(school_code_layout)
        layout.addSpacing(15)

        # إضافة زر الدخول إلى البرنامج
        self.enter_button = QPushButton("الدخول إلى البرنامج")
        self.enter_button.setFont(QFont("Calibri", 14, QFont.Bold))
        self.enter_button.setFixedSize(200, 40)
        self.enter_button.setStyleSheet("""
            QPushButton {
                background-color: #004D40;
                color: white;
                border: none;
                border-radius: 5px;
            }
            QPushButton:hover {
                background-color: #00695C;
            }
            QPushButton:pressed {
                background-color: #004D40;
            }
            QPushButton:disabled {
                background-color: #B0BEC5;
                color: #78909C;
            }
        """)
        self.enter_button.setVisible(False)  # غير مرئي في البداية
        self.enter_button.setEnabled(False)  # غير مفعل في البداية
        self.enter_button.clicked.connect(self.on_enter_clicked)

        button_layout = QHBoxLayout()
        button_layout.addStretch()
        button_layout.addWidget(self.enter_button)
        button_layout.addStretch()
        layout.addLayout(button_layout)

        layout.addStretch()

        # إضافة المحتوى إلى المخطط الرئيسي
        main_layout.addWidget(content_widget)

        # تعيين اتجاه النص من اليمين إلى اليسار
        self.setLayoutDirection(Qt.RightToLeft)

        # متغيرات لتخزين الحالة
        self.window = None
        self.loading_done = False
        self.init_ui_done = False
        self.result_code = 0
        self.school_code = ""  # تخزين رمز المؤسسة
        self.create_timer()

        # وسط الشاشة
        self.center_on_screen()

    def close_application(self):
        """إغلاق التطبيق عند النقر على زر الإغلاق"""
        # إنشاء مربع حوار مخصص أكثر أناقة
        msgBox = QMessageBox(self)
        msgBox.setWindowTitle("تأكيد الخروج")

        # إضافة أيقونة البرنامج إلى نافذة الخروج
        from PyQt5.QtGui import QIcon
        icon_path = "01.ico"
        if os.path.exists(icon_path):
            msgBox.setWindowIcon(QIcon(icon_path))

        # تعيين النص الرئيسي بخط Calibri 13 أزرق غامق
        msgBox.setText("<p style='font-family: Calibri; font-size: 13pt; color: #0D47A1; font-weight: bold;'>هل تريد الخروج من البرنامج؟</p>")

        # إضافة نص توضيحي
        msgBox.setInformativeText("<p style='font-family: Calibri; font-size: 11pt;'>سيتم إغلاق البرنامج وجميع النوافذ المفتوحة.</p>")

        # تعيين أيقونة السؤال
        msgBox.setIcon(QMessageBox.Question)

        # إنشاء أزرار مخصصة
        yesButton = msgBox.addButton("نعم، أريد الخروج", QMessageBox.YesRole)
        noButton = msgBox.addButton("لا، العودة للبرنامج", QMessageBox.NoRole)

        # تخصيص أنماط الأزرار
        yesButton.setStyleSheet("""
            QPushButton {
                font-family: Calibri;
                font-size: 13pt;
                font-weight: bold;
                color: white;
                background-color: #0D47A1;
                border: none;
                border-radius: 5px;
                padding: 5px 15px;
                min-width: 140px;
            }
            QPushButton:hover {
                background-color: #1565C0;
            }
            QPushButton:pressed {
                background-color: #0D47A1;
            }
        """)

        noButton.setStyleSheet("""
            QPushButton {
                font-family: Calibri;
                font-size: 13pt;
                font-weight: bold;
                color: #0D47A1;
                background-color: #E3F2FD;
                border: 1px solid #0D47A1;
                border-radius: 5px;
                padding: 5px 15px;
                min-width: 140px;
            }
            QPushButton:hover {
                background-color: #BBDEFB;
            }
            QPushButton:pressed {
                background-color: #E3F2FD;
            }
        """)

        # تعيين الزر الافتراضي (زر العودة للبرنامج)
        msgBox.setDefaultButton(noButton)

        # تعيين نمط مربع الحوار
        msgBox.setStyleSheet("""
            QMessageBox {
                background-color: white;
            }
            QLabel {
                font-family: Calibri;
                min-width: 300px;
            }
        """)

        # عرض مربع الحوار وانتظار الرد
        msgBox.exec_()

        # معالجة الرد
        if msgBox.clickedButton() == yesButton:
            logging.info("تم إغلاق البرنامج من خلال زر الإغلاق")
            self.reject()  # إغلاق النافذة الحالية
            sys.exit(0)  # إنهاء التطبيق

    def center_on_screen(self):
        """توسيط النافذة على الشاشة"""
        from PyQt5.QtWidgets import QDesktopWidget
        screen = QDesktopWidget().screenGeometry()
        size = self.geometry()
        x = (screen.width() - size.width()) // 2
        y = (screen.height() - size.height()) // 2
        self.move(x, y)

    def create_timer(self):
        """إنشاء مؤقت لتحديث شريط التقدم"""
        self.progress_value = 0
        self.timer = QTimer(self)
        self.timer.timeout.connect(self.update_progress)
        self.timer.start(30)  # سرعة التحديث (30 مللي ثانية)

    def update_progress(self):
        """تحديث قيمة شريط التقدم"""
        if self.progress_value < 100:
            self.progress_value += 1
            self.progress_bar.setValue(self.progress_value)

            # عند بلوغ 50% نقوم بإنشاء النافذة الرئيسية
            if self.progress_value == 50 and not self.init_ui_done:
                try:
                    self.init_main_window()
                except Exception as e:
                    logging.critical(f"خطأ غير متوقع في إنشاء النافذة الرئيسية: {e}")
                    logging.critical(traceback.format_exc())
                    # تعيين علامة نجاح تهيئة واجهة المستخدم حتى يستمر شريط التقدم
                    self.init_ui_done = True
                    self.result_code = 1  # إشارة لوجود خطأ

            # عند بلوغ 85% نقوم باستخراج وعرض رمز المؤسسة
            if self.progress_value == 85:
                try:
                    self.extract_and_show_school_code()
                except Exception as e:
                    logging.error(f"خطأ في استخراج رمز المؤسسة: {e}")
                    logging.error(traceback.format_exc())
                    # عرض رسالة خطأ في مربع النص
                    self.school_code_text.setText("تعذر استخراج الرمز")

        elif self.progress_value >= 100 and not self.loading_done:
            self.loading_done = True
            self.timer.stop()
            self.wait_label.setText("اكتمل التحميل! اضغط على زر الدخول للمتابعة.")

            # تأكد من إظهار مربع النص ورمز المؤسسة
            if not self.school_code_text.isVisible():
                try:
                    self.extract_and_show_school_code()
                except Exception as e:
                    logging.error(f"خطأ في استخراج رمز المؤسسة: {e}")
                    # عرض رسالة خطأ في مربع النص
                    self.school_code_text.setText("تعذر استخراج الرمز")

            self.enter_button.setVisible(True)
            self.enter_button.setEnabled(True)
            logging.info("اكتمل التحميل وتم تفعيل زر الدخول")

    def extract_and_show_school_code(self):
        """استخراج وعرض رمز المؤسسة"""
        try:
            # الحصول على بيانات المؤسسة من قاعدة البيانات
            school_data = self.check_school_data()

            if school_data:
                # استخراج رمز المؤسسة باستخدام الدالة
                academy = school_data.get('academy', '') or 'غير متوفر'
                directorate = school_data.get('directorate', '') or 'غير متوفر'
                school = school_data.get('school', '') or 'غير متوفر'

                logging.info(f"بيانات المؤسسة المستخدمة لتوليد الرمز: الأكاديمية='{academy}', المديرية='{directorate}', المؤسسة='{school}'")

                # التحقق مما إذا كانت المؤسسة فارغة
                if not school_data.get('school') or school_data.get('school') == 'None':
                    logging.info("اسم المؤسسة فارغ، سيتم عرض رسالة خاصة")
                    self.school_code_text.setText("لا يلزم التفعيل - المؤسسة غير محددة")
                    self.school_code = ""
                    return

                # توليد رمز المؤسسة الرقمي (نفس الخوارزمية المستخدمة في verify_activation)
                combined_text = f"{academy}-{directorate}-{school}"
                hash_value = 0
                for char in combined_text:
                    hash_value = (hash_value * 31 + ord(char)) & 0xFFFFFFFF
                numeric_code = hash_value % 10000000000
                if numeric_code < 1000000000:
                    numeric_code += 1000000000  # ضمان 10 أرقام
                self.school_code = f"{numeric_code:010d}"  # كنص

                # عرض الرمز في مربع النص
                self.school_code_text.setText(self.school_code)
                logging.info(f"تم استخراج وعرض رمز المؤسسة: {self.school_code}")

                # للتشخيص: عرض رقم التفعيل المتوقع
                school_code_int = int(self.school_code)
                first_three_digits = int(self.school_code[:3])
                expected_registration = (school_code_int * 98) + (first_three_digits * 71)
                logging.info(f"رقم التفعيل المتوقع: {expected_registration}")
            else:
                # إذا لم تكن هناك بيانات مؤسسة
                self.school_code_text.setText("لا توجد بيانات للمؤسسة")
                logging.info("لا توجد بيانات مؤسسة لاستخراج الرمز")
        except Exception as e:
            # في حالة حدوث خطأ
            self.school_code_text.setText("تعذر استخراج الرمز")
            logging.error(f"خطأ في استخراج رمز المؤسسة: {e}")
            logging.error(traceback.format_exc())
            if __debug__:
                print(f"خطأ في استخراج رمز المؤسسة: {e}")
                traceback.print_exc()

    def init_main_window(self):
        """إنشاء النافذة الرئيسية ومكوناتها"""
        try:
            logging.info("إنشاء النافذة الرئيسية...")
            from main_window import MainWindow

            # إنشاء النافذة الرئيسية مع تمرير معلمة auto_show=False لمنع عرضها تلقائياً
            self.window = MainWindow(auto_show=False)
            logging.info("تم إنشاء النافذة الرئيسية بنجاح")

            # دمج النوافذ المستقلة في النافذة الرئيسية
            try:
                import embed_all_windows
                embed_all_windows.add_embedded_windows_to_main(self.window)
                logging.info("تم دمج النوافذ المستقلة في النافذة الرئيسية")
            except Exception as e:
                logging.error(f"تحذير: فشل دمج النوافذ المستقلة: {e}")
                # نستمر حتى لو فشل دمج النوافذ المستقلة

            # تعيين علامة نجاح تهيئة واجهة المستخدم
            self.init_ui_done = True

        except Exception as e:
            logging.critical(f"فشل إنشاء النافذة الرئيسية: {e}")
            logging.critical(traceback.format_exc())

            # عرض رسالة خطأ ولكن لا نوقف البرنامج
            QMessageBox.warning(self, "تحذير", f"فشل إنشاء النافذة الرئيسية: {e}\nسيتم محاولة المتابعة.")

            # تعيين علامة نجاح تهيئة واجهة المستخدم حتى يستمر شريط التقدم
            self.init_ui_done = True
            self.result_code = 1  # إشارة لوجود خطأ

    def keyPressEvent(self, event):
        """منع إغلاق النافذة بمفتاح Escape"""
        if event.key() == Qt.Key_Escape:
            pass  # تجاهل مفتاح Escape
        else:
            super().keyPressEvent(event)

    def check_school_data(self, db_path="data.db"):
        """التحقق من بيانات المؤسسة في قاعدة البيانات"""
        try:
            # تحديد مسار قاعدة البيانات في مجلد البرنامج الرئيسي
            db_full_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), db_path)
            logging.info(f"مسار قاعدة البيانات: {db_full_path}")
            conn = sqlite3.connect(db_full_path)
            cursor = conn.cursor()

            # التحقق من وجود جدول بيانات_المؤسسة
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='بيانات_المؤسسة'")
            if not cursor.fetchone():
                logging.info("جدول بيانات_المؤسسة غير موجود، إنشاء الجدول...")
                cursor.execute("""
                    CREATE TABLE بيانات_المؤسسة (
                        الأكاديمية TEXT,
                        المديرية TEXT,
                        الجماعة TEXT,
                        المؤسسة TEXT,
                        السنة_الدراسية TEXT,
                        البلدة TEXT,
                        المدير TEXT,
                        الحارس_العام TEXT,
                        السلك TEXT,
                        ImagePath1 TEXT,
                        الأسدس TEXT,
                        رقم_الحراسة TEXT,
                        رقم_التسجيل TEXT
                    )
                """)
                conn.commit()
                logging.info("تم إنشاء جدول بيانات_المؤسسة")
                conn.close()
                return None

            # استرجاع معلومات أعمدة جدول بيانات_المؤسسة للتشخيص
            cursor.execute("PRAGMA table_info(بيانات_المؤسسة)")
            columns_info = cursor.fetchall()
            column_names = [col[1] for col in columns_info]  # استخراج أسماء الأعمدة
            logging.info(f"أسماء أعمدة جدول بيانات_المؤسسة: {column_names}")

            # تحديد مؤشرات الأعمدة المطلوبة بشكل ديناميكي
            academy_idx = column_names.index("الأكاديمية") if "الأكاديمية" in column_names else -1
            directorate_idx = column_names.index("المديرية") if "المديرية" in column_names else -1
            school_idx = column_names.index("المؤسسة") if "المؤسسة" in column_names else -1
            registration_idx = column_names.index("رقم_التسجيل") if "رقم_التسجيل" in column_names else -1

            # التحقق من وجود الأعمدة المطلوبة
            if academy_idx == -1 or directorate_idx == -1 or school_idx == -1:
                logging.error("أعمدة بيانات المؤسسة الأساسية غير موجودة في الجدول")
                conn.close()
                return None

            # استرجاع جميع أعمدة بيانات المؤسسة
            cursor.execute("SELECT * FROM بيانات_المؤسسة LIMIT 1")
            all_columns = cursor.fetchone()

            if all_columns:
                logging.info(f"البيانات المسترجعة من جدول بيانات_المؤسسة: {all_columns}")

                # إنشاء قاموس بيانات المؤسسة من البيانات المسترجعة
                school_data = {
                    'academy': all_columns[academy_idx] if academy_idx >= 0 and academy_idx < len(all_columns) else '',
                    'directorate': all_columns[directorate_idx] if directorate_idx >= 0 and directorate_idx < len(all_columns) else '',
                    'school': all_columns[school_idx] if school_idx >= 0 and school_idx < len(all_columns) else '',
                    'activated': all_columns[registration_idx] != None and all_columns[registration_idx] != '' if registration_idx >= 0 and registration_idx < len(all_columns) else False
                }

                # التحقق من وجود البيانات الأساسية
                # نسمح بأن تكون المؤسسة فارغة ونستمر في إرجاع البيانات
                if (not school_data['academy'] or school_data['academy'] == 'None') and \
                   (not school_data['directorate'] or school_data['directorate'] == 'None'):
                    logging.warning("بيانات الأكاديمية والمديرية فارغة أو غير محددة")
                    conn.close()
                    return None

                # إذا كانت المؤسسة فارغة، نسجل ذلك في السجل ولكن نستمر
                if not school_data['school'] or school_data['school'] == 'None':
                    logging.info("اسم المؤسسة فارغ، سيتم تخطي التفعيل")
                    # نستمر في إرجاع البيانات

                logging.info(f"تم استرجاع بيانات المؤسسة بنجاح: {school_data}")
                conn.close()
                return school_data
            else:
                logging.warning("لا توجد بيانات مؤسسة مسجلة في الجدول")
                conn.close()
                return None
        except Exception as e:
            logging.error(f"خطأ في استرجاع بيانات المؤسسة: {e}")
            logging.error(traceback.format_exc())
            try:
                conn.close()
            except:
                pass
            return None

    def on_enter_clicked(self):
        """معالجة النقر على زر الدخول إلى البرنامج"""
        # ضغط قاعدة البيانات وإنشاء نسخة احتياطية
        try:
            from database_utils import compress_database, auto_backup_database

            # ضغط قاعدة البيانات
            compress_result = compress_database()
            if compress_result:
                logging.info("تم ضغط قاعدة البيانات بنجاح")
            else:
                logging.warning("لم يتم ضغط قاعدة البيانات بنجاح")

            # إنشاء نسخة احتياطية مع الاحتفاظ بآخر 10 نسخ فقط
            backup_result = auto_backup_database(max_backups=10)
            if backup_result:
                logging.info("تم إنشاء نسخة احتياطية من قاعدة البيانات بنجاح")
            else:
                logging.warning("لم يتم إنشاء نسخة احتياطية من قاعدة البيانات")
        except ImportError as e:
            logging.error(f"خطأ في استيراد وحدات قاعدة البيانات: {e}")
            # استخدام الدالة الداخلية القديمة إذا فشل استيراد الوحدات الجديدة
            backup_result = self.backup_database()
            if backup_result:
                logging.info("تم إنشاء نسخة احتياطية من قاعدة البيانات باستخدام الدالة الداخلية")
        except Exception as e:
            logging.error(f"خطأ عام في ضغط وحفظ قاعدة البيانات: {e}")
            logging.error(traceback.format_exc())

        # التحقق من بيانات المؤسسة
        school_data = self.check_school_data()

        # التحقق من حالة التفعيل
        activation_required = False

        # إذا كانت بيانات المؤسسة موجودة ولكن المؤسسة فارغة، نتخطى التفعيل
        if school_data and ('school' not in school_data or not school_data['school'] or school_data['school'] == 'None'):
            logging.info("اسم المؤسسة فارغ، تخطي التفعيل والسماح بالدخول")
            activation_required = False
        # وإلا، نتحقق من حالة التفعيل كالمعتاد
        elif school_data and 'activated' in school_data and not school_data['activated']:
            activation_required = True

        if activation_required:
            logging.info("البرنامج غير مفعل، عرض نافذة التفعيل...")
            # عرض نافذة التفعيل
            activation_dialog = RegistrationDialog(school_data, self)
            if activation_dialog.exec_() == QDialog.Accepted and activation_dialog.activation_status:
                # تحديث حالة التفعيل في قاعدة البيانات
                self.update_activation_status()
                logging.info("تم التفعيل بنجاح")
            else:
                logging.warning("لم يتم التفعيل، إلغاء الدخول")
                return

        # في حالة عدم وجود مشاكل، قبول النافذة وإغلاقها للانتقال إلى البرنامج الرئيسي
        self.accept()
        logging.info("تم قبول نافذة الترحيب والانتقال للبرنامج الرئيسي")

    def update_activation_status(self, db_path="data.db"):
        """تحديث حالة التفعيل في قاعدة البيانات"""
        try:
            # تحديد مسار قاعدة البيانات في مجلد البرنامج الرئيسي
            db_full_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), db_path)
            logging.info(f"مسار قاعدة البيانات للتفعيل: {db_full_path}")
            conn = sqlite3.connect(db_full_path)
            cursor = conn.cursor()
            cursor.execute("UPDATE بيانات_المؤسسة SET مفعل = 1")
            conn.commit()
            conn.close()
            logging.info("تم تحديث حالة التفعيل بنجاح")
            return True
        except Exception as e:
            logging.error(f"خطأ في تحديث حالة التفعيل: {e}")
            logging.error(traceback.format_exc())
            try:
                conn.close()
            except:
                pass
            return False

    def closeEvent(self, event):
        """منع إغلاق النافذة قبل الضغط على زر الدخول"""
        event.ignore()

    def get_main_window(self):
        """الحصول على النافذة الرئيسية"""
        return self.window

    def backup_database(self):
        """إنشاء نسخة احتياطية من قاعدة البيانات وضغطها"""
        try:
            import shutil
            from datetime import datetime

            # تحديد مسار قاعدة البيانات في مجلد البرنامج الرئيسي
            source_db = os.path.join(os.path.dirname(os.path.abspath(__file__)), "data.db")
            backups_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), "Backups")
            logging.info(f"مسار قاعدة البيانات للنسخ الاحتياطي: {source_db}")

            # إنشاء مجلد النسخ الاحتياطية إذا لم يكن موجودًا
            if not os.path.exists(backups_dir):
                os.makedirs(backups_dir)
                logging.info(f"تم إنشاء مجلد النسخ الاحتياطية: {backups_dir}")

            # تنسيق اسم الملف مع التاريخ الحالي
            current_date = datetime.now().strftime("%Y-%m-%d")
            backup_filename = f"data_backup_{current_date}.db"
            backup_path = os.path.join(backups_dir, backup_filename)

            # ضغط قاعدة البيانات
            conn = sqlite3.connect(source_db)
            conn.execute("VACUUM")
            conn.close()
            logging.info(f"تم ضغط قاعدة البيانات: {source_db}")

            # نسخ قاعدة البيانات إلى مجلد النسخ الاحتياطية
            shutil.copy2(source_db, backup_path)
            logging.info(f"تم إنشاء نسخة احتياطية من قاعدة البيانات: {backup_path}")

            return True
        except Exception as e:
            logging.error(f"خطأ في إنشاء نسخة احتياطية من قاعدة البيانات: {e}")
            logging.error(traceback.format_exc())
            return False

def main():
    """
    الدالة الرئيسية لتشغيل التطبيق
    """
    try:
        # تسجيل معلومات إضافية عن النظام للمساعدة في تشخيص مشاكل التوافق
        logging.info(f"معلومات إضافية عن النظام:")
        logging.info(f"بنية النظام (sys.maxsize): {sys.maxsize}")
        logging.info(f"بنية النظام (platform.architecture): {platform.architecture()}")
        logging.info(f"نظام التشغيل (platform.platform): {platform.platform()}")
        logging.info(f"إصدار Windows: {platform.win32_ver() if hasattr(platform, 'win32_ver') else 'غير متاح'}")

        # معالجة مشاكل عدم توافق بنية النظام
        try:
            # اختبار تحميل مكتبات النظام الأساسية
            import ctypes
            logging.info("تم تحميل مكتبة ctypes بنجاح")
        except Exception as e:
            logging.critical(f"خطأ في تحميل مكتبات النظام: {e}")
            # عرض رسالة خطأ للمستخدم
            if getattr(sys, 'frozen', False):
                try:
                    import ctypes
                    ctypes.windll.user32.MessageBoxW(0,
                        "حدث خطأ في تحميل مكتبات النظام. قد يكون هناك عدم توافق بين بنية البرنامج وبنية نظام التشغيل.\n\n"
                        "يرجى التأكد من استخدام الإصدار المناسب للبرنامج (32 بت أو 64 بت) المتوافق مع نظام التشغيل لديك.",
                        "خطأ في توافق النظام", 0x10)
                except:
                    pass  # لا يمكن عرض رسالة الخطأ
            sys.exit(1)

        # ضبط مسار العمل الصحيح إذا كان البرنامج محزماً
        if getattr(sys, 'frozen', False):
            # عند العمل كملف تنفيذي محزم
            base_dir = getattr(sys, '_MEIPASS', os.path.dirname(sys.executable))
            os.chdir(base_dir)
            logging.info(f"تعيين مسار العمل للملف المحزم: {base_dir}")
            if __debug__:
                print(f"تعيين مسار العمل للملف المحزم: {base_dir}")

            # طباعة قائمة بالملفات المتاحة في مجلد التطبيق
            logging.info("قائمة الملفات في مجلد البرنامج:")
            for root, dirs, files in os.walk(base_dir):
                rel_path = os.path.relpath(root, base_dir)
                if rel_path == '.':
                    rel_path = ''
                for file in files:
                    logging.info(f"- {os.path.join(rel_path, file)}")

        # التحقق من وجود قاعدة البيانات وإنشائها إذا كانت غير موجودة
        try:
            # تحديد مسار قاعدة البيانات في مجلد البرنامج الرئيسي
            db_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "data.db")
            logging.info(f"مسار قاعدة البيانات عند بدء التشغيل: {db_path}")

            if not os.path.exists(db_path):
                logging.warning(f"قاعدة البيانات غير موجودة: {db_path}")
                print(f"قاعدة البيانات غير موجودة: {db_path}")

                # التحقق من وجود نسخ احتياطية
                backups_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), "Backups")
                backup_files = []

                if os.path.exists(backups_dir):
                    # البحث عن ملفات النسخ الاحتياطية
                    for file in os.listdir(backups_dir):
                        if file.startswith("data_backup_") and file.endswith(".db"):
                            backup_files.append(file)

                    # ترتيب النسخ الاحتياطية حسب التاريخ (الأحدث أولاً)
                    backup_files.sort(reverse=True)

                # عرض رسالة للمستخدم مع خيارات مختلفة
                from PyQt5.QtWidgets import QMessageBox, QFileDialog
                app_temp = QApplication.instance() or QApplication(sys.argv)

                message = "قاعدة البيانات غير موجودة أو تالفة."
                if backup_files:
                    message += f"\nتم العثور على {len(backup_files)} نسخة احتياطية في مجلد Backups."
                    message += "\nماذا تريد أن تفعل؟"

                    # إنشاء مربع حوار مخصص مع زرين فقط
                    msgBox = QMessageBox()
                    msgBox.setWindowTitle("قاعدة البيانات غير موجودة")
                    msgBox.setText(message)
                    msgBox.setIcon(QMessageBox.Warning)

                    # إضافة الأزرار
                    restoreButton = msgBox.addButton("استرجاع نسخة احتياطية", QMessageBox.ActionRole)
                    cancelButton = msgBox.addButton("إلغاء", QMessageBox.RejectRole)

                    # عرض مربع الحوار وانتظار الرد
                    msgBox.exec_()

                    # معالجة الرد
                    clickedButton = msgBox.clickedButton()

                    if clickedButton == restoreButton:
                        # فتح مجلد النسخ الاحتياطية
                        try:
                            # فتح مستعرض الملفات لاختيار ملف النسخة الاحتياطية
                            backup_path = QFileDialog.getOpenFileName(
                                None,
                                "اختر ملف النسخة الاحتياطية",
                                backups_dir,
                                "ملفات قاعدة البيانات (*.db)"
                            )[0]

                            if backup_path:
                                # نسخ ملف النسخة الاحتياطية إلى المسار الرئيسي
                                import shutil
                                shutil.copy2(backup_path, db_path)
                                logging.info(f"تم استرجاع قاعدة البيانات من النسخة الاحتياطية: {backup_path}")
                                QMessageBox.information(None, "تم بنجاح", "تم استرجاع قاعدة البيانات من النسخة الاحتياطية بنجاح.")
                                result = QMessageBox.Yes  # لتخطي إنشاء قاعدة بيانات جديدة
                            else:
                                logging.info("تم إلغاء استرجاع النسخة الاحتياطية من قبل المستخدم")
                                result = QMessageBox.question(
                                    None,
                                    "إنشاء قاعدة بيانات جديدة",
                                    "هل تريد إنشاء قاعدة بيانات جديدة؟",
                                    QMessageBox.Yes | QMessageBox.No,
                                    QMessageBox.Yes
                                )
                        except Exception as e:
                            logging.error(f"خطأ في استرجاع النسخة الاحتياطية: {e}")
                            QMessageBox.critical(None, "خطأ", f"حدث خطأ أثناء استرجاع النسخة الاحتياطية: {str(e)}")
                            result = QMessageBox.question(
                                None,
                                "إنشاء قاعدة بيانات جديدة",
                                "هل تريد إنشاء قاعدة بيانات جديدة؟",
                                QMessageBox.Yes | QMessageBox.No,
                                QMessageBox.Yes
                            )
                    else:  # cancelButton
                        logging.info("تم إلغاء استرجاع النسخة الاحتياطية من قبل المستخدم")
                        QMessageBox.warning(None, "تحذير", "تم إلغاء استرجاع النسخة الاحتياطية. سيتم إغلاق البرنامج.")
                        sys.exit(0)
                else:
                    # لا توجد نسخ احتياطية، عرض رسالة بسيطة
                    QMessageBox.critical(
                        None,
                        "قاعدة البيانات غير موجودة",
                        "قاعدة البيانات غير موجودة أو تالفة ولا توجد نسخ احتياطية. سيتم إغلاق البرنامج."
                    )
                    sys.exit(1)

                # تم إزالة خيار إنشاء قاعدة بيانات جديدة
            else:
                # التحقق من صحة قاعدة البيانات
                try:
                    conn = sqlite3.connect(db_path)
                    cursor = conn.cursor()
                    cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
                    tables = cursor.fetchall()
                    conn.close()

                    if not tables:
                        logging.warning("قاعدة البيانات موجودة ولكنها فارغة")

                        # التحقق من وجود نسخ احتياطية
                        backups_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), "Backups")
                        backup_files = []

                        if os.path.exists(backups_dir):
                            # البحث عن ملفات النسخ الاحتياطية
                            for file in os.listdir(backups_dir):
                                if file.startswith("data_backup_") and file.endswith(".db"):
                                    backup_files.append(file)

                            # ترتيب النسخ الاحتياطية حسب التاريخ (الأحدث أولاً)
                            backup_files.sort(reverse=True)

                        # عرض رسالة للمستخدم مع خيارات مختلفة
                        from PyQt5.QtWidgets import QMessageBox, QFileDialog

                        message = "قاعدة البيانات موجودة ولكنها فارغة."
                        if backup_files:
                            message += f"\nتم العثور على {len(backup_files)} نسخة احتياطية في مجلد Backups."
                            message += "\nماذا تريد أن تفعل؟"

                            # إنشاء مربع حوار مخصص مع زرين فقط
                            msgBox = QMessageBox()
                            msgBox.setWindowTitle("قاعدة البيانات فارغة")
                            msgBox.setText(message)
                            msgBox.setIcon(QMessageBox.Warning)

                            # إضافة الأزرار
                            restoreButton = msgBox.addButton("استرجاع نسخة احتياطية", QMessageBox.ActionRole)
                            msgBox.addButton("إلغاء", QMessageBox.RejectRole)

                            # عرض مربع الحوار وانتظار الرد
                            msgBox.exec_()

                            # معالجة الرد
                            clickedButton = msgBox.clickedButton()

                            if clickedButton == restoreButton:
                                # فتح مجلد النسخ الاحتياطية
                                try:
                                    # فتح مستعرض الملفات لاختيار ملف النسخة الاحتياطية
                                    backup_path = QFileDialog.getOpenFileName(
                                        None,
                                        "اختر ملف النسخة الاحتياطية",
                                        backups_dir,
                                        "ملفات قاعدة البيانات (*.db)"
                                    )[0]

                                    if backup_path:
                                        # نسخ ملف النسخة الاحتياطية إلى المسار الرئيسي
                                        import shutil
                                        shutil.copy2(backup_path, db_path)
                                        logging.info(f"تم استرجاع قاعدة البيانات من النسخة الاحتياطية: {backup_path}")
                                        QMessageBox.information(None, "تم بنجاح", "تم استرجاع قاعدة البيانات من النسخة الاحتياطية بنجاح.")
                                        result = QMessageBox.No  # لتخطي تهيئة قاعدة البيانات
                                    else:
                                        logging.info("تم إلغاء استرجاع النسخة الاحتياطية من قبل المستخدم")
                                        result = QMessageBox.question(
                                            None,
                                            "تهيئة قاعدة البيانات",
                                            "هل تريد تهيئة قاعدة البيانات؟",
                                            QMessageBox.Yes | QMessageBox.No,
                                            QMessageBox.Yes
                                        )
                                except Exception as e:
                                    logging.error(f"خطأ في استرجاع النسخة الاحتياطية: {e}")
                                    QMessageBox.critical(None, "خطأ", f"حدث خطأ أثناء استرجاع النسخة الاحتياطية: {str(e)}")
                                    result = QMessageBox.question(
                                        None,
                                        "تهيئة قاعدة البيانات",
                                        "هل تريد تهيئة قاعدة البيانات؟",
                                        QMessageBox.Yes | QMessageBox.No,
                                        QMessageBox.Yes
                                    )
                            else:  # cancelButton
                                logging.info("تم إلغاء استرجاع النسخة الاحتياطية من قبل المستخدم")
                                QMessageBox.warning(None, "تحذير", "تم إلغاء استرجاع النسخة الاحتياطية. سيتم إغلاق البرنامج.")
                                sys.exit(0)
                        else:
                            # لا توجد نسخ احتياطية، عرض رسالة بسيطة
                            QMessageBox.critical(
                                None,
                                "قاعدة البيانات فارغة",
                                "قاعدة البيانات موجودة ولكنها فارغة ولا توجد نسخ احتياطية. سيتم إغلاق البرنامج."
                            )
                            sys.exit(1)

                        # تم إزالة خيار تهيئة قاعدة البيانات
                    else:
                        logging.info(f"قاعدة البيانات موجودة وتحتوي على {len(tables)} جدول")
                except Exception as e:
                    logging.error(f"خطأ في التحقق من صحة قاعدة البيانات: {e}")

                    # التحقق من وجود نسخ احتياطية
                    backups_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), "Backups")
                    backup_files = []

                    if os.path.exists(backups_dir):
                        # البحث عن ملفات النسخ الاحتياطية
                        for file in os.listdir(backups_dir):
                            if file.startswith("data_backup_") and file.endswith(".db"):
                                backup_files.append(file)

                        # ترتيب النسخ الاحتياطية حسب التاريخ (الأحدث أولاً)
                        backup_files.sort(reverse=True)

                    # عرض رسالة للمستخدم مع خيارات مختلفة
                    from PyQt5.QtWidgets import QMessageBox, QFileDialog

                    message = f"حدث خطأ في قاعدة البيانات: {str(e)}"
                    if backup_files:
                        message += f"\nتم العثور على {len(backup_files)} نسخة احتياطية في مجلد Backups."
                        message += "\nماذا تريد أن تفعل؟"

                        # إنشاء مربع حوار مخصص مع ثلاثة أزرار
                        msgBox = QMessageBox()
                        msgBox.setWindowTitle("خطأ في قاعدة البيانات")
                        msgBox.setText(message)
                        msgBox.setIcon(QMessageBox.Warning)

                        # إضافة الأزرار
                        restoreButton = msgBox.addButton("استرجاع نسخة احتياطية", QMessageBox.ActionRole)
                        recreateButton = msgBox.addButton("إعادة إنشاء قاعدة البيانات", QMessageBox.ActionRole)
                        msgBox.addButton("إلغاء", QMessageBox.RejectRole)

                        # عرض مربع الحوار وانتظار الرد
                        msgBox.exec_()

                        # معالجة الرد
                        clickedButton = msgBox.clickedButton()

                        if clickedButton == restoreButton:
                            # فتح مجلد النسخ الاحتياطية
                            try:
                                # فتح مستعرض الملفات لاختيار ملف النسخة الاحتياطية
                                backup_path = QFileDialog.getOpenFileName(
                                    None,
                                    "اختر ملف النسخة الاحتياطية",
                                    backups_dir,
                                    "ملفات قاعدة البيانات (*.db)"
                                )[0]

                                if backup_path:
                                    # نسخ ملف النسخة الاحتياطية إلى المسار الرئيسي
                                    import shutil
                                    shutil.copy2(backup_path, db_path)
                                    logging.info(f"تم استرجاع قاعدة البيانات من النسخة الاحتياطية: {backup_path}")
                                    QMessageBox.information(None, "تم بنجاح", "تم استرجاع قاعدة البيانات من النسخة الاحتياطية بنجاح.")
                                    result = QMessageBox.No  # لتخطي إعادة إنشاء قاعدة البيانات
                                else:
                                    logging.info("تم إلغاء استرجاع النسخة الاحتياطية من قبل المستخدم")
                                    result = QMessageBox.question(
                                        None,
                                        "إعادة إنشاء قاعدة البيانات",
                                        "هل تريد إعادة إنشاء قاعدة البيانات؟",
                                        QMessageBox.Yes | QMessageBox.No,
                                        QMessageBox.Yes
                                    )
                            except Exception as e:
                                logging.error(f"خطأ في استرجاع النسخة الاحتياطية: {e}")
                                QMessageBox.critical(None, "خطأ", f"حدث خطأ أثناء استرجاع النسخة الاحتياطية: {str(e)}")
                                result = QMessageBox.question(
                                    None,
                                    "إعادة إنشاء قاعدة البيانات",
                                    "هل تريد إعادة إنشاء قاعدة البيانات؟",
                                    QMessageBox.Yes | QMessageBox.No,
                                    QMessageBox.Yes
                                )
                        elif clickedButton == recreateButton:
                            result = QMessageBox.Yes
                        else:  # cancelButton
                            result = QMessageBox.No
                    else:
                        # لا توجد نسخ احتياطية، عرض رسالة بسيطة
                        result = QMessageBox.question(
                            None,
                            "خطأ في قاعدة البيانات",
                            f"حدث خطأ في قاعدة البيانات: {str(e)}\nولا توجد نسخ احتياطية. هل تريد إعادة إنشاء قاعدة البيانات؟",
                            QMessageBox.Yes | QMessageBox.No,
                            QMessageBox.Yes
                        )

                    if result == QMessageBox.Yes:
                        # استدعاء دالة تهيئة قاعدة البيانات
                        if 'initialize_database' in globals():
                            # حذف قاعدة البيانات القديمة إذا كانت موجودة
                            try:
                                if os.path.exists(db_path):
                                    # إنشاء نسخة احتياطية من قاعدة البيانات القديمة
                                    backup_path = f"{db_path}.bak.{datetime.now().strftime('%Y%m%d%H%M%S')}"
                                    import shutil
                                    shutil.copy2(db_path, backup_path)
                                    logging.info(f"تم إنشاء نسخة احتياطية من قاعدة البيانات القديمة: {backup_path}")

                                    # حذف قاعدة البيانات القديمة
                                    os.remove(db_path)
                                    logging.info("تم حذف قاعدة البيانات القديمة")
                            except Exception as e:
                                logging.error(f"خطأ في حذف قاعدة البيانات القديمة: {e}")

                            # إنشاء قاعدة بيانات جديدة
                            db_init_result = initialize_database(db_path)
                            if db_init_result:
                                logging.info("تم إعادة إنشاء قاعدة البيانات بنجاح")
                                QMessageBox.information(None, "تم بنجاح", "تم إعادة إنشاء قاعدة البيانات بنجاح")
                            else:
                                logging.error("فشل إعادة إنشاء قاعدة البيانات")
                                QMessageBox.critical(None, "خطأ", "فشل إعادة إنشاء قاعدة البيانات. سيتم إغلاق البرنامج.")
                                sys.exit(1)
                        else:
                            logging.error("وحدة تهيئة قاعدة البيانات غير متوفرة")
                            QMessageBox.critical(None, "خطأ", "وحدة تهيئة قاعدة البيانات غير متوفرة. سيتم إغلاق البرنامج.")
                            sys.exit(1)
                    else:
                        logging.info("تم إلغاء إعادة إنشاء قاعدة البيانات من قبل المستخدم")
                        QMessageBox.warning(None, "تحذير", "تم إلغاء إعادة إنشاء قاعدة البيانات. سيتم إغلاق البرنامج.")
                        sys.exit(0)
        except Exception as e:
            logging.error(f"خطأ عام في التحقق من قاعدة البيانات: {e}")
            logging.error(traceback.format_exc())

            # التحقق من وجود نسخ احتياطية
            try:
                backups_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), "Backups")
                backup_files = []

                if os.path.exists(backups_dir):
                    # البحث عن ملفات النسخ الاحتياطية
                    for file in os.listdir(backups_dir):
                        if file.startswith("data_backup_") and file.endswith(".db"):
                            backup_files.append(file)

                    # ترتيب النسخ الاحتياطية حسب التاريخ (الأحدث أولاً)
                    backup_files.sort(reverse=True)

                # عرض رسالة للمستخدم مع خيارات مختلفة
                from PyQt5.QtWidgets import QMessageBox, QFileDialog

                # إنشاء تطبيق مؤقت إذا لم يكن موجوداً
                app_instance = QApplication.instance()
                if not app_instance:
                    app_instance = QApplication(sys.argv)

                message = f"حدث خطأ عام في التحقق من قاعدة البيانات: {str(e)}"
                if backup_files:
                    message += f"\nتم العثور على {len(backup_files)} نسخة احتياطية في مجلد Backups."
                    message += "\nماذا تريد أن تفعل؟"

                    # إنشاء مربع حوار مخصص مع ثلاثة أزرار
                    msgBox = QMessageBox()
                    msgBox.setWindowTitle("خطأ في قاعدة البيانات")
                    msgBox.setText(message)
                    msgBox.setIcon(QMessageBox.Critical)

                    # إضافة الأزرار
                    restoreButton = msgBox.addButton("استرجاع نسخة احتياطية", QMessageBox.ActionRole)
                    msgBox.addButton("إغلاق البرنامج", QMessageBox.RejectRole)

                    # عرض مربع الحوار وانتظار الرد
                    msgBox.exec_()

                    # معالجة الرد
                    clickedButton = msgBox.clickedButton()

                    if clickedButton == restoreButton:
                        # فتح مجلد النسخ الاحتياطية
                        try:
                            # فتح مستعرض الملفات لاختيار ملف النسخة الاحتياطية
                            backup_path = QFileDialog.getOpenFileName(
                                None,
                                "اختر ملف النسخة الاحتياطية",
                                backups_dir,
                                "ملفات قاعدة البيانات (*.db)"
                            )[0]

                            if backup_path:
                                # نسخ ملف النسخة الاحتياطية إلى المسار الرئيسي
                                import shutil
                                shutil.copy2(backup_path, "data.db")
                                logging.info(f"تم استرجاع قاعدة البيانات من النسخة الاحتياطية: {backup_path}")
                                QMessageBox.information(None, "تم بنجاح", "تم استرجاع قاعدة البيانات من النسخة الاحتياطية بنجاح.")
                                # إعادة تشغيل البرنامج
                                QMessageBox.information(None, "إعادة تشغيل", "سيتم إعادة تشغيل البرنامج الآن.")
                                os.execl(sys.executable, sys.executable, *sys.argv)
                            else:
                                logging.info("تم إلغاء استرجاع النسخة الاحتياطية من قبل المستخدم")
                                QMessageBox.warning(None, "تحذير", "تم إلغاء استرجاع النسخة الاحتياطية. سيتم إغلاق البرنامج.")
                                sys.exit(1)
                        except Exception as e:
                            logging.error(f"خطأ في استرجاع النسخة الاحتياطية: {e}")
                            QMessageBox.critical(None, "خطأ", f"حدث خطأ أثناء استرجاع النسخة الاحتياطية: {str(e)}")
                            sys.exit(1)
                    # تم إزالة خيار إعادة إنشاء قاعدة البيانات
                    else:  # exitButton
                        logging.info("تم اختيار إغلاق البرنامج")
                        QMessageBox.warning(None, "إغلاق", "سيتم إغلاق البرنامج.")
                        sys.exit(0)
                else:
                    # لا توجد نسخ احتياطية، عرض رسالة بسيطة
                    QMessageBox.critical(
                        None,
                        "خطأ في قاعدة البيانات",
                        f"حدث خطأ في قاعدة البيانات: {str(e)}\nولا توجد نسخ احتياطية. سيتم إغلاق البرنامج."
                    )
                    sys.exit(1)
            except Exception as e:
                logging.error(f"خطأ في معالجة خطأ قاعدة البيانات: {e}")
                logging.error(traceback.format_exc())

                # عرض رسالة بسيطة للمستخدم
                from PyQt5.QtWidgets import QMessageBox
                QMessageBox.critical(None, "خطأ", f"حدث خطأ عام في التحقق من قاعدة البيانات: {str(e)}")
                sys.exit(1)

        app = QApplication(sys.argv)
        logging.info("تم إنشاء تطبيق QApplication")

        # ضبط ملف النمط للتطبيق بالكامل
        app.setStyleSheet("""
            QMainWindow, QWidget {
                background-color: #f0f2f5;
                font-family: Calibri;
            }
            QPushButton {
                padding: 5px 10px;
                border-radius: 5px;
            }
        """)

        # إنشاء وإظهار نافذة الترحيب
        welcome_dialog = WelcomeDialog()
        welcome_dialog.show()
        logging.info("تم عرض نافذة الترحيب")

        # انتظار قبول النافذة (الضغط على زر الدخول)
        if welcome_dialog.exec_() == QDialog.Accepted:
            # الحصول على النافذة الرئيسية المنشأة مسبقًا
            main_window = welcome_dialog.get_main_window()

            if main_window:
                # إظهار النافذة الرئيسية على كامل الشاشة
                main_window.showMaximized()
                logging.info("تم عرض النافذة الرئيسية بوضع ملء الشاشة")

                # تنفيذ حلقة الحدث الرئيسية
                logging.info("بدء تنفيذ حلقة الحدث الرئيسية")
                exit_code = app.exec_()
                logging.info(f"انتهاء التنفيذ برمز خروج: {exit_code}")
                sys.exit(exit_code)
            else:
                logging.critical("لم يتم إنشاء النافذة الرئيسية بشكل صحيح")
                QMessageBox.critical(None, "خطأ", "فشل تحميل النافذة الرئيسية")
                sys.exit(1)
        else:
            logging.info("تم إلغاء الدخول إلى البرنامج")
            sys.exit(0)

    except Exception as e:
        logging.critical(f"خطأ عند تشغيل التطبيق: {e}")
        logging.critical(traceback.format_exc())
        print(f"خطأ عند تشغيل التطبيق: {e}")
        traceback.print_exc()

        # إظهار نافذة خطأ للمستخدم
        from PyQt5.QtWidgets import QMessageBox
        error_box = QMessageBox()
        error_box.setIcon(QMessageBox.Critical)
        error_box.setWindowTitle("خطأ في التطبيق")
        error_box.setText(f"حدث خطأ أثناء تشغيل التطبيق: {str(e)}")
        error_box.setDetailedText(traceback.format_exc())
        error_box.exec_()

        # للحالات الحرجة في البرنامج المحزم، إظهار رسالة خطأ في نافذة نظام التشغيل
        if getattr(sys, 'frozen', False):
            import ctypes
            ctypes.windll.user32.MessageBoxW(0, f"خطأ عند تشغيل التطبيق: {str(e)}\nراجع ملف السجل: {log_file}", "خطأ في البرنامج", 0)

        sys.exit(1)

if __name__ == "__main__":
    main()

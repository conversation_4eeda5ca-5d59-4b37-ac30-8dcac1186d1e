import os
import sqlite3
from datetime import datetime, timed<PERSON><PERSON>
from PyQt5.QtWidgets import (<PERSON>Widget, QVBoxLayout, QHBoxLayout, QFormLayout, QLabel,
                            QLineEdit, QDateEdit, QSpinBox, QTextEdit, QComboBox,
                            QPushButton, QFileDialog, QFrame, QGridLayout, QMessageBox,
                            QGraphicsDropShadowEffect, QApplication, QMainWindow, QGroupBox,
                            QScrollArea, QDialog)
from PyQt5.QtGui import QFont, QPixmap, QColor, QIcon
from PyQt5.QtCore import Qt, QDate

# --- استيراد نافذة معالجة تبرير الغياب ---
# تم إلغاء استيراد نافذة معالجة تبرير الغياب لأن الملف لم يعد موجوداً
# try:
#     from hirasa600_window import AbsenceManagementWindow
#     ABSENCE_MANAGEMENT_WINDOW_AVAILABLE = True
# except ImportError as e:
#     print(f"تحذير: لم يتم العثور على نافذة معالجة تبرير الغياب: {e}")
#     ABSENCE_MANAGEMENT_WINDOW_AVAILABLE = False

# تعيين المتغير إلى False مباشرة لأن النافذة غير متوفرة
ABSENCE_MANAGEMENT_WINDOW_AVAILABLE = False

# تعريف فئة وهمية لتجنب الأخطاء في حالة استخدامها في مكان آخر في الكود
class AbsenceManagementWindow:
    """فئة وهمية لنافذة معالجة تبرير الغياب التي لم تعد موجودة"""
    def __init__(self, parent=None):
        self.parent = parent
        print("تحذير: نافذة معالجة تبرير الغياب غير متوفرة")

    def show(self):
        QMessageBox.warning(self.parent, "ميزة غير متوفرة", "نافذة معالجة تبرير الغياب غير متوفرة")
        pass

class AbsenceJustificationWindow(QMainWindow):
    def __init__(self, db_path="data.db", parent=None):
        super().__init__(parent)
        self.db_path = db_path

        # تهيئة النافذة
        self.initUI()

    def initUI(self):
        # تعيين اتجاه النافذة من اليمين إلى اليسار
        self.setLayoutDirection(Qt.RightToLeft)

        # إنشاء ويدجت مركزي
        central_widget = QWidget()
        self.setCentralWidget(central_widget)

        # إنشاء تخطيط أفقي رئيسي
        main_layout = QHBoxLayout(central_widget)
        main_layout.setContentsMargins(10, 10, 10, 10)
        main_layout.setSpacing(10)

        # تطبيق الأنماط العامة
        self.setStyleSheet("""
            QGroupBox {
                font-family: 'Calibri';
                font-size: 12pt;
                font-weight: bold;
                color: #2c3e50;
                border: 1px solid #bdc3c7;
                border-radius: 8px;
                margin-top: 10px;
                padding-top: 10px;
                background-color: #f5f8fa;
            }

            QGroupBox::title {
                subcontrol-origin: margin;
                subcontrol-position: top center;
                padding: 0 5px;
                color: #2980b9;
            }

            QLabel {
                font-family: 'Calibri';
                font-size: 11pt;
                font-weight: bold;
                color: #2c3e50;
            }

            QLineEdit, QDateEdit, QSpinBox, QComboBox, QTextEdit {
                font-family: 'Calibri';
                font-size: 11pt;
                border: 1px solid #bdc3c7;
                border-radius: 5px;
                padding: 3px;
            }

            QLineEdit:read-only, QLineEdit:disabled {
                background-color: #f0f0f0;
                color: #7f8c8d;
            }

            QPushButton {
                font-family: 'Calibri';
                font-size: 11pt;
                font-weight: bold;
                color: #ffffff;
                background-color: #3498db;
                border-radius: 6px;
                padding: 6px 12px;
            }

            QPushButton:hover {
                background-color: #2980b9;
            }

            QPushButton:pressed {
                background-color: #1f6aa8;
            }
        """)

        # ==== الجانب الأيمن: معلومات التلميذ وتبرير الغياب ====
        right_panel = QWidget()
        right_layout = QVBoxLayout(right_panel)
        right_layout.setContentsMargins(5, 5, 5, 5)
        right_layout.setSpacing(10)

        # ==== القسم الأول: معلومات التلميذ ====
        student_group = QGroupBox("معلومات التلميذ")
        student_layout = QFormLayout(student_group)
        student_layout.setLabelAlignment(Qt.AlignRight)
        student_layout.setFormAlignment(Qt.AlignLeft)
        student_layout.setFieldGrowthPolicy(QFormLayout.AllNonFixedFieldsGrow)
        student_layout.setVerticalSpacing(8)

        # إنشاء الحقول
        self.code_input = QLineEdit()
        self.code_input.setReadOnly(True)
        self.code_input.setCursor(Qt.PointingHandCursor)

        self.name_input = QLineEdit()
        self.name_input.setReadOnly(True)
        self.name_input.setCursor(Qt.PointingHandCursor)

        self.id_input = QLineEdit()
        self.id_input.setReadOnly(True)
        self.id_input.setCursor(Qt.PointingHandCursor)

        self.level_input = QLineEdit()
        self.level_input.setReadOnly(True)
        self.level_input.setCursor(Qt.PointingHandCursor)

        self.class_input = QLineEdit()
        self.class_input.setReadOnly(True)
        self.class_input.setCursor(Qt.PointingHandCursor)

        # تنسيق الخط للإبراز
        self.name_input.setStyleSheet("font-weight: bold; color: #2c3e50;")
        self.id_input.setStyleSheet("font-weight: bold; color: #2c3e50;")

        # ترتيب أفقي للمعلومات
        code_name_layout = QHBoxLayout()
        code_name_layout.addWidget(QLabel("الرمز:"))
        code_name_layout.addWidget(self.code_input, 1)
        code_name_layout.addSpacing(10)
        code_name_layout.addWidget(QLabel("الاسم والنسب:"))
        code_name_layout.addWidget(self.name_input, 3)

        level_class_layout = QHBoxLayout()
        level_class_layout.addWidget(QLabel("ر.ت:"))
        level_class_layout.addWidget(self.id_input, 1)
        level_class_layout.addSpacing(10)
        level_class_layout.addWidget(QLabel("المستوى:"))
        level_class_layout.addWidget(self.level_input, 1)
        level_class_layout.addSpacing(10)
        level_class_layout.addWidget(QLabel("القسم:"))
        level_class_layout.addWidget(self.class_input, 1)

        # إضافة الحقول المرتبة أفقيًا
        student_layout.addRow(code_name_layout)
        student_layout.addRow(level_class_layout)

        # السنة الدراسية والأسدس
        self.school_year_input = QLineEdit()
        self.school_year_input.setReadOnly(True)
        self.school_year_input.setCursor(Qt.PointingHandCursor)

        self.semester_input = QLineEdit()
        self.semester_input.setReadOnly(True)
        self.semester_input.setCursor(Qt.PointingHandCursor)

        year_semester_layout = QHBoxLayout()
        year_semester_layout.addWidget(QLabel("السنة الدراسية:"))
        year_semester_layout.addWidget(self.school_year_input, 2)
        year_semester_layout.addSpacing(10)
        year_semester_layout.addWidget(QLabel("الأسدس:"))
        year_semester_layout.addWidget(self.semester_input, 1)

        student_layout.addRow(year_semester_layout)

        right_layout.addWidget(student_group)

        # ==== القسم الثاني: معلومات تبرير الغياب ====
        justification_group = QGroupBox("معلومات تبرير الغياب")
        justification_layout = QGridLayout(justification_group)
        justification_layout.setVerticalSpacing(10)
        justification_layout.setHorizontalSpacing(15)

        # إنشاء الحقول
        self.just_date_edit = QDateEdit()
        self.just_date_edit.setCalendarPopup(True)
        self.just_date_edit.setDate(QDate.currentDate())
        self.just_date_edit.setCursor(Qt.PointingHandCursor)

        self.start_date_edit = QDateEdit()
        self.start_date_edit.setCalendarPopup(True)
        self.start_date_edit.setDate(QDate.currentDate())
        self.start_date_edit.dateChanged.connect(self.calculate_end_date)
        self.start_date_edit.setCursor(Qt.PointingHandCursor)

        self.end_date_edit = QDateEdit()
        self.end_date_edit.setCalendarPopup(True)
        self.end_date_edit.setDate(QDate.currentDate())
        self.end_date_edit.setCursor(Qt.PointingHandCursor)

        self.days_spinbox = QSpinBox()
        self.days_spinbox.setRange(1, 365)
        self.days_spinbox.setValue(1)
        self.days_spinbox.valueChanged.connect(self.calculate_end_date)
        self.days_spinbox.setCursor(Qt.PointingHandCursor)

        self.reason_input = QLineEdit()
        self.reason_input.setPlaceholderText("أدخل سبب الغياب...")
        self.reason_input.setText("شهادة طبية")
        self.reason_input.setCursor(Qt.PointingHandCursor)

        self.notes_text = QTextEdit()
        self.notes_text.setMaximumHeight(80)
        self.notes_text.setPlaceholderText("يمكنك إضافة ملاحظات إضافية هنا...")
        self.notes_text.setCursor(Qt.PointingHandCursor)

        # تنسيق في شبكة
        justification_layout.addWidget(QLabel("تاريخ تبرير الغياب:"), 0, 0)
        justification_layout.addWidget(self.just_date_edit, 0, 1)
        justification_layout.addWidget(QLabel("تاريخ البداية:"), 0, 2)
        justification_layout.addWidget(self.start_date_edit, 0, 3)

        justification_layout.addWidget(QLabel("تاريخ النهاية:"), 1, 0)
        justification_layout.addWidget(self.end_date_edit, 1, 1)
        justification_layout.addWidget(QLabel("عدد الأيام:"), 1, 2)
        justification_layout.addWidget(self.days_spinbox, 1, 3)

        justification_layout.addWidget(QLabel("مبرر الغياب:"), 2, 0)
        justification_layout.addWidget(self.reason_input, 2, 1, 1, 3)

        justification_layout.addWidget(QLabel("ملاحظات:"), 3, 0, Qt.AlignTop)
        justification_layout.addWidget(self.notes_text, 3, 1, 1, 3)

        right_layout.addWidget(justification_group)

        # ==== أزرار الإجراءات ====
        actions_layout = QHBoxLayout()

        # زر الحفظ
        self.save_button = QPushButton("حفظ تبرير الغياب")
        self.save_button.setIcon(QIcon.fromTheme("document-save", QIcon()))
        self.save_button.clicked.connect(self.save_justification)
        self.save_button.setStyleSheet("background-color: #27ae60;")
        self.save_button.setMinimumHeight(35)
        self.save_button.setMinimumWidth(180)
        self.save_button.setCursor(Qt.PointingHandCursor)

        # تعديل التخطيط بحيث يحتوي على زر الحفظ فقط
        actions_layout.addStretch()  # إضافة مسافة مرنة على اليمين
        actions_layout.addWidget(self.save_button)
        actions_layout.addStretch()  # إضافة مسافة مرنة على اليسار

        right_layout.addLayout(actions_layout)
        right_layout.addStretch()

        # ==== الجانب الأيسر: صورة تبرير الغياب ====
        left_panel = QWidget()
        left_layout = QVBoxLayout(left_panel)
        left_layout.setContentsMargins(5, 5, 5, 5)
        left_layout.setSpacing(10)

        # ==== صورة تبرير الغياب ====
        image_group = QGroupBox("صورة تبرير الغياب (اختياري)")
        image_layout = QVBoxLayout(image_group)

        # مربع عرض الصورة
        self.image_label = QLabel()
        self.image_label.setMinimumHeight(300)
        self.image_label.setAlignment(Qt.AlignCenter)
        self.image_label.setStyleSheet("""
            border: 2px dashed #bdc3c7;
            background-color: #fdfdfd;
        """)
        self.image_label.setText("لم يتم اختيار أي صورة")

        # زر اختيار الصورة
        self.upload_button = QPushButton("اختيار صورة")
        self.upload_button.setIcon(QIcon.fromTheme("document-open", QIcon()))
        self.upload_button.clicked.connect(self.open_file_dialog)
        self.upload_button.setStyleSheet("background-color: #16a085;")
        self.upload_button.setFixedWidth(120)
        self.upload_button.setCursor(Qt.PointingHandCursor)

        # إضافة العناصر إلى تخطيط الصورة
        image_layout.addWidget(self.image_label)

        # إضافة زر اختيار الصورة في سطر جديد
        upload_layout = QHBoxLayout()
        upload_layout.addWidget(self.upload_button)
        upload_layout.addStretch()
        image_layout.addLayout(upload_layout)

        left_layout.addWidget(image_group)

        # ==== إضافة تعليمات للمستخدم ====
        instruction_label = QLabel("""
            <b>تعليمات:</b>
            <ul>
                <li>تأكد من اختيار التلميذ الصحيح قبل حفظ تبرير الغياب</li>
                <li>يمكنك إضافة صورة للشهادة الطبية أو مبرر الغياب (اختياري)</li>
                <li>تحديد عدد الأيام سيقوم بحساب تاريخ النهاية تلقائيًا</li>
            </ul>
        """)
        instruction_label.setWordWrap(True)
        instruction_label.setAlignment(Qt.AlignRight)
        instruction_label.setStyleSheet("""
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 8px;
            color: #555;
            border: 1px solid #ddd;
        """)

        left_layout.addWidget(instruction_label)
        left_layout.addStretch()

        # ضبط النسب بين الجانبين (65% يمين، 35% يسار)
        main_layout.addWidget(right_panel, 65)
        main_layout.addWidget(left_panel, 35)

        # مسار الصورة المختارة
        self.selected_image_path = None

        # استدعاء دوال تحميل البيانات
        self.load_initial_data()

    def calculate_end_date(self):
        """حساب تاريخ النهاية بناءً على تاريخ البداية وعدد الأيام"""
        try:
            start_date = self.start_date_edit.date().toPyDate()
            days = self.days_spinbox.value()
            end_date = start_date + timedelta(days=days-1)  # نطرح 1 لأن اليوم الأول محسوب
            self.end_date_edit.setDate(QDate(end_date.year, end_date.month, end_date.day))
        except Exception as e:
            print(f"خطأ في حساب تاريخ نهاية الغياب: {e}")

    def load_initial_data(self):
        """تحميل البيانات الأولية مثل السنة الدراسية والأسدس"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # تحميل السنة الدراسية والأسدس من جدول بيانات_المؤسسة
            cursor.execute("SELECT السنة_الدراسية, الأسدس FROM بيانات_المؤسسة LIMIT 1")
            result = cursor.fetchone()
            if result:
                self.school_year_input.setText(str(result[0]))
                self.semester_input.setText(str(result[1]))

            conn.close()
        except Exception as e:
            print(f"خطأ في تحميل البيانات الأولية: {e}")

    def open_file_dialog(self):
        """فتح مربع حوار لاختيار صورة"""
        options = QFileDialog.Options()
        file_path, _ = QFileDialog.getOpenFileName(
            self, "اختيار صورة", "",
            "صور (*.png *.jpg *.jpeg *.bmp *.gif);;كل الملفات (*)",
            options=options
        )

        if file_path:
            self.selected_image_path = file_path
            pixmap = QPixmap(file_path)

            # تغيير حجم الصورة للتناسب مع مربع العرض مع الحفاظ على التناسب
            pixmap = pixmap.scaled(400, 180, Qt.KeepAspectRatio, Qt.SmoothTransformation)
            self.image_label.setPixmap(pixmap)

    def show_custom_success_message(self, student_name, days_count, reason, start_date, end_date):
        """عرض رسالة نجاح مخصصة بعد حفظ تبرير الغياب"""
        # طباعة البيانات للتأكد من أنها صحيحة
        print(f"عرض رسالة تأكيد بالبيانات التالية:")
        print(f"اسم الطالب: {student_name}")
        print(f"عدد أيام الغياب: {days_count}")
        print(f"سبب الغياب: {reason}")
        print(f"تاريخ البداية: {start_date}")
        print(f"تاريخ النهاية: {end_date}")

        # إنشاء نافذة حوار مخصصة
        success_dialog = QDialog(self)
        success_dialog.setWindowTitle("تم حفظ تبرير الغياب")
        success_dialog.setFixedSize(500, 350)
        success_dialog.setLayoutDirection(Qt.RightToLeft)

        # إضافة أيقونة البرنامج
        try:
            app_icon = QIcon("01.ico")
            success_dialog.setWindowIcon(app_icon)
        except Exception as e:
            print(f"خطأ في تحميل أيقونة البرنامج: {e}")

        # إنشاء تخطيط النافذة
        layout = QVBoxLayout(success_dialog)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(15)

        # إضافة أيقونة وعنوان
        header_layout = QHBoxLayout()

        # محاولة إضافة أيقونة البرنامج
        icon_label = QLabel()
        icon_label.setAlignment(Qt.AlignCenter)
        try:
            program_icon = QPixmap("01.ico")
            if not program_icon.isNull():
                program_icon = program_icon.scaled(48, 48, Qt.KeepAspectRatio, Qt.SmoothTransformation)
                icon_label.setPixmap(program_icon)
                header_layout.addWidget(icon_label)
        except Exception as e:
            print(f"خطأ في تحميل أيقونة البرنامج للعنوان: {e}")

        # إضافة عنوان
        title_label = QLabel("تم حفظ تبرير الغياب بنجاح")
        title_label.setFont(QFont("Calibri", 16, QFont.Bold))
        title_label.setStyleSheet("color: #27ae60;")
        title_label.setAlignment(Qt.AlignCenter)
        header_layout.addWidget(title_label)

        layout.addLayout(header_layout)

        # إضافة خط فاصل
        separator = QFrame()
        separator.setFrameShape(QFrame.HLine)
        separator.setFrameShadow(QFrame.Sunken)
        separator.setStyleSheet("background-color: #bdc3c7;")
        layout.addWidget(separator)

        # إضافة معلومات التبرير
        info_frame = QFrame()
        info_frame.setStyleSheet("""
            background-color: #f0f9ff;
            border: 2px solid #3498db;
            border-radius: 10px;
            padding: 10px;
        """)
        info_layout = QVBoxLayout(info_frame)

        # إضافة معلومات التلميذ - استخدام نص ثابت للتأكد من عرض البيانات
        student_text = f"<b>التلميذ(ة):</b> {student_name}"
        student_label = QLabel(student_text)
        student_label.setFont(QFont("Calibri", 14))
        student_label.setStyleSheet("color: #2c3e50;")
        info_layout.addWidget(student_label)

        # إضافة معلومات التبرير - استخدام نص ثابت للتأكد من عرض البيانات
        days_text = f"<b>عدد أيام الغياب:</b> {str(days_count)}"
        days_label = QLabel(days_text)
        days_label.setFont(QFont("Calibri", 14))
        days_label.setStyleSheet("color: #2c3e50;")
        info_layout.addWidget(days_label)

        date_text = f"<b>الفترة:</b> من {start_date} إلى {end_date}"
        date_label = QLabel(date_text)
        date_label.setFont(QFont("Calibri", 14))
        date_label.setStyleSheet("color: #2c3e50;")
        info_layout.addWidget(date_label)

        reason_text = f"<b>سبب الغياب:</b> {reason}"
        reason_label = QLabel(reason_text)
        reason_label.setFont(QFont("Calibri", 14))
        reason_label.setStyleSheet("color: #2c3e50;")
        info_layout.addWidget(reason_label)

        # إضافة التخطيط إلى الإطار
        info_frame.setLayout(info_layout)
        layout.addWidget(info_frame)

        # إضافة ملاحظة
        note_label = QLabel("تم حفظ تبرير الغياب بنجاح في قاعدة البيانات")
        note_label.setFont(QFont("Calibri", 14, QFont.Bold))
        note_label.setAlignment(Qt.AlignCenter)
        note_label.setWordWrap(True)
        note_label.setStyleSheet("""
            color: #27ae60;
            background-color: #e8f8f5;
            border: 2px solid #2ecc71;
            border-radius: 8px;
            padding: 12px;
            margin-top: 10px;
        """)
        layout.addWidget(note_label)

        # إضافة زر موافق
        ok_button = QPushButton("موافق")
        ok_button.setFont(QFont("Calibri", 14, QFont.Bold))
        ok_button.setCursor(Qt.PointingHandCursor)  # هذا الزر بالفعل يحتوي على خاصية تغيير المؤشر
        ok_button.setMinimumHeight(40)
        ok_button.setStyleSheet("""
            QPushButton {
                background-color: #2ecc71;
                color: white;
                border: none;
                border-radius: 5px;
                padding: 8px 16px;
            }
            QPushButton:hover {
                background-color: #27ae60;
                border: 2px solid #2ecc71;
            }
        """)
        ok_button.clicked.connect(success_dialog.accept)
        layout.addWidget(ok_button)

        # عرض النافذة
        success_dialog.exec_()

    def save_justification(self):
        """حفظ بيانات تبرير الغياب"""
        try:
            # التحقق من وجود بيانات الطالب
            if not self.code_input.text() or not self.name_input.text():
                QMessageBox.warning(self, "تنبيه", "الرجاء اختيار تلميذ أولاً")
                return

            # جمع البيانات من الحقول
            student_code = self.code_input.text()
            student_name = self.name_input.text()
            student_id = self.id_input.text()
            level = self.level_input.text()
            class_name = self.class_input.text()
            justification_date = self.just_date_edit.date().toString("yyyy-MM-dd")
            start_date = self.start_date_edit.date().toString("yyyy-MM-dd")
            end_date = self.end_date_edit.date().toString("yyyy-MM-dd")
            days_count = self.days_spinbox.value()
            reason = self.reason_input.text()
            notes = self.notes_text.toPlainText()

            # إنشاء جدول تبريرات الغياب إذا لم يكن موجوداً
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # استعلام للحصول على السنة الدراسية والأسدس من جدول بيانات_المؤسسة
            cursor.execute("SELECT السنة_الدراسية, الأسدس FROM بيانات_المؤسسة LIMIT 1")
            school_data = cursor.fetchone()

            # التحقق من وجود بيانات السنة الدراسية والأسدس
            if not school_data:
                QMessageBox.warning(self, "تنبيه", "لم يتم العثور على بيانات السنة الدراسية والأسدس في جدول بيانات_المؤسسة")
                conn.close()
                return

            # استخراج السنة الدراسية والأسدس
            school_year = school_data[0]
            semester = school_data[1]

            print(f"تم استرجاع السنة الدراسية: {school_year} والأسدس: {semester} من جدول بيانات_المؤسسة")

            cursor.execute('''
                CREATE TABLE IF NOT EXISTS تبريرات_الغياب (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    رمز_التلميذ TEXT,
                    اسم_التلميذ TEXT,
                    ر_ت TEXT,
                    المستوى TEXT,
                    القسم TEXT,
                    تاريخ_التبرير TEXT,
                    تاريخ_البداية TEXT,
                    تاريخ_النهاية TEXT,
                    عدد_الأيام INTEGER,
                    عدد_الساعات INTEGER,
                    سبب_الغياب TEXT,
                    ملاحظات TEXT,
                    مسار_الصورة TEXT,
                    تاريخ_التسجيل TEXT,
                    السنة_الدراسية TEXT,
                    الأسدس TEXT
                )
            ''')

            # تحديد مسار الصورة الذي سيتم تخزينه
            image_path = None
            if self.selected_image_path:
                # إنشاء مجلد للصور إذا لم يكن موجوداً
                images_dir = os.path.join(os.path.dirname(self.db_path), "absence_images")
                if not os.path.exists(images_dir):
                    os.makedirs(images_dir)

                # نسخ الصورة إلى المجلد مع اسم فريد
                file_ext = os.path.splitext(self.selected_image_path)[1]
                new_file_name = f"{student_code}_{datetime.now().strftime('%Y%m%d%H%M%S')}{file_ext}"
                new_file_path = os.path.join(images_dir, new_file_name)

                # نسخ الصورة
                import shutil
                shutil.copy2(self.selected_image_path, new_file_path)

                image_path = new_file_path

            # إدخال البيانات
            cursor.execute('''
                INSERT INTO تبريرات_الغياب (
                    رمز_التلميذ, اسم_التلميذ, ر_ت, المستوى, القسم,
                    تاريخ_التبرير, تاريخ_البداية, تاريخ_النهاية,
                    عدد_الأيام, عدد_الساعات, سبب_الغياب, ملاحظات,
                    مسار_الصورة, تاريخ_التسجيل, السنة_الدراسية, الأسدس
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                student_code, student_name, student_id, level, class_name,
                justification_date, start_date, end_date,
                days_count, 0, reason, notes,  # تعيين عدد الساعات إلى صفر
                image_path, datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                school_year, semester
            ))

            conn.commit()
            conn.close()

            # عرض رسالة نجاح مخصصة
            self.show_custom_success_message(student_name, days_count, reason, start_date, end_date)

            # مسح النموذج للإدخال التالي
            self.notes_text.clear()
            self.days_spinbox.setValue(1)
            self.reason_input.setText("شهادة طبية")
            self.image_label.setText("لم يتم اختيار أي صورة")
            self.image_label.setPixmap(QPixmap())  # إزالة الصورة
            self.selected_image_path = None

            # لا نمسح بيانات الطالب لتسهيل إدخال تبرير آخر لنفس الطالب

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء حفظ البيانات: {str(e)}")
            print(f"خطأ في حفظ تبرير الغياب: {e}")
            if 'conn' in locals() and conn:
                conn.close()

    def set_student_info(self, code, name, id_num, level, class_name):
        """تعيين معلومات الطالب برمجياً مع التحقق من السنة الدراسية الحالية"""
        try:
            print(f"محاولة تعيين معلومات الطالب: {name}, المستوى: {level}, القسم: {class_name}, الرقم الترتيبي: {id_num}")

            # التحقق من وجود التلميذ في السنة الدراسية الحالية
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # استرجاع السنة الدراسية الحالية من جدول بيانات_المؤسسة
            cursor.execute("SELECT السنة_الدراسية FROM بيانات_المؤسسة LIMIT 1")
            current_school_year = cursor.fetchone()
            current_school_year = current_school_year[0] if current_school_year else "2023/2024"

            # التحقق من وجود التلميذ في اللوائح للسنة الدراسية الحالية
            cursor.execute("""
                SELECT COUNT(*) FROM اللوائح
                WHERE الرمز = ? AND السنة_الدراسية = ?
            """, (code, current_school_year))

            exists = cursor.fetchone()[0] > 0
            conn.close()

            if not exists:
                print(f"التلميذ {name} غير مسجل في السنة الدراسية الحالية: {current_school_year}")
                QMessageBox.warning(
                    self,
                    "تنبيه",
                    f"التلميذ {name} غير مسجل في السنة الدراسية الحالية: {current_school_year}"
                )
                return False

            # تعيين جميع حقول النص مباشرة
            self.level_input.setText(level)
            self.class_input.setText(class_name)
            self.code_input.setText(code)
            self.name_input.setText(name)
            self.id_input.setText(id_num)

            print(f"تم تعيين معلومات الطالب بنجاح: {name}")
            return True

        except Exception as e:
            print(f"خطأ في تعيين معلومات الطالب: {e}")
            import traceback
            traceback.print_exc()
            return False

class AbsenceJustificationWidget(QWidget):
    """فئة ويدجت يمكن استخدامها لتضمين واجهة تبرير الغياب في واجهات أخرى"""
    def __init__(self, db_path="data.db", parent=None):
        super().__init__(parent)
        self.db_path = db_path

        # إنشاء التخطيط الرئيسي للعنصر
        layout = QVBoxLayout(self)
        layout.setContentsMargins(0, 0, 0, 0)

        # إنشاء نافذة تبرير الغياب كعنصر داخلي
        self.absence_window = AbsenceJustificationWindow(db_path, parent=self)

        # إستخراج الويدجت المركزي من النافذة وإضافته للتخطيط
        central_widget = self.absence_window.centralWidget()
        layout.addWidget(central_widget)

    # إعادة توجيه الدوال المهمة إلى النافذة الداخلية
    def set_student_info(self, code, name, id_num, level, class_name):
        return self.absence_window.set_student_info(code, name, id_num, level, class_name)

if __name__ == "__main__":
    import sys
    app = QApplication(sys.argv)
    app.setLayoutDirection(Qt.RightToLeft)  # تعيين اتجاه التطبيق بالكامل
    window = AbsenceJustificationWindow()
    window.show()
    sys.exit(app.exec_())

#!/usr/bin/env python
# -*- coding: utf-8 -*-

import sys
import os
import sqlite3
from PyQt5.QtWidgets import (
    QApplication, QMainWindow, QTableWidget, QTableWidgetItem, QVBoxLayout,
    QHBoxLayout, QWidget, QHeaderView, QLabel, QLineEdit, QPushButton,
    QComboBox, QFrame, QMessageBox, QSpinBox, QGridLayout, QCheckBox,
    QFileDialog, QInputDialog, QDialog, QDialogButtonBox, QRadioButton,
    QGroupBox, QFormLayout, QScrollArea, QProgressDialog, QDateEdit,
    QTextBrowser, QTextEdit
)
import math
from PyQt5.QtGui import QFont, QIcon, QColor, QPixmap
from PyQt5.QtCore import Qt, QSize, QDate
from datetime import datetime
import pandas as pd
# استيراد نافذة إضافة أرقام الامتحان
from sub24_window import ExamNumbersDialog
# استيراد نافذة ترقيم القاعات
from sub25_window import RoomAssignmentDialog
import random

class BatchFileRequestDialog(QDialog):
    """نافذة حوارية لطلب الملفات الجماعي"""
    
    def __init__(self, parent=None, count=0):
        super().__init__(parent)
        self.count = count
        self.setupUI()
        
    def setupUI(self):
        self.setWindowTitle("طلب الملفات الجماعي")
        self.setMinimumWidth(400)
        self.setMinimumHeight(300)
        self.setStyleSheet("background-color: #f0f8ff;")
        
        layout = QVBoxLayout(self)
        
        # العنوان
        title_label = QLabel(f"طلب الملفات لـ {self.count} وافد")
        title_label.setFont(QFont("Calibri", 16, QFont.Bold))
        title_label.setStyleSheet("color: #0066cc; padding: 10px; border: 2px solid #0066cc; border-radius: 5px;")
        title_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(title_label)
        
        # إطار البيانات
        data_frame = QFrame()
        data_frame.setStyleSheet("background-color: white; border: 1px solid #d0d0d0; border-radius: 5px; padding: 15px;")
        data_layout = QFormLayout(data_frame)
        
        # تاريخ الطلب
        date_label = QLabel("تاريخ الطلب:")
        date_label.setFont(QFont("Calibri", 13, QFont.Bold))
        
        self.date_edit = QDateEdit()
        self.date_edit.setDate(QDate.currentDate())
        self.date_edit.setFont(QFont("Calibri", 13))
        self.date_edit.setFixedHeight(40)
        self.date_edit.setStyleSheet("border: 1px solid #d0d0d0; padding: 5px; border-radius: 3px;")
        self.date_edit.setCalendarPopup(True)
        
        data_layout.addRow(date_label, self.date_edit)
        
        # معلومات إضافية
        info_label = QLabel("سيتم تحديث السجلات التالية:")
        info_label.setFont(QFont("Calibri", 12, QFont.Bold))
        info_label.setStyleSheet("color: #333; margin-top: 10px;")
        layout.addWidget(info_label)
        
        info_text = QLabel("• رقم الطلب = 1\n• تاريخ الطلب الأول = التاريخ المحدد\n• رقم المراسلة = 1")
        info_text.setFont(QFont("Calibri", 11))
        info_text.setStyleSheet("color: #666; margin-left: 10px; padding: 10px;")
        layout.addWidget(info_text)
        
        layout.addWidget(data_frame)
        
        # الأزرار
        buttons_layout = QHBoxLayout()
        
        self.ok_button = QPushButton("تأكيد طلب الملفات")
        self.ok_button.setFont(QFont("Calibri", 13, QFont.Bold))
        self.ok_button.setFixedHeight(45)
        self.ok_button.setStyleSheet("""
            QPushButton {
                background-color: #28a745;
                color: white;
                border-radius: 5px;
                padding: 10px;
                min-width: 150px;
            }
            QPushButton:hover {
                background-color: #218838;
            }
        """)
        self.ok_button.clicked.connect(self.accept)
        
        cancel_button = QPushButton("إلغاء")
        cancel_button.setFont(QFont("Calibri", 13, QFont.Bold))
        cancel_button.setFixedHeight(45)
        cancel_button.setStyleSheet("""
            QPushButton {
                background-color: #6c757d;
                color: white;
                border-radius: 5px;
                padding: 10px;
                min-width: 100px;
            }
            QPushButton:hover {
                background-color: #5a6268;
            }
        """)
        cancel_button.clicked.connect(self.reject)
        
        buttons_layout.addStretch()
        buttons_layout.addWidget(self.ok_button)
        buttons_layout.addWidget(cancel_button)
        buttons_layout.addStretch()
        
        layout.addLayout(buttons_layout)
        
    def get_request_date(self):
        """الحصول على تاريخ الطلب المحدد"""
        return self.date_edit.date().toString('yyyy-MM-dd')

class ExamSetupWindow44(QMainWindow):
    """نافذة عرض بيانات الامتحانات وتهيئة الامتحانات - النسخة 44"""

    def __init__(self, parent=None, db=None, academic_year=None, db_path="data.db"):
        super().__init__(parent)
        print("🔧 تهيئة نافذة سجلات الوافدين والمغادرين - النسخة 44")
        # تخزين قاعدة البيانات المقدمة أو استخدام المسار المحدد
        self.db_connection = db
        self.academic_year = academic_year
        self.db_path = db_path
        print(f"📁 مسار قاعدة البيانات: {self.db_path}")
        self.initUI()
        self.load_data()

    def initUI(self):
        print("🎨 بناء واجهة المستخدم...")
        # تعيين عنوان النافذة
        self.setWindowTitle("سجلات الوافدين والمغادرين")
        self.setWindowIcon(QIcon("01.ico"))

        # الحصول على حجم الشاشة واستخدامه لضبط حجم النافذة
        screen_size = QApplication.desktop().screenGeometry()
        screen_width = screen_size.width()
        screen_height = screen_size.height()

        # تعيين حجم ومكان النافذة (كامل الشاشة)
        self.setGeometry(0, 0, screen_width, screen_height)
        self.setMinimumSize(980, 600)

        # جعل النافذة تأخذ كامل الشاشة عند الفتح
        self.showMaximized()

        # إنشاء ويدجت مركزي
        central_widget = QWidget(self)
        self.setCentralWidget(central_widget)

        # إنشاء التخطيط الرئيسي
        main_layout = QVBoxLayout(central_widget)
        main_layout.setContentsMargins(10, 10, 10, 10)
        main_layout.setSpacing(1)  # تقليل المسافة العمودية بين الإطارات إلى 2

        # إضافة إطار للكائنات في أعلى النافذة
        top_frame = QFrame()
        top_frame.setFrameShape(QFrame.StyledPanel)
        top_frame.setStyleSheet("background-color: white; border: 1px solid #d0d0d0; border-radius: 5px;")
        top_frame.setFixedHeight(80)  # تحديد ارتفاع ثابت لإطار العنوان
        top_layout = QGridLayout(top_frame)

        # إضافة إطار للعنوان في أعلى النافذة
        header_frame = QFrame()
        header_frame.setFrameShape(QFrame.StyledPanel)
        header_frame.setStyleSheet("background-color: white; border: 1px solid #d0d0d0; border-radius: 5px; margin-bottom: 5px;")
        header_frame.setFixedHeight(80)  # تحديد ارتفاع متساو مع الإطارات الأخرى
        header_layout = QVBoxLayout(header_frame)
        header_layout.setContentsMargins(15, 10, 15, 10)

        # العنوان الرئيسي
        self.header_label = QLabel("سجلات الوافدين والمغادرين")
        self.header_label.setFont(QFont("Calibri", 16, QFont.Bold))
        self.header_label.setStyleSheet("color: #2c3e50; padding: 10px;")
        self.header_label.setAlignment(Qt.AlignCenter)
        header_layout.addWidget(self.header_label)

        # إضافة إطار العنوان إلى التخطيط الرئيسي
        main_layout.addWidget(header_frame)

        # إطار منفصل للأزرار
        buttons_frame = QFrame()
        buttons_frame.setFrameShape(QFrame.StyledPanel)
        buttons_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                          stop: 0 #f8f9fa, stop: 0.5 #e9ecef, stop: 1 #dee2e6);
                border: 2px solid #ced4da;
                border-radius: 10px;
                margin: 5px;
                padding: 10px;
            }
        """)
        buttons_frame.setFixedHeight(70)  # تحديد ارتفاع متساو مع الإطارات الأخرى
        buttons_layout = QHBoxLayout(buttons_frame)
        buttons_layout.setSpacing(8)
        buttons_layout.setContentsMargins(15, 0, 15, 10)

        # إنشاء الأزرار مع ربطها بالوظائف المناسبة
        button_data = [
            {"text": "استيراد سجلات الوافدين والمغادرين", "function": self.import_records, "color": "#FF6B6B", "icon": "📥"},
            {"text": "طلب الملفات الجماعي", "function": self.import_candidates, "color": "#4ECDC4", "icon": "📋"},
            {"text": "التوصل بالملفات", "function": self.receive_files, "color": "#28a745", "icon": "📫"},
            {"text": "تحديث رقم الإرسال حسب المؤسسة الأصلية", "function": self.update_shipment_numbers, "color": "#45B7D1", "icon": "📮"},
            {"text": "نموذج طلب الملفات", "function": self.open_request_form, "color": "#96CEB4", "icon": "📝"},
            {"text": "طباعة المحاضر واللوائح", "function": self.open_print_reports, "color": "#FFEAA7", "icon": "🖨️"},
            {"text": "حذف البيانات", "function": self.delete_all_data, "color": "#FD79A8", "icon": "🗑️"}
        ]

        def darken_color(color, factor=0.2):
            """تغميق اللون بنسبة معينة"""
            if color.startswith('#'):
                color = color[1:]
            
            r, g, b = tuple(int(color[i:i+2], 16) for i in (0, 2, 4))
            r = max(0, int(r * (1 - factor)))
            g = max(0, int(g * (1 - factor)))
            b = max(0, int(b * (1 - factor)))
            
            return f'#{r:02x}{g:02x}{b:02x}'

        for button_info in button_data:
            button = QPushButton()
            
            # تعيين النص مع الأيقونة
            button_text = f"{button_info['icon']} {button_info['text']}"
            button.setText(button_text)
            
            # تعيين الخط - الموقع الأول للتحكم في حجم الخط
            button.setFont(QFont("Calibri", 12, QFont.Bold))  # يمكن تغيير الرقم 12 هنا
            
            # تعيين الحد الأدنى للحجم
            button.setMinimumHeight(40)
            button.setMinimumWidth(140)
            
            # تطبيق الألوان والتنسيق
            button_color = button_info["color"]
            darker_color = darken_color(button_color, 0.15)
            darkest_color = darken_color(button_color, 0.3)
            
            button.setStyleSheet(f"""
                QPushButton {{
                    background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                              stop: 0 {button_color}, 
                                              stop: 0.5 {darker_color}, 
                                              stop: 1 {darkest_color});
                    color: white;
                    border: 2px solid {darkest_color};
                    border-radius: 10px;
                    padding: 8px 10px;
                    margin: 2px;
                    font-weight: bold;
                    text-align: center;
                    font-size: 12px;
                }}
                QPushButton:hover {{
                    background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                              stop: 0 {darker_color}, 
                                              stop: 0.5 {darkest_color}, 
                                              stop: 1 {button_color});
                    border: 3px solid {button_color};
                    transform: translateY(-1px);
                }}
                QPushButton:pressed {{
                    background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                              stop: 0 {darkest_color}, 
                                              stop: 1 {darker_color});
                    border: 2px solid {button_color};
                    padding: 10px 10px 6px 10px;
                }}
            """)
            
            # ربط الوظيفة
            button.clicked.connect(button_info["function"])
            
            # إضافة الزر إلى التخطيط
            buttons_layout.addWidget(button)

        # إضافة إطار الأزرار إلى التخطيط الرئيسي
        main_layout.addWidget(buttons_frame)

        # شريط البحث مع تصفية نوع الحركة
        search_frame = QFrame()
        search_frame.setFrameShape(QFrame.StyledPanel)
        search_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1: 0, y1: 0, x2: 1, y2: 1,
                                          stop: 0 #e3f2fd, stop: 0.5 #bbdefb, stop: 1 #90caf9);
                border: 2px solid #2196f3;
                border-radius: 10px;
                padding: 5px;
                margin: 2px;
            }
            QLabel {
                color: #0d47a1;
                font-weight: bold;
                background: transparent;
                border: none;
            }
            QComboBox {
                background-color: white;
                border: 2px solid #1976d2;
                border-radius: 8px;
                padding: 5px 10px;
                min-height: 30px;
                font-weight: bold;
                color: #0d47a1;
            }
            QComboBox:hover {
                border: 2px solid #0d47a1;
                background-color: #f8fcff;
            }
            QComboBox:focus {
                border: 2px solid #0d47a1;
                background-color: #f0f8ff;
            }
            QComboBox::drop-down {
                subcontrol-origin: padding;
                subcontrol-position: top right;
                width: 25px;
                border-left-width: 1px;
                border-left-color: #1976d2;
                border-left-style: solid;
                border-top-right-radius: 6px;
                border-bottom-right-radius: 6px;
                background-color: #e3f2fd;
            }
            QComboBox::down-arrow {
                image: none;
                width: 0;
                height: 0;
                border-left: 5px solid transparent;
                border-right: 5px solid transparent;
                border-top: 8px solid #1976d2;
            }
            QComboBox QAbstractItemView {
                background-color: white;
                selection-background-color: #e3f2fd;
                selection-color: #0d47a1;
                border: 2px solid #1976d2;
                border-radius: 6px;
                outline: none;
            }
            QLineEdit {
                background-color: white;
                border: 2px solid #1976d2;
                border-radius: 8px;
                padding: 5px 10px;
                min-height: 30px;
                font-weight: bold;
                color: #0d47a1;
            }
            QLineEdit:hover {
                border: 2px solid #0d47a1;
                background-color: #f8fcff;
            }
            QLineEdit:focus {
                border: 2px solid #0d47a1;
                background-color: #f0f8ff;
            }
        """)
        search_frame.setFixedHeight(70)  # تحديد ارتفاع متساو مع الإطارات الأخرى
        search_layout = QHBoxLayout(search_frame)
        search_layout.setSpacing(15)
        search_layout.setContentsMargins(15, 2, 15, 10)

        # قائمة نوع الحركة (بدون تسمية خارجية)
        self.movement_type_combo = QComboBox()
        self.movement_type_combo.setFont(QFont("Calibri", 13, QFont.Bold))
        self.movement_type_combo.setFixedHeight(40)
        self.movement_type_combo.setMinimumWidth(220)
        self.movement_type_combo.setLayoutDirection(Qt.LeftToRight)
        self.movement_type_combo.currentIndexChanged.connect(self.filter_by_movement_type)
        search_layout.addWidget(self.movement_type_combo)

        # قائمة الحركية المزدوجة (بدون تسمية خارجية)
        self.double_movement_combo = QComboBox()
        self.double_movement_combo.setFont(QFont("Calibri", 13, QFont.Bold))
        self.double_movement_combo.setFixedHeight(40)
        self.double_movement_combo.setMinimumWidth(220)
        self.double_movement_combo.setLayoutDirection(Qt.LeftToRight)
        self.double_movement_combo.addItems([
            "الحركية المزدوجة - جميع السجلات",
            "الحركية المزدوجة - التلاميذ ذوو الحركية المزدوجة",
            "الحركية المزدوجة - التلاميذ بحركية واحدة فقط"
        ])
        self.double_movement_combo.currentIndexChanged.connect(self.filter_by_double_movement)
        search_layout.addWidget(self.double_movement_combo)

        # قائمة المؤسسة/المديرية (بدون تسمية خارجية)
        self.institution_combo = QComboBox()
        self.institution_combo.setFont(QFont("Calibri", 13, QFont.Bold))
        self.institution_combo.setFixedHeight(40)
        self.institution_combo.setMinimumWidth(270)
        self.institution_combo.setLayoutDirection(Qt.LeftToRight)
        self.institution_combo.currentIndexChanged.connect(self.filter_by_institution)
        search_layout.addWidget(self.institution_combo)

        # قائمة السنة الدراسية (بدون تسمية خارجية)
        self.academic_year_combo = QComboBox()
        self.academic_year_combo.setFont(QFont("Calibri", 13, QFont.Bold))
        self.academic_year_combo.setFixedHeight(40)
        self.academic_year_combo.setMinimumWidth(200)
        self.academic_year_combo.setLayoutDirection(Qt.LeftToRight)
        self.academic_year_combo.currentIndexChanged.connect(self.filter_by_academic_year)
        search_layout.addWidget(self.academic_year_combo)

        search_layout.addStretch(1)

        # حقل البحث (بدون تسمية خارجية)
        self.search_input = QLineEdit()
        self.search_input.setFont(QFont("Calibri", 13, QFont.Bold))
        self.search_input.setFixedHeight(40)
        self.search_input.setMinimumWidth(220)
        self.search_input.setPlaceholderText("🔍 أدخل نص للبحث...")
        self.search_input.textChanged.connect(self.filter_data)
        self.search_input.setLayoutDirection(Qt.LeftToRight)
        search_layout.addWidget(self.search_input)

        main_layout.addWidget(search_frame)

        # إنشاء جدول لعرض البيانات
        self.table = QTableWidget()
        self.table.setFont(QFont("Calibri", 13))

        # تعيين خصائص الجدول
        self.table.setAlternatingRowColors(False)
        self.table.setSelectionBehavior(QTableWidget.SelectRows)
        self.table.setSelectionMode(QTableWidget.SingleSelection)
        self.table.setEditTriggers(QTableWidget.NoEditTriggers)

        # تعيين خلفية الجدول باللون الأبيض
        self.table.setStyleSheet("""
            QTableWidget {
                background-color: white;
                gridline-color: #d0d0d0;
            }
            QTableWidget::item {
                background-color: white;
            }
            QTableWidget::item:selected {
                background-color: #e0e0ff;
            }
        """)

        # تنسيق رأس الجدول
        header_font = QFont("Calibri", 13, QFont.Bold)
        self.table.horizontalHeader().setFont(header_font)
        self.table.horizontalHeader().setStyleSheet("background-color: #4a86e8; color: white;")
        self.table.horizontalHeader().setDefaultSectionSize(150)
        self.table.horizontalHeader().setMinimumSectionSize(80)

        # تنسيق الصفوف
        self.table.verticalHeader().setDefaultSectionSize(35)

        main_layout.addWidget(self.table)

        # تعيين اتجاه النص من اليمين إلى اليسار
        self.setLayoutDirection(Qt.RightToLeft)

    def connect_to_database(self):
        """الاتصال بقاعدة البيانات"""
        print("🔌 محاولة الاتصال بقاعدة البيانات...")
        try:
            # استخدام الاتصال المقدم إذا كان متاحاً
            if self.db_connection is not None:
                print("✅ استخدام اتصال قاعدة البيانات المقدم")
                # التحقق من نوع الاتصال
                if hasattr(self.db_connection, 'cursor'):
                    # إذا كان اتصال sqlite3 مباشر
                    print("📊 اتصال sqlite3 مباشر")
                    return self.db_connection
                elif hasattr(self.db_connection, 'databaseName'):
                    # إذا كان كائن QSqlDatabase، قم بإنشاء اتصال sqlite3 جديد
                    db_name = self.db_connection.databaseName()
                    print(f"🔄 تحويل اتصال QSqlDatabase إلى اتصال sqlite3 من المسار: {db_name}")
                    return sqlite3.connect(db_name)
                else:
                    print("⚠️ نوع اتصال قاعدة البيانات غير معروف، إنشاء اتصال جديد")

            # إنشاء اتصال جديد باستخدام المسار المحدد
            print(f"🆕 إنشاء اتصال جديد باستخدام المسار: {self.db_path}")
            conn = sqlite3.connect(self.db_path)
            print("✅ تم الاتصال بقاعدة البيانات بنجاح")
            return conn
        except Exception as e:
            print(f"❌ خطأ في الاتصال بقاعدة البيانات: {e}")
            QMessageBox.critical(self, "خطأ", f"فشل الاتصال بقاعدة البيانات: {e}")
            return None

    def load_data(self):
        """تحميل البيانات من جدول سجلات_الوافدين_والمغادرين"""
        print("📥 بدء تحميل البيانات من الجدول...")
        conn = self.connect_to_database()
        if not conn:
            print("❌ فشل الاتصال بقاعدة البيانات، إلغاء تحميل البيانات")
            return

        try:
            cursor = conn.cursor()

            # التحقق من وجود جدول سجلات_الوافدين_والمغادرين
            print("🔍 التحقق من وجود جدول سجلات_الوافدين_والمغادرين...")
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='سجلات_الوافدين_والمغادرين'")
            if not cursor.fetchone():
                print("❌ جدول سجلات الوافدين والمغادرين غير موجود")
                QMessageBox.warning(self, "تنبيه", "جدول سجلات الوافدين والمغادرين غير موجود.")
                conn.close()
                return
            
            print("✅ تم العثور على جدول سجلات_الوافدين_والمغادرين")

            # الحصول على أسماء الأعمدة
            print("📋 الحصول على أسماء الأعمدة...")
            cursor.execute("PRAGMA table_info(سجلات_الوافدين_والمغادرين)")
            columns_info = cursor.fetchall()
            columns = [col[1] for col in columns_info]
            print(f"📝 الأعمدة الموجودة: {columns}")

            # تحميل أنواع الحركة المتاحة (ملاحظات)
            print("📝 تحميل أنواع الحركة المتاحة...")
            self.movement_type_combo.clear()

            try:
                # الحصول على أنواع الحركة الفريدة من عمود ملاحظات
                cursor.execute("SELECT DISTINCT ملاحظات FROM سجلات_الوافدين_والمغادرين WHERE ملاحظات IS NOT NULL AND ملاحظات != '' ORDER BY ملاحظات")
                movement_types = cursor.fetchall()
                print(f"📊 عدد أنواع الحركة المختلفة: {len(movement_types)}")

                # إضافة أنواع الحركة إلى القائمة المنسدلة
                for movement_type in movement_types:
                    self.movement_type_combo.addItem(movement_type[0])
                    print(f"➕ إضافة نوع حركة: {movement_type[0]}")

                # إضافة عنصر افتراضي في البداية
                self.movement_type_combo.insertItem(0, "نوع الحركة - اختر...")

                # البحث عن فهرس "لائحة التحويلات (الوافدون)" وتعيينه كافتراضي
                default_target = "لائحة التحويلات (الوافدون)"
                default_index = -1
                
                for i in range(self.movement_type_combo.count()):
                    if self.movement_type_combo.itemText(i) == default_target:
                        default_index = i
                        break
                
                if default_index != -1:
                    # تعيين "لائحة التحويلات (الوافدون)" كافتراضي
                    self.movement_type_combo.setCurrentIndex(default_index)
                    print(f"✅ تم تعيين التصفية الافتراضية: {default_target}")
                    selected_movement_type = default_target
                elif len(movement_types) > 0:
                    # إذا لم توجد "لائحة التحويلات (الوافدون)"، استخدم العنصر الافتراضي
                    self.movement_type_combo.setCurrentIndex(0)
                    selected_movement_type = None
                    print(f"⚠️ لم يتم العثور على '{default_target}'، استخدام العنصر الافتراضي")
                else:
                    selected_movement_type = None

                # تحميل قائمة المؤسسات/المديريات
                self.load_institutions_list()

                # تحميل قائمة السنوات الدراسية
                self.load_academic_years_list()

            except Exception as e:
                print(f"⚠️ خطأ في تحميل أنواع الحركة: {e}")
                selected_movement_type = None

            # تحديث عدد السجلات الإجمالي
            print("🔢 حساب العدد الإجمالي للسجلات...")
            cursor.execute("SELECT COUNT(*) FROM سجلات_الوافدين_والمغادرين")
            total_count = cursor.fetchone()[0]
            print(f"📈 العدد الإجمالي للسجلات: {total_count}")

            # تحديد الأعمدة التي سيتم عرضها (استبعاد عمود المعرف وتاريخ الاستيراد واسم الملف)
            excluded_columns = ["id", "تاريخ_الاستيراد", "اسم_الملف"]
            display_columns = [col for col in columns if col not in excluded_columns]
            print(f"👁️ الأعمدة التي سيتم عرضها: {display_columns}")

            # تغيير اسم عمود ملاحظات إلى نوع الحركة في العرض
            display_columns_renamed = []
            for col in display_columns:
                if col == "ملاحظات":
                    display_columns_renamed.append("نوع الحركة")
                else:
                    display_columns_renamed.append(col)

            # إضافة عمود مربع الاختيار في البداية
            self.table.setColumnCount(len(display_columns_renamed) + 1)
            headers = ["اختيار"] + display_columns_renamed
            self.table.setHorizontalHeaderLabels(headers)
            print(f"📊 تم تعيين {len(headers)} عمود للجدول")

            # الحصول على البيانات المصفاة مباشرة بدلاً من جميع البيانات
            print("📊 استعلام البيانات المصفاة من الجدول...")
            if selected_movement_type:
                print(f"🔍 تطبيق تصفية افتراضية لنوع الحركة: {selected_movement_type}")
                cursor.execute("SELECT * FROM سجلات_الوافدين_والمغادرين WHERE ملاحظات = ? ORDER BY id", [selected_movement_type])
            else:
                print("📋 لا توجد تصفية، جلب جميع البيانات")
                cursor.execute("SELECT * FROM سجلات_الوافدين_والمغادرين ORDER BY id")
            
            data = cursor.fetchall()
            print(f"📋 تم استرجاع {len(data)} سجل مصفا من قاعدة البيانات")

            # تعيين عدد الصفوف
            self.table.setRowCount(len(data))

            # ملء الجدول بالبيانات المصفاة
            print("🔄 ملء الجدول بالبيانات المصفاة...")
            for row_idx, row_data in enumerate(data):
                # إضافة مربع اختيار في العمود الأول
                checkbox = QCheckBox()
                checkbox.setStyleSheet("QCheckBox { margin: auto; }")
                self.table.setCellWidget(row_idx, 0, checkbox)
                
                display_col_idx = 1  # البدء من العمود الثاني
                for col_idx, value in enumerate(row_data):
                    if columns[col_idx] in excluded_columns:
                        continue

                    # إنشاء عنصر جديد
                    item = QTableWidgetItem(str(value) if value is not None else "")
                    item.setTextAlignment(Qt.AlignCenter)

                    # تعيين خط غامق للنص
                    font = QFont("Calibri", 13, QFont.Bold)
                    item.setFont(font)

                    self.table.setItem(row_idx, display_col_idx, item)
                    display_col_idx += 1

            print("✅ تم ملء الجدول بالبيانات المصفاة بنجاح")

            # تعيين ارتفاع رأس الجدول
            self.table.horizontalHeader().setFixedHeight(40)
            self.table.horizontalHeader().setStyleSheet("QHeaderView::section { background-color: #4a86e8; color: white; font-weight: bold; height: 40px; }")

            # تعيين ارتفاع الصفوف
            for row in range(self.table.rowCount()):
                self.table.setRowHeight(row, 30)

            # تعيين عرض ثابت للأعمدة (مع تحديث اسم عمود ملاحظات)
            column_widths = {
                "اختيار": 80,
                "رقم_التلميذ": 120,
                "النسب": 150,
                "الإسم": 200,
                "تاريخ_التحويل": 120,
                "نوع_التحويل": 120,
                "مؤسسة_الإستقبال": 250,
                "المؤسسة_الأصلية": 250,
                "المديرية_الإقليمية_الأصلية": 200,
                "الأكاديمية_الأصلية": 180,
                "المستوى": 100,
                "السنة_الدراسية": 120,
                "نوع الحركة": 200
            }

            # تطبيق العرض المخصص لكل عمود
            for col_idx, col_name in enumerate(headers):
                if col_name in column_widths:
                    self.table.setColumnWidth(col_idx, column_widths[col_name])
                else:
                    # عرض افتراضي للأعمدة غير المحددة
                    self.table.setColumnWidth(col_idx, 150)

            # منع المستخدم من تغيير عرض الأعمدة
            self.table.horizontalHeader().setSectionResizeMode(QHeaderView.Fixed)

            # تحديث عنوان النافذة والنص الرئيسي
            self.update_window_title()

            conn.close()
            print("🔒 تم إغلاق اتصال قاعدة البيانات")

        except Exception as e:
            print(f"❌ خطأ في تحميل البيانات: {e}")
            QMessageBox.critical(self, "خطأ", f"فشل تحميل البيانات: {e}")
            if conn:
                conn.close()
                print("🔒 تم إغلاق اتصال قاعدة البيانات (في حالة الخطأ)")

    def refresh_data(self):
        """تحديث البيانات في الجدول"""
        print("🔄 تحديث البيانات في الجدول...")
        # حفظ موضع التمرير الحالي
        scrollbar_position = self.table.verticalScrollBar().value()
        print(f"💾 حفظ موضع التمرير: {scrollbar_position}")

        # تحديث البيانات
        self.load_data()

        # استعادة موضع التمرير
        self.table.verticalScrollBar().setValue(scrollbar_position)
        print(f"📍 استعادة موضع التمرير: {scrollbar_position}")

        # عرض رسالة تأكيد
        QMessageBox.information(self, "تحديث", "تم تحديث البيانات بنجاح.")
        print("✅ تم تحديث البيانات بنجاح")

    def filter_data(self):
        """تصفية البيانات حسب نص البحث في جميع الأعمدة في الوقت الفعلي"""
        search_text = self.search_input.text().lower().strip()
        print(f"🔍 البحث في البيانات: '{search_text}'")

        if not search_text:
            print("🔄 مسح نص البحث، إظهار جميع الصفوف")
            # إذا كان حقل البحث فارغًا، أظهر جميع الصفوف
            for row in range(self.table.rowCount()):
                self.table.setRowHidden(row, False)
            self.update_records_count()
            return

        # البحث في جميع الأعمدة المرئية
        hidden_count = 0
        visible_count = 0
        for row in range(self.table.rowCount()):
            row_visible = False

            # البحث في جميع الأعمدة
            for col in range(self.table.columnCount()):
                item = self.table.item(row, col)
                if item and search_text in item.text().lower():
                    row_visible = True
                    break

            # إخفاء أو إظهار الصف حسب نتيجة البحث
            self.table.setRowHidden(row, not row_visible)
            
            if row_visible:
                visible_count += 1
            else:
                hidden_count += 1

        print(f"📊 نتائج البحث - مرئية: {visible_count}, مخفية: {hidden_count}")
        # تحديث عداد السجلات بعد التصفية
        self.update_records_count()

    def get_double_movement_students(self):
        """الحصول على قائمة التلاميذ ذوي الحركية المزدوجة"""
        conn = self.connect_to_database()
        if not conn:
            return []

        try:
            cursor = conn.cursor()
            
            # البحث عن التلاميذ الذين لديهم حركتان (وافد ومغادر)
            query = """
            SELECT رقم_التلميذ, COUNT(DISTINCT ملاحظات) as movement_count
            FROM سجلات_الوافدين_والمغادرين 
            WHERE رقم_التلميذ IS NOT NULL AND رقم_التلميذ != ''
            AND ملاحظات IN ('لائحة التحويلات (المغادرون)', 'لائحة التحويلات (الوافدون)')
            GROUP BY رقم_التلميذ
            HAVING COUNT(DISTINCT ملاحظات) = 2
            """
            
            cursor.execute(query)
            double_movement_students = [row[0] for row in cursor.fetchall()]
            
            print(f"🔄 عدد التلاميذ ذوي الحركية المزدوجة: {len(double_movement_students)}")
            
            conn.close()
            return double_movement_students
            
        except Exception as e:
            print(f"❌ خطأ في الحصول على التلاميذ ذوي الحركية المزدوجة: {e}")
            if conn:
                conn.close()
            return []

    def update_header_label(self):
        """تحديث النص الرئيسي في أعلى النافذة ليعرض بيانات التصفية مع عدد السجلات"""
        base_text = "سجلات الوافدين والمغادرين"
        movement_type = self.movement_type_combo.currentText()
        double_movement_filter = self.double_movement_combo.currentText()
        institution_filter = self.institution_combo.currentText() if hasattr(self, 'institution_combo') else ""
        
        # الحصول على عدد السجلات المرئية
        visible_count = 0
        for row in range(self.table.rowCount()):
            if not self.table.isRowHidden(row):
                visible_count += 1
        
        # بناء النص مع معلومات التصفية
        text_parts = [base_text]
        
        if movement_type:
            text_parts.append(f"نوع الحركة: {movement_type}")
        
        if double_movement_filter != "جميع السجلات":
            text_parts.append(f"التصفية: {double_movement_filter}")
            
        if institution_filter and institution_filter != "جميع المؤسسات/المديريات":
            # تقصير اسم المؤسسة/المديرية إذا كان طويلاً
            if len(institution_filter) > 40:
                institution_filter = institution_filter[:37] + "..."
            text_parts.append(f"المؤسسة: {institution_filter}")
        
        # إضافة عدد السجلات في النهاية
        text_parts.append(f"[عدد السجلات: {visible_count}]")
        
        new_text = " - ".join(text_parts)
        self.header_label.setText(new_text)
        print(f"🏷️ تحديث النص الرئيسي: {new_text}")

    def update_window_title(self):
        """تحديث عنوان النافذة ليعرض بيانات التصفية مع عدد السجلات"""
        base_title = "سجلات الوافدين والمغادرين"
        movement_type = self.movement_type_combo.currentText()
        double_movement_filter = self.double_movement_combo.currentText()
        institution_filter = self.institution_combo.currentText() if hasattr(self, 'institution_combo') else ""
        
        # الحصول على عدد السجلات المرئية
        visible_count = 0
        for row in range(self.table.rowCount()):
            if not self.table.isRowHidden(row):
                visible_count += 1
        
        # بناء العنوان مع معلومات التصفية
        title_parts = [base_title]
        
        if movement_type:
            title_parts.append(f"نوع الحركة: {movement_type}")
        
        if double_movement_filter != "جميع السجلات":
            title_parts.append(f"التصفية: {double_movement_filter}")
            
        if institution_filter and institution_filter != "جميع المؤسسات/المديريات":
            # تقصير اسم المؤسسة/المديرية إذا كان طويلاً
            if len(institution_filter) > 50:
                institution_filter = institution_filter[:47] + "..."
            title_parts.append(f"المؤسسة: {institution_filter}")
        
        # إضافة عدد السجلات في النهاية
        title_parts.append(f"[السجلات: {visible_count}]")
        
        new_title = " - ".join(title_parts)
        self.setWindowTitle(new_title)
        
        # تحديث النص الرئيسي أيضاً
        self.update_header_label()
        
        print(f"🏷️ تحديث عنوان النافذة: {new_title}")

    def filter_by_movement_type(self):
        """تصفية البيانات حسب نوع الحركة المحدد"""
        selected_movement_type = self.movement_type_combo.currentText()
        print(f"📝 تصفية البيانات حسب نوع الحركة: {selected_movement_type}")

        # تحديث قائمة المؤسسات/المديريات عند تغيير نوع الحركة
        if hasattr(self, 'institution_combo'):
            # إزالة الاتصال مؤقتاً لتجنب التكرار
            self.institution_combo.currentIndexChanged.disconnect()
            self.load_institutions_list()
            # إعادة ربط الاتصال
            self.institution_combo.currentIndexChanged.connect(self.filter_by_institution)

        # إذا كان العنصر المحدد هو العنصر الافتراضي أو فارغ، لا تطبق أي تصفية
        if not selected_movement_type or selected_movement_type.startswith("نوع الحركة"):
            # تحديث عنوان النافذة
            self.update_window_title()
            return

        # إعادة تحميل البيانات المصفاة بدلاً من إخفاء/إظهار الصفوف
        self.reload_filtered_data(selected_movement_type)

    def reload_filtered_data(self, movement_type):
        """إعادة تحميل البيانات المصفاة"""
        print(f"🔄 إعادة تحميل البيانات المصفاة لنوع الحركة: {movement_type}")
        
        conn = self.connect_to_database()
        if not conn:
            return

        try:
            cursor = conn.cursor()
            
            # الحصول على أسماء الأعمدة
            cursor.execute("PRAGMA table_info(سجلات_الوافدين_والمغادرين)")
            columns_info = cursor.fetchall()
            columns = [col[1] for col in columns_info]
            
            # الحصول على البيانات المصفاة
            cursor.execute("SELECT * FROM سجلات_الوافدين_والمغادرين WHERE ملاحظات = ? ORDER BY id", [movement_type])
            data = cursor.fetchall()
            print(f"📋 تم استرجاع {len(data)} سجل مصفا من قاعدة البيانات")

            # مسح الجدول الحالي
            self.table.setRowCount(0)

            # تعيين عدد الصفوف الجديد
            self.table.setRowCount(len(data))

            # تحديد الأعمدة المستبعدة
            excluded_columns = ["id", "تاريخ_الاستيراد", "اسم_الملف"]

            # ملء الجدول بالبيانات المصفاة
            print("🔄 ملء الجدول بالبيانات المصفاة...")
            for row_idx, row_data in enumerate(data):
                # إضافة مربع اختيار في العمود الأول
                checkbox = QCheckBox()
                checkbox.setStyleSheet("QCheckBox { margin: auto; }")
                self.table.setCellWidget(row_idx, 0, checkbox)
                
                display_col_idx = 1  # البدء من العمود الثاني
                for col_idx, value in enumerate(row_data):
                    if columns[col_idx] in excluded_columns:
                        continue

                    # إنشاء عنصر جديد
                    item = QTableWidgetItem(str(value) if value is not None else "")
                    item.setTextAlignment(Qt.AlignCenter)

                    # تعيين خط غامق للنص
                    font = QFont("Calibri", 13, QFont.Bold)
                    item.setFont(font)

                    self.table.setItem(row_idx, display_col_idx, item)
                    display_col_idx += 1

            print("✅ تم ملء الجدول بالبيانات المصفاة بنجاح")

            # تعيين ارتفاع الصفوف
            for row in range(self.table.rowCount()):
                self.table.setRowHeight(row, 30)

            conn.close()

            # تحديث عنوان النافذة
            self.update_window_title()

            # إعادة تطبيق تصفية البحث إذا كان هناك نص بحث
            if self.search_input.text().strip():
                self.filter_data()

        except Exception as e:
            print(f"❌ خطأ في إعادة تحميل البيانات المصفاة: {e}")
            QMessageBox.critical(self, "خطأ", f"فشل في إعادة تحميل البيانات المصفاة: {e}")
            if conn:
                conn.close()

    def apply_movement_type_filter_only(self, selected_movement_type):
        """تطبيق تصفية نوع الحركة فقط بدون تداخل"""
        # استخدام إعادة تحميل البيانات بدلاً من إخفاء/إظهار الصفوف
        self.reload_filtered_data(selected_movement_type)

    def filter_by_double_movement(self):
        """تصفية البيانات حسب الحركية المزدوجة"""
        selected_filter = self.double_movement_combo.currentText()
        print(f"🔄 تصفية البيانات حسب الحركية المزدوجة: {selected_filter}")

        # التحقق من النص المحدد وتحويله إلى النص المناسب
        if "جميع السجلات" in selected_filter:
            # إعادة تحميل البيانات حسب نوع الحركة المحدد
            movement_type = self.movement_type_combo.currentText()
            if movement_type and not movement_type.startswith("نوع الحركة"):
                self.reload_filtered_data(movement_type)
            else:
                # تحميل جميع البيانات
                self.load_all_data()
            return

        # الحصول على قائمة التلاميذ ذوي الحركية المزدوجة
        double_movement_students = self.get_double_movement_students()

        conn = self.connect_to_database()
        if not conn:
            return

        try:
            cursor = conn.cursor()
            
            # بناء الاستعلام حسب نوع التصفية
            movement_type = self.movement_type_combo.currentText()
            
            # بناء الاستعلام الأساسي
            base_query = "SELECT * FROM سجلات_الوافدين_والمغادرين WHERE 1=1"
            params = []
            
            # إضافة شرط نوع الحركة إذا كان محدد ولا يبدأ بـ "نوع الحركة"
            if movement_type and not movement_type.startswith("نوع الحركة"):
                base_query += " AND ملاحظات = ?"
                params.append(movement_type)
            
            # تطبيق تصفية الحركية المزدوجة
            if "ذوو الحركية المزدوجة" in selected_filter:
                # إظهار فقط التلاميذ ذوي الحركية المزدوجة
                if double_movement_students:
                    placeholders = ', '.join(['?' for _ in double_movement_students])
                    base_query += f" AND رقم_التلميذ IN ({placeholders})"
                    params.extend(double_movement_students)
                else:
                    # لا توجد نتائج
                    self.table.setRowCount(0)
                    self.update_window_title()
                    conn.close()
                    return
                        
            elif "بحركية واحدة فقط" in selected_filter:
                # إظهار التلاميذ الذين ليس لديهم حركية مزدوجة
                if double_movement_students:
                    placeholders = ', '.join(['?' for _ in double_movement_students])
                    base_query += f" AND (رقم_التلميذ NOT IN ({placeholders}) OR رقم_التلميذ IS NULL)"
                    params.extend(double_movement_students)

            # إضافة ترتيب النتائج
            base_query += " ORDER BY id"
            
            # تنفيذ الاستعلام
            cursor.execute(base_query, params)
            data = cursor.fetchall()
            print(f"📋 تم استرجاع {len(data)} سجل مطابق للتصفية المزدوجة")

            # إعادة ملء الجدول بالبيانات المصفاة
            self.fill_table_with_data(data)

            conn.close()

            # تحديث عنوان النافذة
            self.update_window_title()

            # إعادة تطبيق تصفية البحث إذا كان هناك نص بحث
            if self.search_input.text().strip():
                self.filter_data()

        except Exception as e:
            print(f"❌ خطأ في تصفية البيانات حسب الحركية المزدوجة: {e}")
            QMessageBox.critical(self, "خطأ", f"فشل في تصفية البيانات: {e}")
            if conn:
                conn.close()

    def apply_combined_filters(self):
        """تطبيق التصفيات المدمجة"""
        movement_type = self.movement_type_combo.currentText()
        double_movement_filter = self.double_movement_combo.currentText()
        
        print(f"🔍 تطبيق التصفيات المدمجة - نوع الحركة: {movement_type}, الحركية المزدوجة: {double_movement_filter}")
        
        # التحقق من صحة نوع الحركة
        valid_movement_type = movement_type and not movement_type.startswith("نوع الحركة")
        
        if "جميع السجلات" in double_movement_filter and valid_movement_type:
            # تطبيق تصفية نوع الحركة فقط
            self.reload_filtered_data(movement_type)
        elif "جميع السجلات" not in double_movement_filter:
            # تطبيق تصفية الحركية المزدوجة (التي تأخذ في الاعتبار نوع الحركة)
            self.filter_by_double_movement()
        else:
            # تحميل جميع البيانات
            self.load_all_data()

    def update_records_count(self):
        """تحديث عداد السجلات المرئية وعنوان النافذة والنص الرئيسي"""
        visible_count = 0
        for row in range(self.table.rowCount()):
            if not self.table.isRowHidden(row):
                visible_count += 1
        print(f"📊 تحديث عداد السجلات المرئية: {visible_count}")
        
        # تحديث عنوان النافذة والنص الرئيسي أيضاً
        self.update_window_title()

    def filter_by_institution(self):
        """تصفية البيانات حسب المؤسسة/المديرية المحددة"""
        selected_institution = self.institution_combo.currentText()
        print(f"🏫 تصفية البيانات حسب المؤسسة/المديرية: {selected_institution}")

        # التحقق من النص المحدد
        if "جميع المؤسسات" in selected_institution or selected_institution.startswith("المؤسسة/المديرية"):
            # إعادة تحميل البيانات حسب نوع الحركة والحركية المزدوجة
            self.apply_combined_filters()
            return

        # تحليل النص المحدد للحصول على المؤسسة والمديرية
        if " - " in selected_institution:
            institution_name, directorate_name = selected_institution.split(" - ", 1)
        else:
            print("❌ تنسيق المؤسسة/المديرية غير صحيح")
            return

        conn = self.connect_to_database()
        if not conn:
            return

        try:
            cursor = conn.cursor()
            
            # بناء الاستعلام حسب نوع التصفية
            movement_type = self.movement_type_combo.currentText()
            double_movement_filter = self.double_movement_combo.currentText()
            
            base_query = "SELECT * FROM سجلات_الوافدين_والمغادرين WHERE المؤسسة_الأصلية = ? AND المديرية_الإقليمية_الأصلية = ?"
            params = [institution_name, directorate_name]
            
            # إضافة شرط نوع الحركة إذا كان محدد ولا يبدأ بـ "نوع الحركة"
            if movement_type and not movement_type.startswith("نوع الحركة"):
                base_query += " AND ملاحظات = ?"
                params.append(movement_type)
            
            # تطبيق تصفية الحركية المزدوجة
            if "جميع السجلات" not in double_movement_filter:
                double_movement_students = self.get_double_movement_students()
                
                if "ذوو الحركية المزدوجة" in double_movement_filter:
                    if double_movement_students:
                        placeholders = ', '.join(['?' for _ in double_movement_students])
                        base_query += f" AND رقم_التلميذ IN ({placeholders})"
                        params.extend(double_movement_students)
                    else:
                        # لا توجد نتائج
                        self.table.setRowCount(0)
                        self.update_window_title()
                        conn.close()
                        return
                        
                elif "بحركية واحدة فقط" in double_movement_filter:
                    if double_movement_students:
                        placeholders = ', '.join(['?' for _ in double_movement_students])
                        base_query += f" AND (رقم_التلميذ NOT IN ({placeholders}) OR رقم_التلميذ IS NULL)"
                        params.extend(double_movement_students)

            base_query += " ORDER BY id"
            
            # تنفيذ الاستعلام
            cursor.execute(base_query, params)
            data = cursor.fetchall()
            print(f"📋 تم استرجاع {len(data)} سجل مطابق لتصفية المؤسسة/المديرية")

            # إعادة ملء الجدول بالبيانات المصفاة
            self.fill_table_with_data(data)

            conn.close()

            # تحديث عنوان النافذة
            self.update_window_title()

            # إعادة تطبيق تصفية البحث إذا كان هناك نص بحث
            if self.search_input.text().strip():
                self.filter_data()

        except Exception as e:
            print(f"❌ خطأ في تصفية البيانات حسب المؤسسة/المديرية: {e}")
            QMessageBox.critical(self, "خطأ", f"فشل في تصفية البيانات: {e}")
            if conn:
                conn.close()

    def fill_table_with_data(self, data):
        """ملء الجدول بالبيانات المعطاة"""
        conn = self.connect_to_database()
        if not conn:
            return
            
        try:
            cursor = conn.cursor()
            
            # الحصول على أسماء الأعمدة
            cursor.execute("PRAGMA table_info(سجلات_الوافدين_والمغادرين)")
            columns_info = cursor.fetchall()
            columns = [col[1] for col in columns_info]
            
            # مسح الجدول الحالي
            self.table.setRowCount(0)

            # تعيين عدد الصفوف الجديد
            self.table.setRowCount(len(data))

            # تحديد الأعمدة المستبعدة
            excluded_columns = ["id", "تاريخ_الاستيراد", "اسم_الملف"]

            # ملء الجدول بالبيانات
            for row_idx, row_data in enumerate(data):
                # إضافة مربع اختيار في العمود الأول
                checkbox = QCheckBox()
                checkbox.setStyleSheet("QCheckBox { margin: auto; }")
                self.table.setCellWidget(row_idx, 0, checkbox)
                
                display_col_idx = 1  # البدء من العمود الثاني
                for col_idx, value in enumerate(row_data):
                    if columns[col_idx] in excluded_columns:
                        continue

                    # إنشاء عنصر جديد
                    item = QTableWidgetItem(str(value) if value is not None else "")
                    font = QFont("Calibri", 13, QFont.Bold)
                    item.setFont(font)

                    self.table.setItem(row_idx, display_col_idx, item)
                    display_col_idx += 1

            # تعيين ارتفاع الصفوف
            for row in range(self.table.rowCount()):
                self.table.setRowHeight(row, 30)
                
            conn.close()
            
        except Exception as e:
            print(f"❌ خطأ في ملء الجدول بالبيانات: {e}")
            if conn:
                conn.close()

    def load_all_data(self):
        """تحميل جميع البيانات بدون تصفية"""
        conn = self.connect_to_database()
        if not conn:
            return

        try:
            cursor = conn.cursor()
            cursor.execute("SELECT * FROM سجلات_الوافدين_والمغادرين ORDER BY id")
            data = cursor.fetchall()
            self.fill_table_with_data(data)
            conn.close()
            self.update_window_title()
        except Exception as e:
            print(f"❌ خطأ في تحميل جميع البيانات: {e}")
            if conn:
                conn.close()

    def load_institutions_list(self):
        """تحميل قائمة المؤسسات والمديريات المتاحة حسب نوع الحركة المحدد"""
        print("🏫 تحميل قائمة المؤسسات والمديريات...")
        
        conn = self.connect_to_database()
        if not conn:
            return

        try:
            cursor = conn.cursor()
            
            # الحصول على نوع الحركة المحدد
            selected_movement_type = self.movement_type_combo.currentText()
            
            # مسح القائمة الحالية
            self.institution_combo.clear()
            
            # إضافة خيار "جميع المؤسسات/المديريات"
            self.institution_combo.addItem("جميع المؤسسات/المديريات")
            
            # إضافة خيار افتراضي في البداية مع التسمية
            self.institution_combo.insertItem(0, "المؤسسة/المديرية - جميع المؤسسات/المديريات")
            
            if selected_movement_type and not selected_movement_type.startswith("نوع الحركة"):
                # الحصول على المؤسسات والمديريات الفريدة لنوع الحركة المحدد
                query = """
                SELECT DISTINCT المؤسسة_الأصلية, المديرية_الإقليمية_الأصلية 
                FROM سجلات_الوافدين_والمغادرين 
                WHERE ملاحظات = ? 
                AND المؤسسة_الأصلية IS NOT NULL 
                AND المديرية_الإقليمية_الأصلية IS NOT NULL
                ORDER BY المديرية_الإقليمية_الأصلية, المؤسسة_الأصلية
                """
                
                cursor.execute(query, [selected_movement_type])
                institutions = cursor.fetchall()
                
                print(f"🏫 عدد المؤسسات/المديريات المختلفة: {len(institutions)}")
                
                # إضافة المؤسسات/المديريات إلى القائمة المنسدلة
                for institution, directorate in institutions:
                    display_text = f"{institution} - {directorate}"
                    self.institution_combo.addItem(display_text)
                    print(f"➕ إضافة مؤسسة/مديرية: {display_text}")
            
            conn.close()
            
        except Exception as e:
            print(f"❌ خطأ في تحميل قائمة المؤسسات والمديريات: {e}")
            if conn:
                conn.close()

    def load_academic_years_list(self):
        """تحميل قائمة السنوات الدراسية المتاحة"""
        print("📅 تحميل قائمة السنوات الدراسية...")
        
        conn = self.connect_to_database()
        if not conn:
            return

        try:
            cursor = conn.cursor()
            
            # مسح القائمة الحالية
            self.academic_year_combo.clear()
            
            # إضافة خيار افتراضي في البداية
            self.academic_year_combo.addItem("السنة الدراسية - جميع السنوات")
            
            # الحصول على السنوات الدراسية الفريدة
            cursor.execute("SELECT DISTINCT السنة_الدراسية FROM سجلات_الوافدين_والمغادرين WHERE السنة_الدراسية IS NOT NULL AND السنة_الدراسية != '' ORDER BY السنة_الدراسية")
            academic_years = cursor.fetchall()
            
            print(f"📅 عدد السنوات الدراسية المختلفة: {len(academic_years)}")
            
            # إضافة السنوات الدراسية إلى القائمة المنسدلة
            for academic_year in academic_years:
                self.academic_year_combo.addItem(academic_year[0])
                print(f"➕ إضافة سنة دراسية: {academic_year[0]}")
            
            conn.close()
            
        except Exception as e:
            print(f"❌ خطأ في تحميل قائمة السنوات الدراسية: {e}")
            if conn:
                conn.close()

    def filter_by_academic_year(self):
        """تصفية البيانات حسب السنة الدراسية المحددة"""
        selected_academic_year = self.academic_year_combo.currentText()
        print(f"📅 تصفية البيانات حسب السنة الدراسية: {selected_academic_year}")

        # التحقق من النص المحدد
        if "جميع السنوات" in selected_academic_year or selected_academic_year.startswith("السنة الدراسية"):
            # إعادة تحميل البيانات حسب التصفيات الأخرى
            self.apply_combined_filters()
            return

        conn = self.connect_to_database()
        if not conn:
            return

        try:
            cursor = conn.cursor()
            
            # بناء الاستعلام حسب نوع التصفية
            movement_type = self.movement_type_combo.currentText()
            double_movement_filter = self.double_movement_combo.currentText()
            institution_filter = self.institution_combo.currentText() if hasattr(self, 'institution_combo') else ""
            
            base_query = "SELECT * FROM سجلات_الوافدين_والمغادرين WHERE السنة_الدراسية = ?"
            params = [selected_academic_year]
            
            # إضافة شرط نوع الحركة إذا كان محدد ولا يبدأ بـ "نوع الحركة"
            if movement_type and not movement_type.startswith("نوع الحركة"):
                base_query += " AND ملاحظات = ?"
                params.append(movement_type)
            
            # إضافة شرط المؤسسة/المديرية
            if institution_filter and not institution_filter.startswith("المؤسسة/المديرية") and "جميع المؤسسات" not in institution_filter:
                if " - " in institution_filter:
                    institution_name, directorate_name = institution_filter.split(" - ", 1)
                    base_query += " AND المؤسسة_الأصلية = ? AND المديرية_الإقليمية_الأصلية = ?"
                    params.extend([institution_name, directorate_name])
            
            # تطبيق تصفية الحركية المزدوجة
            if "جميع السجلات" not in double_movement_filter:
                double_movement_students = self.get_double_movement_students()
                
                if "ذوو الحركية المزدوجة" in double_movement_filter:
                    if double_movement_students:
                        placeholders = ', '.join(['?' for _ in double_movement_students])
                        base_query += f" AND رقم_التلميذ IN ({placeholders})"
                        params.extend(double_movement_students)
                    else:
                        # لا توجد نتائج
                        self.table.setRowCount(0)
                        self.update_window_title()
                        conn.close()
                        return
                        
                elif "بحركية واحدة فقط" in double_movement_filter:
                    if double_movement_students:
                        placeholders = ', '.join(['?' for _ in double_movement_students])
                        base_query += f" AND (رقم_التلميذ NOT IN ({placeholders}) OR رقم_التلميذ IS NULL)"
                        params.extend(double_movement_students)

            base_query += " ORDER BY id"
            
            # تنفيذ الاستعلام
            cursor.execute(base_query, params)
            data = cursor.fetchall()
            print(f"📋 تم استرجاع {len(data)} سجل مطابق لتصفية السنة الدراسية")

            # إعادة ملء الجدول بالبيانات المصفاة
            self.fill_table_with_data(data)

            conn.close()

            # تحديث عنوان النافذة
            self.update_window_title()

            # إعادة تطبيق تصفية البحث إذا كان هناك نص بحث
            if self.search_input.text().strip():
                self.filter_data()

        except Exception as e:
            print(f"❌ خطأ في تصفية البيانات حسب السنة الدراسية: {e}")
            QMessageBox.critical(self, "خطأ", f"فشل في تصفية البيانات: {e}")
            if conn:
                conn.close()

    # Button functions - simplified implementations
    def import_candidates(self):
        """فتح نافذة معالجة طلبات الملفات"""
        print("📥 تم النقر على زر طلب الملفات الجماعي - فتح نافذة معالجة الطلبات")
        try:
            from sub444_window import FileRequestProcessingWindow
            self.file_processing_window = FileRequestProcessingWindow(self, self.db_path)
            self.file_processing_window.show()
            print("✅ تم فتح نافذة معالجة طلبات الملفات")
        except Exception as e:
            print(f"❌ خطأ في فتح نافذة معالجة طلبات الملفات: {e}")
            QMessageBox.critical(self, "خطأ", f"فشل في فتح نافذة معالجة طلبات الملفات: {e}")

    def update_shipment_numbers(self):
        """تحديث رقم الإرسال حسب المؤسسة الأصلية"""
        print("📮 تم النقر على زر تحديث رقم الإرسال حسب المؤسسة الأصلية")

        class SmartShipmentNumberDialog(QDialog):
            def __init__(self, parent_window=None):
                super().__init__(parent_window)
                self.parent_window = parent_window
                self.full_academic_year = ""
                self.setWindowTitle("تحديث رقم الإرسال الذكي")
                self.setFixedSize(800, 600)
                self.setLayoutDirection(Qt.RightToLeft)
                
                # تطبيق خط Calibri صراحة على النافذة
                self.setFont(QFont("Calibri", 12))
                
                try:
                    self.setWindowIcon(QIcon("01.ico"))
                except Exception:
                    pass
                
                # تحديث StyleSheet لتضمين خط Calibri صراحة لجميع العناصر
                self.setStyleSheet("""
                    QDialog {
                        background: qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #eef2f7, stop:1 #d6e0ef);
                        border: 2px solid #5a7fa9;
                        border-radius: 15px;
                        font-family: 'Calibri';
                        font-size: 12pt;
                    }
                    QLabel#title_label {
                        color: #2c3e50;
                        font-size: 18pt;
                        font-weight: bold;
                        font-family: 'Calibri';
                        padding: 15px;
                        background-color: #ffffff;
                        border: 1px solid #c8d4e3;
                        border-radius: 10px;
                        margin-bottom: 15px;
                    }
                    QFrame#data_frame {
                        background-color: #f8f9fa;
                        border: 1px solid #d1d9e6;
                        border-radius: 10px;
                        padding: 20px;
                        font-family: 'Calibri';
                    }
                    QLabel {
                        color: #34495e;
                        font-size: 11pt;
                        font-weight: bold;
                        font-family: 'Calibri';
                        margin-bottom: 5px;
                    }
                    QSpinBox, QLineEdit {
                        background-color: #ffffff;
                        border: 1px solid #a9b8ce;
                        border-radius: 8px;
                        padding: 10px;
                        font-size: 11pt;
                        font-family: 'Calibri';
                        font-weight: bold;
                        min-height: 38px;
                        color: #2c3e50;
                    }
                    QSpinBox:focus, QLineEdit:focus {
                        border: 2px solid #3498db;
                        background-color: #fdfdfe;
                    }
                    QLineEdit[readOnly="true"], QSpinBox[readOnly="true"] {
                        background-color: #e9ecef;
                        color: #495057;
                        border: 1px solid #ced4da;
                        font-family: 'Calibri';
                        font-weight: bold;
                    }
                    QPushButton {
                        background-color: #3498db;
                        color: white;
                        border: none;
                        border-radius: 8px;
                        padding: 12px 25px;
                        font-size: 11pt;
                        font-weight: bold;
                        font-family: 'Calibri';
                        min-width: 150px;
                    }
                    QPushButton:hover {
                        background-color: #2980b9;
                    }
                    QPushButton:pressed {
                        background-color: #1f618d;
                    }
                    QPushButton#cancel_button {
                        background-color: #7f8c8d;
                        font-family: 'Calibri';
                        font-weight: bold;
                    }
                    QPushButton#cancel_button:hover {
                        background-color: #6c7a7d;
                    }
                    QPushButton#cancel_button:pressed {
                        background-color: #596568;
                    }
                    QTextBrowser#info_browser {
                        background-color: #e9f5ff;
                        border: 1px dashed #7fadde;
                        border-radius: 8px;
                        padding: 10px;
                        color: #1a5276;
                        font-size: 10pt;
                        font-family: 'Calibri';
                        line-height: 1.5;
                    }
                """)
                
                main_layout = QVBoxLayout(self)
                main_layout.setContentsMargins(25, 25, 25, 25)
                main_layout.setSpacing(18)

                title_label = QLabel("📮 تحديث رقم الإرسال للمؤسسات")
                title_label.setObjectName("title_label")
                title_label.setAlignment(Qt.AlignCenter)
                # تطبيق الخط برمجياً للتأكد
                title_label.setFont(QFont("Calibri", 18, QFont.Bold))
                main_layout.addWidget(title_label)

                data_frame = QFrame()
                data_frame.setObjectName("data_frame")
                data_layout = QFormLayout(data_frame)
                data_layout.setSpacing(12)
                data_layout.setLabelAlignment(Qt.AlignRight)

                # تطبيق الخط على جميع الحقول برمجياً
                self.start_number_spin = QSpinBox()
                self.start_number_spin.setMinimum(1)
                self.start_number_spin.setMaximum(99999)
                self.start_number_spin.setFont(QFont("Calibri", 12, QFont.Bold))

                self.academic_year_edit = QLineEdit()
                self.academic_year_edit.setFont(QFont("Calibri", 12, QFont.Bold))
                self.academic_year_edit.setReadOnly(True)

                self.request_number_spin = QSpinBox()
                self.request_number_spin.setMinimum(1)
                self.request_number_spin.setMaximum(9999)
                self.request_number_spin.setFont(QFont("Calibri", 12, QFont.Bold))
                self.request_number_spin.setReadOnly(True)
                
                # إنشاء التسميات مع تطبيق الخط
                label1 = QLabel("🔢 بداية الترقيم التسلسلي:")
                label1.setFont(QFont("Calibri", 11, QFont.Bold))
                
                label2 = QLabel("📅 اختصار السنة الدراسية:")
                label2.setFont(QFont("Calibri", 11, QFont.Bold))
                
                label3 = QLabel("📋 رقم الطلب المستهدف:")
                label3.setFont(QFont("Calibri", 11, QFont.Bold))
                
                data_layout.addRow(label1, self.start_number_spin)
                data_layout.addRow(label2, self.academic_year_edit)
                data_layout.addRow(label3, self.request_number_spin)
                main_layout.addWidget(data_frame)

                self.info_browser = QTextBrowser()
                self.info_browser.setObjectName("info_browser")
                self.info_browser.setFixedHeight(100)
                # تطبيق الخط برمجياً
                self.info_browser.setFont(QFont("Calibri", 10))
                main_layout.addWidget(self.info_browser)

                buttons_layout = QHBoxLayout()
                buttons_layout.setSpacing(15)
                
                ok_button = QPushButton("✅ تحديث الأرقام")
                ok_button.setFont(QFont("Calibri", 11, QFont.Bold))
                ok_button.clicked.connect(self.accept)
                
                cancel_button = QPushButton("❌ إلغاء")
                cancel_button.setObjectName("cancel_button")
                cancel_button.setFont(QFont("Calibri", 11, QFont.Bold))
                cancel_button.clicked.connect(self.reject)
                
                buttons_layout.addStretch()
                buttons_layout.addWidget(ok_button)
                buttons_layout.addWidget(cancel_button)
                buttons_layout.addStretch()
                main_layout.addLayout(buttons_layout)

                self.load_smart_values()

            def load_smart_values(self):
                conn = self.parent_window.connect_to_database()
                year_abbr_for_info = "غير محدد"
                request_num_for_info = "غير محدد"
                start_num_for_info = "1"
                self.full_academic_year = f"{datetime.now().year}-{datetime.now().year+1}"

                if conn:
                    try:
                        cursor = conn.cursor()
                        
                        # 1. تحديد السنة الدراسية الأكثر شيوعاً
                        cursor.execute("""
                            SELECT السنة_الدراسية FROM بيانات_المؤسسة LIMIT 1
                        """)
                        res_ay = cursor.fetchone()
                        if res_ay and res_ay[0]:
                             self.full_academic_year = res_ay[0]
                        else:
                            cursor.execute("""
                                SELECT السنة_الدراسية, COUNT(*) as count 
                                FROM سجلات_الوافدين_والمغادرين 
                                WHERE السنة_الدراسية IS NOT NULL AND السنة_الدراسية != ''
                                GROUP BY السنة_الدراسية 
                                ORDER BY count DESC 
                                LIMIT 1
                            """)
                            res_ay_records = cursor.fetchone()
                            if res_ay_records and res_ay_records[0]:
                                self.full_academic_year = res_ay_records[0]
                        
                        # 2. اختصار السنة الدراسية
                        year_abbr = self.full_academic_year
                        if "/" in self.full_academic_year:
                            y1, y2 = self.full_academic_year.split("/")
                            year_abbr = f"{y1.strip()[-2:]}/{y2.strip()[-2:]}"
                        elif "-" in self.full_academic_year:
                            y1, y2 = self.full_academic_year.split("-")
                            year_abbr = f"{y1.strip()[-2:]}/{y2.strip()[-2:]}"
                        
                        self.academic_year_edit.setText(year_abbr)
                        year_abbr_for_info = year_abbr

                        # 3. تحديد أعلى رقم طلب للسنة الدراسية بدون رقم إرسال
                        cursor.execute("""
                            SELECT MAX(رقم_الطلب) 
                            FROM سجلات_الوافدين_والمغادرين 
                            WHERE السنة_الدراسية = ? 
                            AND رقم_الطلب IS NOT NULL 
                            AND (رقم_الإرسال IS NULL OR رقم_الإرسال = '')
                        """, (self.full_academic_year,))
                        req_result = cursor.fetchone()
                        request_number = req_result[0] if req_result and req_result[0] is not None else 1
                        self.request_number_spin.setValue(request_number)
                        request_num_for_info = str(request_number)

                        # 4. تحديد التسلسل التالي لرقم الإرسال
                        cursor.execute("""
                            SELECT رقم_الإرسال 
                            FROM سجلات_الوافدين_والمغادرين 
                            WHERE رقم_الإرسال IS NOT NULL AND رقم_الإرسال LIKE ?
                        """, (f"%/{year_abbr}",))
                        results = cursor.fetchall()
                        max_serial = 0
                        for r_tuple in results:
                            r = r_tuple[0]
                            if "/" in r:
                                try:
                                    serial_part = r.split("/")[0]
                                    num = int(serial_part)
                                    if num > max_serial:
                                        max_serial = num
                                except ValueError:
                                    continue
                        
                        self.start_number_spin.setValue(max_serial + 1)
                        start_num_for_info = str(max_serial + 1)
                        
                        conn.close()
                    except Exception as e:
                        self.info_browser.setHtml(f"<p style='color: red;'>⚠️ خطأ في تحميل القيم الذكية: {e}</p><p>سيتم استخدام القيم الافتراضية.</p>")
                        print(f"Error loading smart values: {e}")
                        self.academic_year_edit.setText(year_abbr_for_info if year_abbr_for_info != "غير محدد" else "N/A")
                        self.request_number_spin.setValue(1)
                        self.start_number_spin.setValue(1)

                info_html = f"""
                <div dir='rtl' style='font-family: Calibri; font-size: 10pt;'>
                <p><b>معلومات الترقيم المقترحة:</b></p>
                <ul>
                    <li><b>السنة الدراسية الكاملة المعتمدة:</b> {self.full_academic_year} (الاختصار: {year_abbr_for_info})</li>
                    <li><b>رقم الطلب المقترح:</b> {request_num_for_info} (أعلى رقم طلب لهذه السنة بدون رقم إرسال)</li>
                    <li><b>بداية الترقيم التسلسلي المقترح:</b> {start_num_for_info}</li>
                </ul>
                <p>سيتم تحديث أرقام الإرسال بالتنسيق: <b>[الترقيم التسلسلي]/{year_abbr_for_info}</b> 
                لجميع المؤسسات المرتبطة برقم الطلب <b>{request_num_for_info}</b> للسنة الدراسية <b>{self.full_academic_year}</b>.</p>
                <p style='color: #007bff;'><i>يمكنك تعديل "بداية الترقيم التسلسلي" إذا لزم الأمر.</i></p>
                </div>
                """
                self.info_browser.setHtml(info_html)

            def get_data(self):
                return {
                    'start_number': self.start_number_spin.value(),
                    'academic_year_abbr': self.academic_year_edit.text().strip(),
                    'request_number': self.request_number_spin.value(),
                    'full_academic_year': self.full_academic_year
                }

        dialog = SmartShipmentNumberDialog(self)
        if dialog.exec_() == QDialog.Accepted:
            data = dialog.get_data()
            start_number = data['start_number']
            academic_year_abbr = data['academic_year_abbr']
            request_number = data['request_number']
            full_academic_year = data['full_academic_year']
            
            print(f"📊 بداية الترقيم: {start_number}")
            print(f"📅 اختصار السنة الدراسية: {academic_year_abbr} (الكاملة: {full_academic_year})")
            print(f"📋 رقم الطلب: {request_number}")
            
            self.apply_shipment_number_update(start_number, academic_year_abbr, request_number, full_academic_year)

    def apply_shipment_number_update(self, start_number, academic_year_abbr, request_number, full_academic_year):
        """تطبيق تحديث أرقام الإرسال"""
        conn = self.connect_to_database()
        if not conn:
            return
        
        try:
            cursor = conn.cursor()
            
            cursor.execute("PRAGMA table_info(سجلات_الوافدين_والمغادرين)")
            columns = [col[1] for col in cursor.fetchall()]
            if "رقم_الإرسال" not in columns:
                try:
                   
                    cursor.execute("ALTER TABLE سجلات_الوافدين_والمغادرين ADD COLUMN رقم_الإرسال TEXT")
                    print("✅ تم إضافة عمود رقم_الإرسال")
                except Exception as e:
                    QMessageBox.critical(self, "خطأ", f"فشل في إضافة عمود رقم_الإرسال: {e}")
                    conn.close()
                    return
            
            # الحصول على المؤسسات الفريدة لرقم الطلب والسنة الدراسية المحددين
            query_institutions = """
            SELECT DISTINCT المؤسسة_الأصلية, المديرية_الإقليمية_الأصلية 
            FROM سجلات_الوافدين_والمغادرين 
            WHERE رقم_الطلب = ? AND السنة_الدراسية = ?
            ORDER BY المؤسسة_الأصلية, المديرية_الإقليمية_الأصلية
            """
            cursor.execute(query_institutions, [request_number, full_academic_year])
            unique_institutions = cursor.fetchall()
            
            if not unique_institutions:
                QMessageBox.warning(self, "تنبيه", 
                                  f"لم يتم العثور على سجلات برقم الطلب '{request_number}' للسنة الدراسية '{full_academic_year}'.")
                conn.close()
                return
            
            print(f"📋 عدد المؤسسات/المديريات الفريدة: {len(unique_institutions)} للسنة {full_academic_year} والطلب {request_number}")
            
            updated_count = 0
            current_serial = start_number

            # شريط التقدم
            progress = QProgressDialog("جاري تحديث أرقام الإرسال...", "إلغاء", 0, len(unique_institutions), self)
            progress.setWindowModality(Qt.WindowModal)
            progress.setMinimumDuration(0)
            progress.setValue(0)
            
            for i, (institution, directorate) in enumerate(unique_institutions):
                if progress.wasCanceled():
                    QMessageBox.information(self, "إلغاء", "تم إلغاء عملية تحديث أرقام الإرسال.")
                    conn.rollback()
                    conn.close()
                    return

                shipment_number_formatted = f"{current_serial}/{academic_year_abbr}"
                
                update_query = """
                UPDATE سجلات_الوافدين_والمغادرين 
                SET رقم_الإرسال = ? 
                WHERE المؤسسة_الأصلية = ? 
                AND المديرية_الإقليمية_الأصلية = ? 
                AND رقم_الطلب = ?
                AND السنة_الدراسية = ?
                """
                cursor.execute(update_query, [shipment_number_formatted, institution, directorate, request_number, full_academic_year])
                affected_rows = cursor.rowcount
                updated_count += affected_rows
                
                print(f"📮 {institution} - {directorate}: رقم الإرسال {shipment_number_formatted} ({affected_rows} سجل)")
                current_serial += 1
                progress.setValue(i + 1)
                QApplication.processEvents()

            progress.setValue(len(unique_institutions))
            conn.commit()
            conn.close()
            
            self.refresh_data()
            
            success_message = (
                f"تم تحديث أرقام الإرسال بنجاح!\n\n"
                f"<b>السنة الدراسية:</b> {full_academic_year}\n"
                f"<b>رقم الطلب المستهدف:</b> {request_number}\n"
                f"<b>عدد السجلات المحدثة:</b> {updated_count}\n"
                f"<b>عدد المؤسسات/المديريات:</b> {len(unique_institutions)}\n"
                f"<b>نطاق الأرقام التسلسلية:</b> {start_number} إلى {current_serial-1}\n"
                f"<b>تنسيق رقم الإرسال:</b> [الرقم]/{academic_year_abbr}"
            )
            
            msg_box = QMessageBox(self)
            msg_box.setWindowTitle("نجاح العملية")
            msg_box.setIcon(QMessageBox.Information)
            msg_box.setTextFormat(Qt.RichText)
            msg_box.setText(success_message)
            msg_box.setStandardButtons(QMessageBox.Ok)
            msg_box.exec_()
            
            print(f"✅ تم تحديث {updated_count} سجل بنجاح")
            
        except sqlite3.Error as e_sql:
            print(f"❌ خطأ SQLite في تحديث أرقام الإرسال: {e_sql}")
            QMessageBox.critical(self, "خطأ SQLite", f"فشل في تحديث أرقام الإرسال: {e_sql}")
            if conn:
                conn.rollback()
                conn.close()
        except Exception as e:
            print(f"❌ خطأ عام في تحديث أرقام الإرسال: {e}")
            QMessageBox.critical(self, "خطأ عام", f"فشل في تحديث أرقام الإرسال: {e}")
            if conn:
                conn.rollback()
                conn.close()

    def add_exam_numbers_advanced(self):
        """إضافة أرقام الامتحان"""
        print("🔢 تم النقر على زر إضافة أرقام الامتحان")
        QMessageBox.information(self, "معلومات", "وظيفة إضافة أرقام الامتحان - النسخة 44")

    def open_print_reports(self):
        """فتح نافذة طباعة المحاضر واللوائح"""
        print("📋 تم النقر على زر طباعة المحاضر واللوائح")
        QMessageBox.information(self, "معلومات", "وظيفة طباعة المحاضر واللوائح - النسخة 44")

    def delete_all_data(self):
        """حذف البيانات حسب السنة الدراسية"""
        print("🗑️ تم النقر على زر حذف البيانات")
        
        # الاتصال بقاعدة البيانات للحصول على السنة الدراسية
        conn = self.connect_to_database()
        if not conn:
            return
        
        try:
            cursor = conn.cursor()
            
            # الحصول على السنة الدراسية من جدول بيانات_المؤسسة
            cursor.execute("SELECT السنة_الدراسية FROM بيانات_المؤسسة LIMIT 1")
            result = cursor.fetchone()
            
            if not result:
                QMessageBox.warning(self, "تنبيه", "لا يمكن العثور على السنة الدراسية في جدول بيانات المؤسسة.")
                conn.close()
                return
            
            academic_year = result[0]
            print(f"📅 السنة الدراسية المستهدفة: {academic_year}")
            
            # حساب عدد السجلات التي سيتم حذفها
            cursor.execute("SELECT COUNT(*) FROM سجلات_الوافدين_والمغادرين WHERE السنة_الدراسية = ?", 
                          [academic_year])
            records_count = cursor.fetchone()[0]
            
            print(f"📊 عدد السجلات التي سيتم حذفها: {records_count}")
            
            if records_count == 0:
                QMessageBox.information(self, "معلومات", 
                                      f"لا توجد سجلات للسنة الدراسية {academic_year} لحذفها.")
                conn.close()
                return
            
            # إنشاء نافذة تأكيد مخصصة مشابهة لـ sub100_window.py
            delete_dialog = self.create_delete_confirmation_dialog(academic_year, records_count)
            
            # عرض النافذة والحصول على النتيجة
            if delete_dialog.exec_() != QDialog.Accepted:
                print("❌ إلغاء عملية الحذف")
                conn.close()
                return
            
            # إنشاء عملية حسابية عشوائية للتأكيد النهائي
            num1 = random.randint(10, 50)
            num2 = random.randint(10, 50)
            correct_answer = num1 + num2
            
            # طلب إدخال الإجابة
            while True:
                answer, ok = QInputDialog.getText(self, "التحقق النهائي", 
                                                f"للمتابعة، احسب: {num1} + {num2} = ؟")
                
                if not ok:
                    print("❌ إلغاء عملية الحذف في التحقق النهائي")
                    conn.close()
                    return
                
                try:
                    user_answer = int(answer.strip())
                    if user_answer == correct_answer:
                        print("✅ إجابة صحيحة، المتابعة مع الحذف")
                        break
                    else:
                        QMessageBox.warning(self, "إجابة خاطئة", 
                                          f"الإجابة خاطئة!\n\nالإجابة الصحيحة هي: {correct_answer}\nحاول مرة أخرى.")
                        print(f"❌ إجابة خاطئة: {user_answer} (الصحيحة: {correct_answer})")
                except ValueError:
                    QMessageBox.warning(self, "خطأ في الإدخال", "يرجى إدخال رقم صحيح.")
                   
                    print("❌ خطأ في تنسيق الإجابة")
            
            # تنفيذ عملية الحذف
            print(f"🗑️ بدء حذف السجلات للسنة الدراسية: {academic_year}")
            
            cursor.execute("DELETE FROM سجلات_الوافدين_والمغادرين WHERE السنة_الدراسية = ?", 
                          [academic_year])
            
            deleted_count = cursor.rowcount
            conn.commit()
            
            print(f"✅ تم حذف {deleted_count} سجل بنجاح")
            
            # تحديث عرض الجدول
            self.refresh_data()
            
            # عرض رسالة نجاح مخصصة
            self.show_delete_success_dialog(deleted_count, academic_year)
            
            conn.close()
            
        except Exception as e:
            print(f"❌ خطأ في عملية الحذف: {e}")
            QMessageBox.critical(self, "خطأ", f"فشل في حذف البيانات: {e}")
            if conn:
                conn.rollback()
                conn.close()

    def create_delete_confirmation_dialog(self, academic_year, records_count):
        """إنشاء نافذة تأكيد الحذف المخصصة"""
        confirm_dialog = QDialog(self)
        confirm_dialog.setWindowTitle("تأكيد حذف البيانات")
        confirm_dialog.setFixedSize(600, 500)
        confirm_dialog.setLayoutDirection(Qt.RightToLeft)
        
        # تطبيق خط Calibri صراحة على النافذة
        confirm_dialog.setFont(QFont("Calibri", 12))

        # إضافة أيقونة البرنامج
        try:
            app_icon = QIcon("01.ico")
            confirm_dialog.setWindowIcon(app_icon)
        except Exception as e:
            print(f"خطأ في تحميل أيقونة البرنامج: {e}")

        # تنسيق النافذة مع خط Calibri صريح
        confirm_dialog.setStyleSheet("""
            QDialog {
                background-color: #fff0f0;
                border: 2px solid #e74c3c;
                border-radius: 10px;
                font-family: 'Calibri';
            }
            QLabel {
                color: #333333;
                font-weight: bold;
                font-family: 'Calibri';
            }
            QTextBrowser {
                border: 1px solid #e74c3c;
                border-radius: 5px;
                padding: 15px;
                background-color: white;
                font-family: 'Calibri';
            }
            QPushButton {
                border-radius: 5px;
                padding: 8px 15px;
                font-weight: bold;
                font-family: 'Calibri';
                font-size: 12pt;
                min-height: 35px;
            }
            QPushButton#delete_btn {
                background-color: #e74c3c;
                color: white;
                min-width: 150px;
                font-family: 'Calibri';
                font-weight: bold;
            }
            QPushButton#delete_btn:hover {
                background-color: #c0392b;
                border: 2px solid #e74c3c;
            }
            QPushButton#cancel_btn {
                background-color: #95a5a6;
                color: white;
                min-width: 120px;
                font-family: 'Calibri';
                font-weight: bold;
            }
            QPushButton#cancel_btn:hover {
                background-color: #7f8c8d;
                border: 2px solid #95a5a6;
            }
        """)

        # إنشاء تخطيط النافذة
        layout = QVBoxLayout(confirm_dialog)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(15)

        # إضافة أيقونة وعنوان
        header_layout = QHBoxLayout()

        # محاولة إضافة أيقونة التحذير
        icon_label = QLabel()
        icon_label.setAlignment(Qt.AlignCenter)
        try:
            program_icon = QPixmap("01.ico")
           
            if not program_icon.isNull():
                program_icon = program_icon.scaled(48, 48, Qt.KeepAspectRatio, Qt.SmoothTransformation)
                icon_label.setPixmap(program_icon)
                header_layout.addWidget(icon_label)
        except Exception:
            # استخدام رمز تحذير نصي
            icon_label.setText("⚠️")
            icon_label.setFont(QFont("Arial", 32))
            icon_label.setStyleSheet("color: #e74c3c;")
            header_layout.addWidget(icon_label)

        # إضافة عنوان النافذة
        title_label = QLabel("تحذير - حذف البيانات")
        title_label.setFont(QFont("Calibri", 18, QFont.Bold))
        title_label.setStyleSheet("color: #e74c3c; font-family: 'Calibri';")
        title_label.setAlignment(Qt.AlignCenter)
        header_layout.addWidget(title_label, 1)

        layout.addLayout(header_layout)

        # إضافة رسالة التحذير التفصيلية
        message_browser = QTextBrowser()
        message_browser.setReadOnly(True)
        message_browser.setFont(QFont("Calibri", 12))

        html_content = f"""
        <div dir='rtl' style='text-align: right;'>
            <p style='font-family: Calibri; font-size: 16pt; color: #e74c3c; font-weight: bold; text-align: center; margin-bottom: 20px;'>
                               ⚠️ تحذير هام - عملية حذف نهائية ⚠️
            </p>
            
            <p style='font-family: Calibri; font-size: 13pt; color: black; font-weight: bold; margin-bottom: 15px;'>
                أنت على وشك حذف البيانات التالية:
            </p>

            <table style='width: 100%; border-collapse: collapse; margin: 15px 0; font-family: Calibri; font-size: 13pt; color: black; font-weight: bold;'>
                <tr style='background-color: #e74c3c; color: white;'>
                    <th style='border: 2px solid #c0392b; padding: 12px; text-align: right;'>البيان</th>
                    <th style='border: 2px solid #c0392b; padding: 12px; text-align: center;'>القيمة</th>
                </tr>
                <tr style='background-color: #fdedec;'>
                    <td style='border: 2px solid #e74c3c; padding: 12px; text-align: right;'>السنة الدراسية المستهدفة:</td>
                    <td style='border: 2px solid #e74c3c; padding: 12px; text-align: center; color: #e74c3c; font-weight: bold;'>{academic_year}</td>
                </tr>
                <tr style='background-color: #fff;'>
                    <td style='border: 2px solid #e74c3c; padding: 12px; text-align: right;'>عدد السجلات التي سيتم حذفها:</td>
                    <td style='border: 2px solid #e74c3c; padding: 12px; text-align: center; color: #e74c3c; font-weight: bold;'>{records_count} سجل</td>
                </tr>
                <tr style='background-color: #fdedec;'>
                    <td style='border: 2px solid #e74c3c; padding: 12px; text-align: right;'>نوع البيانات:</td>
                    <td style='border: 2px solid #e74c3c; padding: 12px; text-align: center;'>سجلات الوافدين والمغادرين</td>
                </tr>
            </table>

            <div style='background-color: #f8d7da; border: 2px solid #e74c3c; border-radius: 8px; padding: 15px; margin: 20px 0;'>
                <p style='font-family: Calibri; font-size: 14pt; color: #721c24; font-weight: bold; text-align: center; margin: 0;'>
                    🔴 تنبيه مهم جداً 🔴
                </p>
                <ul style='font-family: Calibri; font-size: 13pt; color: #721c24; font-weight: bold; margin: 10px 0;'>
                    <li>هذه العملية نهائية ولا يمكن التراجع عنها</li>
                    <li>جميع البيانات المحذوفة ستفقد نهائياً</li>
                    <li>لا يمكن استرداد البيانات بعد الحذف</li>
                    <li>سيتم حذف جميع المعلومات المرتبطة بهذه السجلات</li>
                </ul>
            </div>

            <p style='font-family: Calibri; font-size: 14pt; color: #e74c3c; font-weight: bold; text-align: center; margin-top: 20px;'>
                هل أنت متأكد من رغبتك في المتابعة؟
            </p>
        </div>
        """

        message_browser.setHtml(html_content)
        layout.addWidget(message_browser)

        # إضافة أزرار التأكيد والإلغاء
        buttons_layout = QHBoxLayout()

        delete_btn = QPushButton("نعم، احذف البيانات")
        delete_btn.setObjectName("delete_btn")
        delete_btn.setFont(QFont("Calibri", 12, QFont.Bold))
        delete_btn.setCursor(Qt.PointingHandCursor)
        delete_btn.clicked.connect(confirm_dialog.accept)

        cancel_btn = QPushButton("إلغاء")
        cancel_btn.setObjectName("cancel_btn")
        cancel_btn.setFont(QFont("Calibri", 12, QFont.Bold))
        cancel_btn.setCursor(Qt.PointingHandCursor)
        cancel_btn.clicked.connect(confirm_dialog.reject)

        buttons_layout.addStretch()
        buttons_layout.addWidget(delete_btn)
        buttons_layout.addWidget(cancel_btn)
        buttons_layout.addStretch()

        layout.addLayout(buttons_layout)

        return confirm_dialog

    def show_delete_success_dialog(self, deleted_count, academic_year):
        """عرض رسالة نجاح الحذف"""
        success_dialog = QDialog(self)
        success_dialog.setWindowTitle("تم الحذف بنجاح")
        success_dialog.setFixedSize(500, 350)
        success_dialog.setLayoutDirection(Qt.RightToLeft)
        
        # تطبيق خط Calibri صراحة على النافذة
        success_dialog.setFont(QFont("Calibri", 12))

        # إضافة أيقونة البرنامج
        try:
            app_icon = QIcon("01.ico")
            success_dialog.setWindowIcon(app_icon)
        except Exception as e:
            print(f"خطأ في تحميل أيقونة البرنامج: {e}")

        # تنسيق النافذة مع خط Calibri صريح
        success_dialog.setStyleSheet("""
            QDialog {
                background-color: #f0fff0;
                border: 2px solid #2ecc71;
                border-radius: 10px;
                font-family: 'Calibri';
            }
            QLabel {
                color: #333333;
                font-weight: bold;
                font-family: 'Calibri';
            }
            QLabel#message_label {
                background-color: #eafaf1;
                border: 1px solid #2ecc71;
                border-radius: 5px;
                padding: 20px;
                font-size: 14pt;
                font-family: 'Calibri';
                font-weight: bold;
            }
            QPushButton {
                background-color: #2ecc71;
                color: white;
                border-radius: 5px;
                padding: 10px 20px;
                font-weight: bold;
                font-family: 'Calibri';
                font-size: 12pt;
                min-height: 35px;
                min-width: 120px;
            }
            QPushButton:hover {
                background-color: #27ae60;
                border: 2px solid #2ecc71;
            }
        """)

        # إنشاء تخطيط النافذة
        layout = QVBoxLayout(success_dialog)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(20)

        # إضافة أيقونة وعنوان
        header_layout = QHBoxLayout()

        # أيقونة النجاح
        icon_label = QLabel()
        icon_label.setText("✅")
        icon_label.setFont(QFont("Arial", 32))
        icon_label.setStyleSheet("color: #2ecc71;")
        icon_label.setAlignment(Qt.AlignCenter)
        header_layout.addWidget(icon_label)

        # عنوان النجاح
        title_label = QLabel("تم الحذف بنجاح")
        title_label.setFont(QFont("Calibri", 18, QFont.Bold))
        title_label.setStyleSheet("color: #27ae60; font-family: 'Calibri';")
        title_label.setAlignment(Qt.AlignCenter)
        header_layout.addWidget(title_label, 1)

        layout.addLayout(header_layout)

        # رسالة النجاح
        message_text = f"""تم حذف {deleted_count} سجل بنجاح!

السنة الدراسية المحذوفة: {academic_year}

تم تحديث عرض البيانات تلقائياً."""

        message_label = QLabel(message_text)
        message_label.setObjectName("message_label")
        message_label.setFont(QFont("Calibri", 13, QFont.Bold))
        message_label.setAlignment(Qt.AlignCenter)
        message_label.setWordWrap(True)
        layout.addWidget(message_label)

        # زر الموافقة
        button_layout = QHBoxLayout()
        ok_button = QPushButton("موافق")
        ok_button.setFont(QFont("Calibri", 12, QFont.Bold))
        ok_button.setCursor(Qt.PointingHandCursor)
        ok_button.clicked.connect(success_dialog.accept)
        button_layout.addStretch()
        button_layout.addWidget(ok_button)
        button_layout.addStretch()

        layout.addLayout(button_layout)

        # عرض النافذة
        success_dialog.exec_()

    def open_request_form(self):
        """فتح نافذة نموذج طلب الملفات"""
        print("📝 تم النقر على زر نموذج طلب الملفات")
        try:
            from sub45_window import RequestFormWindow
            self.request_form_window = RequestFormWindow(self, self.db_path)
            self.request_form_window.show()
            print("✅ تم فتح نافذة نموذج طلب الملفات")
        except Exception as e:
            print(f"❌ خطأ في فتح نافذة نموذج طلب الملفات: {e}")
            QMessageBox.critical(self, "خطأ", f"فشل في فتح نافذة نموذج طلب الملفات: {e}")

    def import_records(self):
        """استيراد سجلات الوافدين والمغادرين"""
        print("📥 تم النقر على زر استيراد سجلات الوافدين والمغادرين")
        try:
            from sub001_window import Sub001Window
            self.import_window = Sub001Window(self)
            
            # ربط إشارة الانتهاء من الاستيراد بتحديث البيانات
            if hasattr(self.import_window, 'import_finished'):
                self.import_window.import_finished.connect(self.on_import_finished)
            elif hasattr(self.import_window, 'finished'):
                self.import_window.finished.connect(self.on_import_window_closed)
            
            self.import_window.show()
            print("✅ تم فتح نافذة استيراد سجلات الوافدين والمغادرين")
        except Exception as e:
            print(f"❌ خطأ في فتح نافذة الاستيراد: {e}")
            QMessageBox.critical(self, "خطأ", f"فشل في فتح نافذة الاستيراد: {e}")

    def on_import_finished(self):
        """معالج إنهاء عملية الاستيراد"""
        print("🔄 تم إنهاء عملية الاستيراد، تحديث البيانات...")
        self.refresh_data()
        print("✅ تم تحديث البيانات بعد الاستيراد")

    def on_import_window_closed(self, result):
        """معالج إغلاق نافذة الاستيراد"""
        print(f"🔄 تم إغلاق نافذة الاستيراد بالنتيجة: {result}")
        # تحديث البيانات عند إغلاق النافذة (احتياطي)
        self.refresh_data()
        print("✅ تم تحديث البيانات بعد إغلاق نافذة الاستيراد")

    def receive_files(self):
        """التوصل بالملفات"""
        print("📫 تم النقر على زر التوصل بالملفات")
        
        # التحقق من وجود سجلات محددة في الجدول الحالي
        selected_records = self.get_selected_records()
        
        if not selected_records:
            QMessageBox.warning(self, "تنبيه", "يرجى اختيار سجل واحد على الأقل من الجدول للتوصل بملفاته.")
            return
        
        # فتح نافذة تأكيد التوصل بالملفات
        self.open_file_receipt_confirmation(selected_records)

    def get_selected_records(self):
        """الحصول على السجلات المحددة من الجدول الحالي"""
        selected_records = []
        
        for row in range(self.table.rowCount()):
            if self.table.isRowHidden(row):
                continue
                
            checkbox = self.table.cellWidget(row, 0)
            if checkbox and checkbox.isChecked():
                # جمع بيانات السجل المحدد
                record_data = {}
                headers = []
                for col in range(self.table.columnCount()):
                    header_item = self.table.horizontalHeaderItem(col)
                    if header_item:
                        headers.append(header_item.text())
                
                for col in range(1, self.table.columnCount()):  # تجاهل عمود الاختيار
                    item = self.table.item(row, col)
                    if item and col < len(headers):
                        column_name = headers[col]
                        record_data[column_name] = item.text()
                
                # إضافة معرف الصف للمرجعية
                record_data['table_row'] = row
                selected_records.append(record_data)
        
        return selected_records

    def open_file_receipt_confirmation(self, selected_records):
        """فتح نافذة تأكيد التوصل بالملفات"""
        
        class FileReceiptConfirmationDialog(QDialog):
            def __init__(self, parent_window=None, selected_records=None):
                super().__init__(parent_window)
                self.parent_window = parent_window
                self.selected_records = selected_records or []
                self.setWindowTitle("تأكيد التوصل بالملفات")
                self.setFixedSize(700, 600)
                self.setLayoutDirection(Qt.RightToLeft)
                
                try:
                    self.setWindowIcon(QIcon("01.ico"))
                except Exception:
                    pass
                
                self.setStyleSheet("""
                    QDialog {
                        background: qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #f0f8ff, stop:1 #e6f3ff);
                        border: 2px solid #4a90e2;
                        border-radius: 15px;
                        font-family: 'Calibri', sans-serif;
                    }
                    QLabel#title_label {
                        color: #2c3e50;
                        font-size: 18pt;
                        font-weight: bold;
                        padding: 15px;
                        background-color: #ffffff;
                        border: 1px solid #4a90e2;
                        border-radius: 10px;
                        margin-bottom: 15px;
                    }
                    QFrame#data_frame {
                        background-color: #f8f9fa;
                        border: 1px solid #dee2e6;
                        border-radius: 10px;
                        padding: 15px;
                        font-family: 'Calibri';
                    }
                    QLabel {
                        color: #34495e;
                        font-size: 11pt;
                        font-weight: bold;
                        margin-bottom: 5px;
                    }
                    QDateEdit, QTextEdit {
                        background-color: #ffffff;
                        border: 1px solid #ced4da;
                        border-radius: 8px;
                        padding: 8px;
                        font-size: 11pt;
                        color: #2c3e50;
                    }
                    QDateEdit:focus, QTextEdit:focus {
                        border: 2px solid #4a90e2;
                        background-color: #fdfdfe;
                    }
                    QPushButton {
                        background-color: #28a745;
                        color: white;
                        border: none;
                        border-radius: 8px;
                        padding: 12px 25px;
                        font-size: 11pt;
                        font-weight: bold;
                        min-width: 120px;
                    }
                    QPushButton:hover {
                        background-color: #218838;
                    }
                    QPushButton:pressed {
                        background-color: #1e7e34;
                    }
                    QPushButton#cancel_button {
                        background-color: #6c757d;
                        font-family: 'Calibri';
                        font-weight: bold;
                    }
                    QPushButton#cancel_button:hover {
                        background-color: #5a6268;
                    }
                    QPushButton#cancel_button:pressed {
                        background-color: #545b62;
                    }
                    QTextBrowser {
                        background-color: white;
                        border: 1px solid #ced4da;
                        border-radius: 8px;
                        padding: 10px;
                    }
                """)
                
                self.setupUI()

            def setupUI(self):
                main_layout = QVBoxLayout(self)
                main_layout.setContentsMargins(20, 20, 20, 20)
                main_layout.setSpacing(15)

                # العنوان
                title_label = QLabel("📫 تأكيد التوصل بالملفات")
                title_label.setObjectName("title_label")
                title_label.setAlignment(Qt.AlignCenter)
                main_layout.addWidget(title_label)

                # إطار البيانات
                data_frame = QFrame()
                data_frame.setObjectName("data_frame")
                data_layout = QVBoxLayout(data_frame)
                data_layout.setSpacing(15)

                # تاريخ الاستلام
                date_layout = QHBoxLayout()
                date_label = QLabel("📅 تاريخ استلام الملفات:")
                date_label.setFont(QFont("Calibri", 12, QFont.Bold))
                
                self.receipt_date = QDateEdit()
                self.receipt_date.setDate(QDate.currentDate())
                self.receipt_date.setFont(QFont("Calibri", 12, QFont.Bold))
                self.receipt_date.setCalendarPopup(True)
                self.receipt_date.setDisplayFormat("yyyy-MM-dd")
                
                date_layout.addWidget(date_label)
                date_layout.addWidget(self.receipt_date)
                date_layout.addStretch()
                data_layout.addLayout(date_layout)

                # ملاحظات إضافية
                notes_label = QLabel("📝 ملاحظات إضافية (اختيارية):")
                notes_label.setFont(QFont("Calibri", 12, QFont.Bold))
                data_layout.addWidget(notes_label)
                
                self.additional_notes = QTextEdit()
                self.additional_notes.setFont(QFont("Calibri", 11))
                self.additional_notes.setMaximumHeight(80)
                self.additional_notes.setPlaceholderText("يمكنك إضافة ملاحظات إضافية هنا...")
                data_layout.addWidget(self.additional_notes)

                main_layout.addWidget(data_frame)

                # عرض السجلات المحددة
                info_label = QLabel(f"📋 السجلات المحددة للتوصل بها ({len(self.selected_records)} سجل):")
                info_label.setFont(QFont("Calibri", 14, QFont.Bold))
                info_label.setStyleSheet("color: #2c3e50; margin-bottom: 10px;")
                main_layout.addWidget(info_label)

                # عرض تفاصيل السجلات
                records_info = QTextBrowser()
                records_info.setMaximumHeight(150)
                records_content = "<div dir='rtl' style='font-family: Calibri; font-size: 11pt;'>"
                
                for i, record in enumerate(self.selected_records, 1):
                    name = record.get('الإسم', 'غير محدد')
                    surname = record.get('النسب', 'غير محدد')
                    institution = record.get('المؤسسة_الأصلية', 'غير محدد')
                    
                    records_content += f"""
                    <p><b>{i}.</b> <b>الاسم:</b> {name} <b>النسب:</b> {surname} <b>المؤسسة:</b> {institution}</p>
                    """
                
                records_content += "</div>"
                records_info.setHtml(records_content)
                main_layout.addWidget(records_info)

                # أزرار التحكم
                buttons_layout = QHBoxLayout()
                
                confirm_btn = QPushButton("📥 تأكيد التوصل")
                confirm_btn.clicked.connect(self.confirm_receipt)
                
                cancel_btn = QPushButton("🚫 إلغاء")
                cancel_btn.setObjectName("cancel_button")
                cancel_btn.clicked.connect(self.reject)
                
                buttons_layout.addStretch()
                buttons_layout.addWidget(confirm_btn)
                buttons_layout.addWidget(cancel_btn)
                buttons_layout.addStretch()
                
                main_layout.addLayout(buttons_layout)

            def confirm_receipt(self):
                """تأكيد التوصل بالملفات المحددة"""
                if not self.selected_records:
                    QMessageBox.warning(self, "تنبيه", "لا توجد سجلات محددة للتوصل بها.")
                    return
                
                # تأكيد العملية
                reply = QMessageBox.question(self, "تأكيد التوصل", 
                                           f"هل أنت متأكد من التوصل بـ {len(self.selected_records)} ملف؟",
                                           QMessageBox.Yes | QMessageBox.No, 
                                           QMessageBox.No)
                
                if reply == QMessageBox.Yes:
                    self.process_file_receipt()

            def process_file_receipt(self):
                """معالجة التوصل بالملفات"""
                conn = self.parent_window.connect_to_database()
                if not conn:
                    return
                
                try:
                    cursor = conn.cursor()
                    
                    # التأكد من وجود الأعمدة المطلوبة
                    cursor.execute("PRAGMA table_info(سجلات_الوافدين_والمغادرين)")
                    columns = [col[1] for col in cursor.fetchall()]
                    
                    required_columns = ["تاريخ_استلام_الملف", "ملاحظات_الملف"]
                    for column in required_columns:
                        if column not in columns:
                            cursor.execute(f"ALTER TABLE سجلات_الوافدين_والمغادرين ADD COLUMN {column} TEXT")
                            print(f"✅ تم إضافة العمود: {column}")
                    
                    receipt_date = self.receipt_date.date().toString('yyyy-MM-dd')
                    additional_notes = self.additional_notes.toPlainText().strip()
                    
                    updated_count = 0
                    
                    # معالجة كل سجل محدد
                    for record in self.selected_records:
                        name = record.get('الإسم', '')
                        surname = record.get('النسب', '')
                        student_number = record.get('رقم_التلميذ', '')
                        
                        # بناء شروط البحث المتعددة لضمان دقة التحديد
                        where_conditions = []
                        params = []
                        
                        if name:
                            where_conditions.append("الإسم = ?")
                            params.append(name)
                        if surname:
                            where_conditions.append("النسب = ?")
                            params.append(surname)
                        if student_number:
                            where_conditions.append("رقم_التلميذ = ?")
                            params.append(student_number)
                        
                        if not where_conditions:
                            print(f"⚠️ تخطي سجل بدون معرفات كافية")
                            continue
                        
                        where_clause = " AND ".join(where_conditions)
                        
                        # البحث عن السجل في قاعدة البيانات
                        query = f"""
                        UPDATE سجلات_الوافدين_والمغادرين 
                        SET تاريخ_استلام_الملف = ?, ملاحظات_الملف = ?
                        WHERE {where_clause}
                        AND (تاريخ_استلام_الملف IS NULL OR تاريخ_استلام_الملف = '')
                        """
                        
                        # بناء ملاحظات الملف
                        file_notes = f"تم التوصل بالملف بتاريخ {receipt_date}"
                        
                        if additional_notes:
                            file_notes += f" - {additional_notes}"
                        
                        update_params = [receipt_date, file_notes] + params
                        cursor.execute(query, update_params)
                        
                        if cursor.rowcount > 0:
                            updated_count += cursor.rowcount
                            print(f"✅ تم تحديث ملف: {name} {surname}")
                        else:
                            print(f"⚠️ لم يتم العثور على السجل أو تم استلام ملفه مسبقاً: {name} {surname}")
                    
                    conn.commit()
                    conn.close()
                    
                    # تحديث البيانات في النافذة الرئيسية
                    if self.parent_window:
                        self.parent_window.refresh_data()
                    
                    # عرض رسالة النجاح
                    success_message = f"تم التوصل بـ {updated_count} ملف بنجاح!\n\n"
                    success_message += f"تاريخ الاستلام: {receipt_date}\n"
                    if additional_notes:
                        success_message += f"الملاحظات الإضافية: {additional_notes}"
                    
                    QMessageBox.information(self, "نجح العملية", success_message)
                    self.accept()
                    
                except Exception as e:
                    print(f"❌ خطأ في معالجة التوصل بالملفات: {e}")
                    QMessageBox.critical(self, "خطأ", f"فشل في معالجة التوصل بالملفات: {e}")
                    if conn:
                        conn.rollback()
                        conn.close()

        # فتح النافذة
        dialog = FileReceiptConfirmationDialog(self, selected_records)
        dialog.exec_()

# تشغيل التطبيق كنافذة مستقلة للاختبار
if __name__ == "__main__":
    print("🚀 بدء تشغيل التطبيق...")
    app = QApplication(sys.argv)
    print("🌐 تعيين اتجاه التخطيط من اليمين إلى اليسار")
    app.setLayoutDirection(Qt.RightToLeft)  # تعيين اتجاه التخطيط من اليمين إلى اليسار
    print("🖼️ إنشاء النافذة الرئيسية")
    window = ExamSetupWindow44()
    print("👁️ عرض النافذة")
    window.show()
    print("⏳ دخول حلقة الأحداث الرئيسية")
    sys.exit(app.exec_())
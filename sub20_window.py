import sys
import sqlite3
import datetime
import os
from PyQt5.QtWidgets import (QApplication, QWidget, QVBoxLayout, QHBoxLayout,
                            QPushButton, QFrame, QLabel, QComboBox, QLineEdit,
                            QMessageBox, QFileDialog, QGridLayout, QDialog,
                            QTableWidget, QTableWidgetItem, QHeaderView, QTabWidget,
                            QProgressBar)
from PyQt5.QtCore import Qt, QDate, QTimer
from PyQt5.QtGui import QFont, QColor, QIcon
from PyQt5.QtSql import QSqlQuery

# استيراد فئة معالج تقارير الغياب
from attendance_report import AttendanceReport
from split_attendance_report import SplitAttendanceReport  # استيراد الفئة الجديدة

class PrintListsWindow(QWidget):
    def __init__(self, parent=None, db=None, academic_year=None):
        super().__init__(parent)

        # حفظ قاعدة البيانات والسنة الدراسية
        self.db = db
        self.academic_year = academic_year

        # طباعة معلومات تشخيصية
        print(f"INFO: تم إنشاء PrintListsWindow مع db={db is not None}, academic_year={academic_year}")

        # تعيين النافذة كنافذة مستقلة (غير مدمجة)
        self.setWindowModality(Qt.NonModal)

        # تعيين خصائص النافذة (السماح بالتصغير والتكبير)
        self.setWindowFlags(Qt.Window)

        # Set window title and size
        self.setWindowTitle("طباعة اللوائح")
        self.setGeometry(300, 30, 700, 650)

        # Set window direction to right-to-left for Arabic
        self.setLayoutDirection(Qt.RightToLeft)

        # تعيين مؤشر اليد للنافذة الرئيسية
        self.setCursor(Qt.ArrowCursor)  # المؤشر الافتراضي للخلفية

        # إنشاء نسخة من فئة تقرير الغياب
        self.report_generator = AttendanceReport()
        self.split_report_generator = SplitAttendanceReport()  # إنشاء كائن من الفئة الجديدة

        # إنشاء اتصال بقاعدة البيانات إذا لم يتم تمريره
        if self.db is None:
            try:
                self.db = sqlite3.connect("data.db")
                print("INFO: تم إنشاء اتصال جديد بقاعدة البيانات في PrintListsWindow")
            except Exception as e:
                print(f"ERROR: فشل الاتصال بقاعدة البيانات في PrintListsWindow: {e}")

        # Initialize UI
        self.initUI()

        # تحميل رموز الحصص المحفوظة مسبقا
        self.load_period_settings()

        # تحميل الأقسام المسندة عند فتح النافذة
        # نستخدم QTimer لتأخير التحميل قليلاً بعد إنشاء واجهة المستخدم
        QTimer.singleShot(100, self.load_assigned_classes)

    def initUI(self):
        # Main vertical layout
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(5, 5, 5, 5)
        main_layout.setSpacing(1)

        # Window title
        title_label = QLabel("طباعة اللوائح")
        title_label.setFont(QFont("Calibri", 20, QFont.Bold))
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("color: black;")
        main_layout.addWidget(title_label)

        # Content frame with light blue background - تبسيط الإطار
        content_frame = QFrame()
        content_frame.setStyleSheet("""
            QFrame {
                background-color: white;
                border: 1px solid #ddd;
                padding: 15px;
            }
        """)

        # Content layout
        content_layout = QVBoxLayout(content_frame)
        content_layout.setContentsMargins(15, 15, 15, 15)
        content_layout.setSpacing(15)

        # تنسيق العناوين لتكون واضحة
        label_style = """
            QLabel {
                font-family: 'Calibri';
                font-size: 13pt;
                font-weight: bold;
                color: black;
                background-color: transparent;
                padding: 5px;
            }
        """

        # إضافة اختيار رقم الحراسة - في صف واحد أفقياً
        supervision_row = QHBoxLayout()
        supervision_row.setSpacing(10)

        supervision_label = QLabel("اختر رقم الحراسة:")
        supervision_label.setFont(QFont("Calibri", 13, QFont.Bold))
        supervision_label.setStyleSheet(label_style)
        supervision_label.setFixedWidth(150)  # عرض ثابت للعنوان
        supervision_row.addWidget(supervision_label)

        self.supervision_combo = QComboBox()
        self.supervision_combo.setFont(QFont("Calibri", 13))
        self.supervision_combo.setFixedWidth(200)  # تعيين عرض ثابت 200 بكسل
        self.supervision_combo.setCursor(Qt.PointingHandCursor)  # تغيير شكل المؤشر إلى يد
        # ربط تغيير قيمة مربع الحراسة بتحديث الأقسام المسندة
        self.supervision_combo.currentIndexChanged.connect(self.on_supervision_changed)

        # إضافة خيارات الحراسة (من 1 إلى 5)
        for i in range(1, 6):
            self.supervision_combo.addItem(f"حراسة رقم {i}")

        # لا نقوم بتحديد أي حراسة افتراضياً، نترك مربع التحرير والسرد فارغاً
        # حتى يقوم المستخدم بتحديد رقم الحراسة بنفسه
        self.supervision_combo.setCurrentIndex(-1)
        supervision_row.addWidget(self.supervision_combo)
        supervision_row.addStretch(1)  # إضافة مساحة مرنة في نهاية الصف

        content_layout.addLayout(supervision_row)

        # Class selection - في صف واحد أفقياً
        class_row = QHBoxLayout()
        class_row.setSpacing(10)

        class_label = QLabel("اختر القسم:")
        class_label.setFont(QFont("Calibri", 13, QFont.Bold))
        class_label.setStyleSheet(label_style)
        class_label.setFixedWidth(150)  # عرض ثابت للعنوان
        class_row.addWidget(class_label)

        self.class_combo = QComboBox()
        self.class_combo.setFont(QFont("Calibri", 13))
        self.class_combo.setFixedWidth(200)  # تعيين عرض ثابت 200 بكسل
        self.class_combo.setCursor(Qt.PointingHandCursor)  # تغيير شكل المؤشر إلى يد
        class_row.addWidget(self.class_combo)
        class_row.addStretch(1)  # إضافة مساحة مرنة في نهاية الصف

        content_layout.addLayout(class_row)

        # تحميل الأقسام المسندة من قاعدة البيانات حسب رقم الحراسة 1 (الافتراضي)
        # سيتم تحميل الأقسام تلقائياً عند تعيين الحراسة رقم 1 كقيمة افتراضية
        # لذلك لا داعي لاستدعاء الدالة هنا

        # إضافة اختيار الأسبوع - في صف واحد أفقياً
        week_row = QHBoxLayout()
        week_row.setSpacing(10)

        week_label = QLabel("اختر الأسبوع:")
        week_label.setFont(QFont("Calibri", 13, QFont.Bold))
        week_label.setStyleSheet(label_style)
        week_label.setFixedWidth(150)  # عرض ثابت للعنوان
        week_row.addWidget(week_label)

        self.week_combo = QComboBox()
        self.week_combo.setFont(QFont("Calibri", 13))
        self.week_combo.setFixedWidth(200)  # تعيين عرض ثابت 200 بكسل
        self.week_combo.setCursor(Qt.PointingHandCursor)  # تغيير شكل المؤشر إلى يد
        self.week_combo.addItem("الأسبوع الحالي")
        self.week_combo.addItem("الأسبوع القادم")
        self.week_combo.addItem("الأسبوع الثالث")
        week_row.addWidget(self.week_combo)
        week_row.addStretch(1)  # إضافة مساحة مرنة في نهاية الصف

        content_layout.addLayout(week_row)

        # إضافة عنوان لمربعات الحصص
        periods_label = QLabel("رموز الحصص في التقرير:")
        periods_label.setFont(QFont("Calibri", 13, QFont.Bold))
        periods_label.setStyleSheet(label_style)
        content_layout.addWidget(periods_label)

        # إنشاء إطار لاحتواء مربعات الحصص
        periods_frame = QFrame()
        periods_frame.setStyleSheet("""
            QFrame {
                background-color: #f9f9f9;
                border: 1px solid #ddd;
                border-radius: 5px;
                padding: 8px;
            }
        """)
        periods_layout = QHBoxLayout(periods_frame)
        periods_layout.setSpacing(10)
        periods_layout.setAlignment(Qt.AlignCenter)

        # إنشاء قائمة لتخزين مربعات النصوص
        self.period_inputs = []

        # إضافة 8 مربعات نصية للحصص بدون عناوين
        for i in range(8):
            period_input = QLineEdit(str(i + 1))  # القيمة الافتراضية من 1 إلى 8
            period_input.setFont(QFont("Calibri", 12))
            period_input.setFixedSize(30, 30)
            period_input.setAlignment(Qt.AlignCenter)
            period_input.setMaxLength(2)
            period_input.setCursor(Qt.PointingHandCursor)  # تغيير شكل المؤشر إلى يد

            period_input.setStyleSheet("""
                QLineEdit {
                    border: 1px solid #bbb;
                    border-radius: 4px;
                    background-color: white;
                }
                QLineEdit:focus {
                    border: 1px solid #3498db;
                }
            """)

            self.period_inputs.append(period_input)
            periods_layout.addWidget(period_input)

        # إضافة شرح مختصر
        note_label = QLabel("ملاحظة: يمكن تعديل رموز الحصص حسب جدول المؤسسة (8 حصص لكل يوم)")
        note_label.setFont(QFont("Calibri", 10))
        note_label.setStyleSheet("color: #555;")
        note_label.setAlignment(Qt.AlignCenter)

        # إضافة زر حفظ التعديلات للحصص
        save_periods_btn = QPushButton("حفظ التعديلات")
        save_periods_btn.setFont(QFont("Calibri", 11, QFont.Bold))
        save_periods_btn.setCursor(Qt.PointingHandCursor)  # تغيير شكل المؤشر إلى يد
        save_periods_btn.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                color: white;
                border-radius: 5px;
                padding: 8px 15px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #219a52;
            }
            QPushButton:pressed {
                background-color: #1e8449;
            }
        """)
        save_periods_btn.clicked.connect(self.save_period_settings)

        # إضافة العناصر إلى التخطيط
        periods_note_layout = QVBoxLayout()
        periods_note_layout.addWidget(periods_frame)
        periods_note_layout.addWidget(note_label)
        periods_note_layout.addWidget(save_periods_btn)  # إضافة زر الحفظ
        periods_note_layout.setAlignment(save_periods_btn, Qt.AlignCenter)  # محاذاة الزر للوسط

        content_layout.addLayout(periods_note_layout)

        # Add spacer
        content_layout.addStretch()

        # تعديل الأزرار الثلاثة بتغيير التخطيط والأبعاد
        # إنشاء تخطيط شبكي بدلاً من التخطيط الأفقي
        buttons_grid = QGridLayout()
        buttons_grid.setSpacing(10)

        # Create button style
        button_style = """
            QPushButton {
                background-color: #3498db;
                color: white;
                border-radius: 5px;
                padding: 10px 15px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
            QPushButton:pressed {
                background-color: #1c6ea4;
            }
        """

        # الزر الأول: لائحة الغياب الأسبوعي - عرض 200
        self.print_btn1 = QPushButton("لائحة الغياب الأسبوعي")
        self.print_btn1.setFont(QFont("Calibri", 13, QFont.Bold))
        self.print_btn1.setCursor(Qt.PointingHandCursor)  # تغيير شكل المؤشر إلى يد
        self.print_btn1.setStyleSheet(button_style)
        self.print_btn1.setFixedWidth(200)  # تعيين العرض إلى 200
        self.print_btn1.clicked.connect(self.show_loading_and_print_report)

        # الزر الثاني: لائحة الغياب نصف الأسبوعي الاثنين - عرض 200
        self.print_btn2 = QPushButton("لائحة الغياب نصف الأسبوعي الاثنين")
        self.print_btn2.setFont(QFont("Calibri", 13, QFont.Bold))
        self.print_btn2.setCursor(Qt.PointingHandCursor)  # تغيير شكل المؤشر إلى يد
        self.print_btn2.setStyleSheet(button_style)
        self.print_btn2.setFixedWidth(300)  # تعيين العرض إلى 200
        self.print_btn2.clicked.connect(self.print_first_half_report)

        # الزر الثالث: لائحة الغياب نصف الأسبوعي الأربعاء - عرض 200 وتحت الزر الثاني
        self.print_btn3 = QPushButton("لائحة الغياب نصف الأسبوعي الأربعاء")
        self.print_btn3.setFont(QFont("Calibri", 13, QFont.Bold))
        self.print_btn3.setCursor(Qt.PointingHandCursor)  # تغيير شكل المؤشر إلى يد
        self.print_btn3.setStyleSheet(button_style)
        self.print_btn3.setFixedWidth(300)  # تعيين العرض إلى 200
        self.print_btn3.clicked.connect(self.print_second_half_report)

        # إضافة الأزرار في التخطيط الشبكي
        buttons_grid.addWidget(self.print_btn1, 0, 0, 1, 1, Qt.AlignCenter)  # الصف 0، العمود 0
        buttons_grid.addWidget(self.print_btn2, 0, 1, 1, 1, Qt.AlignCenter)  # الصف 0، العمود 1
        buttons_grid.addWidget(self.print_btn3, 1, 1, 1, 1, Qt.AlignCenter)  # الصف 1، العمود 1 (تحت الزر الثاني)

        # تخصيص نسبة عرض الأعمدة في الشبكة
        buttons_grid.setColumnStretch(0, 1)  # تخصيص نسبة للعمود 0
        buttons_grid.setColumnStretch(1, 1)  # تخصيص نسبة للعمود 1
        buttons_grid.setColumnStretch(2, 1)  # تخصيص نسبة للعمود 2 (مساحة فارغة)

        # إضافة تخطيط الأزرار إلى التخطيط الرئيسي
        content_layout.addLayout(buttons_grid)

        # Add content frame to main layout
        main_layout.addWidget(content_frame)

        # Add close button
        close_btn = QPushButton("إغلاق")
        close_btn.setFont(QFont("Calibri", 13, QFont.Bold))
        close_btn.setCursor(Qt.PointingHandCursor)  # تغيير شكل المؤشر إلى يد
        close_btn.setStyleSheet("""
            QPushButton {
                background-color: #e74c3c;
                color: white;
                border-radius: 5px;
                padding: 10px 15px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #c0392b;
            }
        """)
        close_btn.clicked.connect(self.close)

        main_layout.addWidget(close_btn)

    def save_period_settings(self):
        """حفظ إعدادات رموز الحصص في ملف JSON"""
        try:
            import json

            # الحصول على قيم الحصص من مربعات النصوص
            periods = [input_field.text() for input_field in self.period_inputs]

            # التحقق من صحة القيم المدخلة
            for idx, period in enumerate(periods):
                if not period.strip():
                    periods[idx] = str(idx + 1)  # استعادة القيمة الافتراضية في حالة تركها فارغة

            # إنشاء قاموس لتخزين الإعدادات
            settings = {
                "period_codes": periods,
                "last_updated": datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            }

            # اسم الملف للإعدادات
            settings_file = "print_settings.json"

            # حفظ الإعدادات في ملف JSON
            with open(settings_file, 'w', encoding='utf-8') as f:
                json.dump(settings, f, ensure_ascii=False, indent=4)

            # تم إزالة رسالة النجاح لتجنب ظهور نوافذ منبثقة
            print("تم حفظ إعدادات رموز الحصص بنجاح")

        except Exception as e:
            # تم إزالة رسالة الخطأ لتجنب ظهور نوافذ منبثقة
            print(f"خطأ أثناء حفظ الإعدادات: {str(e)}")

    def load_period_settings(self):
        """تحميل إعدادات رموز الحصص من ملف JSON"""
        try:
            import json

            # اسم الملف للإعدادات
            settings_file = "print_settings.json"

            # التحقق من وجود الملف
            if not os.path.exists(settings_file):
                return  # إذا لم يكن الملف موجوداً، لا تفعل شيئاً

            # قراءة الإعدادات من ملف JSON
            with open(settings_file, 'r', encoding='utf-8') as f:
                settings = json.load(f)

            # تحديث مربعات النصوص بالقيم المحفوظة
            periods = settings.get("period_codes", [])
            for idx, period in enumerate(periods):
                if idx < len(self.period_inputs):
                    self.period_inputs[idx].setText(period)

        except Exception as e:
            print(f"Error loading period settings: {str(e)}")
            # Usando un enfoque silencioso para no molestar al usuario con mensajes de error
            # si hay problemas al cargar las configuraciones

    def show_loading_and_print_report(self):
        """عرض شريط التحميل ثم طباعة تقرير الغياب"""
        # إنشاء نافذة حوار للتحميل
        self.loading_dialog = QDialog(self)
        self.loading_dialog.setWindowTitle("جاري إنشاء التقرير...")
        self.loading_dialog.setFixedSize(400, 100)
        self.loading_dialog.setWindowFlags(Qt.Dialog | Qt.CustomizeWindowHint | Qt.WindowTitleHint)

        # إنشاء تخطيط للنافذة
        layout = QVBoxLayout(self.loading_dialog)

        # إضافة نص
        label = QLabel("جاري إنشاء تقرير الغياب الأسبوعي، يرجى الانتظار...")
        label.setFont(QFont("Calibri", 12))
        label.setAlignment(Qt.AlignCenter)
        layout.addWidget(label)

        # إضافة شريط التحميل
        self.progress_bar = QProgressBar()
        self.progress_bar.setRange(0, 0)  # شريط تحميل غير محدد
        self.progress_bar.setTextVisible(False)
        layout.addWidget(self.progress_bar)

        # عرض نافذة التحميل
        self.loading_dialog.show()

        # استخدام مؤقت لبدء عملية الطباعة بعد عرض نافذة التحميل
        QTimer.singleShot(100, lambda: self.print_attendance_report(1))

    def print_attendance_report(self, report_type):
        """Generate and print the attendance report PDF"""
        try:
            # الحصول على قيم الحصص من مربعات النصوص - فقط 8 مربعات
            periods = [input_field.text() for input_field in self.period_inputs]

            # Get selected class and institution data
            selected_class = self.class_combo.currentText()
            institution_data = self.get_institution_data()

            # حساب تاريخ بداية الأسبوع (الاثنين) بناءً على اختيار الأسبوع
            selected_date = self.get_week_start_date()

            # Create appropriate filename based on report type
            report_type_names = {
                1: "تقرير_الغياب",
                2: "نموذج_فارغ",
                3: "نموذج_مختصر"
            }
            report_type_name = report_type_names.get(report_type, "تقرير_الغياب")

            # التحقق مما إذا كان المستخدم قد اختار "جميع الأقسام المسندة"
            if selected_class == "جميع الأقسام المسندة":
                # طباعة جميع الأقسام في ملف واحد
                # جمع جميع الأقسام من مربع التحرير والسرد (تخطي العنصر الأول "جميع الأقسام المسندة")
                classes_to_print = [self.class_combo.itemText(i) for i in range(1, self.class_combo.count())]

                if not classes_to_print:
                    QMessageBox.warning(self, "تنبيه", "لا توجد أقسام مسندة للطباعة.")
                    return

                # إنشاء اسم الملف لجميع الأقسام مع التاريخ والوقت
                current_datetime = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
                file_name = f"{report_type_name}_جميع_الأقسام_{current_datetime}.pdf"

                # إنشاء مجلد رئيسي على سطح المكتب
                desktop_path = os.path.join(os.path.expanduser("~"), "Desktop")
                main_reports_dir = os.path.join(desktop_path, "تقارير برنامج المعين في الحراسة العامة")
                if not os.path.exists(main_reports_dir):
                    os.makedirs(main_reports_dir)
                    print(f"تم إنشاء المجلد الرئيسي: {main_reports_dir}")

                # إنشاء مجلد فرعي لأوراق الغياب الأسبوعية
                reports_dir = os.path.join(main_reports_dir, "أوراق الغياب الأسبوعية")
                if not os.path.exists(reports_dir):
                    os.makedirs(reports_dir)
                    print(f"تم إنشاء المجلد الفرعي: {reports_dir}")

                # تحديد مسار الملف الكامل
                file_path = os.path.join(reports_dir, file_name)

                # إعداد بيانات كل قسم
                class_data_list = []
                for class_name in classes_to_print:
                    # الحصول على بيانات الطلاب حسب نوع التقرير
                    if report_type == 1:  # تقرير الغياب الأسبوعي (كامل)
                        students = self.get_student_data(class_name)
                    elif report_type == 2:  # نموذج فارغ
                        # Create empty template with placeholder for 30 students
                        students = [{"id": i, "name": "", "code": ""} for i in range(1, 31)]
                    elif report_type == 3:  # نموذج مختصر
                        # Get students but limit to 15 or add placeholders if less
                        students = self.get_student_data(class_name)
                        if len(students) < 15:
                            # Add placeholders to reach 15 students
                            for i in range(len(students) + 1, 16):
                                students.append({"id": i, "name": "", "code": ""})
                        else:
                            # Limit to first 15 students
                            students = students[:15]

                    # إضافة البيانات لقائمة الأقسام
                    class_data_list.append({
                        'class_name': class_name,
                        'students': students
                    })

                # إنشاء التقرير بكل الأقسام في ملف واحد
                success = self.report_generator.generate_multi_class_pdf(
                    file_path,
                    class_data_list,
                    institution_data,
                    selected_date,
                    report_type,
                    periods  # إرسال قيم الحصص كمعامل إضافي
                )

                # إغلاق نافذة التحميل
                if hasattr(self, 'loading_dialog') and self.loading_dialog.isVisible():
                    self.loading_dialog.close()

                if success:
                    # فتح الملف بعد الإنشاء - سيتم عرض رسالة النجاح في دالة open_pdf_file
                    self.open_pdf_file(file_path)
                else:
                    QMessageBox.warning(self, "تنبيه", "حدث خطأ أثناء إنشاء التقرير")

                return

            # في حالة اختيار قسم محدد (وليس "جميع الأقسام المسندة")
            # إنشاء اسم الملف مع التاريخ والوقت
            current_datetime = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
            file_name = f"{report_type_name}_{selected_class}_{current_datetime}.pdf"

            # إنشاء مجلد رئيسي على سطح المكتب
            desktop_path = os.path.join(os.path.expanduser("~"), "Desktop")
            main_reports_dir = os.path.join(desktop_path, "تقارير برنامج المعين في الحراسة العامة")
            if not os.path.exists(main_reports_dir):
                os.makedirs(main_reports_dir)
                print(f"تم إنشاء المجلد الرئيسي: {main_reports_dir}")

            # إنشاء مجلد فرعي لأوراق الغياب الأسبوعية
            reports_dir = os.path.join(main_reports_dir, "أوراق الغياب الأسبوعية")
            if not os.path.exists(reports_dir):
                os.makedirs(reports_dir)
                print(f"تم إنشاء المجلد الفرعي: {reports_dir}")

            # تحديد مسار الملف الكامل
            file_path = os.path.join(reports_dir, file_name)

            # طباعة تقرير لقسم واحد
            self._print_single_class_report(selected_class, file_path, report_type, selected_date, institution_data, periods)

        except Exception as e:
            # إغلاق نافذة التحميل في حالة حدوث خطأ
            if hasattr(self, 'loading_dialog') and self.loading_dialog.isVisible():
                self.loading_dialog.close()
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء إنشاء التقرير: {str(e)}")

    def _print_single_class_report(self, class_name, file_path, report_type, selected_date, institution_data, periods):
        """طباعة تقرير لقسم واحد محدد"""
        # Retrieve student data from database based on report type
        students = []
        if report_type == 1:  # تقرير الغياب الأسبوعي (كامل)
            students = self.get_student_data(class_name)
        elif report_type == 2:  # نموذج فارغ
            # Create empty template with placeholder for 30 students
            for i in range(1, 31):
                students.append({"id": i, "name": "", "code": ""})
        elif report_type == 3:  # نموذج مختصر
            # Get students but limit to 15 or add placeholders if less
            students = self.get_student_data(class_name)
            if len(students) < 15:
                # Add placeholders to reach 15 students
                for i in range(len(students) + 1, 16):
                    students.append({"id": i, "name": "", "code": ""})
            else:
                # Limit to first 15 students
                students = students[:15]

        # استخدام كائن معالج التقارير لإنشاء ملف PDF
        success = self.report_generator.generate_pdf(
            file_path,
            students,
            class_name,
            institution_data,
            selected_date,
            report_type,
            periods  # إرسال قيم الحصص كمعامل إضافي
        )

        # إغلاق نافذة التحميل
        if hasattr(self, 'loading_dialog') and self.loading_dialog.isVisible():
            self.loading_dialog.close()

        if success:
            # فتح الملف بعد الإنشاء
            self.open_pdf_file(file_path)
        else:
            QMessageBox.warning(self, "تنبيه", "حدث خطأ أثناء إنشاء التقرير")

    def print_first_half_report(self):
        """طباعة تقرير النصف الأول من الأسبوع (الاثنين والثلاثاء)"""
        # إنشاء نافذة حوار للتحميل
        self.loading_dialog = QDialog(self)
        self.loading_dialog.setWindowTitle("جاري إنشاء التقرير...")
        self.loading_dialog.setFixedSize(400, 100)
        self.loading_dialog.setWindowFlags(Qt.Dialog | Qt.CustomizeWindowHint | Qt.WindowTitleHint)

        # إنشاء تخطيط للنافذة
        layout = QVBoxLayout(self.loading_dialog)

        # إضافة نص
        label = QLabel("جاري إنشاء تقرير الغياب نصف الأسبوعي، يرجى الانتظار...")
        label.setFont(QFont("Calibri", 12))
        label.setAlignment(Qt.AlignCenter)
        layout.addWidget(label)

        # إضافة شريط التحميل
        self.progress_bar = QProgressBar()
        self.progress_bar.setRange(0, 0)  # شريط تحميل غير محدد
        self.progress_bar.setTextVisible(False)
        layout.addWidget(self.progress_bar)

        # عرض نافذة التحميل
        self.loading_dialog.show()

        # استخدام مؤقت لبدء عملية الطباعة بعد عرض نافذة التحميل
        QTimer.singleShot(100, self._print_first_half_report_process)

    def _print_first_half_report_process(self):
        """العملية الفعلية لطباعة تقرير النصف الأول من الأسبوع"""
        try:
            # الحصول على قيم الحصص من مربعات النصوص
            periods = [input_field.text() for input_field in self.period_inputs]

            # Get selected class and institution data
            selected_class = self.class_combo.currentText()
            institution_data = self.get_institution_data()

            # حساب تاريخ بداية الأسبوع (الاثنين) بناءً على اختيار الأسبوع
            selected_date = self.get_week_start_date()

            # التحقق مما إذا كان المستخدم قد اختار "جميع الأقسام المسندة"
            if selected_class == "جميع الأقسام المسندة":
                # طباعة جميع الأقسام في ملف واحد
                # جمع جميع الأقسام من مربع التحرير والسرد (تخطي العنصر الأول "جميع الأقسام المسندة")
                classes_to_print = [self.class_combo.itemText(i) for i in range(1, self.class_combo.count())]

                if not classes_to_print:
                    QMessageBox.warning(self, "تنبيه", "لا توجد أقسام مسندة للطباعة.")
                    return

                # إنشاء اسم الملف لجميع الأقسام مع التاريخ والوقت
                current_datetime = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
                file_name = f"تقرير_الغياب_النصف_الأول_جميع_الأقسام_{current_datetime}.pdf"

                # إنشاء مجلد رئيسي على سطح المكتب
                desktop_path = os.path.join(os.path.expanduser("~"), "Desktop")
                main_reports_dir = os.path.join(desktop_path, "تقارير برنامج المعين في الحراسة العامة")
                if not os.path.exists(main_reports_dir):
                    os.makedirs(main_reports_dir)
                    print(f"تم إنشاء المجلد الرئيسي: {main_reports_dir}")

                # إنشاء مجلد فرعي لأوراق الغياب الأسبوعية
                reports_dir = os.path.join(main_reports_dir, "أوراق الغياب الأسبوعية")
                if not os.path.exists(reports_dir):
                    os.makedirs(reports_dir)
                    print(f"تم إنشاء المجلد الفرعي: {reports_dir}")

                # تحديد مسار الملف الكامل
                file_path = os.path.join(reports_dir, file_name)

                # إعداد بيانات كل قسم
                class_data_list = []
                for class_name in classes_to_print:
                    # الحصول على بيانات الطلاب
                    students = self.get_student_data(class_name)

                    # إضافة البيانات لقائمة الأقسام
                    class_data_list.append({
                        'class_name': class_name,
                        'students': students
                    })

                # إنشاء التقرير بكل الأقسام في ملف واحد
                success = self.split_report_generator.generate_first_half_multi_class(
                    file_path,
                    class_data_list,
                    institution_data,
                    selected_date,
                    1,  # نوع التقرير
                    periods  # إرسال قيم الحصص كمعامل إضافي
                )

                # إغلاق نافذة التحميل
                if hasattr(self, 'loading_dialog') and self.loading_dialog.isVisible():
                    self.loading_dialog.close()

                if success:
                    # فتح الملف بعد الإنشاء
                    self.open_pdf_file(file_path)
                else:
                    QMessageBox.warning(self, "تنبيه", "حدث خطأ أثناء إنشاء التقرير")

                return

            # في حالة اختيار قسم محدد (وليس "جميع الأقسام المسندة")
            # إنشاء اسم الملف مع التاريخ والوقت
            current_datetime = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
            file_name = f"تقرير_الغياب_النصف_الأول_{selected_class}_{current_datetime}.pdf"

            # إنشاء مجلد رئيسي على سطح المكتب
            desktop_path = os.path.join(os.path.expanduser("~"), "Desktop")
            main_reports_dir = os.path.join(desktop_path, "تقارير برنامج المعين في الحراسة العامة")
            if not os.path.exists(main_reports_dir):
                os.makedirs(main_reports_dir)
                print(f"تم إنشاء المجلد الرئيسي: {main_reports_dir}")

            # إنشاء مجلد فرعي لأوراق الغياب الأسبوعية
            reports_dir = os.path.join(main_reports_dir, "أوراق الغياب الأسبوعية")
            if not os.path.exists(reports_dir):
                os.makedirs(reports_dir)
                print(f"تم إنشاء المجلد الفرعي: {reports_dir}")

            # تحديد مسار الملف الكامل
            file_path = os.path.join(reports_dir, file_name)

            # الحصول على بيانات الطلاب
            students = self.get_student_data(selected_class)

            # استخدام كائن معالج التقارير لإنشاء ملف PDF
            success = self.split_report_generator.generate_first_half(
                file_path,
                students,
                selected_class,
                institution_data,
                selected_date,
                1,  # نوع التقرير
                periods  # إرسال قيم الحصص كمعامل إضافي
            )

            # إغلاق نافذة التحميل
            if hasattr(self, 'loading_dialog') and self.loading_dialog.isVisible():
                self.loading_dialog.close()

            if success:
                # فتح الملف بعد الإنشاء
                self.open_pdf_file(file_path)
            else:
                QMessageBox.warning(self, "تنبيه", "حدث خطأ أثناء إنشاء التقرير")

        except Exception as e:
            # إغلاق نافذة التحميل في حالة حدوث خطأ
            if hasattr(self, 'loading_dialog') and self.loading_dialog.isVisible():
                self.loading_dialog.close()
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء إنشاء تقرير النصف الأول: {str(e)}")

    def print_second_half_report(self):
        """طباعة تقرير النصف الثاني من الأسبوع (الأربعاء والخميس والجمعة)"""
        # إنشاء نافذة حوار للتحميل
        self.loading_dialog = QDialog(self)
        self.loading_dialog.setWindowTitle("جاري إنشاء التقرير...")
        self.loading_dialog.setFixedSize(400, 100)
        self.loading_dialog.setWindowFlags(Qt.Dialog | Qt.CustomizeWindowHint | Qt.WindowTitleHint)

        # إنشاء تخطيط للنافذة
        layout = QVBoxLayout(self.loading_dialog)

        # إضافة نص
        label = QLabel("جاري إنشاء تقرير الغياب نصف الأسبوعي، يرجى الانتظار...")
        label.setFont(QFont("Calibri", 12))
        label.setAlignment(Qt.AlignCenter)
        layout.addWidget(label)

        # إضافة شريط التحميل
        self.progress_bar = QProgressBar()
        self.progress_bar.setRange(0, 0)  # شريط تحميل غير محدد
        self.progress_bar.setTextVisible(False)
        layout.addWidget(self.progress_bar)

        # عرض نافذة التحميل
        self.loading_dialog.show()

        # استخدام مؤقت لبدء عملية الطباعة بعد عرض نافذة التحميل
        QTimer.singleShot(100, self._print_second_half_report_process)

    def _print_second_half_report_process(self):
        """العملية الفعلية لطباعة تقرير النصف الثاني من الأسبوع"""
        try:
            # الحصول على قيم الحصص من مربعات النصوص
            periods = [input_field.text() for input_field in self.period_inputs]

            # Get selected class and institution data
            selected_class = self.class_combo.currentText()
            institution_data = self.get_institution_data()

            # حساب تاريخ بداية الأسبوع (الاثنين) بناءً على اختيار الأسبوع
            selected_date = self.get_week_start_date()

            # التحقق مما إذا كان المستخدم قد اختار "جميع الأقسام المسندة"
            if selected_class == "جميع الأقسام المسندة":
                # طباعة جميع الأقسام في ملف واحد
                # جمع جميع الأقسام من مربع التحرير والسرد (تخطي العنصر الأول "جميع الأقسام المسندة")
                classes_to_print = [self.class_combo.itemText(i) for i in range(1, self.class_combo.count())]

                if not classes_to_print:
                    QMessageBox.warning(self, "تنبيه", "لا توجد أقسام مسندة للطباعة.")
                    return

                # إنشاء اسم الملف لجميع الأقسام مع التاريخ والوقت
                current_datetime = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
                file_name = f"تقرير_الغياب_النصف_الثاني_جميع_الأقسام_{current_datetime}.pdf"

                # إنشاء مجلد رئيسي على سطح المكتب
                desktop_path = os.path.join(os.path.expanduser("~"), "Desktop")
                main_reports_dir = os.path.join(desktop_path, "تقارير برنامج المعين في الحراسة العامة")
                if not os.path.exists(main_reports_dir):
                    os.makedirs(main_reports_dir)
                    print(f"تم إنشاء المجلد الرئيسي: {main_reports_dir}")

                # إنشاء مجلد فرعي لأوراق الغياب الأسبوعية
                reports_dir = os.path.join(main_reports_dir, "أوراق الغياب الأسبوعية")
                if not os.path.exists(reports_dir):
                    os.makedirs(reports_dir)
                    print(f"تم إنشاء المجلد الفرعي: {reports_dir}")

                # تحديد مسار الملف الكامل
                file_path = os.path.join(reports_dir, file_name)

                # إعداد بيانات كل قسم
                class_data_list = []
                for class_name in classes_to_print:
                    # الحصول على بيانات الطلاب
                    students = self.get_student_data(class_name)

                    # إضافة البيانات لقائمة الأقسام
                    class_data_list.append({
                        'class_name': class_name,
                        'students': students
                    })

                # إنشاء التقرير بكل الأقسام في ملف واحد
                success = self.split_report_generator.generate_second_half_multi_class(
                    file_path,
                    class_data_list,
                    institution_data,
                    selected_date,
                    1,  # نوع التقرير
                    periods  # إرسال قيم الحصص كمعامل إضافي
                )

                # إغلاق نافذة التحميل
                if hasattr(self, 'loading_dialog') and self.loading_dialog.isVisible():
                    self.loading_dialog.close()

                if success:
                    # فتح الملف بعد الإنشاء
                    self.open_pdf_file(file_path)
                else:
                    QMessageBox.warning(self, "تنبيه", "حدث خطأ أثناء إنشاء التقرير")

                return

            # في حالة اختيار قسم محدد (وليس "جميع الأقسام المسندة")
            # إنشاء اسم الملف مع التاريخ والوقت
            current_datetime = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
            file_name = f"تقرير_الغياب_النصف_الثاني_{selected_class}_{current_datetime}.pdf"

            # إنشاء مجلد رئيسي على سطح المكتب
            desktop_path = os.path.join(os.path.expanduser("~"), "Desktop")
            main_reports_dir = os.path.join(desktop_path, "تقارير برنامج المعين في الحراسة العامة")
            if not os.path.exists(main_reports_dir):
                os.makedirs(main_reports_dir)
                print(f"تم إنشاء المجلد الرئيسي: {main_reports_dir}")

            # إنشاء مجلد فرعي لأوراق الغياب الأسبوعية
            reports_dir = os.path.join(main_reports_dir, "أوراق الغياب الأسبوعية")
            if not os.path.exists(reports_dir):
                os.makedirs(reports_dir)
                print(f"تم إنشاء المجلد الفرعي: {reports_dir}")

            # تحديد مسار الملف الكامل
            file_path = os.path.join(reports_dir, file_name)

            # الحصول على بيانات الطلاب
            students = self.get_student_data(selected_class)

            # استخدام كائن معالج التقارير لإنشاء ملف PDF
            success = self.split_report_generator.generate_second_half(
                file_path,
                students,
                selected_class,
                institution_data,
                selected_date,
                1,  # نوع التقرير
                periods  # إرسال قيم الحصص كمعامل إضافي
            )

            # إغلاق نافذة التحميل
            if hasattr(self, 'loading_dialog') and self.loading_dialog.isVisible():
                self.loading_dialog.close()

            if success:
                # فتح الملف بعد الإنشاء
                self.open_pdf_file(file_path)
            else:
                QMessageBox.warning(self, "تنبيه", "حدث خطأ أثناء إنشاء التقرير")

        except Exception as e:
            # إغلاق نافذة التحميل في حالة حدوث خطأ
            if hasattr(self, 'loading_dialog') and self.loading_dialog.isVisible():
                self.loading_dialog.close()
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء إنشاء تقرير النصف الثاني: {str(e)}")

    def get_week_start_date(self):
        """حساب تاريخ بداية الأسبوع (الاثنين) بناءً على اختيار الأسبوع"""
        today = datetime.date.today()

        # حساب عدد الأيام حتى الاثنين القادم (0 = الاثنين، 1 = الثلاثاء، ..., 6 = الأحد)
        days_until_monday = (0 - today.weekday()) % 7
        if days_until_monday == 0:  # إذا كان اليوم هو الاثنين
            next_monday = today
        else:
            next_monday = today + datetime.timedelta(days=days_until_monday)

        # تحديد تاريخ البداية بناءً على اختيار الأسبوع
        selected_week = self.week_combo.currentText()
        if selected_week == "الأسبوع الحالي":
            # إذا كان اليوم هو الاثنين أو بعده، استخدم الاثنين الماضي
            if today.weekday() >= 0:  # 0 = الاثنين
                days_since_monday = today.weekday()
                start_date = today - datetime.timedelta(days=days_since_monday)
            else:
                # هذا لن يحدث أبداً لأن weekday() يعطي دائماً قيمة بين 0 و 6
                start_date = today
        elif selected_week == "الأسبوع القادم":
            start_date = next_monday
        elif selected_week == "الأسبوع الثالث":
            start_date = next_monday + datetime.timedelta(days=7)
        else:
            # استخدم الأسبوع الحالي كقيمة افتراضية
            days_since_monday = today.weekday()
            start_date = today - datetime.timedelta(days=days_since_monday)

        return start_date

    def get_institution_data(self):
        """الحصول على بيانات المؤسسة من قاعدة البيانات"""
        try:
            # التحقق من وجود اتصال بقاعدة البيانات
            if self.db is None:
                print("خطأ: لا يوجد اتصال بقاعدة البيانات في get_institution_data")
                return {
                    "institution": "المؤسسة التعليمية",
                    "academy": "",
                    "directorate": "",
                    "academic_year": "",
                    "ImagePath1": ""
                }

            # استخدام اتصال قاعدة البيانات المخزن
            cursor = self.db.cursor()

            # استعلام للحصول على بيانات المؤسسة
            # التحقق من أسماء الأعمدة في جدول بيانات_المؤسسة
            cursor.execute("PRAGMA table_info(بيانات_المؤسسة)")
            columns = cursor.fetchall()
            print(f"أعمدة جدول بيانات_المؤسسة: {columns}")

            # استخدام استعلام أكثر أماناً
            try:
                cursor.execute("SELECT * FROM بيانات_المؤسسة LIMIT 1")
                result = cursor.fetchone()
                column_names = [description[0] for description in cursor.description]
                print(f"أسماء الأعمدة في جدول بيانات_المؤسسة: {column_names}")

                # البحث عن الأعمدة المطلوبة
                institution_name = ""
                academy = ""
                direction = ""
                academic_year = ""
                image_path1 = ""

                # البحث عن عمود المؤسسة
                institution_columns = [col for col in column_names if 'المؤسسة' == col]
                if institution_columns:
                    institution_index = column_names.index(institution_columns[0])
                    institution_name = result[institution_index] if result else ""
                else:
                    # البحث عن عمود اسم المؤسسة كبديل
                    name_columns = [col for col in column_names if 'اسم' in col.lower() and 'مؤسسة' in col.lower()]
                    if name_columns:
                        name_index = column_names.index(name_columns[0])
                        institution_name = result[name_index] if result else ""

                # البحث عن عمود الأكاديمية
                academy_columns = [col for col in column_names if 'أكاديمية' in col.lower() or 'اكاديمية' in col.lower()]
                if academy_columns:
                    academy_index = column_names.index(academy_columns[0])
                    academy = result[academy_index] if result else ""

                # البحث عن عمود المديرية
                direction_columns = [col for col in column_names if 'مديرية' in col.lower() or 'اقليم' in col.lower()]
                if direction_columns:
                    direction_index = column_names.index(direction_columns[0])
                    direction = result[direction_index] if result else ""

                # البحث عن عمود السنة الدراسية
                year_columns = [col for col in column_names if 'سنة' in col.lower() and 'دراسية' in col.lower()]
                if year_columns:
                    year_index = column_names.index(year_columns[0])
                    academic_year = result[year_index] if result else ""

                # البحث عن عمود مسار الشعار
                if 'ImagePath1' in column_names:
                    image_index = column_names.index('ImagePath1')
                    image_path1 = result[image_index] if result else ""

                # إنشاء نتيجة مخصصة
                result = (institution_name, academy, direction, academic_year, image_path1)
                print(f"تم استخراج بيانات المؤسسة: {result}")
            except Exception as e:
                print(f"خطأ في استعلام بيانات المؤسسة: {e}")
                result = None

            if result:
                return {
                    "institution": result[0] if result[0] else "المؤسسة التعليمية",
                    "academy": result[1] if result[1] else "",
                    "directorate": result[2] if result[2] else "",
                    "academic_year": result[3] if result[3] else "",
                    "ImagePath1": result[4] if len(result) > 4 and result[4] else ""
                }
            else:
                return {
                    "institution": "المؤسسة التعليمية",
                    "academy": "",
                    "directorate": "",
                    "academic_year": "",
                    "ImagePath1": ""
                }

        except Exception as e:
            print(f"خطأ في الحصول على بيانات المؤسسة: {str(e)}")
            return {
                "institution": "المؤسسة التعليمية",
                "academy": "",
                "directorate": "",
                "academic_year": "",
                "ImagePath1": ""
            }

    def on_supervision_changed(self, index):
        """معالجة تغيير قيمة مربع الحراسة"""
        print(f"تم تغيير رقم الحراسة إلى: {index}")

        # إذا كان المؤشر -1 (لا يوجد اختيار)، لا تقم بتحميل الأقسام
        if index == -1:
            print("لم يتم تحديد رقم حراسة. تخطي تحميل الأقسام المسندة.")
            return

        # حساب رقم الحراسة الفعلي (1-5)
        supervision_number = index + 1
        print(f"رقم الحراسة الفعلي: {supervision_number}")

        # التحقق من وجود class_combo قبل محاولة تحميل الأقسام المسندة
        if hasattr(self, 'class_combo'):
            print(f"class_combo موجود. عدد العناصر الحالية: {self.class_combo.count()}")

            # طباعة العناصر الحالية في مربع التحرير والسرد
            for i in range(self.class_combo.count()):
                print(f"العنصر الحالي {i}: {self.class_combo.itemText(i)}")

            # استدعاء دالة تحميل الأقسام المسندة
            print("جاري استدعاء دالة load_assigned_classes...")
            self.load_assigned_classes()

            # التحقق من نتيجة تحميل الأقسام المسندة
            print(f"تم الانتهاء من تحميل الأقسام المسندة. عدد العناصر الآن: {self.class_combo.count()}")

            # طباعة العناصر بعد التحميل
            for i in range(self.class_combo.count()):
                print(f"العنصر بعد التحميل {i}: {self.class_combo.itemText(i)}")

            # تحديث واجهة المستخدم
            QApplication.processEvents()
        else:
            print("تحذير: لم يتم العثور على class_combo. تخطي تحميل الأقسام المسندة.")

    def load_assigned_classes(self):
        """تحميل الأقسام المسندة من قاعدة البيانات حسب رقم الحراسة"""
        try:
            # التحقق من وجود supervision_combo و class_combo
            if not hasattr(self, 'supervision_combo') or not hasattr(self, 'class_combo'):
                print("تحذير: لم يتم العثور على supervision_combo أو class_combo. تخطي تحميل الأقسام المسندة.")
                return

            # الحصول على رقم الحراسة المحدد (1-5)
            supervision_index = self.supervision_combo.currentIndex()

            # التحقق من أن المؤشر صالح
            if supervision_index == -1:
                print("تحذير: لم يتم تحديد رقم حراسة. تخطي تحميل الأقسام المسندة.")
                return

            supervision_number = supervision_index + 1  # تحويل المؤشر إلى رقم الحراسة (1-5)

            print(f"جاري تحميل الأقسام المسندة للحراسة رقم {supervision_number}...")

            # مسح القائمة الحالية
            self.class_combo.blockSignals(True)  # منع إرسال إشارات أثناء التحديث
            self.class_combo.clear()

            # إضافة خيار "جميع الأقسام المسندة" كأول عنصر
            self.class_combo.addItem("جميع الأقسام المسندة")

            # التحقق من وجود اتصال بقاعدة البيانات
            if self.db is None:
                print("خطأ: لا يوجد اتصال بقاعدة البيانات")
                # محاولة إنشاء اتصال جديد
                try:
                    self.db = sqlite3.connect("data.db")
                    print("تم إنشاء اتصال جديد بقاعدة البيانات")
                except Exception as e:
                    print(f"فشل إنشاء اتصال جديد بقاعدة البيانات: {e}")
                    self.class_combo.blockSignals(False)  # إعادة تفعيل الإشارات
                    return

            # استخدام اتصال قاعدة البيانات المخزن
            try:
                cursor = self.db.cursor()

                # التحقق من وجود جدول البنية_التربوية
                cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='البنية_التربوية'")
                if not cursor.fetchone():
                    print("خطأ: جدول 'البنية_التربوية' غير موجود في قاعدة البيانات")
                    # طباعة قائمة الجداول الموجودة للتشخيص
                    cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
                    tables = cursor.fetchall()
                    print(f"الجداول الموجودة في قاعدة البيانات: {tables}")
                    self.class_combo.blockSignals(False)  # إعادة تفعيل الإشارات
                    return

                # التحقق من وجود عمود السنة_الدراسية في جدول البنية_التربوية
                cursor.execute("PRAGMA table_info(البنية_التربوية)")
                structure_columns = cursor.fetchall()
                print(f"أعمدة جدول البنية_التربوية: {structure_columns}")

                has_year_column = any(col[1] == 'السنة_الدراسية' for col in structure_columns)
                has_assigned_column = any(col[1] == 'الأقسام_المسندة' for col in structure_columns)

                # استعلام للحصول على الأقسام المسندة حسب رقم الحراسة
                if has_year_column and has_assigned_column:
                    # استعلام كامل مع شرط السنة الدراسية
                    query = """
                    SELECT DISTINCT القسم FROM البنية_التربوية
                    WHERE السنة_الدراسية = (SELECT السنة_الدراسية FROM بيانات_المؤسسة LIMIT 1)
                    AND الأقسام_المسندة LIKE ?
                    ORDER BY القسم
                    """
                elif has_assigned_column:
                    # استعلام بدون شرط السنة الدراسية
                    query = """
                    SELECT DISTINCT القسم FROM البنية_التربوية
                    WHERE الأقسام_المسندة LIKE ?
                    ORDER BY القسم
                    """
                else:
                    # استعلام بسيط للحصول على جميع الأقسام
                    query = """
                    SELECT DISTINCT القسم FROM البنية_التربوية
                    ORDER BY القسم
                    """

                search_param = f"%{supervision_number}%"
                print(f"تنفيذ استعلام: {query} مع القيمة {search_param}")

                if "LIKE ?" in query:
                    cursor.execute(query, (search_param,))
                else:
                    cursor.execute(query)

                classes = cursor.fetchall()

                # طباعة نتائج الاستعلام للتشخيص
                print(f"نتائج الاستعلام: {classes}")

                # إذا لم يتم العثور على أي أقسام، جرب استعلامًا بديلاً
                if not classes:
                    print("لم يتم العثور على أقسام. جاري تجربة استعلام بديل...")

                    # استعلام بديل - الحصول على جميع الأقسام من البنية التربوية
                    try:
                        # استعلام بسيط للحصول على جميع الأقسام
                        alt_query = "SELECT DISTINCT القسم FROM البنية_التربوية ORDER BY القسم"
                        print(f"تنفيذ استعلام بديل: {alt_query}")
                        cursor.execute(alt_query)
                        all_classes = cursor.fetchall()
                        print(f"جميع الأقسام في البنية التربوية: {all_classes}")

                        # محاولة التحقق من قيم الأقسام المسندة
                        try:
                            # التحقق من وجود عمود الأقسام_المسندة
                            if has_assigned_column:
                                cursor.execute("SELECT القسم, الأقسام_المسندة FROM البنية_التربوية LIMIT 10")
                                sample_assignments = cursor.fetchall()
                                print(f"عينة من الأقسام وقيم الأقسام المسندة: {sample_assignments}")
                        except Exception as e:
                            print(f"خطأ في استعلام عينة الأقسام المسندة: {e}")
                    except Exception as e:
                        print(f"خطأ في الاستعلام البديل: {e}")
                        all_classes = []

                    # استخدام جميع الأقسام إذا كانت متوفرة
                    if all_classes:
                        classes = all_classes
                        print("تم استخدام جميع الأقسام من البنية التربوية للسنة الدراسية الحالية")
            except Exception as e:
                print(f"خطأ في تنفيذ استعلام قاعدة البيانات: {e}")
                self.class_combo.blockSignals(False)  # إعادة تفعيل الإشارات
                return

            # إضافة الأقسام إلى القائمة المنسدلة
            added_count = 0
            for class_tuple in classes:
                class_name = class_tuple[0]
                if class_name:  # التأكد من أن اسم القسم ليس فارغاً
                    self.class_combo.addItem(class_name)
                    added_count += 1
                    print(f"تمت إضافة القسم: {class_name}")

            # طباعة عدد الأقسام التي تم تحميلها
            print(f"تم تحميل {len(classes)} قسم مسند للحراسة رقم {supervision_number}")
            print(f"تمت إضافة {added_count} قسم إلى مربع التحرير والسرد")

            # التحقق من عدد العناصر في مربع التحرير والسرد
            total_items = self.class_combo.count()
            print(f"إجمالي العناصر في مربع التحرير والسرد: {total_items}")

            # طباعة جميع العناصر في مربع التحرير والسرد للتشخيص
            for i in range(total_items):
                print(f"العنصر {i}: {self.class_combo.itemText(i)}")

            # تحديد العنصر الأول افتراضياً
            if self.class_combo.count() > 0:
                self.class_combo.setCurrentIndex(0)
                print(f"تم تحديد العنصر الأول: {self.class_combo.currentText()}")

            # إضافة أقسام افتراضية للاختبار إذا لم يتم العثور على أي أقسام
            if added_count == 0:
                print("لم يتم العثور على أي أقسام. إضافة أقسام افتراضية للاختبار...")
                default_classes = ["1/1", "1/2", "1/3", "2/1", "2/2"]
                for default_class in default_classes:
                    self.class_combo.addItem(default_class)
                    print(f"تمت إضافة قسم افتراضي: {default_class}")
                print(f"تمت إضافة {len(default_classes)} قسم افتراضي للاختبار")

            self.class_combo.blockSignals(False)  # إعادة تفعيل الإشارات

            # تحديث واجهة المستخدم
            QApplication.processEvents()

        except Exception as e:
            print(f"خطأ في تحميل الأقسام المسندة: {str(e)}")
            # عرض رسالة خطأ للمستخدم
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء تحميل الأقسام المسندة:\n{str(e)}")

            # إضافة أقسام افتراضية في حالة حدوث خطأ
            print("إضافة أقسام افتراضية بسبب حدوث خطأ...")
            self.class_combo.clear()
            self.class_combo.addItem("جميع الأقسام المسندة")
            default_classes = ["1/1", "1/2", "1/3", "2/1", "2/2"]
            for default_class in default_classes:
                self.class_combo.addItem(default_class)

            self.class_combo.blockSignals(False)  # إعادة تفعيل الإشارات

    def get_student_data(self, class_name):
        """الحصول على بيانات الطلاب لقسم معين من قاعدة البيانات"""
        try:
            # التحقق من وجود اتصال بقاعدة البيانات
            if self.db is None:
                print(f"خطأ: لا يوجد اتصال بقاعدة البيانات في get_student_data للقسم {class_name}")
                return []

            # استخدام اتصال قاعدة البيانات المخزن
            cursor = self.db.cursor()

            # التحقق من أعمدة جدول السجل_العام
            cursor.execute("PRAGMA table_info(السجل_العام)")
            student_columns = cursor.fetchall()
            print(f"أعمدة جدول السجل_العام: {student_columns}")

            # التحقق من أعمدة جدول اللوائح
            cursor.execute("PRAGMA table_info(اللوائح)")
            list_columns = cursor.fetchall()
            print(f"أعمدة جدول اللوائح: {list_columns}")

            # استخدام استعلام أكثر أماناً
            try:
                # التحقق من وجود عمود رت في جدول السجل_العام
                has_rt_column = any(col[1] == 'رت' for col in student_columns)

                # الحصول على السنة الدراسية الحالية من جدول بيانات_المؤسسة
                cursor.execute("SELECT السنة_الدراسية FROM بيانات_المؤسسة LIMIT 1")
                academic_year_result = cursor.fetchone()
                academic_year = academic_year_result[0] if academic_year_result else ""

                print(f"السنة الدراسية الحالية: {academic_year}")

                if has_rt_column:
                    query = """
                        SELECT s.الرمز, s.الاسم_والنسب, s.رت
                        FROM السجل_العام s
                        JOIN اللوائح l ON s.الرمز = l.الرمز
                        WHERE l.القسم = ? AND l.السنة_الدراسية = ?
                        ORDER BY s.الاسم_والنسب
                    """
                else:
                    # استعلام بديل بدون عمود رت
                    query = """
                        SELECT s.الرمز, s.الاسم_والنسب
                        FROM السجل_العام s
                        JOIN اللوائح l ON s.الرمز = l.الرمز
                        WHERE l.القسم = ? AND l.السنة_الدراسية = ?
                        ORDER BY s.الاسم_والنسب
                    """

                print(f"تنفيذ استعلام الطلاب للقسم: {class_name}, السنة الدراسية: {academic_year}")
                print(f"استعلام الطلاب: {query}")

                cursor.execute(query, (class_name, academic_year))
            except Exception as e:
                print(f"خطأ في استعلام الطلاب: {e}")

                # محاولة استعلام أبسط
                try:
                    # الحصول على السنة الدراسية الحالية من جدول بيانات_المؤسسة
                    cursor.execute("SELECT السنة_الدراسية FROM بيانات_المؤسسة LIMIT 1")
                    academic_year_result = cursor.fetchone()
                    academic_year = academic_year_result[0] if academic_year_result else ""

                    print(f"محاولة استعلام أبسط مع السنة الدراسية: {academic_year}")

                    # محاولة استعلام مع جدول اللوائح فقط
                    query = """
                        SELECT الرمز, '' as الاسم_والنسب
                        FROM اللوائح
                        WHERE القسم = ? AND السنة_الدراسية = ?
                        LIMIT 30
                    """
                    print(f"محاولة استعلام أبسط: {query}")
                    cursor.execute(query, (class_name, academic_year))
                except Exception as e2:
                    print(f"خطأ في الاستعلام الأبسط: {e2}")
                    # محاولة استعلام بدون شرط السنة الدراسية كملاذ أخير
                    try:
                        query = "SELECT الرمز, الاسم_والنسب FROM السجل_العام LIMIT 30"
                        print(f"محاولة استعلام أبسط بدون شرط السنة الدراسية: {query}")
                        cursor.execute(query)
                    except Exception as e3:
                        print(f"خطأ في الاستعلام الأبسط بدون شرط السنة الدراسية: {e3}")
                        return []

            students_data = cursor.fetchall()
            print(f"تم العثور على {len(students_data)} طالب في القسم {class_name}")

            # تحويل البيانات إلى قائمة من القواميس
            students = []
            for i, student_data in enumerate(students_data, 1):
                # التعامل مع الحالات المختلفة لعدد الأعمدة
                if len(student_data) >= 2:
                    code = student_data[0]
                    name = student_data[1]
                    students.append({
                        "id": i,
                        "name": name if name else "",
                        "code": code if code else ""
                    })
                elif len(student_data) == 1:
                    # حالة وجود عمود واحد فقط
                    students.append({
                        "id": i,
                        "name": student_data[0] if student_data[0] else "",
                        "code": ""
                    })

            # إذا لم يتم العثور على أي طلاب، إضافة طلاب افتراضيين للاختبار
            if not students:
                print("لم يتم العثور على أي طلاب. إضافة طلاب افتراضيين للاختبار...")
                for i in range(1, 11):
                    students.append({
                        "id": i,
                        "name": f"طالب افتراضي {i}",
                        "code": f"CODE{i}"
                    })
                print(f"تمت إضافة {len(students)} طالب افتراضي للاختبار")

            return students

        except Exception as e:
            print(f"خطأ في الحصول على بيانات الطلاب: {str(e)}")
            return []

    def open_pdf_file(self, file_path):
        """فتح ملف PDF بعد إنشائه"""
        try:
            # تم إزالة رسالة النجاح لتجنب ظهور نوافذ منبثقة
            print(f"تم إنشاء التقرير بنجاح وحفظه في: {file_path}")

            # فتح الملف باستخدام البرنامج الافتراضي
            if sys.platform == 'win32':
                os.startfile(file_path)
            # تم إزالة الأكواد غير المستخدمة لأنظمة التشغيل الأخرى

        except Exception as e:
            # تم إزالة رسالة التحذير لتجنب ظهور نوافذ منبثقة
            print(f"تم إنشاء التقرير بنجاح ولكن حدث خطأ أثناء فتحه: {str(e)}")

class RegulationsCardWindow(QDialog):
    """نافذة بطاقة اللوائح والإحصائيات للقسم المحدد"""
    def __init__(self, parent=None, db=None, section=None, academic_year=None):
        super().__init__(parent)
        self.db = db
        self.section = section
        self.academic_year = academic_year

        # إعداد النافذة
        self.setWindowTitle(f"بطاقة اللوائح - {section}")
        self.setGeometry(100, 100, 800, 600)
        self.setLayoutDirection(Qt.RightToLeft)
        try:
            self.setWindowIcon(QIcon("01.ico"))
        except Exception as e:
            print(f"خطأ في تحميل أيقونة البرنامج: {e}")

        # تعيين نمط الخط
        self.font_title = QFont("Calibri", 16)
        self.font_title.setBold(True)
        self.font_subtitle = QFont("Calibri", 14)
        self.font_subtitle.setBold(True)
        self.font_normal = QFont("Calibri", 12)
        self.font_normal.setBold(True)

        # تعيين مؤشر اليد للنافذة الرئيسية
        self.setCursor(Qt.ArrowCursor)  # المؤشر الافتراضي للخلفية

        # إنشاء التخطيط الرئيسي
        self.init_ui()

        # تحميل البيانات
        self.load_data()

    def init_ui(self):
        """إنشاء واجهة المستخدم"""
        # التخطيط الرئيسي
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(20, 20, 20, 20)
        main_layout.setSpacing(15)

        # إطار العنوان
        title_frame = QFrame()
        title_frame.setFrameShape(QFrame.StyledPanel)
        title_frame.setStyleSheet("""
            QFrame {
                background-color: #e6f2ff;
                border: 2px solid #0066cc;
                border-radius: 10px;
                padding: 10px;
            }
        """)
        title_layout = QVBoxLayout(title_frame)

        # عنوان النافذة
        title_label = QLabel(f"بطاقة اللوائح للقسم: {self.section}")
        title_label.setFont(self.font_title)
        title_label.setStyleSheet("color: #0066cc;")
        title_label.setAlignment(Qt.AlignCenter)
        title_layout.addWidget(title_label)

        # السنة الدراسية
        year_label = QLabel(f"السنة الدراسية: {self.academic_year}")
        year_label.setFont(self.font_subtitle)
        year_label.setAlignment(Qt.AlignCenter)
        title_layout.addWidget(year_label)

        main_layout.addWidget(title_frame)

        # إطار المعلومات
        info_frame = QFrame()
        info_frame.setFrameShape(QFrame.StyledPanel)
        info_frame.setStyleSheet("""
            QFrame {
                background-color: #f9f9f9;
                border: 1px solid #ddd;
                border-radius: 5px;
                padding: 15px;
            }
        """)
        info_layout = QVBoxLayout(info_frame)

        # إنشاء علامات التبويب
        tab_widget = QTabWidget()
        tab_widget.setFont(self.font_normal)

        # علامة تبويب المعلومات العامة
        general_tab = QWidget()
        general_layout = QVBoxLayout(general_tab)

        # جدول المعلومات العامة
        self.general_table = QTableWidget()
        self.general_table.setColumnCount(2)
        self.general_table.setRowCount(7)  # زيادة عدد الصفوف لإضافة معدل السماح
        self.general_table.setHorizontalHeaderLabels(["البيان", "القيمة"])
        self.general_table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
        self.general_table.verticalHeader().setVisible(False)
        self.general_table.setFont(self.font_normal)
        self.general_table.setEditTriggers(QTableWidget.NoEditTriggers)
        # تعيين مدة ظهور التلميحات إلى 5 ثواني (5000 مللي ثانية)
        self.general_table.setToolTipDuration(5000)
        # تغيير مؤشر الفأرة إلى يد عند المرور فوق الجدول
        self.general_table.setCursor(Qt.PointingHandCursor)
        self.general_table.horizontalHeader().setCursor(Qt.PointingHandCursor)  # تغيير شكل المؤشر إلى يد لرأس الجدول
        self.general_table.verticalHeader().setCursor(Qt.PointingHandCursor)  # تغيير شكل المؤشر إلى يد للفهرس العمودي

        # تعيين خط التلميحات لكل النافذة
        self.setStyleSheet("""
            QToolTip {
                font-family: 'Calibri';
                font-size: 12pt;
                font-weight: bold;
                color: black;
                background-color: #f9f9f9;
                border: 1px solid #ddd;
                border-radius: 4px;
                padding: 4px;
            }
        """)

        # إعداد صفوف الجدول مع إضافة تلميحات
        item_students = QTableWidgetItem("عدد التلاميذ")
        item_students.setToolTip("العدد الإجمالي للتلاميذ في القسم")
        self.general_table.setItem(0, 0, item_students)
        # تغيير مؤشر الفأرة إلى يد عند المرور فوق العنصر
        self.general_table.item(0, 0).setFlags(self.general_table.item(0, 0).flags() | Qt.ItemIsSelectable)

        item_males = QTableWidgetItem("عدد الذكور")
        item_males.setToolTip("عدد التلاميذ الذكور في القسم")
        self.general_table.setItem(1, 0, item_males)
        # تغيير مؤشر الفأرة إلى يد عند المرور فوق العنصر
        self.general_table.item(1, 0).setFlags(self.general_table.item(1, 0).flags() | Qt.ItemIsSelectable)

        item_females = QTableWidgetItem("عدد الإناث")
        item_females.setToolTip("عدد التلميذات الإناث في القسم")
        self.general_table.setItem(2, 0, item_females)
        # تغيير مؤشر الفأرة إلى يد عند المرور فوق العنصر
        self.general_table.item(2, 0).setFlags(self.general_table.item(2, 0).flags() | Qt.ItemIsSelectable)

        item_absences = QTableWidgetItem("معدل الغياب")
        item_absences.setToolTip("مجموع ساعات غياب التلاميذ داخل الأسدس مقسوماً على عدد التلاميذ")
        self.general_table.setItem(3, 0, item_absences)
        # تغيير مؤشر الفأرة إلى يد عند المرور فوق العنصر
        self.general_table.item(3, 0).setFlags(self.general_table.item(3, 0).flags() | Qt.ItemIsSelectable)

        item_lates = QTableWidgetItem("معدل التأخر")
        item_lates.setToolTip("مجموع حالات تأخر التلاميذ عن بداية الحصة الدراسية مقسوماً على عدد التلاميذ")
        self.general_table.setItem(4, 0, item_lates)
        # تغيير مؤشر الفأرة إلى يد عند المرور فوق العنصر
        self.general_table.item(4, 0).setFlags(self.general_table.item(4, 0).flags() | Qt.ItemIsSelectable)

        item_date = QTableWidgetItem("تاريخ آخر تحديث")
        item_date.setToolTip("تاريخ آخر تحديث للبيانات")
        self.general_table.setItem(5, 0, item_date)
        # تغيير مؤشر الفأرة إلى يد عند المرور فوق العنصر
        self.general_table.item(5, 0).setFlags(self.general_table.item(5, 0).flags() | Qt.ItemIsSelectable)

        item_permissions = QTableWidgetItem("معدل السماح")
        item_permissions.setToolTip("مجموع حالات السماح للتلاميذ بالدخول إلى القسم مقسوماً على عدد التلاميذ")
        self.general_table.setItem(6, 0, item_permissions)
        # تغيير مؤشر الفأرة إلى يد عند المرور فوق العنصر
        self.general_table.item(6, 0).setFlags(self.general_table.item(6, 0).flags() | Qt.ItemIsSelectable)

        general_layout.addWidget(self.general_table)
        tab_widget.addTab(general_tab, "معلومات عامة")

        # علامة تبويب قائمة التلاميذ حسب السن
        students_tab = QWidget()
        students_layout = QVBoxLayout(students_tab)

        # جدول التلاميذ المجمع حسب السن
        self.students_by_age_table = QTableWidget()
        self.students_by_age_table.setColumnCount(2)
        self.students_by_age_table.setHorizontalHeaderLabels(["السن", "عدد التلاميذ"])
        self.students_by_age_table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
        self.students_by_age_table.verticalHeader().setVisible(False)
        self.students_by_age_table.setFont(self.font_normal)
        self.students_by_age_table.setEditTriggers(QTableWidget.NoEditTriggers)
        # تعيين مدة ظهور التلميحات إلى 5 ثواني (5000 مللي ثانية)
        self.students_by_age_table.setToolTipDuration(5000)
        # إضافة تلميح للجدول
        self.students_by_age_table.setToolTip("انقر على أي صف لعرض قائمة التلاميذ ذوي هذا السن")
        # تغيير مؤشر الفأرة إلى يد عند المرور فوق الجدول
        self.students_by_age_table.setCursor(Qt.PointingHandCursor)
        self.students_by_age_table.horizontalHeader().setCursor(Qt.PointingHandCursor)  # تغيير شكل المؤشر إلى يد لرأس الجدول
        self.students_by_age_table.verticalHeader().setCursor(Qt.PointingHandCursor)  # تغيير شكل المؤشر إلى يد للفهرس العمودي
        # ربط حدث النقر على صف في الجدول بدالة عرض تفاصيل التلاميذ حسب السن
        self.students_by_age_table.cellClicked.connect(self.show_students_by_age)

        # لن نستخدم جدول تفاصيل التلاميذ في نفس النافذة، سنفتح نافذة جديدة بدلاً من ذلك
        students_layout.addWidget(self.students_by_age_table)
        tab_widget.addTab(students_tab, "قائمة التلاميذ حسب السن")

        # علامة تبويب الإحصائيات
        stats_tab = QWidget()
        stats_layout = QVBoxLayout(stats_tab)

        # جدول الإحصائيات
        self.stats_table = QTableWidget()
        self.stats_table.setColumnCount(4)  # زيادة عدد الأعمدة إلى 4 لإضافة عمود المجموع
        self.stats_table.setRowCount(4)
        self.stats_table.setHorizontalHeaderLabels(["البيان", "العدد", "المجموع", "النسبة المئوية"])
        self.stats_table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
        self.stats_table.verticalHeader().setVisible(False)
        self.stats_table.setFont(self.font_normal)
        self.stats_table.setEditTriggers(QTableWidget.NoEditTriggers)
        # تعيين مدة ظهور التلميحات إلى 5 ثواني (5000 مللي ثانية)
        self.stats_table.setToolTipDuration(5000)
        # تغيير مؤشر الفأرة إلى يد عند المرور فوق الجدول
        self.stats_table.setCursor(Qt.PointingHandCursor)
        self.stats_table.horizontalHeader().setCursor(Qt.PointingHandCursor)  # تغيير شكل المؤشر إلى يد لرأس الجدول
        self.stats_table.verticalHeader().setCursor(Qt.PointingHandCursor)  # تغيير شكل المؤشر إلى يد للفهرس العمودي

        # إعداد صفوف الجدول مع إضافة تلميحات

        item_regular = QTableWidgetItem("الحضور المنتظم")
        item_regular.setToolTip("عدد التلاميذ الذين ليس لديهم أي حالات غياب أو تأخر")
        self.stats_table.setItem(0, 0, item_regular)
        # تغيير مؤشر الفأرة إلى يد عند المرور فوق العنصر
        self.stats_table.item(0, 0).setFlags(self.stats_table.item(0, 0).flags() | Qt.ItemIsSelectable)

        item_absence = QTableWidgetItem("الغياب المتكرر")
        item_absence.setToolTip("عدد التلاميذ الذين لديهم حالة غياب واحدة أو أكثر")
        self.stats_table.setItem(1, 0, item_absence)
        # تغيير مؤشر الفأرة إلى يد عند المرور فوق العنصر
        self.stats_table.item(1, 0).setFlags(self.stats_table.item(1, 0).flags() | Qt.ItemIsSelectable)

        item_late = QTableWidgetItem("التأخر المتكرر")
        item_late.setToolTip("عدد التلاميذ الذين لديهم حالة تأخر واحدة أو أكثر عن بداية الحصة")
        self.stats_table.setItem(2, 0, item_late)
        # تغيير مؤشر الفأرة إلى يد عند المرور فوق العنصر
        self.stats_table.item(2, 0).setFlags(self.stats_table.item(2, 0).flags() | Qt.ItemIsSelectable)

        item_violations = QTableWidgetItem("المخالفات")
        item_violations.setToolTip("عدد التلاميذ الذين لديهم مخالفات مسجلة")
        self.stats_table.setItem(3, 0, item_violations)
        # تغيير مؤشر الفأرة إلى يد عند المرور فوق العنصر
        self.stats_table.item(3, 0).setFlags(self.stats_table.item(3, 0).flags() | Qt.ItemIsSelectable)

        # إضافة صف للسماح المتكرر
        self.stats_table.setRowCount(5)  # زيادة عدد الصفوف
        item_permissions = QTableWidgetItem("السماح المتكرر")
        item_permissions.setToolTip("عدد التلاميذ الذين لديهم حالة سماح واحدة أو أكثر بالدخول")
        self.stats_table.setItem(4, 0, item_permissions)
        # تغيير مؤشر الفأرة إلى يد عند المرور فوق العنصر
        self.stats_table.item(4, 0).setFlags(self.stats_table.item(4, 0).flags() | Qt.ItemIsSelectable)

        stats_layout.addWidget(self.stats_table)
        tab_widget.addTab(stats_tab, "إحصاءات التأخر والغياب")

        # علامة تبويب قائمة التلاميذ حسب مجموع الغياب
        absences_tab = QWidget()
        absences_layout = QVBoxLayout(absences_tab)

        # جدول التلاميذ المجمع حسب مجموع الغياب
        self.students_by_absences_table = QTableWidget()
        self.students_by_absences_table.setColumnCount(2)
        self.students_by_absences_table.setHorizontalHeaderLabels(["مجموع الغياب", "عدد التلاميذ"])
        self.students_by_absences_table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
        self.students_by_absences_table.verticalHeader().setVisible(False)
        self.students_by_absences_table.setFont(self.font_normal)
        self.students_by_absences_table.setEditTriggers(QTableWidget.NoEditTriggers)
        # تعيين مدة ظهور التلميحات إلى 5 ثواني (5000 مللي ثانية)
        self.students_by_absences_table.setToolTipDuration(5000)
        # إضافة تلميح للجدول
        self.students_by_absences_table.setToolTip("انقر على أي صف لعرض قائمة التلاميذ حسب مجموع الغياب")
        # تغيير مؤشر الفأرة إلى يد عند المرور فوق الجدول
        self.students_by_absences_table.setCursor(Qt.PointingHandCursor)
        self.students_by_absences_table.horizontalHeader().setCursor(Qt.PointingHandCursor)  # تغيير شكل المؤشر إلى يد لرأس الجدول
        self.students_by_absences_table.verticalHeader().setCursor(Qt.PointingHandCursor)  # تغيير شكل المؤشر إلى يد للفهرس العمودي
        # ربط حدث النقر على صف في الجدول بدالة عرض تفاصيل التلاميذ حسب مجموع الغياب
        self.students_by_absences_table.cellClicked.connect(self.show_students_by_absences)

        absences_layout.addWidget(self.students_by_absences_table)
        tab_widget.addTab(absences_tab, "قائمة التلاميذ حسب مجموع الغياب")

        info_layout.addWidget(tab_widget)
        main_layout.addWidget(info_frame)

        # أزرار التحكم
        buttons_layout = QHBoxLayout()

        # زر الطباعة
        print_btn = QPushButton("طباعة")
        print_btn.setFont(self.font_normal)
        print_btn.setCursor(Qt.PointingHandCursor)  # تغيير شكل المؤشر إلى يد
        print_btn.setStyleSheet("""
            QPushButton {
                background-color: #4CAF50;
                color: white;
                border-radius: 5px;
                padding: 8px 16px;
                font-weight: bold;
                min-width: 100px;
            }
            QPushButton:hover {
                background-color: #45a049;
            }
        """)
        print_btn.setCursor(Qt.PointingHandCursor)
        print_btn.clicked.connect(self.print_card)

        # زر التحديث
        refresh_btn = QPushButton("تحديث")
        refresh_btn.setFont(self.font_normal)
        refresh_btn.setCursor(Qt.PointingHandCursor)  # تغيير شكل المؤشر إلى يد
        refresh_btn.setStyleSheet("""
            QPushButton {
                background-color: #2196F3;
                color: white;
                border-radius: 5px;
                padding: 8px 16px;
                font-weight: bold;
                min-width: 100px;
            }
            QPushButton:hover {
                background-color: #0b7dda;
            }
        """)
        refresh_btn.setCursor(Qt.PointingHandCursor)
        refresh_btn.clicked.connect(self.load_data)

        # زر الإغلاق
        close_btn = QPushButton("إغلاق")
        close_btn.setFont(self.font_normal)
        close_btn.setCursor(Qt.PointingHandCursor)  # تغيير شكل المؤشر إلى يد
        close_btn.setStyleSheet("""
            QPushButton {
                background-color: #f44336;
                color: white;
                border-radius: 5px;
                padding: 8px 16px;
                font-weight: bold;
                min-width: 100px;
            }
            QPushButton:hover {
                background-color: #d32f2f;
            }
        """)
        close_btn.setCursor(Qt.PointingHandCursor)
        close_btn.clicked.connect(self.close)

        buttons_layout.addWidget(print_btn)
        buttons_layout.addWidget(refresh_btn)
        buttons_layout.addWidget(close_btn)
        buttons_layout.addStretch()

        main_layout.addLayout(buttons_layout)

    def load_data(self):
        """تحميل البيانات من قاعدة البيانات"""
        if not self.section:
            print("لا يمكن تحميل البيانات. تأكد من تحديد القسم.")
            return

        try:
            # تم إزالة رسالة التحميل لتجنب ظهور نوافذ منبثقة
            print(f"جاري تحميل بيانات القسم {self.section}...")

            # الحصول على بيانات التلاميذ في القسم
            students = self.get_students_data()

            # حساب الإحصائيات
            total_students = len(students)
            male_count = sum(1 for student in students if student.get('gender') in ['ذكر', 'ذ'])
            female_count = total_students - male_count

            # تحديث جدول المعلومات العامة
            self.general_table.setItem(0, 1, QTableWidgetItem(str(total_students)))
            self.general_table.setItem(1, 1, QTableWidgetItem(str(male_count)))
            self.general_table.setItem(2, 1, QTableWidgetItem(str(female_count)))

            # حساب معدلات الغياب والتأخر والسماح
            total_absences = sum(int(student.get('absences', 0)) for student in students)
            total_lates = sum(int(student.get('lates', 0)) for student in students)
            total_permissions = sum(int(student.get('permissions', 0)) for student in students)

            absence_rate = total_absences / total_students if total_students > 0 else 0
            late_rate = total_lates / total_students if total_students > 0 else 0
            permission_rate = total_permissions / total_students if total_students > 0 else 0

            # إضافة القيم إلى الجدول
            absence_item = QTableWidgetItem(f"{absence_rate:.2f}")
            absence_item.setToolTip("مجموع ساعات غياب التلاميذ داخل الأسدس مقسوماً على عدد التلاميذ")
            self.general_table.setItem(3, 1, absence_item)

            late_item = QTableWidgetItem(f"{late_rate:.2f}")
            late_item.setToolTip("مجموع حالات تأخر التلاميذ عن بداية الحصة الدراسية مقسوماً على عدد التلاميذ")
            self.general_table.setItem(4, 1, late_item)

            # إضافة صف جديد لمعدل السماح
            if self.general_table.rowCount() < 7:  # التحقق من عدم وجود الصف بالفعل
                self.general_table.setRowCount(7)
                self.general_table.setItem(6, 0, QTableWidgetItem("معدل السماح"))

            permission_item = QTableWidgetItem(f"{permission_rate:.2f}")
            permission_item.setToolTip("مجموع حالات السماح للتلاميذ بالدخول إلى القسم مقسوماً على عدد التلاميذ")
            self.general_table.setItem(6, 1, permission_item)

            # تاريخ آخر تحديث
            current_date = QDate.currentDate().toString("yyyy-MM-dd")
            self.general_table.setItem(5, 1, QTableWidgetItem(current_date))

            # تجميع التلاميذ حسب السن
            students_by_age = {}
            for student in students:
                age = int(student.get('age', 0)) if student.get('age') is not None else 0
                if age not in students_by_age:
                    students_by_age[age] = []
                students_by_age[age].append(student)

            # تحديث جدول التلاميذ حسب السن
            self.students_by_age_table.setRowCount(len(students_by_age))
            self.age_groups = {}  # قاموس لتخزين مجموعات التلاميذ حسب السن

            for row, (age, age_students) in enumerate(sorted(students_by_age.items())):
                # تخزين مجموعة التلاميذ لهذا السن
                self.age_groups[row] = {
                    'age': age,
                    'students': age_students
                }

                # إضافة البيانات إلى جدول التلاميذ حسب السن
                self.students_by_age_table.setItem(row, 0, QTableWidgetItem(str(age)))
                self.students_by_age_table.setItem(row, 1, QTableWidgetItem(str(len(age_students))))

            # تجميع التلاميذ حسب مجموع الغياب
            # تحديد فئات الغياب: أقل من 10، من 10 إلى 20، من 21 إلى 30، أكثر من 30
            absence_ranges = [
                {"min": 0, "max": 9, "label": "أقل من 10"},
                {"min": 10, "max": 20, "label": "من 10 إلى 20"},
                {"min": 21, "max": 30, "label": "من 21 إلى 30"},
                {"min": 31, "max": float('inf'), "label": "أكثر من 30"}
            ]

            # تجميع التلاميذ حسب فئات الغياب
            students_by_absences = {range_info["label"]: [] for range_info in absence_ranges}

            for student in students:
                absences = int(student.get('absences', 0))
                for range_info in absence_ranges:
                    if range_info["min"] <= absences <= range_info["max"]:
                        students_by_absences[range_info["label"]].append(student)
                        break

            # تحديث جدول التلاميذ حسب مجموع الغياب
            self.students_by_absences_table.setRowCount(len(absence_ranges))
            self.absence_groups = {}  # قاموس لتخزين مجموعات التلاميذ حسب فئات الغياب

            for row, range_info in enumerate(absence_ranges):
                label = range_info["label"]
                absence_students = students_by_absences[label]

                # تخزين مجموعة التلاميذ لهذه الفئة
                self.absence_groups[row] = {
                    'label': label,
                    'students': absence_students
                }

                # إضافة البيانات إلى جدول التلاميذ حسب مجموع الغياب
                self.students_by_absences_table.setItem(row, 0, QTableWidgetItem(label))
                self.students_by_absences_table.setItem(row, 1, QTableWidgetItem(str(len(absence_students))))

            # تحديث جدول الإحصائيات
            regular_attendance = sum(1 for student in students if int(student.get('absences', 0)) == 0 and int(student.get('lates', 0)) == 0)
            frequent_absence = sum(1 for student in students if int(student.get('absences', 0)) >= 1)
            frequent_late = sum(1 for student in students if int(student.get('lates', 0)) >= 1)
            frequent_permission = sum(1 for student in students if int(student.get('permissions', 0)) >= 1)
            violations = sum(1 for student in students if int(student.get('violations', 0)) > 0)

            # حساب مجموع حالات الغياب والتأخر والسماح
            total_absences = sum(int(student.get('absences', 0)) for student in students)
            total_lates = sum(int(student.get('lates', 0)) for student in students)
            total_permissions = sum(int(student.get('permissions', 0)) for student in students)
            total_violations = sum(int(student.get('violations', 0)) for student in students)

            # تعديل حجم جدول الإحصائيات إذا لزم الأمر
            if self.stats_table.rowCount() < 5:
                self.stats_table.setRowCount(5)
                self.stats_table.setItem(4, 0, QTableWidgetItem("السماح المتكرر"))

            # تحديث جدول الإحصائيات مع إضافة تلميحات
            # الحضور المنتظم
            regular_item = QTableWidgetItem(str(regular_attendance))
            regular_item.setToolTip("عدد التلاميذ الذين ليس لديهم أي حالات غياب أو تأخر")
            self.stats_table.setItem(0, 1, regular_item)
            # لا يوجد مجموع للحضور المنتظم
            regular_total = QTableWidgetItem("-")
            regular_total.setToolTip("لا ينطبق")
            self.stats_table.setItem(0, 2, regular_total)

            # الغياب المتكرر
            absence_item = QTableWidgetItem(str(frequent_absence))
            absence_item.setToolTip("عدد التلاميذ الذين لديهم حالة غياب واحدة أو أكثر")
            self.stats_table.setItem(1, 1, absence_item)
            # مجموع حالات الغياب
            absence_total = QTableWidgetItem(str(total_absences))
            absence_total.setToolTip("مجموع حالات الغياب لجميع التلاميذ")
            self.stats_table.setItem(1, 2, absence_total)

            # التأخر المتكرر
            late_item = QTableWidgetItem(str(frequent_late))
            late_item.setToolTip("عدد التلاميذ الذين لديهم حالة تأخر واحدة أو أكثر عن بداية الحصة")
            self.stats_table.setItem(2, 1, late_item)
            # مجموع حالات التأخر
            late_total = QTableWidgetItem(str(total_lates))
            late_total.setToolTip("مجموع حالات التأخر لجميع التلاميذ")
            self.stats_table.setItem(2, 2, late_total)

            # المخالفات
            violations_item = QTableWidgetItem(str(violations))
            violations_item.setToolTip("عدد التلاميذ الذين لديهم مخالفات مسجلة")
            self.stats_table.setItem(3, 1, violations_item)
            # مجموع المخالفات
            violations_total = QTableWidgetItem(str(total_violations))
            violations_total.setToolTip("مجموع المخالفات لجميع التلاميذ")
            self.stats_table.setItem(3, 2, violations_total)

            # السماح المتكرر
            permission_item = QTableWidgetItem(str(frequent_permission))
            permission_item.setToolTip("عدد التلاميذ الذين لديهم حالة سماح واحدة أو أكثر بالدخول")
            self.stats_table.setItem(4, 1, permission_item)
            # مجموع حالات السماح
            permission_total = QTableWidgetItem(str(total_permissions))
            permission_total.setToolTip("مجموع حالات السماح لجميع التلاميذ")
            self.stats_table.setItem(4, 2, permission_total)

            # حساب النسب المئوية
            if total_students > 0:
                # الحضور المنتظم
                regular_percent = QTableWidgetItem(f"{regular_attendance / total_students * 100:.2f}%")
                regular_percent.setToolTip("النسبة المئوية للتلاميذ الذين ليس لديهم أي حالات غياب أو تأخر")
                self.stats_table.setItem(0, 3, regular_percent)

                # الغياب المتكرر
                absence_percent = QTableWidgetItem(f"{frequent_absence / total_students * 100:.2f}%")
                absence_percent.setToolTip("النسبة المئوية للتلاميذ الذين لديهم حالة غياب واحدة أو أكثر")
                self.stats_table.setItem(1, 3, absence_percent)

                # التأخر المتكرر
                late_percent = QTableWidgetItem(f"{frequent_late / total_students * 100:.2f}%")
                late_percent.setToolTip("النسبة المئوية للتلاميذ الذين لديهم حالة تأخر واحدة أو أكثر")
                self.stats_table.setItem(2, 3, late_percent)

                # المخالفات
                violations_percent = QTableWidgetItem(f"{violations / total_students * 100:.2f}%")
                violations_percent.setToolTip("النسبة المئوية للتلاميذ الذين لديهم مخالفات مسجلة")
                self.stats_table.setItem(3, 3, violations_percent)

                # السماح المتكرر
                permission_percent = QTableWidgetItem(f"{frequent_permission / total_students * 100:.2f}%")
                permission_percent.setToolTip("النسبة المئوية للتلاميذ الذين لديهم حالة سماح واحدة أو أكثر")
                self.stats_table.setItem(4, 3, permission_percent)
            else:
                for row in range(5):
                    zero_item = QTableWidgetItem("0.00%")
                    zero_item.setToolTip("لا يوجد تلاميذ")
                    self.stats_table.setItem(row, 3, zero_item)

            # تنسيق الجداول
            self.format_tables()

            # تم إزالة رسالة النجاح لتجنب ظهور نوافذ منبثقة
            print(f"تم تحميل بيانات القسم {self.section} بنجاح.")

        except Exception as e:
            print(f"خطأ أثناء تحميل البيانات: {str(e)}")

    def format_tables(self):
        """تنسيق الجداول لتحسين المظهر"""
        # تنسيق جدول المعلومات العامة
        for row in range(self.general_table.rowCount()):
            for col in range(self.general_table.columnCount()):
                item = self.general_table.item(row, col)
                if item:
                    item.setTextAlignment(Qt.AlignCenter)
                    item.setFont(self.font_normal)
                    item.setForeground(QColor(0, 0, 0))  # لون أسود

        # تنسيق جدول التلاميذ حسب السن
        for row in range(self.students_by_age_table.rowCount()):
            for col in range(self.students_by_age_table.columnCount()):
                item = self.students_by_age_table.item(row, col)
                if item:
                    # محاذاة النص للوسط
                    item.setTextAlignment(Qt.AlignCenter)
                    item.setFont(self.font_normal)
                    item.setForeground(QColor(0, 0, 0))  # لون أسود

                    # تلوين الصفوف بالتناوب
                    if row % 2 == 0:
                        item.setBackground(QColor(240, 240, 240))

                    # تغيير لون خلفية عمود السن ليكون مميزاً
                    if col == 0:
                        item.setBackground(QColor(230, 242, 255))  # لون أزرق فاتح
                        item.setForeground(QColor(0, 102, 204))  # لون أزرق غامق للنص

                    # تم نقل تعيين مؤشر الفأرة إلى مستوى الجدول بدلاً من الخلايا الفردية

        # تنسيق جدول التلاميذ حسب مجموع الغياب
        for row in range(self.students_by_absences_table.rowCount()):
            for col in range(self.students_by_absences_table.columnCount()):
                item = self.students_by_absences_table.item(row, col)
                if item:
                    # محاذاة النص للوسط
                    item.setTextAlignment(Qt.AlignCenter)
                    item.setFont(self.font_normal)
                    item.setForeground(QColor(0, 0, 0))  # لون أسود

                    # تلوين الصفوف بالتناوب
                    if row % 2 == 0:
                        item.setBackground(QColor(240, 240, 240))

                    # تغيير لون خلفية عمود فئة الغياب ليكون مميزاً
                    if col == 0:
                        item.setBackground(QColor(255, 240, 230))  # لون برتقالي فاتح
                        item.setForeground(QColor(204, 102, 0))  # لون برتقالي غامق للنص

                    # تم نقل تعيين مؤشر الفأرة إلى مستوى الجدول بدلاً من الخلايا الفردية

        # تنسيق جدول الإحصائيات
        for row in range(self.stats_table.rowCount()):
            for col in range(self.stats_table.columnCount()):
                item = self.stats_table.item(row, col)
                if item:
                    item.setTextAlignment(Qt.AlignCenter)
                    item.setFont(self.font_normal)
                    item.setForeground(QColor(0, 0, 0))  # لون أسود

                    # تمييز عمود المجموع بلون مختلف
                    if col == 3:  # عمود المجموع
                        item.setBackground(QColor(240, 248, 255))  # لون أزرق فاتح جداً
                        # تغيير مؤشر الفأرة إلى يد عند المرور فوق العنصر
                        item.setFlags(item.flags() | Qt.ItemIsSelectable)

    def get_students_data(self):
        """الحصول على بيانات التلاميذ من قاعدة البيانات"""
        students = []

        try:
            # استخدام sqlite3
            conn = sqlite3.connect("data.db")
            cursor = conn.cursor()

            # طباعة معلومات تشخيصية
            print(f"جاري البحث عن بيانات القسم: {self.section}, السنة الدراسية: {self.academic_year}")

            # التحقق من وجود جدول_عام
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='جدول_عام'")
            has_main_table = cursor.fetchone() is not None

            if has_main_table:
                print("تم العثور على جدول_عام")

                # استعلام للحصول على بيانات التلاميذ من جدول_عام
                query = """
                    SELECT
                        الرمز,
                        الاسم_والنسب,
                        رت,
                        النوع,
                        COALESCE(الغياب, 0) as absences,
                        COALESCE(التأخر, 0) as lates,
                        COALESCE(السماح, 0) as permissions,
                        COALESCE(عدد_المخالفات, 0) as violations,
                        الهاتف_الأول,
                        ملاحظات,
                        COALESCE(السن, 0) as age
                    FROM جدول_عام
                    WHERE القسم = ? AND السنة_الدراسية = ?
                    ORDER BY CAST(COALESCE(رت, '0') AS INTEGER)
                """

                # تنفيذ الاستعلام
                cursor.execute(query, (self.section, self.academic_year))
                results = cursor.fetchall()
                print(f"تم العثور على {len(results)} تلميذ في القسم {self.section}")

                # تحويل النتائج إلى قائمة من القواميس
                for row in results:
                    students.append({
                        'code': row[0],
                        'name': row[1],
                        'rt': row[2],
                        'gender': row[3],
                        'absences': row[4],
                        'lates': row[5],
                        'permissions': row[6],
                        'violations': row[7],
                        'phone': row[8],
                        'notes': row[9],
                        'age': row[10] if len(row) > 10 else 0
                    })
            else:
                # التحقق من وجود جدول السجل_العام
                cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='السجل_العام'")
                has_registry_table = cursor.fetchone() is not None

                if has_registry_table:
                    print("تم العثور على جدول السجل_العام")

                    # التحقق من وجود الأعمدة المطلوبة في جدول السجل_العام
                    cursor.execute("PRAGMA table_info(السجل_العام)")
                    columns = [col[1] for col in cursor.fetchall()]
                    print(f"أعمدة جدول السجل_العام: {columns}")

                    # تعديل الاستعلام ليتوافق مع الأعمدة الموجودة فعلياً
                    gender_column = "الجنس" if "الجنس" in columns else "النوع"
                    absences_column = "الغياب" if "الغياب" in columns else "عدد_الغياب"
                    lates_column = "التأخر" if "التأخر" in columns else "عدد_التأخر"

                    # التحقق من وجود جدول المخالفات
                    cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='المخالفات'")
                    has_violations_table = cursor.fetchone() is not None

                    # التحقق من وجود عمود السماح
                    permissions_column = "السماح" if "السماح" in columns else "عدد_السماح"

                    # بناء استعلام ديناميكي بناءً على الأعمدة المتوفرة
                    query = f"""
                        SELECT s.الرمز, s.الاسم_والنسب, s.رت, s.{gender_column},
                               COALESCE(s.{absences_column}, 0) as absences,
                               COALESCE(s.{lates_column}, 0) as lates,
                               COALESCE(s.{permissions_column}, 0) as permissions,
                               COALESCE(s.الهاتف_الأول, '') as phone,
                               COALESCE(s.ملاحظات, '') as notes
                        FROM السجل_العام s
                        JOIN اللوائح l ON s.الرمز = l.الرمز
                        WHERE l.القسم = ? AND l.السنة_الدراسية = ?
                        ORDER BY CAST(COALESCE(s.رت, '0') AS INTEGER)
                    """

                    # تنفيذ الاستعلام
                    cursor.execute(query, (self.section, self.academic_year))
                    results = cursor.fetchall()
                    print(f"تم العثور على {len(results)} تلميذ في القسم {self.section}")

                    # إذا كان جدول المخالفات موجوداً، احصل على عدد المخالفات لكل تلميذ
                    violations_dict = {}
                    if has_violations_table:
                        cursor.execute("""
                            SELECT رمز_التلميذ, COUNT(*)
                            FROM المخالفات
                            WHERE رمز_التلميذ IN (
                                SELECT s.الرمز
                                FROM السجل_العام s
                                JOIN اللوائح l ON s.الرمز = l.الرمز
                                WHERE l.القسم = ? AND l.السنة_الدراسية = ?
                            )
                            GROUP BY رمز_التلميذ
                        """, (self.section, self.academic_year))
                        violations_results = cursor.fetchall()
                        for code, count in violations_results:
                            violations_dict[code] = count

                    # تحويل النتائج إلى قائمة من القواميس
                    for row in results:
                        code = row[0]
                        students.append({
                            'code': code,
                            'name': row[1],
                            'rt': row[2],
                            'gender': row[3],
                            'absences': row[4],
                            'lates': row[5],
                            'permissions': row[6],
                            'violations': violations_dict.get(code, 0),
                            'phone': row[7],
                            'notes': row[8]
                        })

            # الحصول على معلومات زيارات أولياء الأمور
            try:
                cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='زيارة_ولي_الأمر'")
                has_parent_visits_table = cursor.fetchone() is not None

                if has_parent_visits_table:
                    print("تم العثور على جدول زيارة_ولي_الأمر")

                    # الحصول على عدد زيارات أولياء الأمور لكل تلميذ
                    parent_visits = {}
                    cursor.execute("""
                        SELECT الرمز, COUNT(*)
                        FROM زيارة_ولي_الأمر
                        WHERE الرمز IN (
                            SELECT الرمز
                            FROM جدول_عام
                            WHERE القسم = ? AND السنة_الدراسية = ?
                        )
                        GROUP BY الرمز
                    """, (self.section, self.academic_year))
                    visits_results = cursor.fetchall()
                    for code, count in visits_results:
                        parent_visits[code] = count

                    # إضافة عدد الزيارات إلى بيانات التلاميذ
                    for student in students:
                        student['parent_visits'] = parent_visits.get(student['code'], 0)
            except Exception as e:
                print(f"خطأ في الحصول على بيانات زيارات أولياء الأمور: {str(e)}")

            # الحصول على معلومات المؤسسة من جدول بيانات_المؤسسة
            try:
                cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='بيانات_المؤسسة'")
                has_institution_table = cursor.fetchone() is not None

                if has_institution_table:
                    print("تم العثور على جدول بيانات_المؤسسة")

                    # الحصول على معلومات المؤسسة
                    cursor.execute("""
                        SELECT اسم_المؤسسة, الأكاديمية, المديرية
                        FROM بيانات_المؤسسة
                        WHERE السنة_الدراسية = ?
                        LIMIT 1
                    """, (self.academic_year,))
                    institution_result = cursor.fetchone()
                    if institution_result:
                        self.institution_info = {
                            'name': institution_result[0],
                            'academy': institution_result[1],
                            'direction': institution_result[2]
                        }
                        print(f"تم العثور على معلومات المؤسسة: {self.institution_info}")
            except Exception as e:
                print(f"خطأ في الحصول على بيانات المؤسسة: {str(e)}")

            # إغلاق الاتصال
            conn.close()

            # إضافة بيانات تجريبية إذا كانت القائمة فارغة
            if not students:
                print("لم يتم العثور على بيانات. إضافة بيانات تجريبية...")
                for i in range(1, 11):
                    students.append({
                        'code': f"S{i:03d}",
                        'name': f"تلميذ تجريبي {i}",
                        'rt': i,
                        'gender': "ذكر" if i % 2 == 0 else "أنثى",
                        'absences': i % 5,
                        'lates': i % 3,
                        'permissions': i % 4,
                        'violations': i % 2,
                        'phone': f"0600000{i:03d}",
                        'notes': f"ملاحظات للتلميذ رقم {i}",
                        'parent_visits': i % 4,
                        'age': 14 + (i % 4)  # أعمار تجريبية بين 14 و17
                    })

                # إضافة معلومات مؤسسة تجريبية
                self.institution_info = {
                    'name': "المؤسسة التعليمية",
                    'academy': "الأكاديمية الجهوية للتربية والتكوين",
                    'direction': "المديرية الإقليمية"
                }

        except Exception as e:
            print(f"خطأ في الحصول على بيانات التلاميذ: {str(e)}")
            # إضافة بيانات تجريبية في حالة حدوث خطأ
            for i in range(1, 11):
                students.append({
                    'code': f"S{i:03d}",
                    'name': f"تلميذ تجريبي {i}",
                    'rt': i,
                    'gender': "ذكر" if i % 2 == 0 else "أنثى",
                    'absences': i % 5,
                    'lates': i % 3,
                    'permissions': i % 4,
                    'violations': i % 2,
                    'phone': f"0600000{i:03d}",
                    'notes': f"ملاحظات للتلميذ رقم {i}",
                    'age': 14 + (i % 4)  # أعمار تجريبية بين 14 و17
                })

        return students

    def show_students_by_age(self, row, _):
        """عرض تفاصيل التلاميذ حسب السن المحدد في نافذة جديدة"""
        if row < 0 or row >= len(self.age_groups):
            return

        # الحصول على السن المحدد ومجموعة التلاميذ المرتبطة به
        age_group = self.age_groups[row]
        age = age_group['age']
        students_list = age_group['students']

        # إنشاء نافذة جديدة لعرض التلاميذ حسب السن
        students_dialog = QDialog(self)
        students_dialog.setWindowTitle(f"قائمة التلاميذ ذوي السن {age} سنة")
        students_dialog.setGeometry(0, 0, 700, 500)  # تعيين حجم النافذة
        students_dialog.setLayoutDirection(Qt.RightToLeft)

        # تحديد موقع النافذة فوق النافذة الحالية مباشرة
        parent_pos = self.mapToGlobal(self.rect().topLeft())
        students_dialog.move(parent_pos.x() + 50, parent_pos.y() + 50)

        # إنشاء تخطيط للنافذة
        layout = QVBoxLayout(students_dialog)

        # إضافة عنوان
        title_label = QLabel(f"قائمة التلاميذ ذوي السن {age} سنة - عدد التلاميذ: {len(students_list)}")
        title_label.setFont(self.font_subtitle)
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("color: #0066cc; margin-bottom: 10px;")
        layout.addWidget(title_label)

        # إنشاء جدول لعرض التلاميذ
        students_table = QTableWidget()
        students_table.setColumnCount(4)
        students_table.setHorizontalHeaderLabels(["الرمز", "الرقم الترتيبي", "الاسم والنسب", "تاريخ الازدياد"])
        students_table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
        students_table.verticalHeader().setVisible(False)
        students_table.setFont(self.font_normal)
        students_table.setEditTriggers(QTableWidget.NoEditTriggers)

        # ملء الجدول بالتلاميذ من الفئة العمرية المحددة
        students_table.setRowCount(len(students_list))
        for i, student in enumerate(students_list):
            # تحويل القيم إلى نصوص لتجنب الأخطاء
            code = str(student.get('code', '')) if student.get('code') is not None else ''
            rt = str(student.get('rt', '')) if student.get('rt') is not None else ''
            name = str(student.get('name', '')) if student.get('name') is not None else ''

            # الحصول على تاريخ الازدياد
            birth_date = ''
            try:
                # محاولة الحصول على تاريخ الازدياد من قاعدة البيانات
                birth_date_query = QSqlQuery(db=self.db)
                birth_date_query.prepare("SELECT تاريخ_الازدياد FROM جدول_عام WHERE الرمز = ?")
                birth_date_query.addBindValue(code)
                if birth_date_query.exec_() and birth_date_query.next():
                    birth_date = birth_date_query.value(0) or ''
            except Exception as e:
                print(f"خطأ في الحصول على تاريخ الازدياد: {str(e)}")

            # إضافة البيانات إلى الجدول
            students_table.setItem(i, 0, QTableWidgetItem(code))
            students_table.setItem(i, 1, QTableWidgetItem(rt))
            students_table.setItem(i, 2, QTableWidgetItem(name))
            students_table.setItem(i, 3, QTableWidgetItem(birth_date))

        # تنسيق الجدول
        for row in range(students_table.rowCount()):
            for col in range(students_table.columnCount()):
                item = students_table.item(row, col)
                if item:
                    # محاذاة النص للوسط
                    item.setTextAlignment(Qt.AlignCenter)
                    item.setFont(self.font_normal)
                    item.setForeground(QColor(0, 0, 0))  # لون أسود

                    # تلوين الصفوف بالتناوب
                    if row % 2 == 0:
                        item.setBackground(QColor(240, 240, 240))

        layout.addWidget(students_table)

        # إضافة أزرار الطباعة والإغلاق
        buttons_layout = QHBoxLayout()

        # زر طباعة اللائحة
        print_button = QPushButton("طباعة اللائحة")
        print_button.setFont(self.font_normal)
        print_button.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                color: white;
                border-radius: 5px;
                padding: 8px 16px;
                font-weight: bold;
                min-width: 120px;
            }
            QPushButton:hover {
                background-color: #219a52;
            }
        """)
        print_button.setCursor(Qt.PointingHandCursor)
        print_button.clicked.connect(lambda: self.print_age_list(age, students_list))

        # زر إغلاق
        close_button = QPushButton("إغلاق")
        close_button.setFont(self.font_normal)
        close_button.setStyleSheet("""
            QPushButton {
                background-color: #3498db;
                color: white;
                border-radius: 5px;
                padding: 8px 16px;
                font-weight: bold;
                min-width: 100px;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
        """)
        close_button.setCursor(Qt.PointingHandCursor)
        close_button.clicked.connect(students_dialog.accept)

        # إضافة الأزرار إلى تخطيط أفقي
        buttons_layout.addStretch()
        buttons_layout.addWidget(print_button)
        buttons_layout.addSpacing(20)  # مسافة بين الأزرار
        buttons_layout.addWidget(close_button)
        buttons_layout.addStretch()
        layout.addLayout(buttons_layout)

        # عرض النافذة كنافذة حوار مشروطة
        students_dialog.exec_()

    def show_students_by_absences(self, row, _):
        """عرض تفاصيل التلاميذ حسب مجموع الغياب المحدد في نافذة جديدة"""
        if row < 0 or row >= len(self.absence_groups):
            return

        # الحصول على فئة الغياب المحددة ومجموعة التلاميذ المرتبطة بها
        absence_group = self.absence_groups[row]
        absence_label = absence_group['label']
        students_list = absence_group['students']

        # إنشاء نافذة جديدة لعرض التلاميذ حسب مجموع الغياب
        students_dialog = QDialog(self)
        students_dialog.setWindowTitle(f"قائمة التلاميذ بمجموع غياب {absence_label}")
        students_dialog.setGeometry(0, 0, 700, 500)  # تعيين حجم النافذة
        students_dialog.setLayoutDirection(Qt.RightToLeft)

        # تحديد موقع النافذة فوق النافذة الحالية مباشرة
        parent_pos = self.mapToGlobal(self.rect().topLeft())
        students_dialog.move(parent_pos.x() + 50, parent_pos.y() + 50)

        # إنشاء تخطيط للنافذة
        layout = QVBoxLayout(students_dialog)

        # إضافة عنوان
        title_label = QLabel(f"قائمة التلاميذ بمجموع غياب {absence_label} - عدد التلاميذ: {len(students_list)}")
        title_label.setFont(self.font_subtitle)
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("color: #cc6600; margin-bottom: 10px;")
        layout.addWidget(title_label)

        # إنشاء جدول لعرض التلاميذ
        students_table = QTableWidget()
        students_table.setColumnCount(4)
        students_table.setHorizontalHeaderLabels(["الرمز", "الرقم الترتيبي", "الاسم والنسب", "مجموع الغياب"])
        students_table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
        students_table.verticalHeader().setVisible(False)
        students_table.setFont(self.font_normal)
        students_table.setEditTriggers(QTableWidget.NoEditTriggers)

        # ملء الجدول بالتلاميذ من فئة الغياب المحددة
        students_table.setRowCount(len(students_list))
        for i, student in enumerate(students_list):
            # تحويل القيم إلى نصوص لتجنب الأخطاء
            code = str(student.get('code', '')) if student.get('code') is not None else ''
            rt = str(student.get('rt', '')) if student.get('rt') is not None else ''
            name = str(student.get('name', '')) if student.get('name') is not None else ''
            absences = str(student.get('absences', '0')) if student.get('absences') is not None else '0'

            # إضافة البيانات إلى الجدول
            students_table.setItem(i, 0, QTableWidgetItem(code))
            students_table.setItem(i, 1, QTableWidgetItem(rt))
            students_table.setItem(i, 2, QTableWidgetItem(name))
            students_table.setItem(i, 3, QTableWidgetItem(absences))

        # تنسيق الجدول
        for row in range(students_table.rowCount()):
            for col in range(students_table.columnCount()):
                item = students_table.item(row, col)
                if item:
                    # محاذاة النص للوسط
                    item.setTextAlignment(Qt.AlignCenter)
                    item.setFont(self.font_normal)
                    item.setForeground(QColor(0, 0, 0))  # لون أسود

                    # تلوين الصفوف بالتناوب
                    if row % 2 == 0:
                        item.setBackground(QColor(240, 240, 240))

                    # تمييز عمود مجموع الغياب بلون مختلف
                    if col == 3:
                        absences_value = int(item.text()) if item.text().isdigit() else 0
                        if absences_value > 20:
                            item.setForeground(QColor(204, 0, 0))  # أحمر للغياب الكثير
                        elif absences_value > 10:
                            item.setForeground(QColor(204, 102, 0))  # برتقالي للغياب المتوسط
                        else:
                            item.setForeground(QColor(0, 153, 0))  # أخضر للغياب القليل

        layout.addWidget(students_table)

        # إضافة أزرار الطباعة والإغلاق
        buttons_layout = QHBoxLayout()

        # زر طباعة اللائحة
        print_button = QPushButton("طباعة اللائحة")
        print_button.setFont(self.font_normal)
        print_button.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                color: white;
                border-radius: 5px;
                padding: 8px 16px;
                font-weight: bold;
                min-width: 120px;
            }
            QPushButton:hover {
                background-color: #219a52;
            }
        """)
        print_button.setCursor(Qt.PointingHandCursor)
        print_button.clicked.connect(lambda: self.print_absences_list(absence_label, students_list))

        # زر إغلاق
        close_button = QPushButton("إغلاق")
        close_button.setFont(self.font_normal)
        close_button.setStyleSheet("""
            QPushButton {
                background-color: #3498db;
                color: white;
                border-radius: 5px;
                padding: 8px 16px;
                font-weight: bold;
                min-width: 100px;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
        """)
        close_button.setCursor(Qt.PointingHandCursor)
        close_button.clicked.connect(students_dialog.accept)

        # إضافة الأزرار إلى تخطيط أفقي
        buttons_layout.addStretch()
        buttons_layout.addWidget(print_button)
        buttons_layout.addSpacing(20)  # مسافة بين الأزرار
        buttons_layout.addWidget(close_button)
        buttons_layout.addStretch()
        layout.addLayout(buttons_layout)

        # عرض النافذة كنافذة حوار مشروطة
        students_dialog.exec_()

    def print_age_list(self, age, students_list):
        """طباعة لائحة التلاميذ حسب السن"""
        try:
            # إنشاء اسم الملف
            file_name = f"لائحة_التلاميذ_{age}_سنة.pdf"
            file_path, _ = QFileDialog.getSaveFileName(
                self, "حفظ لائحة التلاميذ", file_name, "ملفات PDF (*.pdf)"
            )

            if not file_path:
                return  # تم إلغاء العملية

            if not file_path.endswith('.pdf'):
                file_path += '.pdf'

            # استخدام مكتبة reportlab لإنشاء ملف PDF
            from reportlab.lib.pagesizes import A4
            from reportlab.lib import colors as reportlab_colors
            from reportlab.platypus import SimpleDocTemplate, Table, TableStyle, Image, Spacer
            from reportlab.lib.units import cm
            from reportlab.pdfbase import pdfmetrics
            from reportlab.pdfbase.ttfonts import TTFont

            # محاولة استيراد مكتبات دعم اللغة العربية
            try:
                import arabic_reshaper
                from bidi.algorithm import get_display
                arabic_support = True
            except ImportError:
                print("لم يتم العثور على مكتبات دعم اللغة العربية، سيتم استخدام الطريقة البديلة")
                arabic_support = False

            # دالة لإصلاح النص العربي
            def fix_arabic(text):
                if not text or not arabic_support:
                    return text
                try:
                    reshaped_text = arabic_reshaper.reshape(text)
                    return get_display(reshaped_text)
                except:
                    return text

            # تسجيل الخط العربي
            try:
                pdfmetrics.registerFont(TTFont('Arabic', 'arial.ttf'))
                font_name = 'Arabic'
            except:
                try:
                    pdfmetrics.registerFont(TTFont('Calibri', 'calibri.ttf'))
                    font_name = 'Calibri'
                except:
                    print("لم يتم العثور على خط عربي، سيتم استخدام الخط الافتراضي")
                    font_name = 'Helvetica'

            # الحصول على معلومات المؤسسة
            institution_name = ""
            logo_path = ""
            try:
                conn = sqlite3.connect("data.db")
                cursor = conn.cursor()

                # التحقق من وجود عمود المؤسسة
                cursor.execute("PRAGMA table_info(بيانات_المؤسسة)")
                columns = [col[1] for col in cursor.fetchall()]

                # تحديد اسم العمود الصحيح للمؤسسة
                institution_column = "المؤسسة"
                if "اسم_المؤسسة" in columns:
                    institution_column = "اسم_المؤسسة"

                # الحصول على اسم المؤسسة ومسار الشعار
                query = f"SELECT {institution_column}, ImagePath1 FROM بيانات_المؤسسة LIMIT 1"
                cursor.execute(query)
                result = cursor.fetchone()

                if result:
                    institution_name = result[0] or "المؤسسة التعليمية"
                    logo_path = result[1] if len(result) > 1 and result[1] else ""

                conn.close()
            except Exception as e:
                print(f"خطأ في الحصول على معلومات المؤسسة: {str(e)}")
                institution_name = "المؤسسة التعليمية"
                logo_path = ""

            # تحديد اللون الأزرق الغامق
            dark_blue = reportlab_colors.Color(0, 0.35, 0.6)

            # إنشاء مستند PDF (عمودي) مع هوامش 0.2 سم
            doc = SimpleDocTemplate(
                file_path,
                pagesize=A4,
                rightMargin=0.2*cm,
                leftMargin=0.2*cm,
                topMargin=0.2*cm,
                bottomMargin=0.2*cm
            )

            # قائمة العناصر التي سيتم إضافتها للمستند
            elements = []

            # استخدام مسار الشعار من قاعدة البيانات أو استخدام المسار الافتراضي
            if not logo_path:
                logo_path = "logo.png"  # المسار الافتراضي للشعار

            # إضافة الشعار في وسط الصفحة
            try:
                import os
                # التحقق من وجود ملف الشعار
                if logo_path and os.path.exists(logo_path):
                    # إنشاء صورة الشعار بالأبعاد المطلوبة (200×90)
                    logo = Image(logo_path, width=200, height=90)
                    logo.hAlign = 'CENTER'  # محاذاة الشعار للوسط
                    elements.append(logo)
                else:
                    print(f"لم يتم العثور على ملف الشعار في المسار: {logo_path}")
            except Exception as e:
                print(f"خطأ في تحميل ملف الشعار: {str(e)}")

            # إضافة مسافة بعد الشعار (0.9 سم)
            elements.append(Spacer(1, 0.9*cm))

            # إنشاء نمط للنصوص
            from reportlab.lib.styles import ParagraphStyle
            from reportlab.platypus import Paragraph

            # نمط اسم المؤسسة (Calibri 17 أزرق غامق)
            institution_style = ParagraphStyle(
                'InstitutionStyle',
                fontName=font_name,
                fontSize=17,
                alignment=1,  # وسط
                textColor=dark_blue
            )

            # نمط العنوان الرئيسي (Calibri 16 أزرق غامق)
            title_style = ParagraphStyle(
                'TitleStyle',
                fontName=font_name,
                fontSize=16,
                alignment=1,  # وسط
                textColor=dark_blue
            )

            # نمط العنوان الفرعي (Calibri 14 أزرق غامق)
            subtitle_style = ParagraphStyle(
                'SubtitleStyle',
                fontName=font_name,
                fontSize=14,
                alignment=1,  # وسط
                textColor=dark_blue
            )

            # إضافة اسم المؤسسة
            institution_paragraph = Paragraph(fix_arabic(institution_name), institution_style)
            elements.append(institution_paragraph)

            # إضافة مسافة بعد اسم المؤسسة (0.9 سم)
            elements.append(Spacer(1, 0.9*cm))

            # عنوان التقرير
            title_text = f"قائمة التلاميذ ذوي السن {age} سنة"
            subtitle_text = f"القسم: {self.section} | السنة الدراسية: {self.academic_year} | عدد التلاميذ: {len(students_list)}"

            # إضافة العنوان الرئيسي
            title_paragraph = Paragraph(fix_arabic(title_text), title_style)
            elements.append(title_paragraph)

            # إضافة مسافة بعد العنوان الرئيسي (0.9 سم)
            elements.append(Spacer(1, 0.9*cm))

            # إضافة العنوان الفرعي
            subtitle_paragraph = Paragraph(fix_arabic(subtitle_text), subtitle_style)
            elements.append(subtitle_paragraph)

            # إضافة مسافة قبل الجدول الرئيسي (0.9 سم)
            elements.append(Spacer(1, 0.9*cm))

            # إعداد بيانات الجدول الرئيسي
            data = []

            # إضافة رؤوس الأعمدة
            headers = ["تاريخ الازدياد", "الاسم والنسب", "الرقم الترتيبي", "الرمز"]
            data.append([fix_arabic(header) for header in headers])

            # إضافة بيانات التلاميذ
            for student in students_list:
                code = str(student.get('code', '')) if student.get('code') is not None else ''
                rt = str(student.get('rt', '')) if student.get('rt') is not None else ''
                name = str(student.get('name', '')) if student.get('name') is not None else ''

                # الحصول على تاريخ الازدياد
                birth_date = ''
                try:
                    # محاولة الحصول على تاريخ الازدياد من قاعدة البيانات
                    birth_date_query = QSqlQuery(db=self.db)
                    birth_date_query.prepare("SELECT تاريخ_الازدياد FROM جدول_عام WHERE الرمز = ?")
                    birth_date_query.addBindValue(code)
                    if birth_date_query.exec_() and birth_date_query.next():
                        birth_date = birth_date_query.value(0) or ''
                except Exception as e:
                    print(f"خطأ في الحصول على تاريخ الازدياد: {str(e)}")

                # إضافة الصف مع إصلاح النص العربي
                row_data = [
                    birth_date,  # تاريخ الازدياد
                    fix_arabic(name),  # الاسم والنسب
                    rt,    # الرقم الترتيبي
                    code   # الرمز
                ]
                data.append(row_data)

            # إنشاء الجدول الرئيسي
            table = Table(data, colWidths=[4*cm, 7*cm, 3*cm, 3.5*cm])

            # تنسيق الجدول
            table_style = TableStyle([
                # تنسيق رؤوس الأعمدة
                ('BACKGROUND', (0, 0), (-1, 0), reportlab_colors.lightblue),
                ('TEXTCOLOR', (0, 0), (-1, 0), reportlab_colors.black),
                ('ALIGN', (0, 0), (-1, 0), 'CENTER'),
                ('FONTNAME', (0, 0), (-1, 0), font_name),
                ('FONTSIZE', (0, 0), (-1, 0), 13),
                ('BOTTOMPADDING', (0, 0), (-1, 0), 12),

                # تنسيق بيانات الجدول
                ('BACKGROUND', (0, 1), (-1, -1), reportlab_colors.white),
                ('GRID', (0, 0), (-1, -1), 1, reportlab_colors.black),
                ('FONTNAME', (0, 1), (-1, -1), font_name),
                ('FONTSIZE', (0, 1), (-1, -1), 12),
                ('ALIGN', (0, 0), (-1, -1), 'CENTER'),  # محاذاة كل الخلايا للوسط
                ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
                ('RIGHTPADDING', (0, 0), (-1, -1), 10),
                ('LEFTPADDING', (0, 0), (-1, -1), 10),
                ('ROWHEIGHT', (0, 0), (-1, -1), 30),
            ])

            # تلوين الصفوف بالتناوب
            for row in range(1, len(data)):
                if row % 2 == 0:
                    table_style.add('BACKGROUND', (0, row), (-1, row), reportlab_colors.lightgrey)

            table.setStyle(table_style)
            elements.append(table)

            # إضافة مسافة قبل توقيع الحراسة العامة
            elements.append(Spacer(1, 1*cm))

            # إضافة توقيع الحراسة العامة وتاريخ الطبع
            import datetime
            current_date = datetime.datetime.now().strftime("%Y-%m-%d")

            # نمط توقيع الحراسة العامة
            signature_style = ParagraphStyle(
                'SignatureStyle',
                fontName=font_name,
                fontSize=12,
                alignment=1,  # وسط
                textColor=dark_blue
            )

            # إضافة توقيع الحراسة العامة
            signature_text = "الحراسة العامة"
            signature_paragraph = Paragraph(fix_arabic(signature_text), signature_style)
            elements.append(signature_paragraph)

            # إضافة مسافة قبل تاريخ الطبع
            elements.append(Spacer(1, 0.5*cm))

            # إضافة تاريخ الطبع
            date_text = f"تاريخ الطبع: {current_date}"
            date_paragraph = Paragraph(fix_arabic(date_text), signature_style)
            elements.append(date_paragraph)

            # إنشاء دالة لتكرار العناوين في الصفحات الإضافية
            def add_header_footer(canvas, doc):
                canvas.saveState()
                # إضافة العناوين في الصفحات الإضافية (بدءًا من الصفحة الثانية)
                if doc.page > 1:
                    # إضافة العنوان الرئيسي
                    canvas.setFont(font_name, 16)
                    canvas.setFillColor(dark_blue)
                    canvas.drawCentredString(doc.width/2 + doc.leftMargin, doc.height - 1*cm, fix_arabic(title_text))

                    # إضافة العنوان الفرعي
                    canvas.setFont(font_name, 14)
                    canvas.drawCentredString(doc.width/2 + doc.leftMargin, doc.height - 1.5*cm, fix_arabic(subtitle_text))
                canvas.restoreState()

            # بناء المستند مع تكرار العناوين في الصفحات الإضافية
            doc.build(elements, onFirstPage=add_header_footer, onLaterPages=add_header_footer)

            # فتح الملف بعد الإنشاء
            import os
            import platform
            import subprocess

            if platform.system() == 'Windows':
                os.startfile(file_path)
            elif platform.system() == 'Darwin':  # macOS
                subprocess.call(('open', file_path))
            else:  # Linux
                subprocess.call(('xdg-open', file_path))

            print(f"تم إنشاء لائحة التلاميذ ذوي السن {age} سنة بنجاح")

        except Exception as e:
            print(f"خطأ أثناء طباعة لائحة التلاميذ: {str(e)}")

    def print_absences_list(self, absence_label, students_list):
        """طباعة لائحة التلاميذ حسب مجموع الغياب"""
        try:
            # إنشاء اسم الملف
            file_name = f"لائحة_التلاميذ_بمجموع_غياب_{absence_label.replace(' ', '_')}.pdf"
            file_path, _ = QFileDialog.getSaveFileName(
                self, "حفظ لائحة التلاميذ", file_name, "ملفات PDF (*.pdf)"
            )

            if not file_path:
                return  # تم إلغاء العملية

            if not file_path.endswith('.pdf'):
                file_path += '.pdf'

            # استخدام مكتبة reportlab لإنشاء ملف PDF
            from reportlab.lib.pagesizes import A4
            from reportlab.lib import colors as reportlab_colors
            from reportlab.platypus import SimpleDocTemplate, Table, TableStyle, Image, Spacer
            from reportlab.lib.units import cm
            from reportlab.pdfbase import pdfmetrics
            from reportlab.pdfbase.ttfonts import TTFont

            # محاولة استيراد مكتبات دعم اللغة العربية
            try:
                import arabic_reshaper
                from bidi.algorithm import get_display
                arabic_support = True
            except ImportError:
                print("لم يتم العثور على مكتبات دعم اللغة العربية، سيتم استخدام الطريقة البديلة")
                arabic_support = False

            # دالة لإصلاح النص العربي
            def fix_arabic(text):
                if not text or not arabic_support:
                    return text
                try:
                    reshaped_text = arabic_reshaper.reshape(text)
                    return get_display(reshaped_text)
                except:
                    return text

            # تسجيل الخط العربي
            try:
                pdfmetrics.registerFont(TTFont('Arabic', 'arial.ttf'))
                font_name = 'Arabic'
            except:
                try:
                    pdfmetrics.registerFont(TTFont('Calibri', 'calibri.ttf'))
                    font_name = 'Calibri'
                except:
                    print("لم يتم العثور على خط عربي، سيتم استخدام الخط الافتراضي")
                    font_name = 'Helvetica'

            # الحصول على معلومات المؤسسة
            institution_name = ""
            logo_path = ""
            try:
                conn = sqlite3.connect("data.db")
                cursor = conn.cursor()

                # التحقق من وجود عمود المؤسسة
                cursor.execute("PRAGMA table_info(بيانات_المؤسسة)")
                columns = [col[1] for col in cursor.fetchall()]

                # تحديد اسم العمود الصحيح للمؤسسة
                institution_column = "المؤسسة"
                if "اسم_المؤسسة" in columns:
                    institution_column = "اسم_المؤسسة"

                # الحصول على اسم المؤسسة ومسار الشعار
                query = f"SELECT {institution_column}, ImagePath1 FROM بيانات_المؤسسة LIMIT 1"
                cursor.execute(query)
                result = cursor.fetchone()

                if result:
                    institution_name = result[0] or "المؤسسة التعليمية"
                    logo_path = result[1] if len(result) > 1 and result[1] else ""

                conn.close()
            except Exception as e:
                print(f"خطأ في الحصول على معلومات المؤسسة: {str(e)}")
                institution_name = "المؤسسة التعليمية"
                logo_path = ""

            # تحديد اللون الأزرق الغامق
            dark_blue = reportlab_colors.Color(0, 0.35, 0.6)

            # إنشاء مستند PDF (عمودي) مع هوامش 0.2 سم
            doc = SimpleDocTemplate(
                file_path,
                pagesize=A4,
                rightMargin=0.2*cm,
                leftMargin=0.2*cm,
                topMargin=0.2*cm,
                bottomMargin=0.2*cm
            )

            # قائمة العناصر التي سيتم إضافتها للمستند
            elements = []

            # استخدام مسار الشعار من قاعدة البيانات أو استخدام المسار الافتراضي
            if not logo_path:
                logo_path = "logo.png"  # المسار الافتراضي للشعار

            # إضافة الشعار في وسط الصفحة
            try:
                import os
                # التحقق من وجود ملف الشعار
                if logo_path and os.path.exists(logo_path):
                    # إنشاء صورة الشعار بالأبعاد المطلوبة (200×90)
                    logo = Image(logo_path, width=200, height=90)
                    logo.hAlign = 'CENTER'  # محاذاة الشعار للوسط
                    elements.append(logo)
                else:
                    print(f"لم يتم العثور على ملف الشعار في المسار: {logo_path}")
            except Exception as e:
                print(f"خطأ في تحميل ملف الشعار: {str(e)}")

            # إضافة مسافة بعد الشعار (0.9 سم)
            elements.append(Spacer(1, 0.9*cm))

            # إنشاء نمط للنصوص
            from reportlab.lib.styles import ParagraphStyle
            from reportlab.platypus import Paragraph

            # نمط اسم المؤسسة (Calibri 17 أزرق غامق)
            institution_style = ParagraphStyle(
                'InstitutionStyle',
                fontName=font_name,
                fontSize=17,
                alignment=1,  # وسط
                textColor=dark_blue
            )

            # نمط العنوان الرئيسي (Calibri 16 أزرق غامق)
            title_style = ParagraphStyle(
                'TitleStyle',
                fontName=font_name,
                fontSize=16,
                alignment=1,  # وسط
                textColor=dark_blue
            )

            # نمط العنوان الفرعي (Calibri 14 أزرق غامق)
            subtitle_style = ParagraphStyle(
                'SubtitleStyle',
                fontName=font_name,
                fontSize=14,
                alignment=1,  # وسط
                textColor=dark_blue
            )

            # إضافة اسم المؤسسة
            institution_paragraph = Paragraph(fix_arabic(institution_name), institution_style)
            elements.append(institution_paragraph)

            # إضافة مسافة بعد اسم المؤسسة (0.9 سم)
            elements.append(Spacer(1, 0.9*cm))

            # عنوان التقرير
            title_text = f"قائمة التلاميذ بمجموع غياب {absence_label}"
            subtitle_text = f"القسم: {self.section} | السنة الدراسية: {self.academic_year} | عدد التلاميذ: {len(students_list)}"

            # إضافة العنوان الرئيسي
            title_paragraph = Paragraph(fix_arabic(title_text), title_style)
            elements.append(title_paragraph)

            # إضافة مسافة بعد العنوان الرئيسي (0.9 سم)
            elements.append(Spacer(1, 0.9*cm))

            # إضافة العنوان الفرعي
            subtitle_paragraph = Paragraph(fix_arabic(subtitle_text), subtitle_style)
            elements.append(subtitle_paragraph)

            # إضافة مسافة قبل الجدول الرئيسي (0.9 سم)
            elements.append(Spacer(1, 0.9*cm))

            # إعداد بيانات الجدول الرئيسي
            data = []

            # إضافة رؤوس الأعمدة
            headers = ["مجموع الغياب", "الاسم والنسب", "الرقم الترتيبي", "الرمز"]
            data.append([fix_arabic(header) for header in headers])

            # إضافة بيانات التلاميذ
            for student in students_list:
                code = str(student.get('code', '')) if student.get('code') is not None else ''
                rt = str(student.get('rt', '')) if student.get('rt') is not None else ''
                name = str(student.get('name', '')) if student.get('name') is not None else ''
                absences = str(student.get('absences', '0')) if student.get('absences') is not None else '0'

                # إضافة الصف مع إصلاح النص العربي
                row_data = [
                    absences,  # مجموع الغياب
                    fix_arabic(name),  # الاسم والنسب
                    rt,    # الرقم الترتيبي
                    code   # الرمز
                ]
                data.append(row_data)

            # إنشاء الجدول الرئيسي
            table = Table(data, colWidths=[3*cm, 8*cm, 3*cm, 3.5*cm])

            # تنسيق الجدول
            table_style = TableStyle([
                # تنسيق رؤوس الأعمدة
                ('BACKGROUND', (0, 0), (-1, 0), reportlab_colors.lightblue),
                ('TEXTCOLOR', (0, 0), (-1, 0), reportlab_colors.black),
                ('ALIGN', (0, 0), (-1, 0), 'CENTER'),
                ('FONTNAME', (0, 0), (-1, 0), font_name),
                ('FONTSIZE', (0, 0), (-1, 0), 13),
                ('BOTTOMPADDING', (0, 0), (-1, 0), 12),

                # تنسيق بيانات الجدول
                ('BACKGROUND', (0, 1), (-1, -1), reportlab_colors.white),
                ('GRID', (0, 0), (-1, -1), 1, reportlab_colors.black),
                ('FONTNAME', (0, 1), (-1, -1), font_name),
                ('FONTSIZE', (0, 1), (-1, -1), 12),
                ('ALIGN', (0, 0), (-1, -1), 'CENTER'),  # محاذاة كل الخلايا للوسط
                ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
                ('RIGHTPADDING', (0, 0), (-1, -1), 10),
                ('LEFTPADDING', (0, 0), (-1, -1), 10),
                ('ROWHEIGHT', (0, 0), (-1, -1), 30),
            ])

            # تلوين الصفوف بالتناوب
            for row in range(1, len(data)):
                if row % 2 == 0:
                    table_style.add('BACKGROUND', (0, row), (-1, row), reportlab_colors.lightgrey)

                # تلوين خلية مجموع الغياب حسب القيمة
                absences_value = int(data[row][0]) if data[row][0].isdigit() else 0
                if absences_value > 20:
                    table_style.add('TEXTCOLOR', (0, row), (0, row), reportlab_colors.red)
                elif absences_value > 10:
                    table_style.add('TEXTCOLOR', (0, row), (0, row), reportlab_colors.orange)

            table.setStyle(table_style)
            elements.append(table)

            # إضافة مسافة قبل توقيع الحراسة العامة
            elements.append(Spacer(1, 1*cm))

            # إضافة توقيع الحراسة العامة وتاريخ الطبع
            import datetime
            current_date = datetime.datetime.now().strftime("%Y-%m-%d")

            # نمط توقيع الحراسة العامة
            signature_style = ParagraphStyle(
                'SignatureStyle',
                fontName=font_name,
                fontSize=12,
                alignment=1,  # وسط
                textColor=dark_blue
            )

            # إضافة توقيع الحراسة العامة
            signature_text = "الحراسة العامة"
            signature_paragraph = Paragraph(fix_arabic(signature_text), signature_style)
            elements.append(signature_paragraph)

            # إضافة مسافة قبل تاريخ الطبع
            elements.append(Spacer(1, 0.5*cm))

            # إضافة تاريخ الطبع
            date_text = f"تاريخ الطبع: {current_date}"
            date_paragraph = Paragraph(fix_arabic(date_text), signature_style)
            elements.append(date_paragraph)

            # إنشاء دالة لتكرار العناوين في الصفحات الإضافية
            def add_header_footer(canvas, doc):
                canvas.saveState()
                # إضافة العناوين في الصفحات الإضافية (بدءًا من الصفحة الثانية)
                if doc.page > 1:
                    # إضافة العنوان الرئيسي
                    canvas.setFont(font_name, 16)
                    canvas.setFillColor(dark_blue)
                    canvas.drawCentredString(doc.width/2 + doc.leftMargin, doc.height - 1*cm, fix_arabic(title_text))

                    # إضافة العنوان الفرعي
                    canvas.setFont(font_name, 14)
                    canvas.drawCentredString(doc.width/2 + doc.leftMargin, doc.height - 1.5*cm, fix_arabic(subtitle_text))
                canvas.restoreState()

            # بناء المستند مع تكرار العناوين في الصفحات الإضافية
            doc.build(elements, onFirstPage=add_header_footer, onLaterPages=add_header_footer)

            # فتح الملف بعد الإنشاء
            import os
            import platform
            import subprocess

            if platform.system() == 'Windows':
                os.startfile(file_path)
            elif platform.system() == 'Darwin':  # macOS
                subprocess.call(('open', file_path))
            else:  # Linux
                subprocess.call(('xdg-open', file_path))

            print(f"تم إنشاء لائحة التلاميذ بمجموع غياب {absence_label} بنجاح")

        except Exception as e:
            print(f"خطأ أثناء طباعة لائحة التلاميذ: {str(e)}")

    def print_card(self):
        """طباعة بطاقة اللوائح"""
        # تم إزالة رسالة الطباعة لتجنب ظهور نوافذ منبثقة
        print("سيتم تنفيذ وظيفة طباعة بطاقة اللوائح قريباً.")
        # هنا يمكن إضافة كود لطباعة البطاقة في المستقبل


if __name__ == "__main__":
    # عند تشغيل الملف مباشرة، نقوم بإنشاء تطبيق PyQt وعرض النافذة
    app = QApplication(sys.argv)
    app.setLayoutDirection(Qt.RightToLeft)  # Set RTL for the entire application

    # طباعة رسالة توضيحية
    print("جاري تشغيل نافذة طباعة اللوائح كنافذة مستقلة...")

    # إنشاء اتصال بقاعدة البيانات
    try:
        db = sqlite3.connect("data.db")
        print("تم الاتصال بقاعدة البيانات بنجاح")

        # الحصول على السنة الدراسية الحالية
        cursor = db.cursor()
        cursor.execute("SELECT السنة_الدراسية FROM بيانات_المؤسسة LIMIT 1")
        result = cursor.fetchone()
        academic_year = result[0] if result else ""
        print(f"السنة الدراسية الحالية: {academic_year}")

        # إنشاء النافذة وعرضها
        window = PrintListsWindow(db=db, academic_year=academic_year)
        window.show()

        # تشغيل حلقة الأحداث
        sys.exit(app.exec_())
    except Exception as e:
        print(f"خطأ في الاتصال بقاعدة البيانات: {e}")

        # إنشاء النافذة بدون اتصال بقاعدة البيانات
        window = PrintListsWindow()
        window.show()

        # تشغيل حلقة الأحداث
        sys.exit(app.exec_())
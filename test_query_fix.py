#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sqlite3

def run_test_query_fix():
    """
    دالة لاختبار الاستعلام المستخدم في YearlyAbsenceSummaryWindow وتصحيحه
    """
    try:
        # الاتصال بقاعدة البيانات
        conn = sqlite3.connect("data.db")
        cur = conn.cursor()

        print("=== اختبار الاستعلام المستخدم في YearlyAbsenceSummaryWindow ===")

        # الحصول على السنة الدراسية الحالية
        cur.execute("SELECT السنة_الدراسية FROM بيانات_المؤسسة LIMIT 1")
        school_year = cur.fetchone()[0]
        print(f"السنة الدراسية الحالية: {school_year}")

        # القسم والشهر المحددين
        section = "TCLSH-1"
        month = "شتنبر"

        print(f"\n=== اختبار الاستعلام للقسم {section} والشهر {month} ===")

        # 1. الاستعلام المستخدم في YearlyAbsenceSummaryWindow (محاكاة)
        # هذا استعلام افتراضي بناءً على الرسائل التشخيصية
        yearly_query = """
            SELECT s.الرمز, s.الاسم_والنسب, m."1", m."2", m."3", m."4", m."5", m.مجموع_شهري
            FROM مجموع_الغياب_السنوي m
            JOIN السجل_العام s ON m.رمز_التلميذ = s.الرمز
            JOIN اللوائح l ON s.الرمز = l.الرمز
            WHERE l.القسم = ? AND m.الشهر = ? AND m.السنة_الدراسية = ?
        """

        print("الاستعلام المستخدم في YearlyAbsenceSummaryWindow:")
        print(yearly_query)

        # تنفيذ الاستعلام
        cur.execute(yearly_query, (section, month, school_year))
        yearly_results = cur.fetchall()
        print(f"عدد النتائج: {len(yearly_results)}")

        # التحقق من وجود الطالب H142106728 في النتائج
        found = False
        for result in yearly_results:
            if result[0] == "H142106728":
                found = True
                print(f"تم العثور على الطالب H142106728 في النتائج: {result}")
                break

        if not found:
            print("لم يتم العثور على الطالب H142106728 في نتائج الاستعلام")

        # 2. استعلام مباشر من جدول مسك_الغياب_الأسبوعي
        print("\n=== استعلام مباشر من جدول مسك_الغياب_الأسبوعي ===")
        direct_query = """
            SELECT رمز_التلميذ, "1", "2", "3", "4", "5", مجموع_شهري
            FROM مسك_الغياب_الأسبوعي
            WHERE رمز_التلميذ = 'H142106728' AND الشهر = ? AND السنة_الدراسية = ?
        """

        print("الاستعلام المباشر:")
        print(direct_query)

        # تنفيذ الاستعلام
        cur.execute(direct_query, (month, school_year))
        direct_results = cur.fetchall()
        print(f"عدد النتائج: {len(direct_results)}")

        if direct_results:
            print(f"نتائج الاستعلام المباشر: {direct_results}")

        # 3. استعلام مقترح للإصلاح
        print("\n=== استعلام مقترح للإصلاح ===")
        fix_query = """
            SELECT l.رت, sg.الاسم_والنسب, l.الرمز,
                   mag."1", mag."2", mag."3", mag."4", mag."5",
                   mag.مجموع_شهري, mag.الغياب_المبرر, mag.ملاحظات
            FROM اللوائح l
            LEFT JOIN مسك_الغياب_الأسبوعي mag ON l.الرمز = mag.رمز_التلميذ
                AND mag.الشهر = ?
                AND mag.السنة_الدراسية = ?
            LEFT JOIN السجل_العام sg ON l.الرمز = sg.الرمز
            WHERE l.القسم = ? AND l.السنة_الدراسية = ?
            ORDER BY CAST(l.رت AS INTEGER)
        """

        print("الاستعلام المقترح للإصلاح:")
        print(fix_query)

        # تنفيذ الاستعلام
        cur.execute(fix_query, (month, school_year, section, school_year))
        fix_results = cur.fetchall()
        print(f"عدد النتائج: {len(fix_results)}")

        # التحقق من وجود الطالب H142106728 في النتائج
        found = False
        for result in fix_results:
            if result[2] == "H142106728":
                found = True
                print(f"تم العثور على الطالب H142106728 في نتائج الاستعلام المقترح: {result}")
                break

        if not found:
            print("لم يتم العثور على الطالب H142106728 في نتائج الاستعلام المقترح")

        # إغلاق الاتصال بقاعدة البيانات
        conn.close()
        print("\nتم إغلاق الاتصال بقاعدة البيانات")

    except Exception as e:
        print(f"خطأ: {e}")
        import traceback
        traceback.print_exc()
        if 'conn' in locals() and conn:
            conn.close()

if __name__ == "__main__":
    run_test_query_fix()

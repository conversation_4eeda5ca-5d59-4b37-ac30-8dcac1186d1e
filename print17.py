import fitz  # PyMuPDF
import tkinter as tk
from tkinter import filedialog, messagebox

def mm_to_pt(mm):
    return mm * 2.8346

def adjust_pdf_margins(input_pdf_path, output_pdf_path, margin_top, margin_bottom, margin_left, margin_right):
    try:
        doc = fitz.open(input_pdf_path)

        mt = mm_to_pt(margin_top)
        mb = mm_to_pt(margin_bottom)
        ml = mm_to_pt(margin_left)
        mr = mm_to_pt(margin_right)

        for page in doc:
            rect = page.rect
            new_rect = fitz.Rect(
                rect.x0 + ml,
                rect.y0 + mt,
                rect.x1 - mr,
                rect.y1 - mb
            )
            page.set_cropbox(new_rect)

        doc.save(output_pdf_path)
        doc.close()
        messagebox.showinfo("نجاح", f"تم حفظ الملف المعدل:\n{output_pdf_path}")
    except Exception as e:
        messagebox.showerror("خطأ", f"حدث خطأ أثناء التعديل:\n{str(e)}")

def browse_file():
    file_path = filedialog.askopenfilename(filetypes=[("PDF Files", "*.pdf")])
    if file_path:
        entry_file.delete(0, tk.END)
        entry_file.insert(0, file_path)

def run_adjustment():
    file_path = entry_file.get()
    if not file_path:
        messagebox.showwarning("تحذير", "الرجاء اختيار ملف PDF.")
        return

    try:
        mt = float(entry_top.get())
        mb = float(entry_bottom.get())
        ml = float(entry_left.get())
        mr = float(entry_right.get())
    except ValueError:
        messagebox.showerror("خطأ", "الرجاء إدخال أرقام صحيحة للهامش.")
        return

    output_path = file_path.replace(".pdf", "_adjusted.pdf")
    adjust_pdf_margins(file_path, output_path, mt, mb, ml, mr)

# إنشاء واجهة المستخدم
root = tk.Tk()
root.title("تعديل هوامش PDF")
root.geometry("400x300")
root.resizable(False, False)

# عناصر الواجهة
tk.Label(root, text="اختر ملف PDF:").pack(pady=5)
entry_file = tk.Entry(root, width=50)
entry_file.pack(pady=5)
tk.Button(root, text="استعراض...", command=browse_file).pack(pady=5)

# الهوامش
frame_margins = tk.Frame(root)
frame_margins.pack(pady=10)

tk.Label(frame_margins, text="الهامش العلوي (مم):").grid(row=0, column=0, sticky='e')
entry_top = tk.Entry(frame_margins, width=10)
entry_top.grid(row=0, column=1)
entry_top.insert(0, "21")

tk.Label(frame_margins, text="الهامش السفلي (مم):").grid(row=1, column=0, sticky='e')
entry_bottom = tk.Entry(frame_margins, width=10)
entry_bottom.grid(row=1, column=1)
entry_bottom.insert(0, "39")

tk.Label(frame_margins, text="الهامش الأيسر (مم):").grid(row=2, column=0, sticky='e')
entry_left = tk.Entry(frame_margins, width=10)
entry_left.grid(row=2, column=1)
entry_left.insert(0, "21")

tk.Label(frame_margins, text="الهامش الأيمن (مم):").grid(row=3, column=0, sticky='e')
entry_right = tk.Entry(frame_margins, width=10)
entry_right.grid(row=3, column=1)
entry_right.insert(0, "27")

# زر تنفيذ التعديل
tk.Button(root, text="تعديل الهوامش", command=run_adjustment, bg="green", fg="white").pack(pady=10)

root.mainloop()

#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
ملف مخصص لطباعة تقرير المخالفة بتنسيق PDF (print5_test.py)
"""

from datetime import datetime
import os
import webbrowser
import sys
import json
import sqlite3
import traceback
import html # لاستخدام html.escape

# استيراد مكتبات معالجة النصوص العربية
try:
    import arabic_reshaper
    from bidi.algorithm import get_display
    ARABIC_SUPPORT = True
    print("تم تحميل مكتبات معالجة النصوص العربية بنجاح (print5_test).")
except ImportError:
    ARABIC_SUPPORT = False
    print("تنبيه (print5_test): مكتبات معالجة النصوص العربية غير متوفرة. قم بتثبيت: pip install arabic-reshaper python-bidi")

# استيراد مكتبات ReportLab لإنشاء ملفات PDF
from reportlab.lib.pagesizes import A4, landscape
from reportlab.lib.units import cm, inch
from reportlab.pdfbase import pdfmetrics
from reportlab.pdfbase.ttfonts import TTFont
from reportlab.pdfgen import canvas
from reportlab.lib import colors
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, Table, TableStyle, Image

# تسجيل الخطوط العربية
def register_arabic_fonts():
    """تسجيل الخطوط العربية لاستخدامها في ReportLab"""
    try:
        # تحديد مسار الخطوط
        font_path = os.path.dirname(os.path.abspath(__file__))

        # تسجيل الخط العربي
        pdfmetrics.registerFont(TTFont('Amiri', os.path.join(font_path, 'Amiri-Regular.ttf')))
        pdfmetrics.registerFont(TTFont('Amiri-Bold', os.path.join(font_path, 'Amiri-Bold.ttf')))

        print("تم تسجيل الخطوط العربية بنجاح")
        return True
    except Exception as e:
        print(f"خطأ في تسجيل الخطوط العربية: {e}")
        traceback.print_exc()
        return False

# تسجيل الخطوط عند استيراد الملف
register_arabic_fonts()

# دالة لإعادة تشكيل النص العربي
def reshape_arabic_text(text):
    """إعادة تشكيل النص العربي للعرض الصحيح في PDF"""
    if not text:
        return ""

    try:
        if ARABIC_SUPPORT:
            reshaped_text = arabic_reshaper.reshape(text)
            bidi_text = get_display(reshaped_text)
            return bidi_text
        else:
            return text
    except Exception as e:
        print(f"خطأ في إعادة تشكيل النص العربي: {e}")
        return text
# --- >> نهاية التعريفات الافتراضية << ---


# --------- وظائف مساعدة ---------

# --- >> تعديل fix_arabic_text لـ HTML (إزالة get_display) << ---
def fix_arabic_text(text):
    """معالجة النص العربي (تشكيل فقط) وتجهيزه لـ HTML."""
    if not text:
        return ""
    text = str(text).strip()
    if not text:
        return ""
    try:
        if ARABIC_SUPPORT:
            # التشكيل فقط، المتصفح سيهتم بالاتجاه
            reshaped = arabic_reshaper.reshape(text)
            # --- >> إزالة bidi.algorithm.get_display << ---
            # bidi_text = get_display(reshaped)
            # return html.escape(bidi_text)
            # --- >> استخدام النص المشكل فقط << ---
            return html.escape(reshaped)
        else:
            return html.escape(text)
    except Exception as e:
        print(f"خطأ في معالجة النص العربي '{text[:20]}...': {e}")
        return html.escape(text)
# --- >> نهاية التعديل << ---

def get_institution_data(db_path="data.db"):
    """استخراج بيانات المؤسسة من قاعدة البيانات"""
    institution_data = {
        "name": "الثانوية التأهيلية النموذجية",
        "logo_path": None,
        "address": "تقرير مخالفة" # عنوان افتراضي لتقرير المخالفة
    }
    if not db_path or not os.path.exists(db_path):
        print(f"تحذير: مسار قاعدة البيانات غير صالح أو غير متوفر: {db_path}")
        return institution_data
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        cursor.execute("SELECT المؤسسة, ImagePath1 FROM بيانات_المؤسسة LIMIT 1")
        result = cursor.fetchone()
        if result:
            institution_data["name"] = result[0] if result[0] else institution_data["name"]
            institution_data["logo_path"] = result[1]
        conn.close()
        print(f"تم استخراج بيانات المؤسسة: {institution_data['name']}")
    except sqlite3.OperationalError as oe:
        print(f"خطأ في استعلام قاعدة البيانات (بيانات المؤسسة): {oe}")
    except Exception as e:
        print(f"خطأ في استخراج بيانات المؤسسة من '{db_path}': {e}")
    return institution_data

def locate_logo_file(db_path=None):
    """البحث عن ملف الشعار وبيانات المؤسسة"""
    institution_data = get_institution_data(db_path)
    logo_path = institution_data["logo_path"]
    school_name = institution_data["name"]
    school_address = institution_data["address"] # العنوان الافتراضي لتقرير المخالفة

    if logo_path and os.path.exists(logo_path):
        print(f"تم العثور على الشعار من قاعدة البيانات: {logo_path}")
        return logo_path, school_name, school_address

    print("تحذير: لم يتم العثور على ملف شعار صالح.")
    return None, school_name, school_address

def save_settings(settings, filename="print_settings_violation.json"): # اسم ملف مختلف
    """حفظ إعدادات طباعة المخالفات"""
    try:
        settings_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), filename)
        with open(settings_path, 'w', encoding='utf-8') as f:
            json.dump(settings, f, ensure_ascii=False, indent=4)
        print(f"تم حفظ إعدادات طباعة المخالفات في: {settings_path}")
        return True
    except Exception as e:
        print(f"خطأ في حفظ إعدادات المخالفات: {e}")
        return False

# --- >> تعديل load_settings لاستخدام أسماء الخطوط الجديدة << ---
def load_settings(filename="print_settings_violation.json"):
    """استعادة إعدادات طباعة المخالفات"""
    # استخدام أسماء الخطوط المسجلة كقيم افتراضية
    global default_font_name, bold_font_name # الآن هذه المتغيرات معرفة عالمياً
    default_settings = {
        "school_name": "الثانوية التأهيلية النموذجية",
        "page_size": "A4", # قد لا يكون مهماً لـ HTML
        "default_font": default_font_name, # <-- استخدام القيمة الافتراضية المعرفة أعلاه
        "font_size": 12,
        "bold_font": bold_font_name     # <-- استخدام القيمة الافتراضية المعرفة أعلاه
    }
    try:
        settings_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), filename)
        if os.path.exists(settings_path):
            with open(settings_path, 'r', encoding='utf-8') as f:
                settings = json.load(f)

            # --- >> إزالة التحقق من تسجيل الخطوط (غير ضروري لـ HTML) << ---
            # loaded_font_name = settings.get('default_font', default_font_name)
            # if loaded_font_name not in pdfmetrics.getRegisteredFontNames() and loaded_font_name not in ['Helvetica', 'Times-Roman', 'Courier']:
            #      print(f"تحذير: الخط الأساسي المحمل '{loaded_font_name}' غير مسجل. العودة إلى '{default_font_name}'.")
            #      settings['default_font'] = default_font_name
            #
            # loaded_bold_font = settings.get('bold_font', bold_font_name)
            # if loaded_bold_font not in pdfmetrics.getRegisteredFontNames() and loaded_bold_font not in ['Helvetica-Bold', 'Times-Bold', 'Courier-Bold']:
            #      print(f"تحذير: الخط الغامق المحمل '{loaded_bold_font}' غير مسجل. العودة إلى '{bold_font_name}'.")
            #      settings['bold_font'] = bold_font_name
            # --- >> نهاية إزالة التحقق << ---

            print(f"تم استعادة إعدادات طباعة المخالفات من: {settings_path}")
            default_settings.update(settings) # دمج الإعدادات المحملة مع الافتراضية
            return default_settings
        else:
            print("لم يتم العثور على ملف إعدادات المخالفات. سيتم استخدام وحفظ الإعدادات الافتراضية.")
            save_settings(default_settings, filename)
            return default_settings
    except Exception as e:
        print(f"خطأ في استعادة إعدادات المخالفات: {e}")
        traceback.print_exc()
        return default_settings
# --- >> نهاية تعديل load_settings << ---

# --- >> وظيفة إنشاء ملف PDF للمخالفات << ---
def create_violation_pdf(data, filename):
    """إنشاء ملف PDF للمخالفات باستخدام ReportLab"""
    try:
        print(f"بدء إنشاء ملف PDF: {filename}")

        # إنشاء مستند PDF
        doc = SimpleDocTemplate(
            filename,
            pagesize=A4,
            rightMargin=1*cm,
            leftMargin=1*cm,
            topMargin=1*cm,
            bottomMargin=1*cm
        )

        # قائمة العناصر التي سيتم إضافتها للمستند
        elements = []

        # إضافة شعار المؤسسة إذا كان متوفرًا
        logo_path = data.get('logo_path')
        if logo_path and os.path.exists(logo_path):
            try:
                # إضافة الشعار في أعلى الصفحة
                logo = Image(logo_path, width=150*0.0264583333*cm, height=80*0.0264583333*cm)
                logo.hAlign = 'CENTER'
                elements.append(logo)
                elements.append(Spacer(1, 0.5*cm))
            except Exception as logo_error:
                print(f"خطأ في إضافة الشعار: {logo_error}")

        # إنشاء نمط للعناوين
        title_style = ParagraphStyle(
            'TitleStyle',
            fontName='Amiri-Bold',
            fontSize=16,
            alignment=1,  # وسط
            textColor=colors.darkblue,
            leading=20
        )

        # إنشاء نمط للنص العادي
        normal_style = ParagraphStyle(
            'NormalStyle',
            fontName='Amiri',
            fontSize=12,
            alignment=2,  # يمين
            leading=16
        )

        # إضافة عنوان التقرير
        title_text = reshape_arabic_text("تقرير المخالفة")
        elements.append(Paragraph(title_text, title_style))
        elements.append(Spacer(1, 0.5*cm))

        # إضافة معلومات المؤسسة
        if 'institution_name' in data:
            institution_text = reshape_arabic_text(f"المؤسسة: {data.get('institution_name', '')}")
            elements.append(Paragraph(institution_text, normal_style))
            elements.append(Spacer(1, 0.2*cm))

        if 'academic_year' in data:
            year_text = reshape_arabic_text(f"السنة الدراسية: {data.get('academic_year', '')}")
            elements.append(Paragraph(year_text, normal_style))
            elements.append(Spacer(1, 0.5*cm))

        # إضافة معلومات التلميذ
        student_data = [
            [reshape_arabic_text("اسم التلميذ"), reshape_arabic_text(data.get('student_name', ''))],
            [reshape_arabic_text("الرمز"), reshape_arabic_text(data.get('student_code', ''))],
            [reshape_arabic_text("المستوى"), reshape_arabic_text(data.get('level', ''))],
            [reshape_arabic_text("القسم"), reshape_arabic_text(data.get('class', ''))]
        ]

        student_table = Table(student_data, colWidths=[4*cm, 12*cm])
        student_table.setStyle(TableStyle([
            ('BACKGROUND', (0, 0), (0, -1), colors.lightgrey),
            ('TEXTCOLOR', (0, 0), (-1, -1), colors.black),
            ('ALIGN', (0, 0), (-1, -1), 'RIGHT'),
            ('FONTNAME', (0, 0), (-1, -1), 'Amiri'),
            ('FONTSIZE', (0, 0), (-1, -1), 12),
            ('BOTTOMPADDING', (0, 0), (-1, -1), 6),
            ('GRID', (0, 0), (-1, -1), 1, colors.black)
        ]))

        elements.append(student_table)
        elements.append(Spacer(1, 0.5*cm))

        # إضافة معلومات المخالفة
        violation_title = reshape_arabic_text("تفاصيل المخالفة")
        elements.append(Paragraph(violation_title, title_style))
        elements.append(Spacer(1, 0.3*cm))

        violation_data = [
            [reshape_arabic_text("تاريخ المخالفة"), reshape_arabic_text(data.get('violation_date', ''))],
            [reshape_arabic_text("نوع المخالفة"), reshape_arabic_text(data.get('violation_type', ''))],
            [reshape_arabic_text("وصف المخالفة"), reshape_arabic_text(data.get('description', ''))],
            [reshape_arabic_text("الإجراء المتخذ"), reshape_arabic_text(data.get('action_taken', ''))]
        ]

        violation_table = Table(violation_data, colWidths=[4*cm, 12*cm])
        violation_table.setStyle(TableStyle([
            ('BACKGROUND', (0, 0), (0, -1), colors.lightgrey),
            ('TEXTCOLOR', (0, 0), (-1, -1), colors.black),
            ('ALIGN', (0, 0), (-1, -1), 'RIGHT'),
            ('FONTNAME', (0, 0), (-1, -1), 'Amiri'),
            ('FONTSIZE', (0, 0), (-1, -1), 12),
            ('BOTTOMPADDING', (0, 0), (-1, -1), 6),
            ('GRID', (0, 0), (-1, -1), 1, colors.black)
        ]))

        elements.append(violation_table)
        elements.append(Spacer(1, 1*cm))

        # إضافة التوقيعات
        signature_data = [
            [reshape_arabic_text("توقيع ولي الأمر"), "", reshape_arabic_text("توقيع الإدارة")]
        ]

        signature_table = Table(signature_data, colWidths=[5*cm, 6*cm, 5*cm])
        signature_table.setStyle(TableStyle([
            ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
            ('FONTNAME', (0, 0), (-1, -1), 'Amiri'),
            ('FONTSIZE', (0, 0), (-1, -1), 12),
            ('BOTTOMPADDING', (0, 0), (-1, -1), 30)
        ]))

        elements.append(signature_table)

        # إضافة تاريخ الطباعة
        current_date = datetime.now().strftime("%Y-%m-%d")
        date_text = reshape_arabic_text(f"تاريخ الطباعة: {current_date}")
        elements.append(Paragraph(date_text, normal_style))

        # بناء المستند
        doc.build(elements)

        print(f"تم إنشاء ملف PDF بنجاح: {filename}")
        return True
    except Exception as e:
        print(f"خطأ في إنشاء ملف PDF: {e}")
        traceback.print_exc()
        return False

# --- >> دالة فتح ملف PDF << ---

# --- >> دالة حفظ وفتح ملف HTML << ---
def save_and_open_html(html_content, filename):
    """حفظ محتوى HTML في ملف وفتحه باستخدام المتصفح الافتراضي."""
    try:
        # التأكد من أن امتداد الملف هو .html
        if not filename.lower().endswith('.html'):
            filename += '.html'

        # حفظ محتوى HTML في الملف
        with open(filename, 'w', encoding='utf-8') as f:
            f.write(html_content)
        print(f"تم حفظ ملف HTML بنجاح: {filename}")

        # فتح الملف باستخدام المتصفح الافتراضي
        webbrowser.open(f'file:///{os.path.abspath(filename)}')
        return True
    except Exception as e:
        print(f"خطأ في حفظ أو فتح ملف HTML: {e}")
        traceback.print_exc()
        return False
# --- >> نهاية دالة حفظ وفتح ملف HTML << ---
def open_pdf_file(filename):
    """فتح الملف المنشأ باستخدام البرنامج الافتراضي."""
    try:
        absolute_path = os.path.abspath(filename)
        print(f"فتح الملف: {absolute_path}")
        webbrowser.open(f'file:///{absolute_path}')
        return True
    except Exception as open_error:
        print(f"خطأ في فتح الملف تلقائياً: {open_error}")
        return False
# --- >> نهاية الدالة << ---

# --- >> دالة حفظ وفتح ملف PDF << ---
def save_and_open_pdf(data, filename):
    """إنشاء ملف PDF وفتحه"""
    try:
        # التأكد من أن امتداد الملف هو .pdf
        if not filename.lower().endswith('.pdf'):
            filename += '.pdf'

        # إنشاء ملف PDF
        if create_violation_pdf(data, filename):
            print(f"تم إنشاء ملف PDF بنجاح: {filename}")
            # فتح الملف
            if open_pdf_file(filename):
                print("تم فتح ملف PDF بنجاح")
                return True
            else:
                print("فشل في فتح ملف PDF")
                return False
        else:
            print("فشل في إنشاء ملف PDF")
            return False
    except Exception as e:
        print(f"خطأ في حفظ أو فتح ملف PDF: {e}")
        traceback.print_exc()
        return False
# --- >> نهاية الدالة << ---


# --- >> دالة لإنشاء ترويسة HTML مع CSS << ---
def generate_html_header(title, base_font_size=12):
    # استخدام خط Amiri كأساس، مع بدائل شائعة
    font_family = "Amiri, Arial, sans-serif"
    # تحويل الأنماط السابقة إلى CSS
    css = f"""
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{html.escape(title)}</title>
    <style>
        body {{
            font-family: {font_family};
            font-size: {base_font_size}pt;
            direction: rtl; /* الاتجاه العام للصفحة */
            margin: 0.3cm; /* --- تعديل الهوامش إلى 0.3cm --- */
        }}
        .center {{ text-align: center; }}
        .right {{ text-align: right; }}
        .left {{ text-align: left; }}
        /* --- تعديل الشعار --- */
        .logo {{
            max-width: 250px;
            max-height: 100px;
            display: block;
            margin: 10px auto 10px auto; /* أعلى 10، أفقي تلقائي (توسيط)، أسفل 10 */
        }}
        /* --- تعديل التباعد للعناصر الأخرى --- */
        .institution-name {{ font-size: 16pt; font-weight: bold; text-align: center; margin-top: 10px; margin-bottom: 10px; }}
        .report-title {{ font-size: 18pt; font-weight: bold; text-align: center; margin-top: 10px; margin-bottom: 10px; }}
        .label {{ font-weight: bold; text-align: right; padding-left: 5px; }}
        .data {{ text-align: right; }}
        .long-data {{ text-align: right; line-height: {base_font_size + 2}pt; margin-top: 10px; margin-bottom: 10px; white-space: pre-wrap; /* للحفاظ على فواصل الأسطر إن وجدت */ }}
        .signature {{ text-align: left; font-weight: bold; }}
        .details-section p {{ margin-top: 10px; margin-bottom: 10px; }} /* تباعد 10px لتفاصيل المخالفة */
        .section-title {{ font-size: 14pt; font-weight: bold; text-align: center; margin-top: 10px; margin-bottom: 10px; }} /* عنوان القسم في سجل الطالب */

        /* أنماط الجداول */
        table {{
            width: 100%;
            border-collapse: collapse;
            margin-top: 10px; /* تعديل التباعد العلوي */
            margin-bottom: 10px; /* تعديل التباعد السفلي */
        }}
        th, td {{
            border: 1.5px solid black; /* حدود سوداء وأكثر سمكًا */
            padding: 3px 6px;
            vertical-align: top;
            font-size: {base_font_size}pt; /* حجم خط أساسي للخلايا */
        }}
        .table-label-col {{ background-color: #f2f2f2; font-weight: bold; }} /* خلفية أعمدة العناوين */

        /* جدول سجل المخالفات */
        .violations-table th {{
            background-color: #1a237e;
            color: white;
            font-weight: bold;
            font-size: {base_font_size + 1}pt;
            text-align: center;
        }}
        .violations-table td {{
             font-size: {base_font_size -1}pt;
             text-align: center; /* محاذاة افتراضية للخلايا */
        }}
        .violations-table .notes-cell {{
             text-align: right; /* محاذاة خاصة لخلايا الملاحظات */
             line-height: {base_font_size + 1}pt;
             white-space: pre-wrap; /* --- إضافة التفاف النص وعرضه عمودياً --- */
        }}
        .violations-table tr:nth-child(even) td {{ background-color: #f5f7fa; }} /* خلفيات متناوبة */

        /* جدول التقرير السنوي */
        .yearly-table {{ font-size: {base_font_size - 1}pt; }} /* حجم خط أصغر للتقرير السنوي */
        .yearly-table th {{
            background-color: #1a237e;
            color: white;
            font-weight: bold;
            font-size: {base_font_size}pt;
            text-align: center;
        }}
         .yearly-table td {{
             font-size: {base_font_size - 1}pt;
             text-align: center; /* محاذاة افتراضية */
             vertical-align: middle;
             padding: 2px 3px; /* تباعد أقل */
        }}
        .yearly-table .cell-right {{ text-align: right; }}
        .yearly-table .cell-wrap {{ text-align: right; white-space: pre-wrap; }}
        .yearly-table tr:nth-child(even) td {{ background-color: #f5f7fa; }}

        /* إعدادات الطباعة للتقرير السنوي */
        @media print {{
            .yearly-report-body {{
                /* @page {{ size: A4 landscape; margin: 1cm; }} */ /* قد لا تعمل في كل المتصفحات */
                width: 277mm; /* <-- إعادة تفعيل هذا السطر للعودة إلى الوضع الأفقي */
            }}
        }}

        /* جدول التوقيع */
        .signature-table {{
            border: none;
            width: 100%;
            margin-top: 10px; /* تباعد علوي */
        }}
        .signature-table td {{
            border: none;
            width: 33%; /* أو 50% حسب عدد الأعمدة */
        }}

    </style>
    """
    return f"<!DOCTYPE html>\n<html lang=\"ar\">\n<head>\n{css}\n</head>\n"
# --- >> نهاية دالة الترويسة << ---


# --------- وظيفة طباعة تقرير المخالفة (HTML) ---------

def print_violation_details(violation_data, db_path=None):
    """طباعة تقرير مخالفة محددة بتنسيق HTML"""
    settings = load_settings()
    base_font_size = settings.get('font_size', 12)
    report_title_text = "تقرير مخالفة"

    try:
        # إنشاء مجلد المخالفات داخل مجلد البرنامج Taheri200
        program_dir = os.path.dirname(os.path.abspath(__file__))
        violations_dir = os.path.join(program_dir, "المخالفات")
        os.makedirs(violations_dir, exist_ok=True)
        print(f"تم التأكد من وجود مجلد المخالفات: {violations_dir}")

        # تحديد اسم ملف الإخراج
        student_name = violation_data.get('student_name', 'بدون_اسم').replace(' ', '_')
        student_code = violation_data.get('student_code', 'بدون_رمز')
        current_date = datetime.now().strftime("%Y%m%d")

        # إنشاء اسم الملف باستخدام اسم التلميذ ورمزه وتاريخ الطباعة
        filename = os.path.join(violations_dir, f"مخالفة_{student_name}_{student_code}_{current_date}.pdf")
        print(f"إنشاء ملف PDF في: {filename}")

        # بناء محتوى HTML
        html_content = generate_html_header(report_title_text, base_font_size)
        html_content += "<body dir=\"rtl\">\n" # Ensure body direction

        # 1. الشعار واسم وعنوان المؤسسة
        logo_path, school_name, school_address = locate_logo_file(db_path)
        if logo_path and os.path.exists(logo_path):
            try:
                # استخدام مسار file:/// للصور المحلية
                img_src = f"file:///{os.path.abspath(logo_path)}"
                html_content += f"<img src=\"{img_src}\" alt=\"شعار المؤسسة\" class=\"logo\">\n"
            except Exception as img_err:
                 print(f"--- التشخيص: خطأ في معالجة مسار الشعار لـ HTML: {img_err}")

        html_content += f"<p class=\"institution-name\">{fix_arabic_text(school_name)}</p>\n"
        html_content += f"<p class=\"report-title\">{fix_arabic_text(school_address)}</p>\n" # العنوان الافتراضي للمخالفة

        # 2. بيانات التلميذ والمخالفة (جدول HTML)
        html_content += "<table>\n"
        html_content += f"""
            <tr>
                <td class="label table-label-col">{fix_arabic_text("اسم التلميذ:")}</td>
                <td class="data">{fix_arabic_text(violation_data.get('student_name', ''))}</td>
                <td class="label table-label-col">{fix_arabic_text("تاريخ المخالفة:")}</td>
                <td class="data">{fix_arabic_text(violation_data.get('date', ''))}</td>
            </tr>
            <tr>
                <td class="label table-label-col">{fix_arabic_text("رمز التلميذ:")}</td>
                <td class="data">{fix_arabic_text(violation_data.get('student_code', ''))}</td>
                <td class="label table-label-col">{fix_arabic_text("المادة:")}</td>
                <td class="data">{fix_arabic_text(violation_data.get('subject', ''))}</td>
            </tr>
            <tr>
                <td class="label table-label-col">{fix_arabic_text("المستوى:")}</td>
                <td class="data">{fix_arabic_text(violation_data.get('level', ''))}</td>
                <td class="label table-label-col">{fix_arabic_text("الأستاذ:")}</td>
                <td class="data">{fix_arabic_text(violation_data.get('teacher', ''))}</td>
            </tr>
            <tr>
                <td class="label table-label-col">{fix_arabic_text("القسم:")}</td>
                <td class="data">{fix_arabic_text(violation_data.get('section', ''))}</td>
                <td class="label table-label-col">{fix_arabic_text("رقم المخالفة:")}</td>
                <td class="data">{fix_arabic_text(str(violation_data.get('id', '')))}</td>
            </tr>
        """
        html_content += "</table>\n"

        # 3. تفاصيل المخالفة (الملاحظات، الإجراءات)
        html_content += "<div class=\"details-section\">\n"
        html_content += f"<p class=\"label\">{fix_arabic_text('ملاحظات الأستاذ:')}</p>\n"
        html_content += f"<p class=\"long-data\">{fix_arabic_text(violation_data.get('notes', 'لا توجد'))}</p>\n"

        html_content += f"<p class=\"label\">{fix_arabic_text('الإجراءات المتخذة من طرف الأستاذ:')}</p>\n"
        html_content += f"<p class=\"long-data\">{fix_arabic_text(violation_data.get('procedures', 'لا توجد'))}</p>\n"
        save_and_open_html(html_content, filename)
        html_content += f"<p class=\"label\">{fix_arabic_text('إجراءات الحراسة العامة:')}</p>\n"
        html_content += f"<p class=\"long-data\">{fix_arabic_text(violation_data.get('guard_actions', 'لا توجد'))}</p>\n"
        html_content += "</div>\n"

        # 4. التوقيعات والتاريخ
        today = datetime.now().strftime("%Y-%m-%d")
        # استخدام جدول بسيط للتوقيعات مع الكلاس الجديد
        html_content += "<table class=\"signature-table\">\n" # استخدام الكلاس
        html_content += f"""
            <tr>
                <td class="signature">{fix_arabic_text("توقيع الأستاذ:")}</td>
                <td class="signature">{fix_arabic_text("توقيع الحارس العام:")}</td>
                <td class="signature">{fix_arabic_text(f"تاريخ الطباعة: {today}")}</td>
            </tr>
        """
        html_content += "</table>\n"

        # إغلاق HTML
        html_content += "</body>\n</html>"

        # حفظ وفتح الملف
        save_and_open_html(html_content, filename)
        return True

    except Exception as e:
        print(f"خطأ في طباعة تقرير المخالفة (HTML): {e}")
        traceback.print_exc()
        return False

# --------- وظيفة طباعة سجل المخالفات للتلميذ (HTML) ---------

def print_student_violations_record(student_info, violations_records, db_path=None):
    """طباعة سجل مخالفات التلميذ بتنسيق HTML"""
    settings = load_settings(filename="print_settings_violation.json")
    base_font_size = settings.get('font_size', 12)
    report_title_text = "سجل المخالفات"

    try:
        # إنشاء مجلد المخالفات داخل مجلد البرنامج Taheri200
        program_dir = os.path.dirname(os.path.abspath(__file__))
        violations_dir = os.path.join(program_dir, "المخالفات")
        os.makedirs(violations_dir, exist_ok=True)
        print(f"تم التأكد من وجود مجلد المخالفات: {violations_dir}")

        # تحديد اسم ملف الإخراج
        student_name = student_info.get('name', 'بدون_اسم').replace(' ', '_')
        student_code = student_info.get('code', 'بدون_رمز')
        current_date = datetime.now().strftime("%Y%m%d")

        # إنشاء اسم الملف باستخدام اسم التلميذ ورمزه وتاريخ الطباعة
        filename = os.path.join(violations_dir, f"سجل_مخالفات_{student_name}_{student_code}_{current_date}.pdf")
        print(f"إنشاء ملف PDF لسجل المخالفات في: {filename}")

        # بناء محتوى HTML
        html_content = generate_html_header(report_title_text, base_font_size)
        html_content += "<body dir=\"rtl\">\n"

        # 1. الشعار واسم المؤسسة
        logo_path, school_name, _ = locate_logo_file(db_path)
        if logo_path and os.path.exists(logo_path):
            try:
                img_src = f"file:///{os.path.abspath(logo_path)}"
                html_content += f"<img src=\"{img_src}\" alt=\"شعار المؤسسة\" class=\"logo\">\n"
            except Exception as img_err:
                 print(f"--- التشخيص: خطأ في معالجة مسار الشعار لـ HTML: {img_err}")
        html_content += f"<p class=\"institution-name\">{fix_arabic_text(school_name)}</p>\n"
        html_content += f"<p class=\"report-title\">{fix_arabic_text(report_title_text)}</p>\n"

        # 2. بيانات التلميذ (جدول HTML)
        html_content += "<table>\n"
        html_content += f"""
             <tr>
                <td class="label table-label-col" style="width: 20%;">{fix_arabic_text("اسم التلميذ:")}</td>
                <td class="data" style="width: 80%;">{fix_arabic_text(student_info.get('name', ''))}</td>
             </tr>
             <tr>
                <td class="label table-label-col">{fix_arabic_text("رمز التلميذ:")}</td>
                <td class="data">{fix_arabic_text(student_info.get('code', ''))}</td>
             </tr>
             <tr>
                <td class="label table-label-col">{fix_arabic_text("المستوى:")}</td>
                <td class="data">{fix_arabic_text(student_info.get('level', ''))}</td>
             </tr>
             <tr>
                <td class="label table-label-col">{fix_arabic_text("القسم:")}</td>
                <td class="data">{fix_arabic_text(student_info.get('section', ''))}</td>
             </tr>
        """
        html_content += "</table>\n"

        # 3. جدول المخالفات
        if violations_records:
            html_content += "<table class=\"violations-table\">\n"
            html_content += f"""
                <thead>
                    <tr>
                        <th>{fix_arabic_text("رقم")}</th>
                        <th>{fix_arabic_text("التاريخ")}</th>
                        <th>{fix_arabic_text("المادة")}</th>
                        <th>{fix_arabic_text("الأستاذ")}</th>
                        <th>{fix_arabic_text("المخالفات")}</th>
                        <th>{fix_arabic_text("الإجراءات")}</th>
                    </tr>
                </thead>
                <tbody>
            """

            for i, record in enumerate(violations_records, 1):
                html_content += f"""
                    <tr>
                        <td>{str(i)}</td>
                        <td>{fix_arabic_text(record.get('date', ''))}</td>
                        <td>{fix_arabic_text(record.get('subject', ''))}</td>
                        <td>{fix_arabic_text(record.get('teacher', ''))}</td>
                        <td class="notes-cell">{fix_arabic_text(record.get('notes', ''))}</td>
                        <td class="notes-cell">{fix_arabic_text(record.get('procedures', ''))}</td>
                    </tr>
                """
            html_content += "</tbody>\n</table>\n"
        else:
            html_content += f"<p class=\"data\">{fix_arabic_text('لا توجد مخالفات مسجلة لهذا التلميذ.')}</p>\n"

        # 4. التوقيع والتاريخ
        today = datetime.now().strftime("%Y-%m-%d")
        html_content += "<table class=\"signature-table\">\n" # استخدام الكلاس
        html_content += f"""
            <tr>
                <td style="width: 50%;" class="signature">{fix_arabic_text("توقيع الحارس العام:")}</td>
                <td style="width: 50%;" class="signature">{fix_arabic_text(f"تاريخ الطباعة: {today}")}</td>
            </tr>
        """
        html_content += "</table>\n"

        # إغلاق HTML
        html_content += "</body>\n</html>"

        # حفظ وفتح الملف
        save_and_open_html(html_content, filename)
        return True

    except Exception as e:
        print(f"خطأ في طباعة سجل مخالفات التلميذ (HTML): {e}")
        traceback.print_exc()
        return False

# --------- وظيفة طباعة سجل المخالفات حسب السنة الدراسية (HTML) ---------

# --- >> إضافة متغير لاسم الجدول << ---
# <-- قم بتغيير "violations" هنا إلى اسم جدول المخالفات الصحيح في قاعدة بياناتك data.db
VIOLATIONS_TABLE_NAME = "المخالفات" # <--- تم التعديل إلى الاسم الصحيح
# --- >> نهاية الإضافة << ---

def print_violations_by_academic_year(academic_year, db_path=None):
    """طباعة تقرير بجميع المخالفات المسجلة خلال سنة دراسية معينة بتنسيق HTML"""
    settings = load_settings(filename="print_settings_violation.json")
    base_font_size = settings.get('font_size', 10) # حجم أصغر للتقرير العام
    report_title_text = f"تقرير المخالفات للسنة الدراسية: {academic_year}"

    try:
        # إنشاء مجلد المخالفات داخل مجلد البرنامج Taheri200
        program_dir = os.path.dirname(os.path.abspath(__file__))
        violations_dir = os.path.join(program_dir, "المخالفات")
        os.makedirs(violations_dir, exist_ok=True)
        print(f"تم التأكد من وجود مجلد المخالفات: {violations_dir}")

        # تحديد اسم ملف الإخراج
        current_date = datetime.now().strftime("%Y%m%d")
        filename = os.path.join(violations_dir, f"تقرير_المخالفات_السنة_{academic_year.replace('/', '-')}_{current_date}.pdf")
        print(f"إنشاء ملف PDF للتقرير السنوي في: {filename}")

        # بناء محتوى HTML
        html_content = generate_html_header(report_title_text, base_font_size)
        # إضافة كلاس للـ body للتحكم بالطباعة الأفقية
        html_content += "<body dir=\"rtl\" class=\"yearly-report-body\">\n"

        # 1. الترويسة والعنوان
        logo_path, school_name, _ = locate_logo_file(db_path)
        if logo_path and os.path.exists(logo_path):
             try:
                 img_src = f"file:///{os.path.abspath(logo_path)}"
                 # --- إزالة النمط المضمن واستخدام الكلاس ---
                 html_content += f"<img src=\"{img_src}\" alt=\"شعار المؤسسة\" class=\"logo\">\n"
             except Exception as img_err:
                  print(f"--- التشخيص: خطأ في معالجة مسار الشعار لـ HTML: {img_err}")
        html_content += f"<p class=\"institution-name\" style=\"font-size: 14pt;\">{fix_arabic_text(school_name)}</p>\n"
        html_content += f"<p class=\"report-title\" style=\"font-size: 16pt;\">{fix_arabic_text(report_title_text)}</p>\n"

        # 2. جلب بيانات المخالفات
        if not db_path or not os.path.exists(db_path):
            raise FileNotFoundError(f"ملف قاعدة البيانات غير موجود: {db_path}")

        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        query = f"""
        SELECT الأسدس, التاريخ, رمز_التلميذ, اسم_التلميذ, القسم, المادة, الأستاذ, الملاحظات, الإجراءات, إجراءات_الحراسة
        FROM {VIOLATIONS_TABLE_NAME}
        WHERE السنة_الدراسية = ?
        ORDER BY التاريخ DESC
        """
        try:
            cursor.execute(query, (academic_year,))
            records = cursor.fetchall()
        except sqlite3.OperationalError as oe:
            print(f"خطأ في استعلام قاعدة البيانات (التقرير السنوي): {oe}")
            conn.close()
            error_message = (
                f"خطأ استعلام: لم يتم العثور على الجدول '{VIOLATIONS_TABLE_NAME}' أو أحد أعمدته المطلوبة.\n"
                f"تأكد من أن الجدول '{VIOLATIONS_TABLE_NAME}' موجود في قاعدة البيانات '{db_path}' وأن أسماء الأعمدة (الأسدس, التاريخ, رمز_التلميذ, اسم_التلميذ, القسم, ...) صحيحة.\n"
                f"الاستعلام الذي تم تنفيذه: {query}\n" # إضافة الاستعلام لرسالة الخطأ
                f"الخطأ الأصلي: {oe}"
            )
            print(error_message)
            return None # Return None to indicate error
        conn.close()

        if not records:
            html_content += f"<p>{fix_arabic_text('لا توجد مخالفات مسجلة لهذه السنة الدراسية.')}</p>\n"
            html_content += "</body>\n</html>"
            save_and_open_html(html_content, filename)
            return True

        # 3. بناء جدول المخالفات
        html_content += "<table class=\"yearly-table\">\n"
        html_content += f"""
            <thead>
                <tr>
                    <th>{fix_arabic_text("الأسدس")}</th>
                    <th>{fix_arabic_text("التاريخ")}</th>
                    <th>{fix_arabic_text("رمز التلميذ")}</th>
                    <th>{fix_arabic_text("اسم التلميذ")}</th>
                    <th>{fix_arabic_text("القسم")}</th>
                    <th>{fix_arabic_text("المادة")}</th>
                    <th>{fix_arabic_text("الأستاذ")}</th>
                    <th>{fix_arabic_text("المخالفات")}</th>
                    <th>{fix_arabic_text("الإجراءات")}</th>
                    <th>{fix_arabic_text("إجراءات الحراسة")}</th>
                </tr>
            </thead>
            <tbody>
        """

        for record in records:
            (semester, date, student_code, student_name, section, subject, teacher, notes, procedures, guard_actions) = record
            html_content += f"""
                <tr>
                    <td>{fix_arabic_text(semester or '')}</td>
                    <td>{fix_arabic_text(date or '')}</td>
                    <td>{fix_arabic_text(student_code or '')}</td>
                    <td class="cell-right">{fix_arabic_text(student_name or '')}</td>
                    <td>{fix_arabic_text(section or '')}</td>
                    <td class="cell-right">{fix_arabic_text(subject or '')}</td>
                    <td class="cell-right">{fix_arabic_text(teacher or '')}</td>
                    <td class="cell-wrap">{fix_arabic_text(notes or '')}</td>
                    <td class="cell-wrap">{fix_arabic_text(procedures or '')}</td>
                    <td class="cell-wrap">{fix_arabic_text(guard_actions or '')}</td>
                </tr>
            """
        html_content += "</tbody>\n</table>\n"

        # إغلاق HTML
        html_content += "</body>\n</html>"

        # حفظ وفتح الملف
        save_and_open_html(html_content, filename)
        return True

    except FileNotFoundError as fnf_err:
         print(f"خطأ: {fnf_err}")
         print(f"خطأ ملف: ملف قاعدة البيانات غير موجود:\n{db_path}")
         return None # Return None or False on error
    except Exception as e:
        print(f"خطأ في طباعة تقرير المخالفات للسنة الدراسية: {e}")
        traceback.print_exc()
        print(f"خطأ طباعة: حدث خطأ أثناء إنشاء تقرير PDF:\n{e}")
        return None # Return None or False on error

# قسم اختباري مبسط
if __name__ == "__main__":
    print("\n===== برنامج اختبار طباعة تقرير المخالفة (HTML) =====")

    # بيانات تجريبية للاختبار
    violation_info_test = {
        "id": 123,
        "date": "2024-03-15",
        "student_name": "أحمد المنصوري",
        "student_code": "ST54321",
        "section": "2BACSP-1",
        "level": "الثانية باكالوريا علوم فيزيائية",
        "subject": "الرياضيات",
        "teacher": "الأستاذ كريمي",
        "notes": "تأخر عن الحصة لمدة 15 دقيقة بدون مبرر.",
        "procedures": "تم تسجيل التأخر وتنبيه التلميذ.",
        "guard_actions": "تم استدعاء ولي الأمر وإشعاره بالمخالفة.",
        "birth_date": "2006-05-20", # مثال لبيانات إضافية
        "phone1": "0611223344"      # مثال لبيانات إضافية
    }

    # استدعاء الدالة (HTML version)
    print_violation_details(violation_info_test, db_path="data.db")

    print("\n===== اختبار طباعة سجل المخالفات للتلميذ (HTML) =====")
    student_info_test_record = {
        'name': "سارة الفهري",
        'code': "ST67890",
        'level': "الأولى إعدادي",
        'section': "1/3"
    }
    violations_records_test = [
        {'id': 1, 'date': '2024-02-10', 'subject': 'الفرنسية', 'teacher': 'الأستاذة لبنى', 'notes': 'لم يحضر دفتر النصوص.', 'procedures': 'تنبيه وتدوين الملاحظة.'},
        {'id': 5, 'date': '2024-03-01', 'subject': 'الاجتماعيات', 'teacher': 'الأستاذ رشيد', 'notes': 'تشويش أثناء الحصة.', 'procedures': 'تنبيه شفهي.'},
        {'id': 8, 'date': '2024-03-12', 'subject': 'الرياضيات', 'teacher': 'الأستاذ كريمي', 'notes': 'عدم إنجاز الواجب المنزلي للمرة الثانية.', 'procedures': 'استدعاء ولي الأمر.'}
    ]

    print_student_violations_record(student_info_test_record, violations_records_test, db_path="data.db")
    print("\nانتهى اختبار طباعة سجل المخالفات.")

    # --- اختبار طباعة تقرير المخالفات السنوي (HTML) ---
    print("\n===== اختبار طباعة تقرير المخالفات السنوي (HTML) =====")
    test_academic_year = "2023-2024" # سنة دراسية للاختبار
    print(f"محاولة طباعة تقرير المخالفات للسنة الدراسية: {test_academic_year}")
    print_violations_by_academic_year(test_academic_year, db_path="data.db")
    print("\nانتهى اختبار طباعة تقرير المخالفات السنوي.")
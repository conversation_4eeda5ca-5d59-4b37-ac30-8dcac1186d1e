import os
import sys
import re

def patch_file(file_path):
    """تعديل ملف sub9_window.py لإزالة رسائل التأكيد العادية"""
    
    # قراءة محتوى الملف
    with open(file_path, 'r', encoding='utf-8') as file:
        content = file.read()
    
    # تعديل الكود لتجاوز رسائل التأكيد عند إنشاء الجداول التجميعية
    pattern1 = r"(                # إنشاء جداول الغياب السنوي\n                absence_window\.generate_summary_tables\(\)\n\n                print\(\"تم حساب المجموع الشهري للغياب بنجاح!\"\))"
    replacement1 = """                # تجاوز رسائل التأكيد العادية عند إنشاء الجداول التجميعية
                try:
                    # حفظ الدالة الأصلية
                    original_info = QMessageBox.information
                    
                    # تعريف دالة فارغة لتجاوز عرض الرسائل
                    def empty_info(*args, **kwargs):
                        return QMessageBox.Ok
                    
                    # استبدال الدالة الأصلية بالدالة الفارغة
                    QMessageBox.information = empty_info
                    
                    # إنشاء جداول الغياب السنوي
                    absence_window.generate_summary_tables()
                    
                    # إعادة الدالة الأصلية
                    QMessageBox.information = original_info
                except Exception as e:
                    print(f"خطأ أثناء محاولة تجاوز رسائل إنشاء الجداول التجميعية: {e}")
                    # في حالة حدوث خطأ، نستخدم الطريقة العادية
                    absence_window.generate_summary_tables()
                
                print("تم حساب المجموع الشهري للغياب بنجاح!")"""
    
    # تطبيق التعديلات
    modified_content = re.sub(pattern1, replacement1, content)
    
    # حفظ الملف المعدل
    with open(file_path, 'w', encoding='utf-8') as file:
        file.write(modified_content)
    
    print(f"تم تعديل الملف {file_path} بنجاح")

if __name__ == "__main__":
    if len(sys.argv) > 1:
        file_path = sys.argv[1]
    else:
        file_path = "sub9_window.py"
    
    if os.path.exists(file_path):
        patch_file(file_path)
    else:
        print(f"الملف {file_path} غير موجود")

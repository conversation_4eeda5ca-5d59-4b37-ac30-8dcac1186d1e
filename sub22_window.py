#!/usr/bin/env python
# -*- coding: utf-8 -*-

import sys
import os
import sqlite3
from PyQt5.QtWidgets import (
    QApplication, QMainWindow, QTableWidget, QTableWidgetItem, QVBoxLayout,
    QHBoxLayout, QWidget, QHeaderView, QLabel, QLineEdit, QPushButton,
    QComboBox, QFrame, QMessageBox, QSpinBox, QGridLayout, QCheckBox,
    QFileDialog, QInputDialog, QDialog, QDialogButtonBox, QRadioButton,
    QGroupBox, QFormLayout, QScrollArea, QProgressDialog, QDateEdit
)
import math
from PyQt5.QtGui import QFont, QIcon, QColor
from PyQt5.QtCore import Qt, QSize, QDate
from datetime import datetime
import pandas as pd
# استيراد نافذة إضافة أرقام الامتحان
from sub24_window import ExamNumbersDialog
# استيراد نافذة ترقيم القاعات
from sub25_window import RoomAssignmentDialog

class AbsenceDialog(QDialog):
    """نافذة حوارية لتسجيل الغياب"""
    
    def __init__(self, parent=None, selected_count=0):
        super().__init__(parent)
        self.selected_count = selected_count
        self.setupUI()
        
    def setupUI(self):
        self.setWindowTitle("تسجيل الغياب")
        self.setMinimumWidth(500)
        self.setMinimumHeight(400)
        self.setStyleSheet("background-color: #f0f8ff;")
        
        layout = QVBoxLayout(self)
        
        # العنوان
        title_label = QLabel(f"تسجيل غياب {self.selected_count} مترشح")
        title_label.setFont(QFont("Calibri", 16, QFont.Bold))
        title_label.setStyleSheet("color: #0066cc; padding: 10px; border: 2px solid #0066cc; border-radius: 5px;")
        title_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(title_label)
        
        # إطار البيانات
        data_frame = QFrame()
        data_frame.setStyleSheet("background-color: white; border: 1px solid #d0d0d0; border-radius: 5px; padding: 15px;")
        data_layout = QFormLayout(data_frame)
        
        # تاريخ الغياب
        date_label = QLabel("تاريخ الغياب:")
        date_label.setFont(QFont("Calibri", 13, QFont.Bold))
        
        self.date_edit = QDateEdit()
        self.date_edit.setDate(QDate.currentDate())
        self.date_edit.setFont(QFont("Calibri", 13))
        self.date_edit.setFixedHeight(40)
        self.date_edit.setStyleSheet("border: 1px solid #d0d0d0; padding: 5px; border-radius: 3px;")
        self.date_edit.setCalendarPopup(True)
        
        data_layout.addRow(date_label, self.date_edit)
        
        # الفترة
        period_label = QLabel("الفترة:")
        period_label.setFont(QFont("Calibri", 13, QFont.Bold))
        
        self.period_combo = QComboBox()
        self.period_combo.addItems(["الفترة الصباحية", "الفترة المسائية"])
        self.period_combo.setFont(QFont("Calibri", 13))
        self.period_combo.setFixedHeight(40)
        self.period_combo.setStyleSheet("border: 1px solid #d0d0d0; padding: 5px; border-radius: 3px;")
        
        data_layout.addRow(period_label, self.period_combo)
        
        # المادة - تحويل من حقل نصي إلى قائمة منسدلة
        subject_label = QLabel("المادة:")
        subject_label.setFont(QFont("Calibri", 13, QFont.Bold))
        
        self.subject_combo = QComboBox()
        # إضافة المواد الدراسية المطلوبة
        subjects = [
            "اللغة العربية",
            "اللغة الأجنبية الأولى", 
            "اللغة الأجنبية الثانية",
            "التربية الإسلامية",
            "التاريخ والجغرافيا",
            "الرياضيات",
            "العلوم الفيزيائية",
            "علوم الحياة والأرض",
            "الفلسفة"
        ]
        
        # إضافة خيار فارغ في البداية
        self.subject_combo.addItem("اختر المادة...")
        self.subject_combo.addItems(subjects)
        
        self.subject_combo.setFont(QFont("Calibri", 13))
        self.subject_combo.setFixedHeight(40)
        self.subject_combo.setStyleSheet("""
            QComboBox {
                border: 1px solid #d0d0d0; 
                padding: 5px; 
                border-radius: 3px;
                background-color: white;
            }
            QComboBox::drop-down {
                border: none;
                width: 30px;
            }
            QComboBox::down-arrow {
                image: none;
                border-left: 1px solid #d0d0d0;
                width: 0;
                height: 0;
                border-left: 5px solid transparent;
                border-right: 5px solid transparent;
                border-top: 8px solid #666;
            }
            QComboBox QAbstractItemView {
                background-color: white;
                selection-background-color: #e6f3ff;
                border: 1px solid #d0d0d0;
            }
        """)
        
        data_layout.addRow(subject_label, self.subject_combo)
        
        # ملاحظات
        notes_label = QLabel("ملاحظات:")
        notes_label.setFont(QFont("Calibri", 13, QFont.Bold))
        
        self.notes_edit = QLineEdit()
        self.notes_edit.setFont(QFont("Calibri", 13))
        self.notes_edit.setFixedHeight(40)
        self.notes_edit.setStyleSheet("border: 1px solid #d0d0d0; padding: 5px; border-radius: 3px;")
        self.notes_edit.setPlaceholderText("ملاحظات إضافية (اختياري)...")
        
        data_layout.addRow(notes_label, self.notes_edit)
        
        layout.addWidget(data_frame)
        
        # الأزرار
        buttons_layout = QHBoxLayout()
        
        self.ok_button = QPushButton("تسجيل الغياب وإنشاء التقرير")
        self.ok_button.setFont(QFont("Calibri", 13, QFont.Bold))
        self.ok_button.setFixedHeight(45)
        self.ok_button.setStyleSheet("""
            QPushButton {
                background-color: #dc3545;
                color: white;
                border-radius: 5px;
                padding: 10px;
                min-width: 200px;
            }
            QPushButton:hover {
                background-color: #c82333;
            }
        """)
        self.ok_button.clicked.connect(self.accept)
        
        cancel_button = QPushButton("إلغاء")
        cancel_button.setFont(QFont("Calibri", 13, QFont.Bold))
        cancel_button.setFixedHeight(45)
        cancel_button.setStyleSheet("""
            QPushButton {
                background-color: #6c757d;
                color: white;
                border-radius: 5px;
                padding: 10px;
                min-width: 100px;
            }
            QPushButton:hover {
                background-color: #5a6268;
            }
        """)
        cancel_button.clicked.connect(self.reject)
        
        buttons_layout.addStretch()
        buttons_layout.addWidget(self.ok_button)
        buttons_layout.addWidget(cancel_button)
        buttons_layout.addStretch()
        
        layout.addLayout(buttons_layout)
        
    def get_absence_data(self):
        """الحصول على بيانات الغياب المدخلة"""
        return {
            'date': self.date_edit.date().toString('yyyy-MM-dd'),
            'period': self.period_combo.currentText(),
            'subject': self.subject_combo.currentText() if self.subject_combo.currentIndex() > 0 else '',
            'notes': self.notes_edit.text().strip()
        }

class ExamSetupWindow(QMainWindow):
    """نافذة عرض بيانات الامتحانات وتهيئة الامتحانات"""

    def __init__(self, parent=None, db=None, academic_year=None, db_path="data.db"):
        super().__init__(parent)
        # تخزين قاعدة البيانات المقدمة أو استخدام المسار المحدد
        self.db_connection = db
        self.academic_year = academic_year
        self.db_path = db_path
        self.initUI()
        self.load_data()

    def initUI(self):
        # تعيين عنوان النافذة
        self.setWindowTitle("بيانات الامتحانات")
        self.setWindowIcon(QIcon("01.ico"))

        # الحصول على حجم الشاشة واستخدامه لضبط حجم النافذة
        screen_size = QApplication.desktop().screenGeometry()
        screen_width = screen_size.width()
        screen_height = screen_size.height()

        # تعيين حجم ومكان النافذة (كامل الشاشة)
        self.setGeometry(0, 0, screen_width, screen_height)
        self.setMinimumSize(980, 600)

        # جعل النافذة تأخذ كامل الشاشة عند الفتح
        self.showMaximized()

        # إنشاء ويدجت مركزي
        central_widget = QWidget(self)
        self.setCentralWidget(central_widget)

        # إنشاء التخطيط الرئيسي
        main_layout = QVBoxLayout(central_widget)
        main_layout.setContentsMargins(10, 10, 10, 10)
        main_layout.setSpacing(10)

        # إضافة إطار للكائنات في أعلى النافذة
        top_frame = QFrame()
        top_frame.setFrameShape(QFrame.StyledPanel)
        top_frame.setStyleSheet("background-color: white; border: 1px solid #d0d0d0; border-radius: 5px;")
        top_layout = QGridLayout(top_frame)

        # إنشاء الكائنات المطلوبة في أعلى النافذة
        # العنوان الرئيسي
        header_label = QLabel("بيانات الامتحانات")
        header_label.setFont(QFont("Calibri", 14, QFont.Bold))
        header_label.setStyleSheet("color: black;")
        header_label.setAlignment(Qt.AlignCenter)
        top_layout.addWidget(header_label, 0, 0, 1, 3)

        # إطار للأزرار
        buttons_frame = QFrame()
        buttons_frame.setStyleSheet("background-color: white; border: none;")
        buttons_layout = QHBoxLayout(buttons_frame)  # تغيير إلى تخطيط أفقي
        buttons_layout.setSpacing(10)  # المسافة بين الأزرار

        # إنشاء الأزرار مع ربطها بالوظائف المناسبة
        button_data = [
            {"text": "استيراد المترشحين", "function": self.import_candidates},
            {"text": "إضافة رقم الامتحان", "function": self.add_exam_numbers_advanced},
            {"text": "ترقيم القاعات", "function": self.assign_rooms},
            {"text": "تسجيل الغياب", "function": self.record_absence, "color": "red"},
            {"text": "طباعة تقرير الغياب", "function": self.print_absence_reports, "color": "orange"},
            {"text": "شبكة الغياب", "function": self.open_absence_grid, "color": "blue"},
            {"text": "جدولة الامتحانات", "function": self.open_exam_schedule},
            {"text": "طباعة المحاضر واللوائح", "function": self.open_print_reports},
            {"text": "حذف البيانات", "function": self.delete_all_data}
        ]

        for button_info in button_data:
            button = QPushButton(button_info["text"])
            button.setFont(QFont("Calibri", 13, QFont.Bold))
            
            # تطبيق لون خاص لزر تسجيل الغياب
            if button_info.get("color") == "red":
                button.setStyleSheet("""
                    QPushButton {
                        background-color: #dc3545;
                        color: white;
                        border: 1px solid #dc3545;
                        border-radius: 5px;
                        padding: 8px;
                        margin: 5px;
                        min-width: 100px;
                    }
                    QPushButton:hover {
                        background-color: #c82333;
                        border: 1px solid #c82333;
                    }
                """)
            elif button_info.get("color") == "orange":
                button.setStyleSheet("""
                    QPushButton {
                        background-color: #fd7e14;
                        color: white;
                        border: 1px solid #fd7e14;
                        border-radius: 5px;
                        padding: 8px;
                        margin: 5px;
                        min-width: 100px;
                    }
                    QPushButton:hover {
                        background-color: #e55100;
                        border: 1px solid #e55100;
                    }
                """)
            elif button_info.get("color") == "blue":
                button.setStyleSheet("""
                    QPushButton {
                        background-color: #007bff;
                        color: white;
                        border: 1px solid #007bff;
                        border-radius: 5px;
                        padding: 8px;
                        margin: 5px;
                        min-width: 100px;
                    }
                    QPushButton:hover {
                        background-color: #0056b3;
                        border: 1px solid #0056b3;
                    }
                """)
            else:
                button.setStyleSheet("""
                    QPushButton {
                        background-color: #f0f0f0;
                        color: black;
                        border: 1px solid #cccccc;
                        border-radius: 5px;
                        padding: 8px;
                        margin: 5px;
                        min-width: 100px;
                    }
                    QPushButton:hover {
                        background-color: #e0e0e0;
                    }
                """)
            button.clicked.connect(button_info["function"])
            buttons_layout.addWidget(button)

        top_layout.addWidget(buttons_frame, 1, 0, 1, 3)  # جعل الأزرار تمتد على كامل العرض

        # إضافة الإطار العلوي إلى التخطيط الرئيسي
        main_layout.addWidget(top_frame)

        # إنشاء متغيرات الحقول المطلوبة ولكن بدون إضافتها للواجهة
        self.level_combo = QComboBox()
        self.level_combo.currentIndexChanged.connect(self.filter_by_level)

        self.exam_number_spin = QSpinBox()
        self.exam_number_spin.setMinimum(1)
        self.exam_number_spin.setMaximum(999999)
        self.exam_number_spin.setValue(1)

        self.total_candidates_spin = QSpinBox()
        self.total_candidates_spin.setValue(0)
        self.total_candidates_spin.setReadOnly(True)

        self.max_students_spin = QSpinBox()
        self.max_students_spin.setValue(20)

        self.room_name_input = QLineEdit("قاعة")

        self.room_number_spin = QSpinBox()
        self.room_number_spin.setValue(1)

        # إضافة شريط البحث
        search_frame = QFrame()
        search_frame.setFrameShape(QFrame.StyledPanel)
        search_frame.setStyleSheet("background-color: white; border: 1px solid #d0d0d0; border-radius: 5px;")
        search_layout = QHBoxLayout(search_frame)

        # إضافة قائمة المستويات
        level_label = QLabel("المستوى:")
        level_label.setFont(QFont("Calibri", 13, QFont.Bold))
        level_label.setFixedHeight(40)  # توحيد الارتفاع
        level_label.setAlignment(Qt.AlignVCenter)
        search_layout.addWidget(level_label)

        # استخدام قائمة المستويات المنسدلة التي تم إنشاؤها مسبقًا
        self.level_combo.setFont(QFont("Calibri", 13))
        self.level_combo.setFixedHeight(40)  # توحيد الارتفاع
        self.level_combo.setStyleSheet("background-color: white; border: 1px solid #d0d0d0; min-width: 200px; padding: 5px;")
        search_layout.addWidget(self.level_combo)

        # إضافة قائمة المؤسسة الأصلية
        institution_label = QLabel("المؤسسة الأصلية:")
        institution_label.setFont(QFont("Calibri", 13, QFont.Bold))
        institution_label.setFixedHeight(40)  # توحيد الارتفاع
        institution_label.setAlignment(Qt.AlignVCenter)
        search_layout.addWidget(institution_label)

        self.institution_combo = QComboBox()
        self.institution_combo.setFont(QFont("Calibri", 13))
        self.institution_combo.setFixedHeight(40)  # توحيد الارتفاع
        self.institution_combo.setStyleSheet("background-color: white; border: 1px solid #d0d0d0; min-width: 200px; padding: 5px;")
        self.institution_combo.currentIndexChanged.connect(self.filter_by_institution)
        search_layout.addWidget(self.institution_combo)

        # إضافة عداد السجلات
        records_count_label = QLabel("عدد السجلات:")
        records_count_label.setFont(QFont("Calibri", 13, QFont.Bold))
        records_count_label.setFixedHeight(40)  # توحيد الارتفاع
        records_count_label.setAlignment(Qt.AlignVCenter)
        search_layout.addWidget(records_count_label)

        self.records_count_display = QLabel("0")
        self.records_count_display.setFont(QFont("Calibri", 13, QFont.Bold))
        self.records_count_display.setFixedHeight(40)  # توحيد الارتفاع
        self.records_count_display.setAlignment(Qt.AlignVCenter)
        self.records_count_display.setStyleSheet("color: #0066cc; background-color: #f0f8ff; border: 1px solid #d0d0d0; padding: 5px; border-radius: 3px; min-width: 50px;")
        search_layout.addWidget(self.records_count_display)

        # إضافة مساحة مرنة
        search_layout.addStretch(1)

        # إضافة حقل البحث
        search_label = QLabel("بحث:")
        search_label.setFont(QFont("Calibri", 13, QFont.Bold))
        search_label.setFixedHeight(40)  # توحيد الارتفاع
        search_label.setAlignment(Qt.AlignVCenter)
        search_layout.addWidget(search_label)

        self.search_input = QLineEdit()
        self.search_input.setFont(QFont("Calibri", 13))
        self.search_input.setFixedHeight(40)  # توحيد الارتفاع
        self.search_input.setPlaceholderText("أدخل نص للبحث...")
        self.search_input.textChanged.connect(self.filter_data)
        self.search_input.setStyleSheet("background-color: white; border: 1px solid #d0d0d0; min-width: 200px; padding: 5px;")
        search_layout.addWidget(self.search_input)

        # إضافة زر تحديث
        refresh_button = QPushButton("تحديث")
        refresh_button.setFont(QFont("Calibri", 13, QFont.Bold))
        refresh_button.setFixedHeight(40)  # توحيد الارتفاع
        refresh_button.setStyleSheet("""
            QPushButton {
                background-color: #4CAF50;
                color: white;
                border-radius: 5px;
                padding: 5px 10px;
                min-width: 80px;
            }
            QPushButton:hover {
                background-color: #45a049;
            }
        """)
        refresh_button.clicked.connect(self.refresh_data)
        search_layout.addWidget(refresh_button)

        main_layout.addWidget(search_frame)

        # إنشاء جدول لعرض البيانات
        self.table = QTableWidget()
        self.table.setFont(QFont("Calibri", 13))

        # تعيين خصائص الجدول
        self.table.setAlternatingRowColors(False)  # إلغاء تبديل ألوان الصفوف
        self.table.setSelectionBehavior(QTableWidget.SelectRows)
        self.table.setSelectionMode(QTableWidget.SingleSelection)
        self.table.setEditTriggers(QTableWidget.NoEditTriggers)  # منع التعديل على الجدول

        # تعيين خلفية الجدول باللون الأبيض
        self.table.setStyleSheet("""
            QTableWidget {
                background-color: white;
                gridline-color: #d0d0d0;
            }
            QTableWidget::item {
                background-color: white;
            }
            QTableWidget::item:selected {
                background-color: #e0e0ff;
            }
        """)

        # تنسيق رأس الجدول
        header_font = QFont("Calibri", 13, QFont.Bold)
        self.table.horizontalHeader().setFont(header_font)
        self.table.horizontalHeader().setStyleSheet("background-color: #4a86e8; color: white;")
        self.table.horizontalHeader().setDefaultSectionSize(150)
        self.table.horizontalHeader().setMinimumSectionSize(80)

        # تنسيق الصفوف
        self.table.verticalHeader().setDefaultSectionSize(35)  # ارتفاع الصف

        main_layout.addWidget(self.table)

        # تعيين اتجاه النص من اليمين إلى اليسار
        self.setLayoutDirection(Qt.RightToLeft)

    def connect_to_database(self):
        """الاتصال بقاعدة البيانات"""
        try:
            # استخدام الاتصال المقدم إذا كان متاحاً
            if self.db_connection is not None:
                # التحقق من نوع الاتصال
                if hasattr(self.db_connection, 'cursor'):
                    # إذا كان اتصال sqlite3 مباشر
                    return self.db_connection
                elif hasattr(self.db_connection, 'databaseName'):
                    # إذا كان كائن QSqlDatabase، قم بإنشاء اتصال sqlite3 جديد
                    db_name = self.db_connection.databaseName()
                    print(f"تحويل اتصال QSqlDatabase إلى اتصال sqlite3 من المسار: {db_name}")
                    return sqlite3.connect(db_name)
                else:
                    print("نوع اتصال قاعدة البيانات غير معروف، إنشاء اتصال جديد")

            # إنشاء اتصال جديد باستخدام المسار المحدد
            conn = sqlite3.connect(self.db_path)
            return conn
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل الاتصال بقاعدة البيانات: {e}")
            return None

    def load_data(self):
        """تحميل البيانات من جدول امتحانات"""
        conn = self.connect_to_database()
        if not conn:
            return

        try:
            cursor = conn.cursor()

            # الحصول على أسماء الأعمدة
            cursor.execute("PRAGMA table_info(امتحانات)")
            columns_info = cursor.fetchall()
            columns = [col[1] for col in columns_info]

            # تحميل المستويات المتاحة في قائمة المستويات المنسدلة
            self.level_combo.clear()
            self.level_combo.addItem("جميع المستويات")

            try:
                # الحصول على المستويات الفريدة
                cursor.execute("SELECT DISTINCT المستوى FROM امتحانات WHERE المستوى IS NOT NULL AND المستوى != '' ORDER BY المستوى")
                levels = cursor.fetchall()

                # إضافة المستويات إلى القائمة المنسدلة
                for level in levels:
                    self.level_combo.addItem(level[0])

                # تحميل المؤسسات الأصلية المتاحة
                self.institution_combo.clear()
                self.institution_combo.addItem("جميع المؤسسات")

                cursor.execute("SELECT DISTINCT المؤسسة_الأصلية FROM امتحانات WHERE المؤسسة_الأصلية IS NOT NULL AND المؤسسة_الأصلية != '' ORDER BY المؤسسة_الأصلية")
                institutions = cursor.fetchall()

                # إضافة المؤسسات إلى القائمة المنسدلة
                for institution in institutions:
                    self.institution_combo.addItem(institution[0])

                # تحديث عدد المترشحين الإجمالي
                cursor.execute("SELECT COUNT(*) FROM امتحانات")
                total_count = cursor.fetchone()[0]
                self.total_candidates_spin.setValue(total_count)
                self.records_count_display.setText(str(total_count))

            except Exception:
                pass

            # تحديد الأعمدة التي سيتم عرضها (استبعاد عمود المعرف وعمود مركز_الامتحان وعمود المستوى)
            excluded_columns = ["id", "مركز_الامتحان", "المستوى"]
            display_columns = [col for col in columns if col not in excluded_columns]

            # إضافة عمود مربع الاختيار في البداية
            self.table.setColumnCount(len(display_columns) + 1)
            headers = ["اختيار"] + display_columns
            self.table.setHorizontalHeaderLabels(headers)

            # الحصول على البيانات مرتبة حسب رقم الامتحان
            cursor.execute("""
                SELECT * FROM امتحانات
                ORDER BY CASE
                    WHEN رقم_الامتحان IS NULL OR رقم_الامتحان = '' THEN 9999999
                    ELSE CAST(رقم_الامتحان AS INTEGER)
                END
            """)
            data = cursor.fetchall()

            # تعيين عدد الصفوف
            self.table.setRowCount(len(data))

            # ملء الجدول بالبيانات
            for row_idx, row_data in enumerate(data):
                # إضافة مربع اختيار في العمود الأول
                checkbox = QCheckBox()
                checkbox.setStyleSheet("QCheckBox { margin: auto; }")
                self.table.setCellWidget(row_idx, 0, checkbox)
                
                display_col_idx = 1  # البدء من العمود الثاني
                for col_idx, value in enumerate(row_data):
                    if columns[col_idx] in excluded_columns:
                        continue

                    # إنشاء عنصر جديد
                    item = QTableWidgetItem(str(value) if value is not None else "")
                    item.setTextAlignment(Qt.AlignCenter)

                    # تعيين خط غامق للنص
                    font = QFont("Calibri", 13, QFont.Bold)
                    item.setFont(font)

                    # إذا كان العمود هو رقم_الامتحان، نقوم بتخزين القيمة الرقمية
                    if columns[col_idx] == "رقم_الامتحان" and value:
                        try:
                            # محاولة تحويل القيمة إلى رقم
                            num_value = int(value)
                            # تخزين القيمة الرقمية لضمان الترتيب الصحيح
                            item.setData(Qt.UserRole, num_value)
                        except (ValueError, TypeError):
                            pass

                    self.table.setItem(row_idx, display_col_idx, item)
                    display_col_idx += 1

            # تعيين ارتفاع رأس الجدول
            self.table.horizontalHeader().setFixedHeight(40)
            self.table.horizontalHeader().setStyleSheet("QHeaderView::section { background-color: #4a86e8; color: white; font-weight: bold; height: 40px; }")

            # تعيين ارتفاع الصفوف
            for row in range(self.table.rowCount()):
                self.table.setRowHeight(row, 30)

            # تعيين عرض ثابت للأعمدة
            column_widths = {
                "اختيار": 80,
                "الرمز": 120,
                "رقم_الامتحان": 100,
                "الجنس": 80,
                "الاسم_الكامل": 250,
                "تاريخ_الازدياد": 120,
                "المؤسسة_الأصلية": 300,
                "القسم": 150,
                "القاعة": 150
            }

            # تطبيق العرض المخصص لكل عمود
            for col_idx, col_name in enumerate(headers):
                if col_name in column_widths:
                    self.table.setColumnWidth(col_idx, column_widths[col_name])
                else:
                    # عرض افتراضي للأعمدة غير المحددة
                    self.table.setColumnWidth(col_idx, 200)

            # منع المستخدم من تغيير عرض الأعمدة
            self.table.horizontalHeader().setSectionResizeMode(QHeaderView.Fixed)

            # إعادة تطبيق تصفية البحث إذا كان هناك نص بحث
            if self.search_input.text().strip():
                self.filter_data()

            conn.close()

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل تحميل البيانات: {e}")
            if conn:
                conn.close()

    def refresh_data(self):
        """تحديث البيانات في الجدول"""
        # حفظ موضع التمرير الحالي
        scrollbar_position = self.table.verticalScrollBar().value()

        # تحديث البيانات
        self.load_data()

        # استعادة موضع التمرير
        self.table.verticalScrollBar().setValue(scrollbar_position)

        # عرض رسالة تأكيد
        QMessageBox.information(self, "تحديث", "تم تحديث البيانات بنجاح.")

    def filter_by_level(self):
        """تصفية البيانات حسب المستوى المحدد"""
        selected_level = self.level_combo.currentText()
        selected_institution = self.institution_combo.currentText()

        # إذا كان المستوى هو الخيار الافتراضي، أظهر جميع الصفوف
        if selected_level == "جميع المستويات":
            # إعادة تطبيق تصفية المؤسسة إذا كانت محددة
            if selected_institution != "جميع المؤسسات":
                self.filter_by_institution()
            else:
                for row in range(self.table.rowCount()):
                    self.table.setRowHidden(row, False)
                
                # تحديث عدد المترشحين الإجمالي
                self.update_records_count()
            
            # إعادة تطبيق تصفية البحث إذا كان هناك نص بحث
            if self.search_input.text().strip():
                self.filter_data()
            return

        # تطبيق تصفية المستوى والمؤسسة معاً
        self.apply_combined_filters()

    def filter_by_institution(self):
        """تصفية البيانات حسب المؤسسة الأصلية المحددة"""
        selected_institution = self.institution_combo.currentText()
        selected_level = self.level_combo.currentText()

        # إذا كانت المؤسسة هي الخيار الافتراضي، أظهر جميع الصفوف
        if selected_institution == "جميع المؤسسات":
            # إعادة تطبيق تصفية المستوى إذا كان محدداً
            if selected_level != "جميع المستويات":
                self.filter_by_level()
            else:
                for row in range(self.table.rowCount()):
                    self.table.setRowHidden(row, False)
                
                # تحديث عدد المترشحين الإجمالي
                self.update_records_count()
            
            # إعادة تطبيق تصفية البحث إذا كان هناك نص بحث
            if self.search_input.text().strip():
                self.filter_data()
            return

        # تطبيق تصفية المستوى والمؤسسة معاً
        self.apply_combined_filters()

    def apply_combined_filters(self):
        """تطبيق التصفية المجمعة للمستوى والمؤسسة"""
        selected_level = self.level_combo.currentText()
        selected_institution = self.institution_combo.currentText()

        conn = self.connect_to_database()
        if not conn:
            return

        try:
            cursor = conn.cursor()

            # بناء الاستعلام حسب المرشحات المحددة
            conditions = []
            params = []

            if selected_level != "جميع المستويات":
                conditions.append("المستوى = ?")
                params.append(selected_level)

            if selected_institution != "جميع المؤسسات":
                conditions.append("المؤسسة_الأصلية = ?")
                params.append(selected_institution)

            where_clause = " AND ".join(conditions) if conditions else "1=1"
            
            # الحصول على معرفات الطلاب المطابقين للشروط
            query = f"SELECT id FROM امتحانات WHERE {where_clause}"
            cursor.execute(query, params)
            student_ids = [row[0] for row in cursor.fetchall()]

            # تحديث عدد المترشحين
            self.records_count_display.setText(str(len(student_ids)))

            if not student_ids:
                for row in range(self.table.rowCount()):
                    self.table.setRowHidden(row, True)
                conn.close()
                return

            # إعادة تحميل البيانات المصفاة
            placeholders = ', '.join(['?' for _ in student_ids])
            data_query = f"""
                SELECT * FROM امتحانات
                WHERE id IN ({placeholders})
                ORDER BY CASE
                    WHEN رقم_الامتحان IS NULL OR رقم_الامتحان = '' THEN 9999999
                    ELSE CAST(رقم_الامتحان AS INTEGER)
                END
            """
            cursor.execute(data_query, student_ids)
            students_data = cursor.fetchall()

            # تفريغ الجدول
            self.table.setRowCount(0)

            # الحصول على أسماء الأعمدة
            cursor.execute("PRAGMA table_info(امتحانات)")
            columns_info = cursor.fetchall()
            columns = [col[1] for col in columns_info]

            # تحديد الأعمدة التي سيتم عرضها
            excluded_columns = ["id", "مركز_الامتحان", "المستوى"]
            display_columns = [col for col in columns if col not in excluded_columns]

            # إضافة عمود مربع الاختيار في البداية
            self.table.setColumnCount(len(display_columns) + 1)
            headers = ["اختيار"] + display_columns
            self.table.setHorizontalHeaderLabels(headers)

            # تعيين عدد الصفوف
            self.table.setRowCount(len(students_data))

            # ملء الجدول بالبيانات
            for row_idx, row_data in enumerate(students_data):
                # إضافة مربع اختيار في العمود الأول
                checkbox = QCheckBox()
                checkbox.setStyleSheet("QCheckBox { margin: auto; }")
                self.table.setCellWidget(row_idx, 0, checkbox)
                
                display_col_idx = 1  # البدء من العمود الثاني
                for col_idx, value in enumerate(row_data):
                    if columns[col_idx] in excluded_columns:
                        continue

                    # إنشاء عنصر جديد
                    item = QTableWidgetItem(str(value) if value is not None else "")
                    item.setTextAlignment(Qt.AlignCenter)

                    # تعيين خط غامق للنص
                    font = QFont("Calibri", 13, QFont.Bold)
                    item.setFont(font)

                    # إذا كان العمود هو رقم_الامتحان، نقوم بتخزين القيمة الرقمية
                    if columns[col_idx] == "رقم_الامتحان" and value:
                        try:
                            # محاولة تحويل القيمة إلى رقم
                            num_value = int(value)
                            # تخزين القيمة الرقمية لضمان الترتيب الصحيح
                            item.setData(Qt.UserRole, num_value)
                        except (ValueError, TypeError):
                            pass

                    self.table.setItem(row_idx, display_col_idx, item)
                    display_col_idx += 1

            # تعيين ارتفاع رأس الجدول
            self.table.horizontalHeader().setFixedHeight(40)
            self.table.horizontalHeader().setStyleSheet("QHeaderView::section { background-color: #4a86e8; color: white; font-weight: bold; height: 40px; }")

            # تعيين ارتفاع الصفوف
            for row in range(self.table.rowCount()):
                self.table.setRowHeight(row, 30)

            # تعيين عرض ثابت للأعمدة
            column_widths = {
                "اختيار": 80,
                "الرمز": 120,
                "رقم_الامتحان": 100,
                "الجنس": 80,
                "الاسم_الكامل": 250,
                "تاريخ_الازدياد": 120,
                "المؤسسة_الأصلية": 300,
                "القسم": 150,
                "القاعة": 150
            }

            # تطبيق العرض المخصص لكل عمود
            for col_idx, col_name in enumerate(headers):
                if col_name in column_widths:
                    self.table.setColumnWidth(col_idx, column_widths[col_name])
                else:
                    # عرض افتراضي للأعمدة غير المحددة
                    self.table.setColumnWidth(col_idx, 200)

            # منع المستخدم من تغيير عرض الأعمدة
            self.table.horizontalHeader().setSectionResizeMode(QHeaderView.Fixed)

            # إعادة تطبيق تصفية البحث إذا كان هناك نص بحث
            if self.search_input.text().strip():
                self.filter_data()

            # إعادة تطبيق تصفية الغياب إذا كانت هناك سجلات محددة
            if self.get_selected_records():
                self.record_absence()

            conn.close()

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في تصفية البيانات: {e}")
            if conn:
                conn.close()

    def update_records_count(self):
        """تحديث عداد السجلات المرئية"""
        visible_count = 0
        for row in range(self.table.rowCount()):
            if not self.table.isRowHidden(row):
                visible_count += 1
        self.records_count_display.setText(str(visible_count))

    def filter_data(self):
        """تصفية البيانات حسب نص البحث في جميع الأعمدة في الوقت الفعلي"""
        search_text = self.search_input.text().lower().strip()

        if not search_text:
            # إذا كان حقل البحث فارغًا، أظهر جميع الصفوف
            for row in range(self.table.rowCount()):
                self.table.setRowHidden(row, False)
            self.update_records_count()
            return

        # البحث في جميع الأعمدة المرئية
        for row in range(self.table.rowCount()):
            row_visible = False

            # البحث في جميع الأعمدة
            for col in range(self.table.columnCount()):
                item = self.table.item(row, col)
                if item and search_text in item.text().lower():
                    row_visible = True
                    break

            # إخفاء أو إظهار الصف حسب نتيجة البحث
            self.table.setRowHidden(row, not row_visible)

        # تحديث عداد السجلات بعد التصفية
        self.update_records_count()

    def get_selected_records(self):
        """الحصول على السجلات المحددة"""
        selected_records = []
        
        for row in range(self.table.rowCount()):
            if not self.table.isRowHidden(row):
                checkbox = self.table.cellWidget(row, 0)
                if checkbox and checkbox.isChecked():
                    record_data = {}
                    # جمع بيانات الصف
                    for col in range(1, self.table.columnCount()):  # البدء من العمود الثاني (تجاهل عمود الاختيار)
                        header = self.table.horizontalHeaderItem(col).text()
                        item = self.table.item(row, col)
                        record_data[header] = item.text() if item else ""
                    selected_records.append(record_data)
                    
        return selected_records

    def save_absence_records(self, records, absence_data):
        """حفظ سجلات الغياب في قاعدة البيانات"""
        conn = self.connect_to_database()
        if not conn:
            return False
            
        try:
            cursor = conn.cursor()
            
            # إنشاء جدول الغياب إذا لم يكن موجوداً
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS سجل_الغياب (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    رقم_الامتحان TEXT,
                    الاسم_الكامل TEXT,
                    المادة TEXT,
                    تاريخ_الغياب DATE,
                    الفترة TEXT,
                    ملاحظات TEXT,
                    تاريخ_التسجيل DATETIME DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            # إدراج سجلات الغياب
            for record in records:
                cursor.execute('''
                    INSERT INTO سجل_الغياب 
                    (رقم_الامتحان, الاسم_الكامل, المادة, تاريخ_الغياب, الفترة, ملاحظات)
                    VALUES (?, ?, ?, ?, ?, ?)
                ''', (
                    record.get('رقم_الامتحان', ''),
                    record.get('الاسم_الكامل', ''),
                    absence_data['subject'],
                    absence_data['date'],
                    absence_data['period'],
                    absence_data['notes']
                ))
            
            conn.commit()
            conn.close()
            return True
            
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في حفظ سجلات الغياب: {str(e)}")
            if conn:
                conn.close()
            return False

    def record_absence(self):
        """تسجيل الغياب للسجلات المحددة"""
        selected_records = self.get_selected_records()
        
        if not selected_records:
            QMessageBox.warning(self, "تنبيه", "الرجاء تحديد مترشح واحد على الأقل لتسجيل الغياب.")
            return
            
        # استيراد واستخدام نافذة الغياب من sub222_window.py
        try:
            from sub222_window import show_absence_dialog
            
            success, output_path, message = show_absence_dialog(
                parent=self,
                selected_count=len(selected_records),
                selected_records=selected_records
            )
            
            if success:
                # إلغاء تحديد جميع مربعات الاختيار بعد إنشاء التقرير
                self.clear_all_selections()
                
                QMessageBox.information(
                    self,
                    "نجح العملية",
                    f"تم تسجيل الغياب وإنشاء التقرير بنجاح.\n\n"
                    f"عدد المترشحين الغائبين: {len(selected_records)}\n"
                    f"مسار التقرير: {output_path}"
                )
            else:
                QMessageBox.critical(self, "خطأ", f"فشل في إنشاء التقرير: {message}")
                
        except ImportError:
            QMessageBox.critical(self, "خطأ", "ملف sub222_window غير موجود.")
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء إنشاء تقرير الغياب: {str(e)}")

    def clear_all_selections(self):
        """إلغاء تحديد جميع مربعات الاختيار في الجدول"""
        for row in range(self.table.rowCount()):
            checkbox = self.table.cellWidget(row, 0)
            if checkbox and isinstance(checkbox, QCheckBox):
                checkbox.setChecked(False)

    def generate_absence_report(self, records, absence_data):
        """إنشاء تقرير الغياب"""
        try:
            # حفظ بيانات الغياب في قاعدة البيانات أولاً
            save_success = self.save_absence_records(records, absence_data)
            
            if not save_success:
                QMessageBox.warning(self, "تنبيه", "فشل في حفظ سجلات الغياب في قاعدة البيانات.")
                return
            
            # استيراد دالة طباعة تقرير الغياب من print13.py
            from print13 import print_absence_report
            
            # إنشاء التقرير
            success, output_path, message = print_absence_report(
                parent=self,
                selected_records=records,
                absence_data=absence_data
            )
            
            if success:
                QMessageBox.information(
                    self,
                    "نجح العملية",
                    f"تم تسجيل الغياب وإنشاء التقرير بنجاح.\n\n"
                    f"عدد المترشحين الغائبين: {len(records)}\n"
                    f"المادة: {absence_data['subject']}\n"
                    f"التاريخ: {absence_data['date']}\n"
                    f"الفترة: {absence_data['period']}\n\n"
                    f"مسار التقرير: {output_path}"
                )
            else:
                QMessageBox.critical(self, "خطأ", f"فشل في إنشاء التقرير: {message}")
                
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء إنشاء تقرير الغياب: {str(e)}")

    def select_and_order_levels(self):
        """اختيار وترتيب المستويات"""
        conn = self.connect_to_database()
        if not conn:
            return []

        try:
            cursor = conn.cursor()
            
            # الحصول على المستويات المتاحة
            cursor.execute("SELECT DISTINCT المستوى FROM امتحانات WHERE المستوى IS NOT NULL AND المستوى != '' ORDER BY المستوى")
            levels = [row[0] for row in cursor.fetchall()]
            
            conn.close()
            
            if not levels:
                QMessageBox.warning(self, "تنبيه", "لا توجد مستويات متاحة.")
                return []

            # إنشاء مربع حوار لاختيار وترتيب المستويات
            from PyQt5.QtWidgets import QListWidget
            level_dialog = QDialog(self)
            level_dialog.setWindowTitle("اختيار وترتيب المستويات")
            level_dialog.setMinimumWidth(400)
            level_dialog.setMinimumHeight(300)
            
            layout = QVBoxLayout(level_dialog)
            
            # إضافة عنوان
            title_label = QLabel("اختر المستويات وحدد ترتيبها:")
            title_label.setFont(QFont("Calibri", 14, QFont.Bold))
            layout.addWidget(title_label)
            
            # قائمة المستويات
            level_list = QListWidget()
            level_list.setSelectionMode(QListWidget.MultiSelection)
            for level in levels:
                level_list.addItem(level)
            
            layout.addWidget(level_list)
            
            # أزرار
            buttons_layout = QHBoxLayout()
            ok_button = QPushButton("موافق")
            cancel_button = QPushButton("إلغاء")
            
            ok_button.clicked.connect(level_dialog.accept)
            cancel_button.clicked.connect(level_dialog.reject)
            
            buttons_layout.addWidget(ok_button)
            buttons_layout.addWidget(cancel_button)
            layout.addLayout(buttons_layout)
            
            if level_dialog.exec_() == QDialog.Accepted:
                selected_levels = []
                for i in range(level_list.count()):
                    item = level_list.item(i)
                    if item.isSelected():
                        selected_levels.append(item.text())
                return selected_levels if selected_levels else levels
            
            return []
            
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في جلب المستويات: {str(e)}")
            if conn:
                conn.close()
            return []

    def add_exam_numbers_advanced(self):
        """إضافة أرقام الامتحان للطلاب مع إمكانية اختيار طريقة الترتيب والمستويات المتعددة"""
        start_number = self.exam_number_spin.value()

        # عرض مربع حوار لاختيار وترتيب المستويات
        ordered_levels = self.select_and_order_levels()

        if not ordered_levels:
            return

        # قائمة الأعمدة المتاحة للترتيب
        available_columns = [
            "القسم",
            "ر.ت",
            "الجنس",
            "الاسم_الكامل",
            "تاريخ_الازدياد",
            "الرمز",
            "المؤسسة_الأصلية"
        ]

        # إنشاء نافذة إضافة أرقام الامتحان
        try:
            sort_dialog = ExamNumbersDialog(
                parent=self,
                start_number=start_number,
                available_columns=available_columns,
                ordered_levels=ordered_levels
            )

            # عرض مربع الحوار
            if sort_dialog.exec_() != QDialog.Accepted:
                return

            # الحصول على الأعمدة المحددة بالترتيب
            sort_columns = sort_dialog.get_selected_columns()

            if not sort_columns:
                QMessageBox.warning(self, "تنبيه", "الرجاء اختيار عمود واحد على الأقل للترتيب.")
                return

            # الحصول على رقم البداية من الحقل الجديد
            start_number = sort_dialog.get_start_number()
            
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في فتح نافذة إضافة أرقام الامتحان: {e}")
            return

        # تأكيد العملية
        columns_text = "\n".join([f"{i+1}. {col}" for i, col in enumerate(sort_columns)])
        levels_text = "\n".join([f"{i+1}. {level}" for i, level in enumerate(ordered_levels)])

        reply = QMessageBox.question(
            self,
            "تأكيد إضافة أرقام الامتحان",
            f"هل تريد توليد أرقام امتحان للمستويات التالية بدءًا من الرقم {start_number}؟\n\n"
            f"المستويات المحددة:\n{levels_text}\n\n"
            f"سيتم ترتيب المترشحين حسب الأعمدة التالية:\n{columns_text}",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

        if reply != QMessageBox.Yes:
            return

        QMessageBox.information(self, "معلومات", "هذه الوظيفة قيد التطوير.")

    def assign_rooms(self):
        """تعيين القاعات للطلاب"""
        try:
            # استدعاء نافذة ترقيم القاعات
            room_dialog = RoomAssignmentDialog(parent=self, db_path=self.db_path)
            room_dialog.exec_()
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في فتح نافذة ترقيم القاعات: {e}")

    def import_candidates(self):
        """استيراد المترشحين من اللوائح أو من ملف إكسل"""
        # إنشاء مربع حوار للاختيار بين استيراد من قاعدة البيانات الحالية أو من ملف إكسل
        import_dialog = QDialog(self)
        import_dialog.setWindowTitle("استيراد المترشحين")
        import_dialog.setMinimumWidth(400)
        import_dialog.setMinimumHeight(200)
        import_dialog.setStyleSheet("background-color: #00FFFF;")

        layout = QVBoxLayout(import_dialog)

        # إضافة عنوان
        title_label = QLabel("اختر مصدر استيراد بيانات المترشحين:")
        title_label.setFont(QFont("Calibri", 14, QFont.Bold))
        title_label.setStyleSheet("color: darkblue; border: 1.5px solid blue; padding: 5px;")
        title_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(title_label)

        # إضافة خيارات الاستيراد
        db_button = QPushButton("استيراد من قاعدة البيانات الحالية")
        db_button.setFont(QFont("Calibri", 13, QFont.Bold))
        db_button.setMinimumHeight(40)
        db_button.setStyleSheet("""
            QPushButton {
                background-color: #0066cc;
                color: white;
                border-radius: 5px;
            }
            QPushButton:hover {
                background-color: #0055aa;
            }
        """)
        layout.addWidget(db_button)

        excel_button = QPushButton("استيراد من ملف إكسل")
        excel_button.setFont(QFont("Calibri", 13, QFont.Bold))
        excel_button.setMinimumHeight(40)
        excel_button.setStyleSheet("""
            QPushButton {
                background-color: #009900;
                color: white;
                border-radius: 5px;
            }
            QPushButton:hover {
                background-color: #008800;
            }
        """)
        layout.addWidget(excel_button)

        # إضافة زر إلغاء
        cancel_button = QPushButton("إلغاء")
        cancel_button.setFont(QFont("Calibri", 13, QFont.Bold))
        cancel_button.setMinimumHeight(40)
        cancel_button.setStyleSheet("""
            QPushButton {
                background-color: #cc0000;
                color: white;
                border-radius: 5px;
            }
            QPushButton:hover {
                background-color: #bb0000;
            }
        """)
        layout.addWidget(cancel_button)

        # ربط الأزرار بالوظائف
        db_button.clicked.connect(lambda: self.handle_import_choice("db", import_dialog))
        excel_button.clicked.connect(lambda: self.handle_import_choice("excel", import_dialog))
        cancel_button.clicked.connect(import_dialog.reject)

        # عرض مربع الحوار
        import_dialog.exec_()

    def handle_import_choice(self, choice, dialog):
        """معالجة اختيار مصدر الاستيراد"""
        dialog.accept()

        if choice == "db":
            # استيراد من قاعدة البيانات الحالية
            self.import_from_database()
        elif choice == "excel":
            # استيراد من ملف إكسل
            self.import_from_excel()

    def import_from_excel(self):
        """استيراد البيانات من ملف إكسل"""
        try:
            # إنشاء اتصال بقاعدة البيانات
            conn = self.connect_to_database()
            if not conn:
                QMessageBox.critical(self, "خطأ", "فشل الاتصال بقاعدة البيانات")
                return

            try:
                from sub23_window import ExcelImportWindow
                excel_import_window = ExcelImportWindow(self, conn)
                excel_import_window.show()
            except ImportError:
                QMessageBox.critical(self, "خطأ", "ملف sub23_window غير موجود.")
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء فتح نافذة استيراد البيانات من إكسل:\n{str(e)}")

    def import_from_database(self):
        """استيراد المترشحين من اللوائح حسب السنة الدراسية والمستوى"""
        QMessageBox.information(self, "معلومات", "هذه الوظيفة قيد التطوير.")

    def open_exam_schedule(self):
        """فتح نافذة جدولة الامتحان"""
        try:
            from sub26_window import ExamScheduleProfessional
            schedule_dialog = ExamScheduleProfessional(parent=self)
            schedule_dialog.exec_()
        except ImportError:
            QMessageBox.critical(self, "خطأ", "ملف sub26_window غير موجود.")
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في فتح نافذة جدولة الامتحان: {e}")

    def open_print_reports(self):
        """فتح نافذة طباعة المحاضر واللوائح"""
        try:
            from sub40_window import Sub40Window
            reports_dialog = Sub40Window(parent=self, db_path=self.db_path)
            reports_dialog.exec_()
        except ImportError:
            QMessageBox.critical(self, "خطأ", "ملف sub40_window غير موجود.")
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في فتح نافذة طباعة المحاضر واللوائح: {e}")

    def delete_all_data(self):
        """حذف جميع بيانات جدول امتحانات"""
        # تأكيد العملية
        reply = QMessageBox.question(
            self,
            "تأكيد الحذف",
            "هل أنت متأكد من حذف جميع بيانات جدول امتحانات؟\n\nهذا الإجراء لا يمكن التراجع عنه!",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

        if reply != QMessageBox.Yes:
            return

        try:
            # الاتصال بقاعدة البيانات
            conn = self.connect_to_database()
            if not conn:
                return

            cursor = conn.cursor()

            # حذف جميع البيانات من جدول امتحانات
            cursor.execute("DELETE FROM امتحانات")

            # حفظ التغييرات
            conn.commit()
            conn.close()

            # تحديث الجدول
            self.load_data()

            # عرض رسالة نجاح
            QMessageBox.information(
                self,
                "تم الحذف",
                "تم حذف جميع بيانات جدول امتحانات بنجاح."
            )

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في حذف البيانات: {e}")

    def print_absence_reports(self):
        """طباعة تقارير الغياب - وظيفة فارغة حالياً"""
        QMessageBox.information(self, "معلومات", "هذه الوظيفة قيد التطوير.")

    def select_and_reprint_absence(self, selected_records, absence_data):
        """تحديد المترشحين الغائبين في الجدول وإعادة طباعة التقرير"""
        try:
            # إلغاء تحديد جميع المربعات أولاً
            self.clear_all_selections()
            
            # البحث عن المترشحين في الجدول وتحديدهم
            records_found = 0
            for record in selected_records:
                exam_number = record.get('رقم_الامتحان', '')
                if exam_number:
                    # البحث عن المترشح في الجدول
                    for row in range(self.table.rowCount()):
                        if not self.table.isRowHidden(row):
                            # البحث في عمود رقم الامتحان
                            for col in range(1, self.table.columnCount()):
                                header = self.table.horizontalHeaderItem(col).text()
                                if header == 'رقم_الامتحان':
                                    item = self.table.item(row, col)
                                    if item and item.text() == exam_number:
                                        # تحديد هذا المترشح
                                        checkbox = self.table.cellWidget(row, 0)
                                        if checkbox and isinstance(checkbox, QCheckBox):
                                            checkbox.setChecked(True)
                                            records_found += 1
                                        break
            
            if records_found > 0:
                # عرض رسالة توضح عدد المترشحين المحددين
                reply = QMessageBox.question(
                    self,
                    "إعادة طباعة التقرير",
                    f"تم تحديد {records_found} مترشح غائب في الجدول.\n\n"
                    f"المادة: {absence_data['subject']}\n"
                    f"التاريخ: {absence_data['date']}\n"
                    f"الفترة: {absence_data['period']}\n\n"
                    f"هل تريد إعادة طباعة التقرير؟",
                    QMessageBox.Yes | QMessageBox.No,
                    QMessageBox.Yes
                )
                
                if reply == QMessageBox.Yes:
                    # إنشاء التقرير مباشرة باستخدام البيانات المحفوظة
                    success, output_path, message = self.create_absence_report_directly(selected_records, absence_data)
                    
                    if success:
                        QMessageBox.information(
                            self,
                            "نجح العملية",
                            f"تم إعادة إنشاء التقرير بنجاح.\n\n"
                            f"عدد المترشحين الغائبين: {len(selected_records)}\n"
                            f"مسار التقرير: {output_path}"
                        )
                    else:
                        QMessageBox.critical(self, "خطأ", f"فشل في إعادة إنشاء التقرير: {message}")
            else:
                QMessageBox.warning(
                    self,
                    "تنبيه",
                    "لم يتم العثور على المترشحين الغائبين في الجدول الحالي.\n"
                    "قد يكونوا مخفيين بسبب تصفية البيانات أو تم حذفهم."
                )
                
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء تحديد المترشحين: {str(e)}")

    def create_absence_report_directly(self, selected_records, absence_data):
        """إنشاء تقرير الغياب مباشرة باستخدام البيانات المحفوظة"""
        try:
            # استيراد دالة طباعة تقرير الغياب من print13.py
            from print13 import print_absence_report
            
            # إنشاء التقرير مباشرة
            success, output_path, message = print_absence_report(
                parent=self,
                selected_records=selected_records,
                absence_data=absence_data
            )
            
            return success, output_path, message
                
        except Exception as e:
            import traceback
            traceback.print_exc()
            return False, None, f"حدث خطأ أثناء إنشاء التقرير: {str(e)}"

    def open_absence_grid(self):
        """فتح نافذة شبكة الغياب"""
        try:
            from print16 import SimpleAbsenceGrid
            
            # إنشاء النافذة
            absence_grid_window = SimpleAbsenceGrid()
            
            # تعيين النافذة كنافذة مشروطة
            absence_grid_window.setWindowModality(Qt.ApplicationModal)
            
            # جعل النافذة تعمل في كامل الشاشة
            absence_grid_window.showMaximized()
            
            # عرض النافذة
            absence_grid_window.show()
            
            # تعطيل النافذة الحالية حتى يتم إغلاق نافذة شبكة الغياب
            self.setEnabled(False)
            
            # ربط إشارة إغلاق النافذة لإعادة تفعيل النافذة الحالية
            def on_window_closed():
                self.setEnabled(True)
                
            # إذا كانت النافذة لها إشارة finished أو closed
            if hasattr(absence_grid_window, 'finished'):
                absence_grid_window.finished.connect(on_window_closed)
            elif hasattr(absence_grid_window, 'closeEvent'):
                # حفظ دالة closeEvent الأصلية
                original_close = absence_grid_window.closeEvent
                
                def new_close_event(event):
                    original_close(event)
                    on_window_closed()
                
                absence_grid_window.closeEvent = new_close_event
            else:
                # إذا لم تكن هناك إشارة، استخدم مؤقت للتحقق
                from PyQt5.QtCore import QTimer
                timer = QTimer()
                
                def check_window():
                    if not absence_grid_window.isVisible():
                        timer.stop()
                        on_window_closed()
                
                timer.timeout.connect(check_window)
                timer.start(500)  # فحص كل 500 مللي ثانية
            
        except ImportError:
            QMessageBox.critical(self, "خطأ", "لم يتم العثور على ملف print16.py اللازم لإنشاء شبكة الغياب.")
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء فتح نافذة شبكة الغياب: {str(e)}")

# تشغيل التطبيق كنافذة مستقلة للاختبار
if __name__ == "__main__":
    app = QApplication(sys.argv)
    app.setLayoutDirection(Qt.RightToLeft)  # تعيين اتجاه التخطيط من اليمين إلى اليسار
    window = ExamSetupWindow()
    window.show()
    sys.exit(app.exec_())

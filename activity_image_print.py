"""
وحدة للطباعة الحرارية للنشاطات باستخدام تقنية تحويل النص إلى صورة
تستخدم مكتبة PIL لإنشاء صورة من النص ثم طباعتها مباشرة على الطابعة
مما يحل مشكلات الطباعة باستخدام الملف النصي
"""

import os
import datetime
import sqlite3
import traceback
from PyQt5.QtPrintSupport import QPrinterInfo
import win32print, win32ui
from PIL import Image, ImageDraw, ImageFont, ImageWin
import arabic_reshaper
from bidi.algorithm import get_display

def get_thermal_printer_name():
    """الحصول على اسم الطابعة الحرارية من قاعدة البيانات"""
    try:
        # تحديد مسار قاعدة البيانات
        script_dir = os.path.dirname(os.path.abspath(__file__))
        db_path = os.path.join(script_dir, "data.db")
        
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        cursor.execute("SELECT الطابعة_الحرارية FROM إعدادات_الطابعة LIMIT 1")
        result = cursor.fetchone()
        conn.close()

        if result and result[0]:
            return result[0]
    except Exception as e:
        print(f"خطأ في الحصول على اسم الطابعة الحرارية: {e}")

    # إذا لم يتم العثور على طابعة في قاعدة البيانات، استخدم الطابعة الافتراضية
    try:
        return win32print.GetDefaultPrinter()
    except Exception as e:
        print(f"خطأ في الحصول على الطابعة الافتراضية: {e}")

    return None

def get_thermal_printer_settings():
    """الحصول على إعدادات الطابعة الحرارية من قاعدة البيانات"""
    settings = {
        'paper_width': 540,    # عرض الورق بالنقاط
        'paper_height': 800,   # ارتفاع الورق بالنقاط
        'scale': 4,            # مضاعف الدقة
        'font_path': r"C:\Windows\Fonts\calibrib.ttf",  # مسار الخط
        'font_size': 33,       # حجم الخط الأساسي
        'row_height': 45,      # ارتفاع الصف
        'margin': 10,          # الهامش
    }

    try:
        # تحديد مسار قاعدة البيانات
        script_dir = os.path.dirname(os.path.abspath(__file__))
        db_path = os.path.join(script_dir, "data.db")
        
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()

        # محاولة استخراج إعدادات الطابعة الحرارية
        try:
            cursor.execute("""
                SELECT عرض_الورق, عدد_الأحرف_في_السطر, اسم_الخط, حجم_الخط, خط_عريض
                FROM إعدادات_الطابعة LIMIT 1
            """)
            result = cursor.fetchone()

            if result:
                # تعديل الإعدادات حسب قاعدة البيانات
                if result[2]:  # اسم الخط
                    font_name = result[2]
                    # تحديد مسار الخط بناءً على اسمه
                    if font_name.lower() == "calibri":
                        if result[4]:  # خط عريض
                            settings['font_path'] = r"C:\Windows\Fonts\calibrib.ttf"
                        else:
                            settings['font_path'] = r"C:\Windows\Fonts\calibri.ttf"
                    elif font_name.lower() == "arial":
                        if result[4]:  # خط عريض
                            settings['font_path'] = r"C:\Windows\Fonts\arialbd.ttf"
                        else:
                            settings['font_path'] = r"C:\Windows\Fonts\arial.ttf"
                    elif font_name.lower() == "amiri":
                        if result[4]:  # خط عريض
                            settings['font_path'] = os.path.join(script_dir, "Amiri-Bold.ttf")
                        else:
                            settings['font_path'] = os.path.join(script_dir, "Amiri-Regular.ttf")
                
                if result[3]:  # حجم الخط
                    settings['font_size'] = int(result[3]) * 2.5  # تحويل حجم الخط إلى مقياس مناسب للصورة
        
        except Exception as e:
            print(f"خطأ في استخراج إعدادات الطابعة الحرارية المتقدمة: {e}")

        conn.close()
    except Exception as e:
        print(f"خطأ في الحصول على إعدادات الطابعة الحرارية: {e}")

    return settings

def get_institution_info():
    """الحصول على معلومات المؤسسة من قاعدة البيانات"""
    info = {
        'institution_name': "المؤسسة التعليمية",
        'school_year': "2024/2025",
    }

    try:
        # تحديد مسار قاعدة البيانات
        script_dir = os.path.dirname(os.path.abspath(__file__))
        db_path = os.path.join(script_dir, "data.db")
        
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()

        # استخراج اسم المؤسسة والسنة الدراسية
        cursor.execute("SELECT المؤسسة, السنة_الدراسية FROM بيانات_المؤسسة LIMIT 1")
        result = cursor.fetchone()
        if result:
            info['institution_name'] = result[0] or info['institution_name']
            info['school_year'] = result[1] or info['school_year']

        conn.close()
    except Exception as e:
        print(f"خطأ في استخراج معلومات المؤسسة: {e}")

    return info

def print_activity_image(activity, date_str=None):
    """
    طباعة نشاط واحد على الطابعة الحرارية باستخدام تقنية تحويل النص إلى صورة
    """
    # تحديد التاريخ إذا لم يتم تمريره
    if not date_str:
        date_str = datetime.datetime.now().strftime("%Y/%m/%d")

    # الحصول على معلومات المؤسسة
    institution_info = get_institution_info()
    
    # الحصول على إعدادات الطابعة
    settings = get_thermal_printer_settings()
    
    try:
        # 1) إعداد الـ supersampling والدقة الحقيقية
        width, height = settings['paper_width'], settings['paper_height']
        SCALE = settings['scale']
        hr_w, hr_h = width * SCALE, height * SCALE
        
        # نرسم على صورة رمادية عالية الدقة
        hr_img = Image.new("L", (hr_w, hr_h), 255)  # خلفية بيضاء
        draw = ImageDraw.Draw(hr_img)
        
        # تحميل الخط
        hr_font = ImageFont.truetype(settings['font_path'], settings['font_size'] * SCALE)
        hr_font_title = ImageFont.truetype(settings['font_path'], int(settings['font_size'] * 1.2) * SCALE)  # خط أكبر للعنوان
        
        hr_margin = settings['margin'] * SCALE
        border_w = 2 * SCALE
        
        y = 10 * SCALE
        
        # 2) رسم الرأس
        header = [
            institution_info['institution_name'],
            f"السنة الدراسية: {institution_info['school_year']}",
            "النشاطات المدرسية",
        ]
        
        for line in header:
            text = get_display(arabic_reshaper.reshape(line))
            bbox = draw.textbbox((0, 0), text, font=hr_font)
            w, h = bbox[2]-bbox[0], bbox[3]-bbox[1]
            x = (hr_w - w)//2
            draw.text((x, y), text, font=hr_font, fill=0)
            y += h + 5 * SCALE
        
        # 3) رسم خط فاصل
        y += 5 * SCALE
        draw.line([(hr_margin, y), (hr_w - hr_margin, y)], fill=0, width=border_w)
        y += 10 * SCALE
        
        # 4) رسم عنوان النشاط
        title = activity.get('title', '')
        if title:
            text = get_display(arabic_reshaper.reshape(title))
            bbox = draw.textbbox((0, 0), text, font=hr_font_title)
            w, h = bbox[2]-bbox[0], bbox[3]-bbox[1]
            x = (hr_w - w)//2  # توسيط العنوان
            draw.text((x, y), text, font=hr_font_title, fill=0)
            y += h + 10 * SCALE
        
        # 5) رسم محتوى النشاط
        activity_text = activity.get('activity', '')
        if activity_text:
            # تقسيم النص إلى أسطر
            max_width = hr_w - 2 * hr_margin
            lines = []
            
            # تقسيم النص إلى أسطر حسب عرض الورقة
            words = activity_text.split()
            current_line = ""
            
            for word in words:
                test_line = current_line + " " + word if current_line else word
                reshaped_text = get_display(arabic_reshaper.reshape(test_line))
                bbox = draw.textbbox((0, 0), reshaped_text, font=hr_font)
                w = bbox[2] - bbox[0]
                
                if w <= max_width:
                    current_line = test_line
                else:
                    if current_line:
                        lines.append(current_line)
                        current_line = word
                    else:
                        lines.append(word)
            
            if current_line:
                lines.append(current_line)
            
            # رسم الأسطر
            for line in lines:
                text = get_display(arabic_reshaper.reshape(line))
                bbox = draw.textbbox((0, 0), text, font=hr_font)
                w, h = bbox[2]-bbox[0], bbox[3]-bbox[1]
                x = hr_w - w - hr_margin  # محاذاة إلى اليمين
                draw.text((x, y), text, font=hr_font, fill=0)
                y += h + 5 * SCALE
        
        # 6) رسم خط فاصل آخر
        y += 5 * SCALE
        draw.line([(hr_margin, y), (hr_w - hr_margin, y)], fill=0, width=border_w)
        y += 10 * SCALE
        
        # 7) رسم التاريخ
        date_text = f"التاريخ: {date_str}"
        text = get_display(arabic_reshaper.reshape(date_text))
        bbox = draw.textbbox((0, 0), text, font=hr_font)
        w, h = bbox[2]-bbox[0], bbox[3]-bbox[1]
        x = hr_w - w - hr_margin  # محاذاة إلى اليمين
        draw.text((x, y), text, font=hr_font, fill=0)
        
        # 8) تصغير الصورة ثم تحويلها ثنائية اللون مع dithering
        img = hr_img.resize((width, height), resample=Image.LANCZOS)
        img = img.convert("1")           # ثنائية اللون مع dithering
        img = img.convert("RGB")         # للطباعة
        
        # 9) إرسال للطابعة
        printer_name = get_thermal_printer_name()
        if not printer_name:
            print("لم يتم العثور على طابعة حرارية")
            return False
            
        hPrinter = win32print.OpenPrinter(printer_name)
        pdc = win32ui.CreateDC()
        pdc.CreatePrinterDC(printer_name)
        pdc.StartDoc("ActivityPrint")
        pdc.StartPage()
        dib = ImageWin.Dib(img)
        dib.draw(pdc.GetSafeHdc(), (0,0,width,height))
        pdc.EndPage()
        pdc.EndDoc()
        pdc.DeleteDC()
        win32print.ClosePrinter(hPrinter)
        
        print(f"تمت طباعة النشاط '{activity.get('title', '')}' بنجاح")
        return True
        
    except Exception as e:
        print(f"خطأ في طباعة النشاط: {e}")
        traceback.print_exc()
        return False

def print_activities_direct(activities, date_str=None):
    """طباعة النشاطات مباشرة على الطابعة الحرارية"""
    if not date_str:
        date_str = datetime.datetime.now().strftime("%Y/%m/%d")
    
    success = True
    # طباعة كل نشاط في ورقة منفصلة
    for activity in activities:
        result = print_activity_image(activity, date_str)
        if not result:
            success = False
            print(f"فشلت طباعة النشاط: {activity.get('title', '')}")
    
    return success

# اختبار الطباعة
if __name__ == "__main__":
    activities = [
        {
            "title": "حفل نهاية السنة",
            "activity": "سيقام حفل نهاية السنة الدراسية يوم الخميس القادم في قاعة الاحتفالات بالمؤسسة. يرجى من جميع التلاميذ الحضور مع أولياء أمورهم."
        },
        {
            "title": "مسابقة الرياضيات",
            "activity": "ستقام مسابقة الرياضيات السنوية يوم الثلاثاء القادم. على الراغبين في المشاركة التسجيل لدى أستاذ الرياضيات."
        }
    ]
    
    print("\n=== بدء اختبار الطباعة باستخدام تحويل النص إلى صورة للنشاطات ===")
    result = print_activities_direct(activities)
    print(f"نتيجة طباعة النشاطات: {'نجاح' if result else 'فشل'}")

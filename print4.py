#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
وحدة طباعة تقرير سجل مخالفات التلميذ باستخدام HTML
"""

import os
import traceback
import sqlite3
from datetime import datetime
import webbrowser
import shutil

def format_bulleted_text(text):
    """تنسيق النص الذي يحتوي على نقاط (تبدأ بـ -) إلى نص منسق بعلامات HTML

    Args:
        text (str): النص الأصلي الذي يحتوي على نقاط تبدأ بـ -

    Returns:
        str: النص المنسق بعلامات HTML
    """
    if not text:
        return ""

    try:
        # تقسيم النص إلى أسطر
        lines = text.split('\n')
        formatted_lines = []

        # بداية قائمة النقاط
        in_list = False

        for line in lines:
            line = line.strip()

            # إذا كان السطر يبدأ بـ -، فهو عنصر في قائمة
            if line.startswith('-'):
                # إذا لم نكن في قائمة بعد، نبدأ قائمة جديدة
                if not in_list:
                    formatted_lines.append('<ul>')
                    in_list = True

                # إضافة عنصر القائمة (مع إزالة الرمز - والمسافة التي تليه)
                list_item = line[1:].strip()
                formatted_lines.append(f'<li>{list_item}</li>')
            else:
                # إذا كنا في قائمة، نغلقها
                if in_list:
                    formatted_lines.append('</ul>')
                    in_list = False

                # إضافة السطر العادي
                if line:
                    formatted_lines.append(f'<p>{line}</p>')

        # إذا انتهى النص ونحن ما زلنا في قائمة، نغلقها
        if in_list:
            formatted_lines.append('</ul>')

        # دمج الأسطر المنسقة
        return ''.join(formatted_lines)

    except Exception as e:
        print(f"خطأ في تنسيق النص ذو النقاط: {e}")
        return text

def get_institution_info(db_path=None):
    """الحصول على معلومات المؤسسة من قاعدة البيانات"""
    try:
        if db_path is None:
            db_path = "data.db"

        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()

        # محاولة قراءة من جدول بيانات_المؤسسة
        try:
            cursor.execute("SELECT المؤسسة, السنة_الدراسية, ImagePath1 FROM بيانات_المؤسسة LIMIT 1")
            institution_row = cursor.fetchone()
            if institution_row:
                institution_data = {
                    'name': institution_row[0],
                    'school_year': institution_row[1],
                    'logo_path': institution_row[2] if institution_row[2] and os.path.exists(institution_row[2]) else None
                }
                return institution_data
        except Exception as e:
            print(f"خطأ في قراءة بيانات المؤسسة: {e}")

        conn.close()
    except Exception as db_error:
        print(f"خطأ في الاتصال بقاعدة البيانات: {db_error}")

    # إرجاع قاموس فارغ في حالة حدوث خطأ
    return {}

def open_pdf(file_path):
    """فتح ملف PDF في المتصفح الافتراضي (للتوافق مع الكود القديم)"""
    return open_html_file(file_path)

def open_html_file(file_path):
    """فتح ملف HTML في المتصفح الافتراضي"""
    try:
        print(f"محاولة فتح الملف: {file_path}")

        # التحقق من وجود الملف
        if os.path.exists(file_path):
            print(f"الملف موجود، حجمه: {os.path.getsize(file_path)} بايت")

            # محاولة فتح الملف باستخدام المتصفح الافتراضي
            try:
                absolute_path = os.path.abspath(file_path)
                file_url = f'file:///{absolute_path}'
                print(f"محاولة فتح الملف في المتصفح: {file_url}")
                result = webbrowser.open(file_url)

                if result:
                    print(f"تم فتح الملف بنجاح: {file_path}")
                    return True
                else:
                    print(f"فشل في فتح الملف باستخدام المتصفح")
                    return False

            except Exception as open_error:
                print(f"خطأ في فتح الملف: {open_error}")
                return False
        else:
            print(f"خطأ: الملف غير موجود: {file_path}")
            return False
    except Exception as e:
        print(f"خطأ في فتح الملف: {str(e)}")
        traceback.print_exc()
        return False

def print_student_violations_record(student_info, violations_records, db_path=None, auto_open=True):
    """طباعة سجل مخالفات التلميذ بتنسيق HTML

    Args:
        student_info (dict): قاموس يحتوي على معلومات التلميذ
        violations_records (list): قائمة من قواميس تحتوي على معلومات المخالفات
        db_path (str, optional): مسار قاعدة البيانات. Defaults to None.
        auto_open (bool, optional): فتح الملف تلقائياً. Defaults to True.

    Returns:
        tuple: (bool, str) - نجاح العملية ومسار الملف
    """
    try:
        # طباعة معلومات التلميذ للتشخيص
        print(f"معلومات التلميذ المستلمة:")
        print(f"الاسم: {student_info.get('name', 'غير محدد')}")
        print(f"الرمز: {student_info.get('code', 'غير محدد')}")
        print(f"المستوى: {student_info.get('level', 'غير محدد')}")
        print(f"القسم: {student_info.get('section', 'غير محدد')}")

        # إنشاء مجلد رئيسي على سطح المكتب
        desktop_path = os.path.join(os.path.expanduser("~"), "Desktop")
        main_reports_dir = os.path.join(desktop_path, "تقارير برنامج المعين في الحراسة العامة")
        os.makedirs(main_reports_dir, exist_ok=True)

        # إنشاء مجلد فرعي لتقارير سجلات المخالفات
        reports_dir = os.path.join(main_reports_dir, "تقارير سجلات المخالفات")
        os.makedirs(reports_dir, exist_ok=True)

        # إنشاء مجلد للموارد (CSS, JS, الصور)
        resources_dir = os.path.join(reports_dir, "resources")
        os.makedirs(resources_dir, exist_ok=True)

        # تحديد اسم ملف الإخراج
        student_name = student_info.get('name', 'بدون_اسم').replace(' ', '_')
        student_code = student_info.get('code', 'بدون_رمز')
        current_date = datetime.now().strftime("%Y%m%d")

        # إنشاء اسم الملف باستخدام اسم التلميذ ورمزه وتاريخ الطباعة
        filename = os.path.join(reports_dir, f"سجل_مخالفات_{student_name}_{student_code}_{current_date}.html")

        # إنشاء ملف HTML
        if create_student_violations_record_html(student_info, violations_records, filename, resources_dir, db_path):
            # فتح الملف إذا كان مطلوباً
            if auto_open:
                if os.path.exists(filename):
                    if open_html_file(filename):
                        return True, filename
                    else:
                        print(f"فشل في فتح الملف: {filename}")
                        return False, filename
                else:
                    print("لم يتم العثور على أي ملف للفتح")
                    return False, filename
            else:
                # إرجاع نجاح العملية ومسار الملف
                return True, filename
        else:
            print("فشل في إنشاء ملف سجل مخالفات التلميذ")
            return False, ""

    except Exception as e:
        print(f"خطأ في طباعة سجل مخالفات التلميذ: {str(e)}")
        traceback.print_exc()
        return False, ""


def create_student_violations_record_html(student_info, violations_records, file_path, resources_dir, db_path=None):
    """إنشاء ملف HTML لسجل مخالفات التلميذ

    Args:
        student_info (dict): قاموس يحتوي على معلومات التلميذ
        violations_records (list): قائمة من قواميس تحتوي على معلومات المخالفات
        file_path (str): مسار حفظ ملف HTML
        resources_dir (str): مجلد الموارد (CSS, JS, الصور)
        db_path (str, optional): مسار قاعدة البيانات. Defaults to None.

    Returns:
        bool: True إذا تم إنشاء الملف بنجاح، False في حالة حدوث خطأ
    """
    try:
        # الحصول على بيانات المؤسسة
        institution_data = get_institution_info(db_path)

        # نسخ شعار المؤسسة إلى مجلد الموارد إذا كان متوفراً
        logo_path = ""
        if institution_data.get('logo_path') and os.path.exists(institution_data.get('logo_path')):
            try:
                logo_filename = os.path.basename(institution_data.get('logo_path'))
                logo_dest_path = os.path.join(resources_dir, logo_filename)
                shutil.copy2(institution_data.get('logo_path'), logo_dest_path)
                logo_path = f"resources/{logo_filename}"
                print(f"تم نسخ شعار المؤسسة إلى: {logo_dest_path}")
            except Exception as logo_error:
                print(f"خطأ في نسخ شعار المؤسسة: {logo_error}")

        # إنشاء محتوى HTML
        html_content = f"""<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>سجل مخالفات التلميذ - {student_info.get('name', '')}</title>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Tajawal:wght@400;700&display=swap');

        body {{
            font-family: 'Calibri', 'Tajawal', Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            background-color: #f9f9f9;
            margin: 0;
            padding: 20px;
            direction: rtl;
        }}

        .container {{
            max-width: 800px;
            margin: 0 auto;
            background-color: #fff;
            padding: 20px;
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
            border-radius: 5px;
        }}

        .header {{
            text-align: center;
            margin-bottom: 20px;
            border-bottom: 2px solid #1a4a7a;
            padding-bottom: 10px;
        }}

        .logo {{
            max-width: 200px;
            max-height: 90px;
            margin: 0 auto;
            display: block;
        }}

        .institution-name {{
            font-size: 22px;
            font-weight: bold;
            color: #1a4a7a;
            margin: 10px 0;
        }}

        .school-year {{
            font-size: 16px;
            color: #1a4a7a;
            margin: 10px 0;
        }}

        .report-title {{
            font-size: 20px;
            font-weight: bold;
            color: #1a4a7a;
            margin: 15px 0;
        }}

        .student-info {{
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
            font-family: 'Calibri', 'Tajawal', Arial, sans-serif;
            font-size: 14px;
            font-weight: bold;
        }}

        .student-info td {{
            border: 1px solid #ddd;
            padding: 8px 12px;
            background-color: #f0f5ff;
        }}

        .student-info td:nth-child(odd) {{
            font-weight: bold;
            background-color: #e0ebff;
            width: 25%;
        }}

        .violations-table {{
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
            font-family: 'Calibri', 'Tajawal', Arial, sans-serif;
        }}

        .violations-table th {{
            background-color: #1a4a7a;
            color: white;
            border: 1px solid #1a4a7a;
            padding: 10px;
            text-align: center;
            font-size: 14px;
            font-weight: bold;
        }}

        .violations-table td {{
            border: 1px solid #ddd;
            padding: 10px;
            vertical-align: top;
            font-size: 13px;
            font-weight: bold;
        }}

        .violations-table tr:nth-child(even) {{
            background-color: #f0f5ff;
        }}

        .violations-table tr:nth-child(odd) {{
            background-color: #fff;
        }}

        .footer {{
            margin-top: 30px;
            border-top: 1px solid #ddd;
            padding-top: 10px;
            display: flex;
            justify-content: space-between;
        }}

        ul {{
            margin: 0;
            padding-right: 20px;
        }}

        li {{
            margin-bottom: 5px;
        }}

        p {{
            margin: 5px 0;
        }}

        @media print {{
            body {{
                background-color: #fff;
                padding: 0;
            }}

            .container {{
                box-shadow: none;
                max-width: 100%;
            }}

            @page {{
                size: A4;
                margin: 1cm;
            }}
        }}
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            {f'<img src="{logo_path}" class="logo" alt="شعار المؤسسة" />' if logo_path else ''}
            <div class="institution-name">{institution_data.get('name', '')}</div>
            <div class="school-year">السنة الدراسية: {institution_data.get('school_year', '')}</div>
            <div class="report-title">سجل مخالفات التلميذ</div>
        </div>

        <table class="student-info">
            <tr>
                <td>اسم التلميذ:</td>
                <td>{student_info.get('name', '')}</td>
                <td>رمز التلميذ:</td>
                <td>{student_info.get('code', '')}</td>
            </tr>
            <tr>
                <td>المستوى:</td>
                <td>{student_info.get('level', '')}</td>
                <td>القسم:</td>
                <td>{student_info.get('section', '')}</td>
            </tr>
        </table>
"""

        # إضافة جدول المخالفات
        if not violations_records:
            html_content += """
        <div style="text-align: center; padding: 20px; background-color: #f0f5ff; border: 1px solid #ddd; border-radius: 5px;">
            <p>لا توجد مخالفات مسجلة</p>
        </div>
"""
        else:
            html_content += """
        <table class="violations-table">
            <thead>
                <tr>
                    <th>التاريخ</th>
                    <th>المادة</th>
                    <th>الأستاذ</th>
                    <th>الملاحظات</th>
                    <th>الإجراءات</th>
                </tr>
            </thead>
            <tbody>
"""

            # إضافة صفوف المخالفات
            for violation in violations_records:
                date_val = str(violation.get('date', '')) if violation.get('date') else ''
                subject_val = str(violation.get('subject', '')) if violation.get('subject') else ''
                teacher_val = str(violation.get('teacher', '')) if violation.get('teacher') else ''
                notes_val = str(violation.get('notes', '')) if violation.get('notes') else ''
                procedures_val = str(violation.get('procedures', '')) if violation.get('procedures') else ''

                # تنسيق النصوص التي تحتوي على نقاط
                notes_formatted = format_bulleted_text(notes_val)
                procedures_formatted = format_bulleted_text(procedures_val)

                html_content += f"""
                <tr>
                    <td style="text-align: center;">{date_val}</td>
                    <td style="text-align: center;">{subject_val}</td>
                    <td style="text-align: center;">{teacher_val}</td>
                    <td>{notes_formatted}</td>
                    <td>{procedures_formatted}</td>
                </tr>
"""

            html_content += """
            </tbody>
        </table>
"""

        # إضافة تذييل التقرير
        html_content += f"""
        <div class="footer">
            <div>توقيع الحراسة العامة</div>
            <div>تاريخ الطباعة: {datetime.now().strftime('%Y-%m-%d')}</div>
        </div>
    </div>

    <script>
        // إضافة زر للطباعة
        window.onload = function() {{
            // إنشاء زر الطباعة
            var printButton = document.createElement('button');
            printButton.innerHTML = 'طباعة التقرير';
            printButton.style.padding = '10px 20px';
            printButton.style.backgroundColor = '#1a4a7a';
            printButton.style.color = 'white';
            printButton.style.border = 'none';
            printButton.style.borderRadius = '5px';
            printButton.style.cursor = 'pointer';
            printButton.style.margin = '20px auto';
            printButton.style.display = 'block';

            // إضافة حدث النقر للطباعة
            printButton.onclick = function() {{
                window.print();
            }};

            // إضافة الزر إلى الصفحة
            document.querySelector('.container').appendChild(printButton);

            // إخفاء الزر عند الطباعة
            var style = document.createElement('style');
            style.innerHTML = '@media print {{ button {{ display: none !important; }} }}';
            document.head.appendChild(style);
        }};
    </script>
</body>
</html>
"""

        # كتابة محتوى HTML إلى الملف
        with open(file_path, 'w', encoding='utf-8') as html_file:
            html_file.write(html_content)

        print(f"تم حفظ ملف HTML بنجاح: {file_path}")
        return True

    except Exception as e:
        print(f"خطأ في إنشاء ملف HTML لسجل مخالفات التلميذ: {str(e)}")
        traceback.print_exc()
        return False


# قسم اختباري
if __name__ == "__main__":
    # بيانات تجريبية للاختبار
    student_info_test = {
        "name": "أحمد المنصوري",
        "code": "A123456",
        "section": "1APIC-1",
        "level": "الأولى إعدادي"
    }

    violations_records_test = [
        {
            "id": 1,
            "date": "2024-01-15",
            "subject": "الرياضيات",
            "teacher": "محمد العلوي",
            "notes": "تأخر عن الحصة بدون مبرر",
            "procedures": "إنذار شفوي"
        },
        {
            "id": 2,
            "date": "2024-02-20",
            "subject": "اللغة العربية",
            "teacher": "فاطمة الزهراء",
            "notes": "عدم إنجاز الواجبات المنزلية",
            "procedures": "إخبار ولي الأمر"
        },
        {
            "id": 3,
            "date": "2024-03-10",
            "subject": "الفيزياء",
            "teacher": "عبد الله الناصري",
            "notes": "سلوك غير لائق أثناء الحصة",
            "procedures": "استدعاء ولي الأمر"
        }
    ]

    # طباعة سجل المخالفات التجريبي
    print_student_violations_record(student_info_test, violations_records_test)

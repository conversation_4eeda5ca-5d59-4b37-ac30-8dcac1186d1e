#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import os
import sqlite3
import traceback
from datetime import datetime
from PyQt5.QtWidgets import (QApplication, QMainWindow, QWidget, QVBoxLayout,
                            QHBoxLayout, QLabel, QPushButton, QFrame,
                            QMessageBox, QFileDialog, QTableWidget, QTableWidgetItem,
                            QHeaderView, QInputDialog, QListWidget, QDialog, QDialogButtonBox,
                            QListWidgetItem, QAbstractItemView, QComboBox, QFormLayout, QTableWidgetSelectionRange)
from PyQt5.QtGui import QFont, QColor, QIcon
from PyQt5.QtCore import Qt, QDateTime
from PyQt5.QtSql import QSqlQuery
from sub100_window import ConfirmationDialogs

# ReportLab imports
from reportlab.lib.pagesizes import A4
from reportlab.lib.units import cm
from reportlab.pdfgen import canvas
from reportlab.pdfbase import pdfmetrics
from reportlab.pdfbase.ttfonts import TTFont

# Arabic text handling
pdfmetrics.registerFont(TTFont("Arabic", "arial.ttf"))
import arabic_reshaper
from bidi.algorithm import get_display

# تضمين دالة طباعة التبرير من print5.py
try:
    from print5 import print_absence_justification
    PRINT5_AVAILABLE = True
    print("تم استيراد دالة طباعة التبرير من print5.py بنجاح")
except ImportError:
    PRINT5_AVAILABLE = False
    print("تحذير: فشل استيراد print_absence_justification من print5.py")
    # Define dummy function if import fails
    def print_absence_justification(*args, **kwargs):
        print("خطأ: دالة الطباعة print_absence_justification غير متوفرة.")
        return False

# تضمين دالة إشعار الشهادات الطبية من print6.py
try:
    from print6 import print_medical_certificate_notification
    PRINT6_AVAILABLE = True
    print("تم استيراد دالة إشعار الشهادات الطبية من print6.py بنجاح")
except ImportError:
    PRINT6_AVAILABLE = False
    print("تحذير: فشل استيراد print_medical_certificate_notification من print6.py")
    # Define dummy function if import fails
    def print_medical_certificate_notification(*args, **kwargs):
        print("خطأ: دالة الطباعة print_medical_certificate_notification غير متوفرة.")
        return False

class AbsenceManagementWindow(QMainWindow):
    def __init__(self, student_code=None, parent=None, db=None, academic_year=None):
        super().__init__(parent)
        # تخزين المعاملات مع طباعة تشخيصية
        print(f"تهيئة نافذة معالجة تبرير الغياب مع: db={db}, academic_year={academic_year}")
        self.student_code = student_code
        self.academic_year = academic_year
        self.selected_record = None  # تهيئة السجل المحدد
        self.selected_record_data = {}  # لتخزين بيانات السجل المحدد
        self.selected_record_id = None  # لتخزين معرف السجل المحدد

        # تحديد مسار قاعدة البيانات
        self.db_path = os.path.join(os.path.dirname(__file__), "data.db")

        # Handle database connection
        if db and hasattr(db, 'isOpen') and db.isOpen():
            print("استخدام اتصال قاعدة البيانات الخارجي")
            self.db = db
            self.using_qsql = True
        else:
            print("إنشاء اتصال قاعدة البيانات المحلي (أو لا يوجد اتصال)")
            self.db = None  # التأكد من تعيينه إلى None
            self.using_qsql = False
            # محاولة إنشاء اتصال sqlite3 للعمليات المحلية إذا لم يتم توفير اتصال QSql
            try:
                conn_test = sqlite3.connect(self.db_path)
                conn_test.close()
                print(f"تم التأكد من إمكانية الاتصال بـ {self.db_path}")
            except Exception as e:
                print(f"خطأ في الاتصال بقاعدة البيانات المحلية {self.db_path}: {e}")
                QMessageBox.critical(self, "خطأ قاعدة البيانات", f"لا يمكن الاتصال بقاعدة البيانات: {self.db_path}\n{e}")
                # قد تحتاج إلى تعطيل بعض الميزات أو إغلاق النافذة هنا

        # التأكد من وجود السنة الدراسية
        if not self.academic_year:
            print("تحذير: لم يتم تمرير السنة الدراسية، محاولة استرجاعها من قاعدة البيانات")
            self.academic_year = self._get_current_academic_year()
            if not self.academic_year:
                 print("خطأ: فشل استرجاع السنة الدراسية.")
                 # يمكنك عرض رسالة خطأ للمستخدم هنا

        print(f"السنة الدراسية المستخدمة: {self.academic_year}")

        self.setWindowTitle("معالجة تبرير الغياب")
        self.setMinimumSize(1200, 600)
        self.setLayoutDirection(Qt.RightToLeft)
        self.setWindowFlags(Qt.Window | Qt.CustomizeWindowHint | Qt.WindowTitleHint)

        # إعداد الواجهة الرئيسية
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        layout = QVBoxLayout(central_widget)

        header_frame = QFrame()
        header_layout = QHBoxLayout(header_frame)
        header_layout.setContentsMargins(5, 5, 5, 5)

        # زر الإغلاق المخصص
        self.close_button = QPushButton("×")
        self.close_button.setFont(QFont("Arial", 16, QFont.Bold))
        self.close_button.setCursor(Qt.PointingHandCursor)  # تغيير المؤشر إلى شكل يد
        self.close_button.setStyleSheet("""
            QPushButton {
                background-color: #e74c3c;
                color: white;
                border-radius: 15px;
                min-width: 30px;
                min-height: 30px;
                max-width: 30px;
                max-height: 30px;
                padding: 0px;
            }
            QPushButton:hover {
                background-color: #c0392b;
            }
        """)
        self.close_button.clicked.connect(self.close)

        # إضافة عنوان
        title_label = QLabel("معالجة تبرير الغياب")
        title_label.setFont(QFont("Calibri", 16, QFont.Bold))
        title_label.setAlignment(Qt.AlignCenter)

        header_layout.addWidget(self.close_button, 0, Qt.AlignLeft) # زر الإغلاق على اليسار
        header_layout.addWidget(title_label, 1) # العنوان يملأ المساحة المتبقية

        layout.addWidget(header_frame) # إضافة إطار العنوان للتخطيط الرئيسي

        # إنشاء أزرار التحكم
        buttons_frame = QFrame()
        buttons_layout = QHBoxLayout(buttons_frame)

        # Delete button
        self.delete_btn = QPushButton("حذف التبرير")
        self.delete_btn.setFont(QFont("Calibri", 13))
        self.delete_btn.setCursor(Qt.PointingHandCursor)  # تغيير المؤشر إلى شكل يد
        self.delete_btn.setStyleSheet("""
            QPushButton {
                background-color: #e74c3c;
                color: white;
                padding: 8px 16px;
                border-radius: 4px;
            }
            QPushButton:hover {
                background-color: #c0392b;
            }
        """)
        self.delete_btn.clicked.connect(self.delete_record)

        # Print button
        self.print_btn = QPushButton("طباعة التبرير")
        self.print_btn.setFont(QFont("Calibri", 13))
        self.print_btn.setCursor(Qt.PointingHandCursor)  # تغيير المؤشر إلى شكل يد
        self.print_btn.setStyleSheet("""
            QPushButton {
                background-color: #3498db;
                color: white;
                padding: 8px 16px;
                border-radius: 4px;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
        """)
        self.print_btn.clicked.connect(self.print_record)

        # --- >> إضافة زر إخبار بوضع شهادة طبية << ---
        self.notify_cert_btn = QPushButton("إخبار بوضع شهادة طبية")
        self.notify_cert_btn.setFont(QFont("Calibri", 13))
        self.notify_cert_btn.setCursor(Qt.PointingHandCursor)  # تغيير المؤشر إلى شكل يد
        self.notify_cert_btn.setStyleSheet("""
            QPushButton {
                background-color: #f39c12; /* لون برتقالي */
                color: white;
                padding: 8px 16px;
                border-radius: 4px;
            }
            QPushButton:hover {
                background-color: #e67e22;
            }
            QPushButton:disabled {
                background-color: #bdc3c7;
                color: #7f8c8d;
            }
        """)
        self.notify_cert_btn.clicked.connect(self.notify_teachers_about_certificates)
        self.notify_cert_btn.setEnabled(False) # تعطيل مبدئي

        # Add buttons to layout
        buttons_layout.addWidget(self.delete_btn)
        buttons_layout.addWidget(self.print_btn)
        buttons_layout.addWidget(self.notify_cert_btn)
        layout.addWidget(buttons_frame)

        # إنشاء جدول لعرض التبريرات
        self.table = QTableWidget()
        self.table.setFont(QFont("Calibri", 13))
        self.table.setCursor(Qt.PointingHandCursor)  # تغيير المؤشر إلى شكل يد
        self.table.setColumnCount(9)  # تقليل عدد الأعمدة من 10 إلى 9 لإزالة عمود المستوى
        self.table.setHorizontalHeaderLabels([
            "الرقم التسلسلي",
            "رمز التلميذ",
            "اسم التلميذ",
            "القسم",
            "تاريخ التبرير",
            "تاريخ البداية",
            "تاريخ النهاية",
            "عدد الأيام",
            "سبب الغياب"
        ])
        header = self.table.horizontalHeader()
        header.setSectionResizeMode(QHeaderView.Stretch)
        header.setCursor(Qt.PointingHandCursor)  # تغيير المؤشر إلى شكل يد للعناوين
        self.table.itemSelectionChanged.connect(self.on_table_selection_changed)
        layout.addWidget(self.table)

        # تعطيل الأزرار في البداية حتى يتم اختيار سجل
        self.enable_action_buttons(False)

        # تحديث الجدول عند فتح النافذة
        self.load_data()

    def load_data(self):
        """تحميل بيانات التبرير من قاعدة البيانات"""
        try:
            print(f"بدء تحميل البيانات... student_code={self.student_code}, academic_year={self.academic_year}")
            self.table.setRowCount(0) # مسح الجدول قبل التحميل

            # تأكد من وجود قيمة للسنة الدراسية
            if not self.academic_year:
                QMessageBox.critical(self, "خطأ", "لم يتم تحديد السنة الدراسية.")
                print("خطأ: السنة الدراسية غير متوفرة في load_data")
                return

            # استعلام SQL محسّن مع إزالة عمود المستوى
            query_text = """
                SELECT id, رمز_التلميذ, اسم_التلميذ, القسم,
                       تاريخ_التبرير, تاريخ_البداية, تاريخ_النهاية,
                       عدد_الأيام, سبب_الغياب
                FROM تبريرات_الغياب
                WHERE السنة_الدراسية = ?
            """
            params = [self.academic_year]

            # تنظيف وإضافة شرط تصفية رمز التلميذ
            if self.student_code:
                query_text += " AND رمز_التلميذ = ?"
                params.append(self.student_code)
                print(f"تمت إضافة تصفية حسب رمز التلميذ: {self.student_code}")

            # إضافة الترتيب
            query_text += " ORDER BY تاريخ_التبرير DESC"
            print(f"الاستعلام النهائي: {query_text}")
            print(f"المعاملات: {params}")

            records = []
            if self.using_qsql:
                query = QSqlQuery(self.db)
                query.prepare(query_text)
                for param in params:
                    query.addBindValue(param)
                if not query.exec_():
                    raise Exception(f"فشل تنفيذ الاستعلام: {query.lastError().text()}")
                while query.next():
                    record = [query.value(i) for i in range(9)] # قراءة 9 أعمدة بدلاً من 10
                    records.append(record)
            else:
                conn = None
                try:
                    conn = sqlite3.connect(self.db_path)
                    cursor = conn.cursor()
                    cursor.execute(query_text, params)
                    records = cursor.fetchall()
                finally:
                    if conn:
                        conn.close()

            print(f"تم العثور على {len(records)} سجل")

            # تحديث الجدول
            self.table.setRowCount(len(records))
            for row, record in enumerate(records):
                for col, value in enumerate(record):
                    item = QTableWidgetItem(str(value) if value is not None else "")
                    item.setTextAlignment(Qt.AlignCenter)
                    item.setFlags(item.flags() & ~Qt.ItemIsEditable)
                    if col == 0: # الرقم التسلسلي
                        item.setBackground(QColor("#f0f0f0"))
                    self.table.setItem(row, col, item)
            print("تم تحديث الجدول بنجاح")
            self.selected_record = None # إلغاء تحديد أي سجل سابق
        except Exception as e:
            error_msg = f"خطأ في تحميل البيانات: {str(e)}"
            QMessageBox.critical(self, "خطأ تحميل البيانات", error_msg)
            print(error_msg)
            traceback.print_exc()

    def on_table_selection_changed(self):
        """معالجة تغيير التحديد في جدول تبريرات الغياب باستخدام selectedItems"""
        try:
            selected_items = self.table.selectedItems()
            if not selected_items:
                # لا يوجد تحديد فعلي
                self.selected_record = None
                self.selected_record_data = {}
                self.selected_record_id = None
                self.enable_action_buttons(False)
                return

            # الحصول على الصف المحدد (نأخذ فقط أول صف محدد)
            selected_row = selected_items[0].row()

            # استخراج معرّف السجل من العمود الأول
            record_id_item = self.table.item(selected_row, 0)
            if not record_id_item:
                self.enable_action_buttons(False)
                return

            record_id = record_id_item.text()

            # جمع كل بيانات الصف المحدد في قاموس
            record_data = {}
            column_names = ["id", "student_code", "student_name", "section",
                           "justification_date", "start_date", "end_date",
                           "days_count", "reason"]  # إزالة "level" من القائمة

            for col, field_name in enumerate(column_names):
                item = self.table.item(selected_row, col)
                record_data[field_name] = item.text() if item else ""

            print(f"تم تخزين بيانات السجل المحدد (الصف {selected_row}): {record_data}")

            # تخزين بيانات السجل المحدد
            self.selected_record = record_data
            self.selected_record_data = record_data  # للتوافقية مع الكود الحالي
            self.selected_record_id = record_id

            # تفعيل الأزرار للسجل المحدد
            self.enable_action_buttons(True)

            # تفعيل زر الإخبار فقط للشهادات الطبية
            reason = record_data.get("reason", "")
            self.notify_cert_btn.setEnabled("شهادة طبية" in reason)

        except Exception as e:
            print(f"خطأ في معالجة تحديد السجل: {e}")
            import traceback
            traceback.print_exc()
            self.selected_record = None
            self.selected_record_data = {}
            self.selected_record_id = None
            self.enable_action_buttons(False)

    def enable_action_buttons(self, enabled=True):
        """تفعيل أو تعطيل أزرار الإجراءات بناءً على حالة التحديد"""
        self.delete_btn.setEnabled(enabled)
        self.print_btn.setEnabled(enabled)
        self.notify_cert_btn.setEnabled(enabled)

    def delete_record(self):
        """حذف التبرير المحدد"""
        if not self.selected_record or not self.selected_record.get('id'):
            QMessageBox.warning(self, "تنبيه", "الرجاء تحديد سجل لحذفه أولاً بالضغط على أي خلية في صفه.")
            return

        record_id = self.selected_record.get('id')
        student_name = self.selected_record.get('student_name', 'غير معروف')

        reply = QMessageBox.question(self, "تأكيد الحذف",
                                     f"هل أنت متأكد من حذف التبرير رقم {record_id} الخاص بالتلميذ {student_name}؟",
                                     QMessageBox.Yes | QMessageBox.No, QMessageBox.No)

        if reply == QMessageBox.Yes:
            try:
                print(f"محاولة حذف السجل ID: {record_id}")
                delete_query = "DELETE FROM تبريرات_الغياب WHERE id = ?"
                params = (int(record_id),)

                if self.using_qsql:
                    query = QSqlQuery(self.db)
                    query.prepare(delete_query)
                    query.addBindValue(params[0])
                    if not query.exec_():
                        raise Exception(f"فشل حذف السجل (QSql): {query.lastError().text()}")
                    print("تم الحذف بنجاح (QSql)")
                else:
                    conn = None
                    try:
                        conn = sqlite3.connect(self.db_path)
                        cursor = conn.cursor()
                        cursor.execute(delete_query, params)
                        conn.commit()
                        print("تم الحذف بنجاح (sqlite3)")
                    finally:
                        if conn:
                            conn.close()

                QMessageBox.information(self, "نجاح", "تم حذف التبرير بنجاح.")
                self.load_data()
                self.selected_record = None

            except Exception as e:
                error_msg = f"خطأ في حذف السجل: {str(e)}"
                QMessageBox.critical(self, "خطأ الحذف", error_msg)
                print(error_msg)
                traceback.print_exc()

    def print_record(self):
        """طباعة التبرير المحدد (التقرير الفردي)"""
        if not self.selected_record or not self.selected_record.get('id'):
            QMessageBox.warning(self, "تنبيه", "الرجاء تحديد سجل لطباعته أولاً.")
            return

        if not PRINT5_AVAILABLE:
             QMessageBox.critical(self, "خطأ", "وحدة الطباعة (print5.py) غير متوفرة.")
             return

        try:
            record_id = self.selected_record.get('id')
            print(f"محاولة طباعة السجل ID: {record_id}")

            # جلب الملاحظات ومسار الصورة
            notes = ""
            image_path = None
            conn = None
            try:
                conn = sqlite3.connect(self.db_path)
                cursor = conn.cursor()
                cursor.execute("SELECT ملاحظات, مسار_الصورة FROM تبريرات_الغياب WHERE id = ?", (int(record_id),))
                result = cursor.fetchone()
                if result:
                    notes = result[0] if result[0] is not None else ""
                    image_path = result[1]
            finally:
                if conn:
                    conn.close()

            justification_data_to_print = self.selected_record.copy()
            justification_data_to_print['notes'] = notes
            justification_data_to_print['image_path'] = image_path

            # عرض نافذة تأكيد مميزة
            confirm_dialog = ConfirmationDialogs.create_absence_justification_print_dialog(self, justification_data_to_print)
            result = confirm_dialog.exec_()

            if result == QDialog.Accepted:  # تم الضغط على زر الطباعة
                print(f"بيانات الطباعة (تقرير فردي): {justification_data_to_print}")
                success, file_path = print_absence_justification(justification_data_to_print, self.db_path)

                if not success:
                    QMessageBox.warning(self, "فشل الطباعة", "حدث خطأ أثناء عملية الطباعة.")
            # إذا كان الناتج QDialog.Rejected، فلا نفعل شيئاً (تم الإلغاء)

        except Exception as e:
            error_msg = f"خطأ غير متوقع في طباعة السجل: {str(e)}"
            QMessageBox.critical(self, "خطأ الطباعة", error_msg)
            print(error_msg)
            traceback.print_exc()

    def notify_teachers_about_certificates(self):
        """جمع بيانات الشهادات الطبية واختيار الأساتذة (اختياري) وطباعة الإشعار."""
        selected_items = self.table.selectedItems()
        if not selected_items:
            QMessageBox.warning(self, "تنبيه", "الرجاء تحديد شهادة طبية واحدة على الأقل.")
            return

        selected_rows = sorted(list({item.row() for item in selected_items}))

        certificates_data = []
        student_info = {}
        class_name = None

        for i, row in enumerate(selected_rows):
            reason_item = self.table.item(row, 8)
            if reason_item and "شهادة طبية" in reason_item.text():
                cert_id = self.table.item(row, 0).text()
                start_date = self.table.item(row, 5).text()
                end_date = self.table.item(row, 6).text()
                days_count = self.table.item(row, 7).text()

                certificates_data.append({
                    "number": i + 1,
                    "id": cert_id,
                    "start_date": start_date,
                    "end_date": end_date,
                    "duration": days_count
                })

                if not student_info:
                    student_info['student_code'] = self.table.item(row, 1).text()
                    student_info['student_name'] = self.table.item(row, 2).text()
                    class_name = self.table.item(row, 3).text()
                    student_info['class_name'] = class_name

        if not certificates_data:
            QMessageBox.warning(self, "تنبيه", "لم يتم تحديد أي شهادة طبية صالحة.")
            return

        if not class_name:
             QMessageBox.warning(self, "خطأ بيانات", "لم يتم العثور على اسم القسم للتلميذ.")
             return

        # استخدام نافذة تأكيد مميزة بدلاً من رسالة التأكيد القياسية
        confirm_dialog = ConfirmationDialogs.create_teacher_selection_confirmation_dialog(self, class_name)
        result = confirm_dialog.exec_()

        selected_teachers = None
        if result == QDialog.Accepted:  # تم الضغط على زر "نعم"
            # استخدام نافذة اختيار الأساتذة المحسنة
            teachers_data = self.get_all_teachers()
            if not teachers_data:
                QMessageBox.warning(self, "تنبيه", "لم يتم العثور على بيانات الأساتذة.")
                return

            tree_dialog = ConfirmationDialogs.create_tree_teacher_selection_dialog(self, class_name, self.db_path, teachers_data)
            if tree_dialog.exec_() == QDialog.Accepted:
                selected_teachers = tree_dialog.get_selected_teachers()
                if not selected_teachers:
                    print("لم يتم تحديد أي أستاذ.")
                    return
            else:
                print("تم إلغاء اختيار الأساتذة.")
                return
        else:  # تم الضغط على زر "لا" - إنشاء جدول أساتذة فارغ مباشرة بدون رسالة تأكيد إضافية
            print("سيتم إنشاء جدول أساتذة فارغ.")
            selected_teachers = []

        notification_data = {
            "student_name": student_info.get('student_name'),
            "student_code": student_info.get('student_code'),
            "class_name": student_info.get('class_name'),
            "certificates": certificates_data,
            "teachers": selected_teachers
        }

        print(f"بيانات إشعار الشهادات الطبية للطباعة: {notification_data}")

        if PRINT6_AVAILABLE:
            try:
                # عرض نافذة تأكيد مميزة
                confirm_dialog = ConfirmationDialogs.create_medical_certificate_notification_dialog(self, notification_data)
                result = confirm_dialog.exec_()

                if result == QDialog.Accepted:  # تم الضغط على زر الطباعة
                    success = print_medical_certificate_notification(notification_data, self.db_path)
                    if not success:
                        QMessageBox.warning(self, "فشل الطباعة", "حدث خطأ أثناء عملية الطباعة.")
            except Exception as e:
                QMessageBox.critical(self, "خطأ طباعة", f"حدث خطأ غير متوقع أثناء طباعة الإشعار:\n{e}")
                traceback.print_exc()
        else:
            QMessageBox.critical(self, "خطأ", "وحدة طباعة الإشعارات (print6.py) غير متوفرة.")

    def select_subjects_and_teachers(self, class_name):
        """عرض حوار لاختيار الأساتذة للقسم المحدد باستخدام جدول."""
        dialog = TeacherSelectionDialog(class_name, self.db_path, parent=self)
        if dialog.exec_() == QDialog.Accepted:
            return dialog.get_selected_teachers()
        else:
            return None

    def get_subjects_for_class(self, class_name):
        """استعلام لجلب المواد الدراسية المرتبطة بقسم معين."""
        subjects = []
        conn = None
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            cursor.execute("""
                SELECT DISTINCT المادة
                FROM توزيع_المواد
                WHERE القسم = ?
                ORDER BY المادة
            """, (class_name,))
            results = cursor.fetchall()
            subjects = [row[0] for row in results if row[0]]
            print(f"المواد الدراسية للقسم '{class_name}': {subjects}")
        except Exception as e:
            print(f"خطأ في جلب المواد الدراسية للقسم '{class_name}': {e}")
            traceback.print_exc()
        finally:
            if conn:
                conn.close()
        return subjects

    def get_all_teachers(self):
        """استعلام لجلب بيانات جميع الأساتذة."""
        teachers = []
        conn = None
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            query = """
                SELECT المعرف, اسم_الأستاذ, المادة
                FROM الأساتذة
                WHERE اسم_الأستاذ IS NOT NULL AND اسم_الأستاذ != '' AND المادة IS NOT NULL AND المادة != ''
                ORDER BY المادة, اسم_الأستاذ
            """
            cursor.execute(query)
            results = cursor.fetchall()

            for teacher_id, teacher_name, subject in results:
                teachers.append({
                    "id": teacher_id,
                    "teacher_name": teacher_name,
                    "subject": subject
                })

            print(f"تم جلب {len(teachers)} أستاذ من قاعدة البيانات")
        except Exception as e:
            print(f"خطأ في جلب بيانات الأساتذة: {e}")
            traceback.print_exc()
        finally:
            if conn:
                conn.close()
        return teachers

    def _get_current_academic_year(self):
        """استرجاع السنة الدراسية من قاعدة البيانات"""
        try:
            year = None
            if self.using_qsql:
                query = QSqlQuery("SELECT السنة_الدراسية FROM بيانات_المؤسسة LIMIT 1", self.db)
                if query.exec_() and query.next():
                    year = query.value(0)
            else:
                conn = None
                try:
                    conn = sqlite3.connect(self.db_path)
                    cursor = conn.cursor()
                    cursor.execute("SELECT السنة_الدراسية FROM بيانات_المؤسسة LIMIT 1")
                    result = cursor.fetchone()
                    if result:
                        year = result[0]
                finally:
                    if conn:
                        conn.close()
            return year
        except Exception as e:
            print(f"خطأ في استرجاع السنة الدراسية: {e}")
            traceback.print_exc()
        return None

class TeacherSelectionDialog(QDialog):
    def __init__(self, class_name, db_path, parent=None):
        super().__init__(parent)
        self.class_name = class_name
        self.db_path = db_path
        self.selected_teachers_data = []

        self.setWindowTitle(f"اختيار أساتذة القسم: {class_name}")
        self.setMinimumWidth(600)
        self.setLayoutDirection(Qt.RightToLeft)

        layout = QVBoxLayout(self)

        layout.addWidget(QLabel(f"حدد الأساتذة المطلوب إشعارهم للقسم: {class_name}"))

        self.teachers_table = QTableWidget()
        self.teachers_table.setColumnCount(3)
        self.teachers_table.setHorizontalHeaderLabels(["المعرف", "اسم الأستاذ", "المادة"])
        self.teachers_table.setFont(QFont("Calibri", 11))
        self.teachers_table.setCursor(Qt.PointingHandCursor)  # تغيير المؤشر إلى شكل يد
        self.teachers_table.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.teachers_table.setSelectionMode(QAbstractItemView.MultiSelection)
        self.teachers_table.setEditTriggers(QAbstractItemView.NoEditTriggers)
        self.teachers_table.setAlternatingRowColors(True)
        self.teachers_table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
        self.teachers_table.horizontalHeader().setSectionResizeMode(0, QHeaderView.ResizeToContents)
        self.teachers_table.horizontalHeader().setCursor(Qt.PointingHandCursor)  # تغيير المؤشر إلى شكل يد للعناوين

        layout.addWidget(self.teachers_table)

        self.load_teachers()

        button_box = QDialogButtonBox(QDialogButtonBox.Ok | QDialogButtonBox.Cancel)
        ok_button = button_box.button(QDialogButtonBox.Ok)
        ok_button.setText("موافق")
        ok_button.setCursor(Qt.PointingHandCursor)  # تغيير المؤشر إلى شكل يد

        cancel_button = button_box.button(QDialogButtonBox.Cancel)
        cancel_button.setText("إلغاء")
        cancel_button.setCursor(Qt.PointingHandCursor)  # تغيير المؤشر إلى شكل يد

        button_box.accepted.connect(self.accept_selection)
        button_box.rejected.connect(self.reject)
        layout.addWidget(button_box)

    def load_teachers(self):
        """تحميل وعرض بيانات الأساتذة للقسم المحدد في الجدول."""
        conn = None
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            query = """
                SELECT المعرف, اسم_الأستاذ, المادة
                FROM الأساتذة
                WHERE اسم_الأستاذ IS NOT NULL AND اسم_الأستاذ != '' AND المادة IS NOT NULL AND المادة != ''
                ORDER BY المادة, اسم_الأستاذ
            """
            cursor.execute(query)
            teachers = cursor.fetchall()

            self.teachers_table.setRowCount(len(teachers))
            for row, (teacher_id, teacher_name, subject) in enumerate(teachers):
                id_item = QTableWidgetItem(str(teacher_id) if teacher_id is not None else "")
                name_item = QTableWidgetItem(teacher_name)
                subject_item = QTableWidgetItem(subject)

                id_item.setTextAlignment(Qt.AlignCenter)

                self.teachers_table.setItem(row, 0, id_item)
                self.teachers_table.setItem(row, 1, name_item)
                self.teachers_table.setItem(row, 2, subject_item)

            print(f"تم تحميل {len(teachers)} أستاذ من جدول 'الأساتذة'")

        except sqlite3.OperationalError as oe:
             if "no such table" in str(oe).lower() or "no such column" in str(oe).lower():
                 QMessageBox.critical(self, "خطأ قاعدة بيانات",
                                      f"خطأ في الوصول إلى جدول 'الأساتذة' أو أحد أعمدته.\n"
                                      f"تأكد من وجود جدول 'الأساتذة' وأعمدة 'المعرف', 'اسم_الأستاذ', 'المادة'.\n"
                                      f"الخطأ الأصلي: {oe}")
             else:
                 QMessageBox.critical(self, "خطأ قاعدة بيانات", f"خطأ في جلب بيانات الأساتذة:\n{oe}")
             traceback.print_exc()
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ غير متوقع في جلب بيانات الأساتذة:\n{e}")
            traceback.print_exc()
        finally:
            if conn:
                conn.close()

    def accept_selection(self):
        """تأكيد اختيار الأساتذة وتخزين بياناتهم."""
        selected_items = self.teachers_table.selectedItems()
        if not selected_items:
            QMessageBox.warning(self, "تنبيه", "الرجاء تحديد أستاذ واحد على الأقل.")
            return

        self.selected_teachers_data = []
        selected_rows = sorted(list({item.row() for item in selected_items}))

        for row in selected_rows:
            teacher_name_item = self.teachers_table.item(row, 1)
            subject_item = self.teachers_table.item(row, 2)

            if teacher_name_item and subject_item:
                self.selected_teachers_data.append({
                    "teacher_name": teacher_name_item.text(),
                    "subject": subject_item.text()
                })

        if not self.selected_teachers_data:
             QMessageBox.warning(self, "تنبيه", "حدث خطأ أثناء استخلاص بيانات الأساتذة المختارين.")
             return

        print(f"الأساتذة المختارون: {self.selected_teachers_data}")
        self.accept()

    def get_selected_teachers(self):
        """إرجاع قائمة ببيانات الأساتذة المختارين."""
        return self.selected_teachers_data

if __name__ == "__main__":
    app = QApplication(sys.argv)
    window = AbsenceManagementWindow()
    window.show()
    sys.exit(app.exec_())

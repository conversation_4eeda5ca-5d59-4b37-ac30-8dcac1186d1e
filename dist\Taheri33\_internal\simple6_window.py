import sqlite3
# تم تعطيل استيراد pandas لأنه غير مستخدم في هذا الملف
# import pandas as pd
from PyQt5.QtWidgets import (QWidget, QLineEdit, QTableView, QHeaderView, QAbstractItemView,
                            QFrame, QGraphicsDropShadowEffect, QLabel, QPushButton)
from PyQt5.QtGui import QFont, QColor, QStandardItemModel, QStandardItem
from PyQt5.QtCore import Qt, QSortFilterProxyModel
from PyQt5.QtSql import QSqlQuery

class Simple6Window(QWidget):
    def __init__(self, db, academic_year, parent=None):
        super().__init__(parent)
        self.db = db  # تخزين اتصال قاعدة البيانات
        self.current_academic_year = academic_year  # تخزين السنة الدراسية
        self.setWindowTitle("نافذة بحث")
        self.setGeometry(50, 50, 1200, 800)
        self.setMinimumSize(1000, 600)
        self.setLayoutDirection(Qt.RightToLeft)
        self.setStyleSheet("background-color: #f0f2f5;")  # خلفية رمادية فاتحة للنافذة

        # تعيين الخط الرئيسي للنافذة
        self.setFont(QFont("Calibri", 13, QFont.Bold))

        # إنشاء عناصر واجهة المستخدم
        self.init_ui()

        # تعبئة البيانات من قاعدة البيانات
        self.load_data()

    def init_ui(self):
        """إنشاء واجهة المستخدم بتصميم عصري"""
        # إنشاء إطار رئيسي مع تأثير الظل
        content_frame = QFrame(self)
        content_frame.setGeometry(20, 20, 1160, 760)
        content_frame.setFrameShape(QFrame.NoFrame)
        content_frame.setStyleSheet("""
            QFrame {
                background-color: white;
                border-radius: 15px;
            }
        """)

        # إضافة تأثير الظل
        shadow = QGraphicsDropShadowEffect()
        shadow.setBlurRadius(15)
        shadow.setXOffset(5)
        shadow.setYOffset(5)
        shadow.setColor(QColor(0, 0, 0, 150))
        content_frame.setGraphicsEffect(shadow)

        # إضافة عنوان للبحث مع تصميم عصري
        search_label = QLabel("البحث:", content_frame)
        search_label.setGeometry(1080, 25, 60, 30)
        search_label.setFont(QFont("Amiri", 14, QFont.Bold))
        search_label.setStyleSheet("color: #1976d2;")

        # إضافة مربع نص للبحث في أعلى النافذة
        self.search_box = QLineEdit(content_frame)
        self.search_box.setGeometry(250, 20, 820, 40)
        self.search_box.setFont(QFont("Calibri",  13, QFont.Bold))
        self.search_box.setPlaceholderText("اكتب للبحث في السجل العام...")
        self.search_box.textChanged.connect(self.filter_general_record)
        self.search_box.setStyleSheet("""
            QLineEdit {
                border: 2px solid #dce1e6;
                border-radius: 8px;
                padding: 5px 10px;
                background-color: #f7f9fc;
            }
            QLineEdit:focus {
                border: 2px solid #1976d2;
                background-color: white;
            }
        """)

        # زر تنظيف البحث
        clear_button = QPushButton("✕", content_frame)
        clear_button.setGeometry(200, 20, 40, 40)
        clear_button.setFont(QFont("Arial", 12))
        clear_button.clicked.connect(self.clear_search)
        clear_button.setStyleSheet("""
            QPushButton {
                background-color: #f44336;
                color: white;
                border-radius: 8px;
                border: none;
            }
            QPushButton:hover {
                background-color: #e53935;
            }
        """)

        # إضافة عنوان بتصميم عصري للجداول
        table_style = """
            font-family: 'Amiri';
            font-size: 16px;
            color: #1976d2;
            font-weight: bold;
            padding-bottom: 5px;
            border-bottom: 2px solid #1976d2;
        """



        # إنشاء جدول السجل العام
        self.general_record_table = QTableView(content_frame)
        self.general_record_table.setGeometry(30, 80, 1100, 300)
        # تعديل الخط للصفوف - أميري 13 غليظ أسود
        self.general_record_table.setFont(QFont("Calibri",  13, QFont.Bold))
        self.general_record_table.verticalHeader().setVisible(False)

        # منع التعديل على الجدول
        self.general_record_table.setEditTriggers(QAbstractItemView.NoEditTriggers)

        # ضمان تحديد الصف بأكمله عند النقر
        self.general_record_table.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.general_record_table.setSelectionMode(QAbstractItemView.SingleSelection)

        # إزالة مربع التحديد عند النقر على الخلية
        self.general_record_table.setFocusPolicy(Qt.NoFocus)
        self.general_record_table.setAlternatingRowColors(True)
        self.general_record_table.clicked.connect(self.on_general_record_clicked)

        # تحسين التحكم في الحجم
        self.general_record_table.horizontalHeader().setCascadingSectionResizes(False)
        self.general_record_table.verticalHeader().setCascadingSectionResizes(False)

        self.general_record_table.setStyleSheet("""
            QTableView {
                background-color: white;
                alternate-background-color: #f5f8fa;
                border: 1px solid #dce1e6;
                border-radius: 8px;
                gridline-color: #e0e6ed;
                outline: 0; /* إزالة الحدود المحيطة عند التركيز */
            }
            QTableView::item {
                padding: 5px;
                border-bottom: 1px solid #eef1f5;
                border: none; /* إزالة الحدود حول العناصر */
            }
            QTableView::item:selected {
                background-color: #bbdefb;
                color: #01579b;
                border: none; /* إزالة الحدود حول العناصر المحددة */
            }
        """)

        # إنشاء نموذج البيانات للسجل العام
        self.general_record_model = QStandardItemModel()
        self.general_record_model.setHorizontalHeaderLabels(["الرمز", "الاسم_والنسب", "النوع", "تاريخ_الازدياد", "مكان_الازدياد", "الهاتف_الأول", "الهاتف_الثاني"])

        # إنشاء نموذج الفلترة للسجل العام
        self.general_record_proxy_model = QSortFilterProxyModel()
        self.general_record_proxy_model.setSourceModel(self.general_record_model)
        self.general_record_proxy_model.setFilterCaseSensitivity(Qt.CaseInsensitive)
        self.general_record_proxy_model.setFilterKeyColumn(-1)  # البحث في جميع الأعمدة

        # ربط نموذج الفلترة بالجدول
        self.general_record_table.setModel(self.general_record_proxy_model)

        # إضافة دالة لتطبيق عرض الأعمدة
        def apply_column_widths(table, widths):
            for column, width in enumerate(widths):
                table.setColumnWidth(column, width)
                # تعيين حالة القسم على Fixed لمنع إعادة الضبط التلقائي
                table.horizontalHeader().setSectionResizeMode(column, QHeaderView.Fixed)
            # بعد تعيين العرض، إعادة تعيين الوضع التفاعلي
            for column in range(len(widths)):
                table.horizontalHeader().setSectionResizeMode(column, QHeaderView.Interactive)

        # تطبيق تنسيق رأس الجدول
        header = self.general_record_table.horizontalHeader()
        # تعديل الخط للعناوين - أميري 13 غليظ
        header.setFont(QFont("Calibri",  13, QFont.Bold))

        # منع الضبط التلقائي تمامًا
        for i in range(7):  # عدد الأعمدة في السجل العام
            header.setSectionResizeMode(i, QHeaderView.Fixed)

        # استخدام الوضع التفاعلي لجميع الجداول بدلاً من ResizeToContents
        header.setSectionResizeMode(QHeaderView.Interactive)

        # السماح بتغيير حجم الأعمدة يدويًا
        header.setStretchLastSection(False)
        header.setSectionsMovable(True)
        header.setSectionsClickable(True)

        # إضافة هامش بسيط للأعمدة مع تعديل الخط إلى أبيض
        header.setStyleSheet("""
            QHeaderView::section {
                background-color: #1976d2;  /* 🔵 لون الرأس أزرق داكن */
                color: white;  /* ⚪ النص أبيض */
                font-family: 'Amiri';  /* نوع الخط أميري */
                font-size: 13px;
                font-weight: bold;
                padding: 8px 12px;  /* إضافة هوامش أعلى/أسفل 8 ويمين/يسار 12 */
                border-radius: 4px;
                margin: 1px;  /* هامش بسيط بين الأقسام */
            }
        """)

        # وظيفة تعيين أوضاع الجدول للتحكم الكامل
        def setup_table_for_full_control(table, num_columns):
            # ضبط التحكم الكامل في الأعمدة
            header = table.horizontalHeader()
            header.setSectionResizeMode(QHeaderView.Interactive)
            header.setDefaultAlignment(Qt.AlignRight | Qt.AlignVCenter)
            header.setStretchLastSection(False)
            header.setSectionsMovable(True)
            header.setSectionsClickable(True)

            # إزالة جميع القيود على الحد الأدنى للحجم
            header.setMinimumSectionSize(0)

            # التحكم في ارتفاع رأس الأعمدة
            header.setFixedHeight(40)  # تعيين ارتفاع ثابت لرأس الأعمدة

            # ضبط التحكم الكامل في الصفوف
            v_header = table.verticalHeader()
            v_header.setSectionResizeMode(QHeaderView.Interactive)
            v_header.setMinimumSectionSize(0)
            v_header.setDefaultSectionSize(35)

            # تنسيق الرأس بخط أميري 13 غليظ أبيض
            header.setStyleSheet("""
                QHeaderView::section {
                    background-color: #1976d2;
                    color: white;
                    font-family: 'Amiri'18;  /* نوع الخط أميري */
                    font-size: 13px;
                    font-weight: bold;
                    padding: 8px 12px;
                    border-radius: 4px;
                    margin: 1px;
                }
            """)

            return header

        # تطبيق التحكم الكامل على جدول السجل العام
        general_header = setup_table_for_full_control(self.general_record_table, 7)



        # إنشاء جدول اللوائح
        self.lists_table = QTableView(content_frame)
        self.lists_table.setGeometry(30, 400, 1100, 250)
        # تعديل الخط للصفوف - أميري 13 غليظ أسود
        self.lists_table.setFont(QFont("Calibri",  13, QFont.Bold))
        self.lists_table.verticalHeader().setVisible(False)

        # منع التعديل على الجدول
        self.lists_table.setEditTriggers(QAbstractItemView.NoEditTriggers)

        # ضمان تحديد الصف بأكمله عند النقر
        self.lists_table.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.lists_table.setSelectionMode(QAbstractItemView.SingleSelection)

        # إزالة مربع التحديد عند النقر على الخلية
        self.lists_table.setFocusPolicy(Qt.NoFocus)
        self.lists_table.setAlternatingRowColors(True)

        # تحسين التحكم في الحجم للجدول الثاني
        self.lists_table.horizontalHeader().setCascadingSectionResizes(False)
        self.lists_table.verticalHeader().setCascadingSectionResizes(False)

        self.lists_table.setStyleSheet(self.general_record_table.styleSheet())

        # إنشاء نموذج البيانات للوائح
        self.lists_model = QStandardItemModel()
        self.lists_model.setHorizontalHeaderLabels(["السنة_الدراسية", "المستوى", "الاسم_والنسب", "القسم", "رت"])

        # ربط نموذج البيانات بالجدول
        self.lists_table.setModel(self.lists_model)

        # تطبيق تنسيق رأس الجدول للوائح
        lists_header = self.lists_table.horizontalHeader()
        # تعديل الخط للعناوين - أميري 13 غليظ
        lists_header.setFont(QFont("Calibri",  13, QFont.Bold))
        lists_header.setSectionResizeMode(QHeaderView.Interactive)

        # منع الضبط التلقائي تمامًا


        # السماح بتغيير حجم الأعمدة يدويًا للجدول الثاني
        lists_header.setStretchLastSection(False)
        lists_header.setSectionsMovable(True)
        lists_header.setSectionsClickable(True)

        lists_header.setStyleSheet(header.styleSheet())  # نفس تنسيق رأس الجدول الأول

        # تطبيق التحكم الكامل على جدول اللوائح
        lists_header = setup_table_for_full_control(self.lists_table, 5)

        # تعيين عرض محدد لأعمدة السجل العام
        general_widths = [80, 80, 80, 60, 80, 80, 80]  # عرض كل عمود
        apply_column_widths(self.general_record_table, general_widths)

        # تعيين عرض محدد لأعمدة اللوائح
        lists_widths = [120, 250, 250, 150, 60]  # عرض كل عمود
        apply_column_widths(self.lists_table, lists_widths)

        # ضبط عرض الأعمدة في جدول السجل العام
        self.general_record_table.setColumnWidth(0, 80)  # الرمز
        self.general_record_table.setColumnWidth(1, 180)  # الاسم_والنسب
        self.general_record_table.setColumnWidth(2, 80)   # النوع
        self.general_record_table.setColumnWidth(3, 80)  # تاريخ_الازدياد
        self.general_record_table.setColumnWidth(4, 80)  # مكان_الازدياد
        self.general_record_table.setColumnWidth(5, 80)  # الهاتف_الأول
        self.general_record_table.setColumnWidth(6, 80)  # الهاتف_الثاني

        # تفعيل خاصية تحجيم الصفوف يدويًا
        self.general_record_table.verticalHeader().setSectionResizeMode(QHeaderView.Interactive)
        self.general_record_table.verticalHeader().setDefaultSectionSize(35)
        self.general_record_table.verticalHeader().setMinimumSectionSize(0)  # إزالة الحد الأدنى للسماح بالتحكم الكامل

        # ضبط عرض الأعمدة في جدول اللوائح
        self.lists_table.setColumnWidth(0, 120)  # السنة_الدراسية
        self.lists_table.setColumnWidth(1, 250)  # المستوى
        self.lists_table.setColumnWidth(2, 200)  # الاسم_والنسب
        self.lists_table.setColumnWidth(3, 120)  # القسم
        self.lists_table.setColumnWidth(4, 100)  # رت

        # تثبيت عرض الأعمدة لمنع تغيير حجمها
        for i in range(5):  # عدد الأعمدة في جدول اللوائح
            self.lists_table.horizontalHeader().setSectionResizeMode(i, QHeaderView.Fixed)

        # تفعيل خاصية تحجيم الصفوف يدويًا للجدول الثاني
        self.lists_table.verticalHeader().setSectionResizeMode(QHeaderView.Interactive)
        self.lists_table.verticalHeader().setDefaultSectionSize(35)
        self.lists_table.verticalHeader().setMinimumSectionSize(0)  # إزالة الحد الأدنى للسماح بالتحكم الكامل

        # لافتة لعرض عدد النتائج
        self.results_label = QLabel("النتائج: 0", content_frame)
        self.results_label.setGeometry(10, 20, 100, 40)
        self.results_label.setFont(QFont("Calibri",  13, QFont.Bold))
        self.results_label.setStyleSheet("color: #5c6bc0;")

    def clear_search(self):
        """مسح حقل البحث"""
        self.search_box.clear()

    def load_data(self):
        """تحميل البيانات من قاعدة البيانات"""
        try:
            # استخدام اتصال قاعدة البيانات المُمرر
            if not self.db or not self.db.isOpen():
                print("خطأ: قاعدة البيانات غير متصلة")
                self.results_label.setText("خطأ اتصال")
                return

            # استعلام للحصول على بيانات السجل العام
            query = QSqlQuery(self.db)
            query_str = """
            SELECT الرمز, الاسم_والنسب, النوع, تاريخ_الازدياد, مكان_الازدياد,
                   الهاتف_الأول, الهاتف_الثاني
            FROM السجل_العام
            """

            if query.exec_(query_str):
                # تعبئة نموذج السجل العام
                self.general_record_model.setRowCount(0)  # مسح البيانات السابقة

                while query.next():
                    items = []
                    for i in range(7):  # 7 columns
                        value = query.value(i)
                        items.append(QStandardItem(str(value) if value else ""))
                    self.general_record_model.appendRow(items)

                # تحديث عدد النتائج
                self.update_results_count()

            else:
                print(f"خطأ في تنفيذ الاستعلام: {query.lastError().text()}")
                self.results_label.setText("خطأ استعلام")

            # إعادة تطبيق عرض الأعمدة بعد تحميل البيانات
            column_widths_general = [120, 220, 80, 150, 250, 120, 120]
            for i, width in enumerate(column_widths_general):
                self.general_record_table.setColumnWidth(i, width)

            # ثم السماح بتغيير الحجم يدويًا
            for i in range(7):
                self.general_record_table.horizontalHeader().setSectionResizeMode(i, QHeaderView.Interactive)

            # إعادة تطبيق ارتفاع رأس الأعمدة بعد تحميل البيانات
            self.general_record_table.horizontalHeader().setFixedHeight(40)

        except Exception as e:
            print(f"خطأ في تحميل البيانات: {str(e)}")
            self.results_label.setText("خطأ تحميل")

    def filter_general_record(self):
        """تصفية جدول السجل العام بناءً على النص المدخل"""
        search_text = self.search_box.text()
        self.general_record_proxy_model.setFilterFixedString(search_text)

        # تحديث عدد النتائج
        self.update_results_count()

        # مسح جدول اللوائح عند تغيير البحث
        self.lists_model.setRowCount(0)

    def update_results_count(self):
        """تحديث عدد النتائج المعروضة"""
        count = self.general_record_proxy_model.rowCount()
        self.results_label.setText(f"النتائج: {count}")

    def on_general_record_clicked(self, index):
        """عند النقر على صف في جدول السجل العام"""
        # الحصول على الرمز من العمود الأول في الصف المحدد
        proxy_index = index.sibling(index.row(), 0)  # الحصول على مؤشر الرمز
        code_index = self.general_record_proxy_model.mapToSource(proxy_index)
        code = self.general_record_model.data(code_index)

        # الحصول على الاسم من العمود الثاني في الصف المحدد
        name_proxy_index = index.sibling(index.row(), 1)
        name_index = self.general_record_proxy_model.mapToSource(name_proxy_index)
        name = self.general_record_model.data(name_index)

        # البحث عن بيانات هذا الرمز في جدول اللوائح
        self.load_lists_data(code, name)

    def load_lists_data(self, code, name):
        """تحميل بيانات اللوائح المرتبطة بالرمز المحدد"""
        try:
            # استخدام اتصال قاعدة البيانات المُمرر
            if not self.db or not self.db.isOpen():
                print("خطأ: قاعدة البيانات غير متصلة")
                return

            # استعلام للحصول على بيانات اللوائح المرتبطة بالرمز
            query = QSqlQuery(self.db)
            query_str = """
            SELECT اللوائح.السنة_الدراسية, اللوائح.المستوى,
                  :name AS الاسم_والنسب,
                  اللوائح.القسم, اللوائح.رت
            FROM اللوائح
            WHERE اللوائح.الرمز = :code
            """

            query.prepare(query_str)
            query.bindValue(":name", name)
            query.bindValue(":code", code)

            if query.exec_():
                # تعبئة نموذج اللوائح
                self.lists_model.setRowCount(0)  # مسح البيانات السابقة

                while query.next():
                    items = []
                    for i in range(5):  # 5 columns
                        value = query.value(i)
                        items.append(QStandardItem(str(value) if value else ""))
                    self.lists_model.appendRow(items)

            else:
                print(f"خطأ في تنفيذ استعلام اللوائح: {query.lastError().text()}")

            # إعادة تثبيت عرض الأعمدة بعد تحديث البيانات
            self.lists_table.setColumnWidth(0, 120)  # السنة_الدراسية
            self.lists_table.setColumnWidth(1, 250)  # المستوى
            self.lists_table.setColumnWidth(2, 200)  # الاسم_والنسب
            self.lists_table.setColumnWidth(3, 120)  # القسم
            self.lists_table.setColumnWidth(4, 100)  # رت

            # إعادة تثبيت نمط العرض لمنع تغيير الحجم
            for i in range(5):
                self.lists_table.horizontalHeader().setSectionResizeMode(i, QHeaderView.Fixed)

            # ثم السماح بتغيير الحجم يدويًا
            for i in range(5):
                self.lists_table.horizontalHeader().setSectionResizeMode(i, QHeaderView.Interactive)

            # إعادة تطبيق ارتفاع رأس الأعمدة بعد تحميل بيانات اللوائح
            self.lists_table.horizontalHeader().setFixedHeight(40)

        except Exception as e:
            print(f"خطأ في تحميل بيانات اللوائح: {str(e)}")


if __name__ == '__main__':
    import sys
    from PyQt5.QtWidgets import QApplication
    from PyQt5.QtSql import QSqlDatabase
    import os

    app = QApplication(sys.argv)

    # إنشاء اتصال بقاعدة البيانات
    db_path = os.path.join(os.path.dirname(__file__), "data.db")
    db = QSqlDatabase.addDatabase("QSQLITE")
    db.setDatabaseName(db_path)

    if not db.open():
        print(f"خطأ في فتح قاعدة البيانات: {db.lastError().text()}")
        sys.exit(1)

    # الحصول على السنة الدراسية الحالية
    query = db.exec_("SELECT السنة_الدراسية FROM بيانات_المؤسسة LIMIT 1")
    current_year = None
    if query.next():
        current_year = query.value(0)
    else:
        current_year = "2024/2025"  # قيمة افتراضية

    window = Simple6Window(db=db, academic_year=current_year)
    window.show()

    ret = app.exec_()

    # إغلاق قاعدة البيانات قبل الخروج
    if db.isOpen():
        db.close()

    sys.exit(ret)

import os
import sqlite3
import logging
import traceback

def initialize_database(db_path="data.db"):
    """
    التحقق من وجود قاعدة البيانات وإنشائها مع الجداول الأساسية إذا كانت غير موجودة

    Args:
        db_path (str): مسار قاعدة البيانات

    Returns:
        bool: True إذا تم إنشاء أو التحقق من قاعدة البيانات بنجاح، False في حالة حدوث خطأ
    """
    try:
        # تحديد مسار قاعدة البيانات في مجلد البرنامج الرئيسي
        db_full_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), db_path)
        logging.info(f"مسار قاعدة البيانات للتهيئة: {db_full_path}")

        # التحقق من وجود قاعدة البيانات
        db_exists = os.path.exists(db_full_path)

        # استخدام المسار الكامل لقاعدة البيانات
        db_path = db_full_path

        # إنشاء اتصال بقاعدة البيانات (سيتم إنشاؤها إذا لم تكن موجودة)
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()

        if not db_exists:
            logging.info(f"قاعدة البيانات غير موجودة، سيتم إنشاء قاعدة بيانات جديدة: {db_path}")
            print(f"قاعدة البيانات غير موجودة، سيتم إنشاء قاعدة بيانات جديدة: {db_path}")

        # قائمة بالجداول الأساسية التي يجب إنشاؤها
        tables = {
            "بيانات_المؤسسة": """
                CREATE TABLE IF NOT EXISTS بيانات_المؤسسة (
                    الأكاديمية TEXT,
                    المديرية TEXT,
                    الجماعة TEXT,
                    المؤسسة TEXT,
                    السنة_الدراسية TEXT,
                    البلدة TEXT,
                    المدير TEXT,
                    الحارس_العام TEXT,
                    السلك TEXT,
                    ImagePath1 TEXT,
                    الأسدس TEXT,
                    رقم_الحراسة TEXT,
                    رقم_التسجيل TEXT
                )
            """,
            "إعدادات_الطابعة": """
                CREATE TABLE IF NOT EXISTS إعدادات_الطابعة (
                    id INTEGER PRIMARY KEY,
                    الطابعة_الحرارية TEXT,
                    الطابعة_العادية TEXT,
                    عرض_الورق INTEGER DEFAULT 80,
                    نوع_الاتصال TEXT DEFAULT 'windows',
                    معرف_البائع TEXT,
                    معرف_المنتج TEXT,
                    عنوان_IP TEXT,
                    المنفذ INTEGER DEFAULT 9100,
                    المنفذ_التسلسلي TEXT,
                    معدل_الباود INTEGER DEFAULT 9600,
                    قص_الورق BOOLEAN DEFAULT 1,
                    عدد_الأحرف_في_السطر INTEGER DEFAULT 42
                )
            """,
            "الرمز_السري": """
                CREATE TABLE IF NOT EXISTS الرمز_السري (
                    id INTEGER PRIMARY KEY,
                    الرمز TEXT
                )
            """,
            "اللوائح": """
                CREATE TABLE IF NOT EXISTS اللوائح (
                    السنة_الدراسية TEXT,
                    رمز_القسم TEXT,
                    اسم_القسم TEXT,
                    رقم_مسار TEXT,
                    المستوى INTEGER,
                    عدد_التلاميذ INTEGER
                )
            """,
            "السجل_الاولي": """
                CREATE TABLE IF NOT EXISTS السجل_الاولي (
                    رقم_مسار TEXT,
                    رمز_القسم TEXT,
                    رقم_الترتيب INTEGER,
                    رقم_التسجيل TEXT,
                    الاسم_الكامل TEXT,
                    تاريخ_الازدياد TEXT,
                    الجنس TEXT,
                    العنوان TEXT,
                    اسم_الاب TEXT,
                    مهنة_الاب TEXT,
                    اسم_الام TEXT,
                    مهنة_الام TEXT,
                    هاتف_الاب TEXT,
                    هاتف_الام TEXT,
                    هاتف_التلميذ TEXT,
                    البريد_الالكتروني TEXT,
                    ملاحظات TEXT
                )
            """,
            "مسك_الغياب_الأسبوعي": """
                CREATE TABLE IF NOT EXISTS مسك_الغياب_الأسبوعي (
                    رقم_مسار TEXT,
                    رمز_القسم TEXT,
                    رقم_الترتيب INTEGER,
                    رقم_التسجيل TEXT,
                    الاسم_الكامل TEXT,
                    الاسبوع INTEGER,
                    اليوم TEXT,
                    التاريخ TEXT,
                    الحصة INTEGER,
                    المادة TEXT,
                    الاستاذ TEXT,
                    نوع_الغياب TEXT,
                    مبرر TEXT,
                    ملاحظات TEXT
                )
            """,
            "غياب_الأسدس_الأول": """
                CREATE TABLE IF NOT EXISTS غياب_الأسدس_الأول (
                    رقم_مسار TEXT,
                    رمز_القسم TEXT,
                    رقم_الترتيب INTEGER,
                    رقم_التسجيل TEXT,
                    الاسم_الكامل TEXT,
                    غياب_مبرر INTEGER DEFAULT 0,
                    غياب_غير_مبرر INTEGER DEFAULT 0,
                    تأخر INTEGER DEFAULT 0,
                    طرد INTEGER DEFAULT 0,
                    مجموع_الغياب INTEGER DEFAULT 0,
                    ملاحظات TEXT
                )
            """,
            "مجموع_الغياب_السنوي": """
                CREATE TABLE IF NOT EXISTS مجموع_الغياب_السنوي (
                    رقم_مسار TEXT,
                    رمز_القسم TEXT,
                    رقم_الترتيب INTEGER,
                    رقم_التسجيل TEXT,
                    الاسم_الكامل TEXT,
                    غياب_مبرر INTEGER DEFAULT 0,
                    غياب_غير_مبرر INTEGER DEFAULT 0,
                    تأخر INTEGER DEFAULT 0,
                    طرد INTEGER DEFAULT 0,
                    مجموع_الغياب INTEGER DEFAULT 0,
                    ملاحظات TEXT
                )
            """,
            "زيارات_أولياء_الأمور": """
                CREATE TABLE IF NOT EXISTS زيارات_أولياء_الأمور (
                    رقم_مسار TEXT,
                    رمز_القسم TEXT,
                    رقم_الترتيب INTEGER,
                    رقم_التسجيل TEXT,
                    الاسم_الكامل TEXT,
                    تاريخ_الزيارة TEXT,
                    الزائر TEXT,
                    صفة_الزائر TEXT,
                    سبب_الزيارة TEXT,
                    الاجراء_المتخذ TEXT,
                    ملاحظات TEXT
                )
            """,
            "اخبار_بنشاط": """
                CREATE TABLE IF NOT EXISTS اخبار_بنشاط (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    التاريخ TEXT,
                    النشاط TEXT,
                    المكان TEXT,
                    المشاركون TEXT,
                    المنظمون TEXT,
                    ملاحظات TEXT
                )
            """,
            "مسك_أوراق_الفروض": """
                CREATE TABLE IF NOT EXISTS مسك_أوراق_الفروض (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    التاريخ TEXT,
                    المادة TEXT,
                    الأستاذ TEXT,
                    المستوى TEXT,
                    القسم TEXT,
                    نوع_الفرض TEXT,
                    المتغيبون TEXT,
                    تاريخ_التسجيل TEXT,
                    السنة_الدراسية TEXT,
                    الأسدس TEXT
                )
            """
        }

        # إنشاء الجداول إذا لم تكن موجودة
        for table_name, create_query in tables.items():
            cursor.execute(f"SELECT name FROM sqlite_master WHERE type='table' AND name='{table_name}'")
            if not cursor.fetchone():
                logging.info(f"إنشاء جدول {table_name}...")
                cursor.execute(create_query)
                logging.info(f"تم إنشاء جدول {table_name} بنجاح")

                # إضافة بيانات افتراضية للجداول التي تحتاج إلى ذلك
                if table_name == "إعدادات_الطابعة":
                    cursor.execute("""
                        INSERT OR IGNORE INTO إعدادات_الطابعة
                        (id, الطابعة_الحرارية, الطابعة_العادية, عرض_الورق, نوع_الاتصال, عدد_الأحرف_في_السطر)
                        VALUES (1, NULL, NULL, 80, 'windows', 42)
                    """)
                    logging.info("تم إضافة بيانات افتراضية لجدول إعدادات_الطابعة")

                elif table_name == "الرمز_السري":
                    cursor.execute("""
                        INSERT OR IGNORE INTO الرمز_السري (id, الرمز)
                        VALUES (1, '1234')
                    """)
                    logging.info("تم إضافة رمز سري افتراضي (1234)")

                elif table_name == "اللوائح" and not db_exists:
                    cursor.execute("""
                        INSERT INTO اللوائح
                        (السنة_الدراسية, رمز_القسم, اسم_القسم, رقم_مسار, المستوى, عدد_التلاميذ)
                        VALUES ('2024/2025', '1APIC-1', 'الأولى إعدادي مسار دولي', 'A12345678', 1, 43)
                    """)
                    logging.info("تم إضافة بيانات افتراضية لجدول اللوائح")

        # حفظ التغييرات وإغلاق الاتصال
        conn.commit()
        conn.close()

        logging.info("تم التحقق من قاعدة البيانات وإنشاء الجداول الأساسية بنجاح")
        return True

    except Exception as e:
        logging.error(f"خطأ في إنشاء أو التحقق من قاعدة البيانات: {e}")
        logging.error(traceback.format_exc())
        print(f"خطأ في إنشاء أو التحقق من قاعدة البيانات: {e}")
        traceback.print_exc()

        try:
            if conn:
                conn.close()
        except:
            pass

        return False

if __name__ == "__main__":
    # يمكن استدعاء الدالة مباشرة لإنشاء قاعدة البيانات
    initialize_database()

#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
ملف مخصص لطباعة إشعار الشهادات الطبية (print6.py)
تم فصله من ملف print1_test.py
"""

import os
from datetime import datetime
import sqlite3
from reportlab.lib.pagesizes import A4
from reportlab.lib.units import cm
from reportlab.pdfgen import canvas
from reportlab.pdfbase import pdfmetrics
from reportlab.pdfbase.ttfonts import TTFont
from reportlab.platypus import SimpleDocTemplate, Table, TableStyle, Paragraph, Spacer, Image
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.lib import colors
import sys
import traceback
import webbrowser
import subprocess
from PyQt5.QtWidgets import QMessageBox

# تسجيل الخطوط العربية
try:
    # محاولة تسجيل الخط العربي الأساسي
    pdfmetrics.registerFont(TTFont("Arabic", "arial.ttf"))
    print("تم تسجيل خط Arabic (arial.ttf) بنجاح")
except Exception as font_error:
    print(f"خطأ في تسجيل خط Arabic (arial.ttf): {font_error}")
    try:
        # محاولة استخدام مسار مطلق للخط
        script_dir = os.path.dirname(os.path.abspath(__file__))
        arial_path = os.path.join(script_dir, "arial.ttf")
        if os.path.exists(arial_path):
            pdfmetrics.registerFont(TTFont("Arabic", arial_path))
            print(f"تم تسجيل خط Arabic من المسار المطلق: {arial_path}")
        else:
            print(f"خطأ: ملف الخط غير موجود في المسار: {arial_path}")
    except Exception as alt_font_error:
        print(f"خطأ في تسجيل خط Arabic من المسار المطلق: {alt_font_error}")

# محاولة تسجيل خطوط Amiri و Calibri
try:
    script_dir = os.path.dirname(os.path.abspath(__file__))

    # تحديد مسارات الخطوط
    amiri_regular_path = os.path.join(script_dir, "Amiri-Regular.ttf")
    amiri_bold_path = os.path.join(script_dir, "Amiri-Bold.ttf")
    calibri_regular_path = os.path.join(script_dir, "calibri.ttf")
    calibri_bold_path = os.path.join(script_dir, "calibrib.ttf")

    # البحث عن خطوط Calibri في مجلد الخطوط في Windows
    if os.name == 'nt':  # Windows
        windows_fonts_dir = os.path.join(os.environ.get('SystemRoot', 'C:\\Windows'), 'Fonts')
        if not os.path.exists(calibri_regular_path):
            windows_calibri_path = os.path.join(windows_fonts_dir, 'calibri.ttf')
            if os.path.exists(windows_calibri_path):
                calibri_regular_path = windows_calibri_path
                print(f"تم العثور على خط Calibri في مجلد خطوط Windows: {calibri_regular_path}")

        if not os.path.exists(calibri_bold_path):
            windows_calibri_bold_path = os.path.join(windows_fonts_dir, 'calibrib.ttf')
            if os.path.exists(windows_calibri_bold_path):
                calibri_bold_path = windows_calibri_bold_path
                print(f"تم العثور على خط Calibri Bold في مجلد خطوط Windows: {calibri_bold_path}")

    # تسجيل خطوط Amiri
    if os.path.exists(amiri_regular_path):
        pdfmetrics.registerFont(TTFont("Amiri", amiri_regular_path))
        print(f"تم تسجيل خط Amiri من المسار: {amiri_regular_path}")
    else:
        print(f"تحذير: لم يتم العثور على خط Amiri في المسار: {amiri_regular_path}")

    if os.path.exists(amiri_bold_path):
        pdfmetrics.registerFont(TTFont("Amiri-Bold", amiri_bold_path))
        print(f"تم تسجيل خط Amiri-Bold من المسار: {amiri_bold_path}")
    else:
        print(f"تحذير: لم يتم العثور على خط Amiri-Bold في المسار: {amiri_bold_path}")

    # تسجيل خطوط Calibri
    if os.path.exists(calibri_regular_path):
        pdfmetrics.registerFont(TTFont("Calibri", calibri_regular_path))
        print(f"تم تسجيل خط Calibri من المسار: {calibri_regular_path}")
    else:
        print(f"تحذير: لم يتم العثور على خط Calibri في المسار: {calibri_regular_path}")

    if os.path.exists(calibri_bold_path):
        pdfmetrics.registerFont(TTFont("Calibri-Bold", calibri_bold_path))
        print(f"تم تسجيل خط Calibri-Bold من المسار: {calibri_bold_path}")
    else:
        print(f"تحذير: لم يتم العثور على خط Calibri-Bold في المسار: {calibri_bold_path}")

    # إذا لم يتم العثور على أي خط، استخدم الخط العربي الأساسي
    registered_fonts = pdfmetrics.getRegisteredFontNames()
    if "Amiri" not in registered_fonts and "Amiri-Bold" not in registered_fonts and "Calibri" not in registered_fonts and "Calibri-Bold" not in registered_fonts:
        print("تحذير: لم يتم العثور على خطوط Amiri أو Calibri، سيتم استخدام الخط العربي الأساسي")
except Exception as font_error:
    print(f"خطأ في تسجيل الخطوط: {font_error}")

# Arabic text handling
import arabic_reshaper
from bidi.algorithm import get_display

def open_pdf(filename):
    """Opens the generated PDF file using the default system viewer."""
    try:
        absolute_path = os.path.abspath(filename)
        print(f"محاولة فتح الملف: {absolute_path}")

        # --- >> إضافة: معالجة الخطأ عند فتح الملف << ---
        try:
            if sys.platform == "win32":
                os.startfile(absolute_path)
            elif sys.platform == "darwin": # macOS
                os.system(f'open "{absolute_path}"')
            else: # Linux and other Unix-like
                os.system(f'xdg-open "{absolute_path}"')
        except OSError as e:
            # تحقق من رمز الخطأ الخاص بـ "No application associated"
            if hasattr(e, 'winerror') and e.winerror == 1155: # WinError 1155
                print(f"خطأ فتح الملف: لا يوجد تطبيق مرتبط لفتح ملفات PDF.")
                QMessageBox.warning(
                    None, # لا يوجد نافذة أب محددة هنا، يمكن تمريرها إذا كانت متوفرة
                    "فشل فتح الملف",
                    f"تم إنشاء ملف PDF بنجاح في:\n{absolute_path}\n\n"
                    "ولكن، لم يتم العثور على تطبيق افتراضي لفتح ملفات PDF.\n"
                    "الرجاء فتح الملف يدوياً."
                )
            else:
                # عرض الأخطاء الأخرى
                print(f"خطأ غير متوقع في فتح الملف: {e}")
                traceback.print_exc()
                QMessageBox.critical(
                    None,
                    "خطأ",
                    f"حدث خطأ أثناء محاولة فتح الملف:\n{e}"
                )
        except Exception as open_error:
            # التعامل مع أي أخطاء أخرى قد تحدث
            print(f"خطأ عام في فتح الملف: {open_error}")
            traceback.print_exc()
            QMessageBox.critical(
                None,
                "خطأ",
                f"حدث خطأ عام أثناء محاولة فتح الملف:\n{open_error}"
            )
        # --- >> نهاية الإضافة << ---

    except Exception as path_error:
        print(f"خطأ في تحديد مسار الملف: {path_error}")
        traceback.print_exc()

def fix_arabic(text):
    """إصلاح النص العربي للعرض في PDF"""
    if not text:
        return ""
    try:
        reshaped_text = arabic_reshaper.reshape(str(text))
        return get_display(reshaped_text)
    except Exception as e:
        print(f"خطأ في إعادة تشكيل النص: {e}")
        return str(text)

def get_institution_info(db_path=None):
    """الحصول على معلومات المؤسسة من قاعدة البيانات"""
    try:
        if db_path is None:
            db_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "data.db")

        if not os.path.exists(db_path):
            print(f"تحذير: ملف قاعدة البيانات غير موجود: {db_path}")
            return {}

        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()

        # محاولة قراءة من جدول بيانات_المؤسسة
        try:
            cursor.execute("SELECT المؤسسة, السنة_الدراسية, ImagePath1, الحارس_العام FROM بيانات_المؤسسة LIMIT 1")
            institution_row = cursor.fetchone()
            if institution_row:
                institution_data = {
                    'name': institution_row[0],
                    'school_year': institution_row[1],
                    'logo_path': institution_row[2] if institution_row[2] and os.path.exists(institution_row[2]) else None,
                    'supervisor': institution_row[3] if len(institution_row) > 3 else ""
                }
                return institution_data
        except Exception as e:
            print(f"خطأ في قراءة بيانات المؤسسة: {e}")

        conn.close()
    except Exception as db_error:
        print(f"خطأ في الاتصال بقاعدة البيانات: {db_error}")

    # إرجاع قاموس فارغ في حالة حدوث خطأ
    return {}

def print_medical_certificate_notification(data, db_path=None):
    """طباعة إشعار الشهادات الطبية"""
    try:
        # حفظ الملف في المجلد المؤقت
        student_name = data.get('student_name', '').replace(' ', '_')
        file_name = f"إشعار_شهادة_طبية_{student_name}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.pdf"
        temp_dir = os.path.join(os.path.dirname(__file__), "temp")
        if not os.path.exists(temp_dir):
            os.makedirs(temp_dir)
        file_path = os.path.join(temp_dir, file_name)

        # الحصول على معلومات المؤسسة
        institution_data = get_institution_info(db_path)

        # إنشاء مستند PDF
        doc = SimpleDocTemplate(
            file_path,
            pagesize=A4,
            rightMargin=1.2*cm,
            leftMargin=1.2*cm,
            topMargin=0.2*cm,
            bottomMargin=0.2*cm
        )

        # قائمة العناصر التي سيتم إضافتها للمستند
        elements = []

        # إنشاء أنماط الفقرات
        styles = getSampleStyleSheet()

        # إنشاء نمط للنص العربي
        arabic_style = ParagraphStyle(
            'ArabicStyle',
            parent=styles['Normal'],
            fontName='Calibri',
            fontSize=14,
            textColor=colors.black,
            alignment=2,  # يمين
            leading=16,
            rightIndent=0,
            leftIndent=0
        )

        # إنشاء نمط للموضوع - محاذاة للوسط
        subject_style = ParagraphStyle(
            'SubjectStyle',
            parent=styles['Normal'],
            fontName='Calibri-Bold',
            fontSize=17,
            textColor=colors.black,
            alignment=1,  # وسط (1 = وسط، 2 = يمين)
            leading=19,
            rightIndent=0,
            leftIndent=0
        )

        # إنشاء نمط للعناوين
        title_style = ParagraphStyle(
            'TitleStyle',
            parent=styles['Title'],
            fontName='Calibri-Bold',
            fontSize=16,
            textColor=colors.darkblue,
            alignment=1,  # وسط
            spaceAfter=0.1*cm
        )

        # إنشاء نمط للعناوين الفرعية
        subtitle_style = ParagraphStyle(
            'SubtitleStyle',
            parent=styles['Heading2'],
            fontName='Calibri',
            fontSize=14,
            textColor=colors.darkblue,
            alignment=1,  # وسط
            spaceAfter=0.1*cm
        )

        # إنشاء نمط لاسم المؤسسة
        institution_style = ParagraphStyle(
            'InstitutionStyle',
            parent=styles['Title'],
            fontName='Calibri-Bold',
            fontSize=17,
            textColor=colors.darkblue,
            alignment=1,  # وسط
            spaceAfter=0.1*cm
        )

        # إضافة الشعار
        logo_path = institution_data.get('logo_path')
        if logo_path and os.path.exists(logo_path):
            try:
                # إنشاء صورة بعرض 200 وارتفاع 90
                img = Image(logo_path, width=200, height=90)
                img.hAlign = 'CENTER'  # محاذاة الصورة للوسط
                elements.append(img)

                # إنشاء نمط للتاريخ - Calibri 14 أسود
                date_style = ParagraphStyle(
                    'DateStyle',
                    parent=styles['Normal'],
                    fontName='Calibri',
                    fontSize=14,
                    textColor=colors.black,
                    alignment=0,  # يسار (0 = يسار)
                    leading=16,
                    rightIndent=0,
                    leftIndent=0
                )

                # إضافة التاريخ في الجانب الأيسر قليلاً تحت الشعار
                date_text = f"حرر بتاريخ: {datetime.now().strftime('%Y-%m-%d')}"
                date_paragraph = Paragraph(fix_arabic(date_text), date_style)

                # إنشاء جدول للتاريخ فقط بمحاذاة لليسار
                date_table = Table([[date_paragraph]], colWidths=[7*cm], hAlign='LEFT')
                date_table.setStyle(TableStyle([
                    ('ALIGN', (0, 0), (0, 0), 'LEFT'),  # محاذاة التاريخ لليسار
                    ('LEFTPADDING', (0, 0), (0, 0), 0),  # إزالة الهوامش
                    ('RIGHTPADDING', (0, 0), (0, 0), 0),
                    ('TOPPADDING', (0, 0), (0, 0), 0),
                    ('BOTTOMPADDING', (0, 0), (0, 0), 0),
                ]))

                elements.append(Spacer(1, -1.0*cm))  # مسافة سالبة لوضع التاريخ تحت الشعار
                elements.append(date_table)
                elements.append(Spacer(1, 0.5*cm))  # مسافة بعد الشعار والتاريخ

                print(f"تم إضافة الشعار بنجاح من: {logo_path}")
            except Exception as logo_error:
                print(f"خطأ في إضافة الشعار: {logo_error}")

        # إضافة اسم المؤسسة بخط Calibri 17 أزرق غامق
        institution_name = institution_data.get('name', '')
        if institution_name:
            elements.append(Paragraph(fix_arabic(institution_name), institution_style))
            elements.append(Spacer(1, 0.1*cm))

        # إضافة السنة الدراسية
        school_year = institution_data.get('school_year', '')
        if school_year:
            elements.append(Paragraph(fix_arabic(f"السنة الدراسية: {school_year}"), subtitle_style))
            elements.append(Spacer(1, 0.1*cm))

        # إضافة معلومات المراسلة
        supervisor = institution_data.get('supervisor', '')
        class_name = data.get('class_name', '')

        # إضافة "من + الحارس العام" بخط Calibri 14 أسود
        if supervisor:
            elements.append(Paragraph(fix_arabic(f"من: {supervisor}"), arabic_style))
            elements.append(Spacer(1, 0.3*cm))

        # إضافة "إلى السادة أساتذة قسم" بخط Calibri 14 أسود
        elements.append(Paragraph(fix_arabic(f"إلى السادة أساتذة قسم: {class_name}"), arabic_style))
        elements.append(Spacer(1, 0.3*cm))

        # إضافة الموضوع بخط Calibri 17 أسود غامق
        elements.append(Paragraph(fix_arabic("الموضوع: إخبار بوضع شهادة طبية"), subject_style))
        elements.append(Spacer(1, 0.5*cm))

        # إضافة نص المقدمة بخط Calibri 14 أسود
        intro_text = f"يشرفني إخباركم أن التلميذ(ة) الاسم والنسب: {data.get('student_name', '')} رقم التسجيل: {data.get('student_code', '')}"
        elements.append(Paragraph(fix_arabic(intro_text), arabic_style))
        elements.append(Spacer(1, 0.3*cm))

        # إضافة نص الشهادات بخط Calibri 14 أسود
        elements.append(Paragraph(fix_arabic("قد أدلى بشهادة / شهادات طبية"), arabic_style))
        elements.append(Spacer(1, 0.3*cm))

        # تحديد عرض موحد للجداول
        total_width = 17.5*cm
        col_widths = [5*cm, 5*cm, 6*cm, 1.5*cm]  # عرض الأعمدة الموحد

        # إضافة جدول الشهادات الطبية
        certificates = data.get('certificates', [])
        if certificates:
            # إنشاء عنوان جدول الشهادات الطبية
            cert_header_data = [[Paragraph(fix_arabic("الشهادات الطبية"), subtitle_style)]]
            cert_header_table = Table(cert_header_data, colWidths=[total_width], hAlign='RIGHT')
            cert_header_table.setStyle(TableStyle([
                ('GRID', (0, 0), (-1, -1), 1, colors.darkblue),
                ('BACKGROUND', (0, 0), (0, 0), colors.lightblue),
                ('TEXTCOLOR', (0, 0), (0, 0), colors.darkblue),
                ('ALIGN', (0, 0), (0, 0), 'CENTER'),
                ('VALIGN', (0, 0), (0, 0), 'MIDDLE'),
                ('LEFTPADDING', (0, 0), (0, 0), 6),
                ('RIGHTPADDING', (0, 0), (0, 0), 6),
                ('TOPPADDING', (0, 0), (0, 0), 6),
                ('BOTTOMPADDING', (0, 0), (0, 0), 6),
            ]))

            # إنشاء بيانات جدول الشهادات - عكس ترتيب الأعمدة
            cert_data = [
                [fix_arabic("المدة (أيام)"), fix_arabic("تاريخ النهاية"), fix_arabic("تاريخ البداية"), fix_arabic("الرقم")]
            ]

            for cert in certificates:
                cert_data.append([
                    fix_arabic(str(cert.get('duration', ''))),
                    fix_arabic(cert.get('end_date', '')),
                    fix_arabic(cert.get('start_date', '')),
                    fix_arabic(str(cert.get('number', '')))
                ])

            # إنشاء جدول الشهادات الطبية بنفس عرض الجدول السابق
            cert_table = Table(cert_data, colWidths=col_widths, hAlign='RIGHT')
            cert_table.setStyle(TableStyle([
                ('GRID', (0, 0), (-1, -1), 1, colors.darkblue),
                ('BACKGROUND', (0, 0), (-1, 0), colors.darkblue),
                ('TEXTCOLOR', (0, 0), (-1, 0), colors.white),
                ('FONTNAME', (0, 0), (-1, 0), 'Calibri-Bold'),
                ('FONTNAME', (0, 1), (-1, -1), 'Calibri'),
                ('FONTSIZE', (0, 0), (-1, -1), 12),
                ('ALIGN', (0, 0), (-1, -1), 'CENTER'),  # محاذاة كل الخلايا للوسط
                ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
                ('LEFTPADDING', (0, 0), (-1, -1), 6),
                ('RIGHTPADDING', (0, 0), (-1, -1), 6),
                ('TOPPADDING', (0, 0), (-1, -1), 6),
                ('BOTTOMPADDING', (0, 0), (-1, -1), 6),
            ]))

            elements.append(cert_header_table)
            elements.append(cert_table)

            # إضافة نص بعد جدول الشهادات بخط Calibri 14 أسود
            elements.append(Spacer(1, 0.3*cm))
            elements.append(Paragraph(fix_arabic("لذا المرجو منكم أخذها بعين الاعتبار."), arabic_style))
            elements.append(Spacer(1, 0.3*cm))

        # إضافة جدول الأساتذة
        teachers = data.get('teachers', [])

        # إنشاء عنوان جدول الأساتذة
        teacher_header_data = [[Paragraph(fix_arabic("الأساتذة المعنيون"), subtitle_style)]]
        teacher_header_table = Table(teacher_header_data, colWidths=[total_width], hAlign='RIGHT')
        teacher_header_table.setStyle(TableStyle([
            ('GRID', (0, 0), (-1, -1), 1, colors.darkblue),
            ('BACKGROUND', (0, 0), (0, 0), colors.lightblue),
            ('TEXTCOLOR', (0, 0), (0, 0), colors.darkblue),
            ('ALIGN', (0, 0), (0, 0), 'CENTER'),
            ('VALIGN', (0, 0), (0, 0), 'MIDDLE'),
            ('LEFTPADDING', (0, 0), (0, 0), 6),
            ('RIGHTPADDING', (0, 0), (0, 0), 6),
            ('TOPPADDING', (0, 0), (0, 0), 6),
            ('BOTTOMPADDING', (0, 0), (0, 0), 6),
        ]))

        # إنشاء بيانات جدول الأساتذة - عكس ترتيب الأعمدة
        teacher_data = [
            [fix_arabic("التوقيع"), fix_arabic("المادة"), fix_arabic("الأستاذ(ة)"), fix_arabic("ر.ت")]
        ]

        if teachers:
            for i, teacher in enumerate(teachers):
                teacher_data.append([
                    ".........................",  # إضافة نقاط للتوقيع لتوضيح المكان
                    fix_arabic(teacher.get('subject', '')),
                    fix_arabic(teacher.get('teacher_name', '')),
                    fix_arabic(str(i + 1))
                ])
        else:
            # إضافة 12 صفًا فارغًا إذا لم يتم تحديد أساتذة
            for i in range(12):
                teacher_data.append([".........................", "", "", fix_arabic(str(i + 1))])

        # تعيين ارتفاع الصفوف بشكل صريح (25 نقطة لكل صف)
        row_heights = [20]  # ارتفاع صف العنوان (20 نقطة)
        for _ in range(len(teacher_data) - 1):
            row_heights.append(20)  # ارتفاع 25 نقطة لكل صف من صفوف البيانات

        # إنشاء جدول الأساتذة بنفس عرض الجداول السابقة
        teacher_table = Table(teacher_data, colWidths=col_widths, rowHeights=row_heights, hAlign='RIGHT')
        teacher_table.setStyle(TableStyle([
            ('GRID', (0, 0), (-1, -1), 1, colors.darkblue),
            ('BACKGROUND', (0, 0), (-1, 0), colors.darkblue),
            ('TEXTCOLOR', (0, 0), (-1, 0), colors.white),
            ('FONTNAME', (0, 0), (-1, 0), 'Calibri-Bold'),
            ('FONTNAME', (0, 1), (-1, -1), 'Calibri'),
            ('FONTSIZE', (0, 0), (-1, 0), 12),
            ('FONTSIZE', (0, 1), (-1, -1), 12),
            ('ALIGN', (0, 0), (-1, -1), 'CENTER'),  # محاذاة كل الخلايا للوسط
            ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
            ('LEFTPADDING', (0, 0), (-1, -1), 6),
            ('RIGHTPADDING', (0, 0), (-1, -1), 6),
            ('TOPPADDING', (0, 0), (-1, -1), 6),
            ('BOTTOMPADDING', (0, 0), (-1, -1), 6),
        ]))

        elements.append(teacher_header_table)
        elements.append(teacher_table)
        elements.append(Spacer(1, 0.2*cm))

        # إضافة التوقيع فقط (التاريخ تم إضافته مع الشعار)
        signature_data = [
            [fix_arabic("توقيع الحراسة العامة"), ""]
        ]

        # تحديد عرض الجدول ليكون متناسباً مع باقي الجداول
        signature_table = Table(signature_data, colWidths=[5*cm, 12.5*cm], hAlign='RIGHT')
        signature_table.setStyle(TableStyle([
            ('ALIGN', (0, 0), (-1, -1), 'RIGHT'),  # محاذاة كل الخلايا إلى اليمين
            ('FONTNAME', (0, 0), (-1, -1), 'Calibri-Bold'),  # تغيير الخط إلى غامق
            ('FONTSIZE', (0, 0), (-1, -1), 13),  # زيادة حجم الخط
            ('BOTTOMPADDING', (0, 0), (-1, -1), 10),  # زيادة المسافة أسفل النص
            ('TOPPADDING', (0, 0), (-1, -1), 10),  # زيادة المسافة فوق النص
        ]))

        elements.append(signature_table)

        # بناء المستند
        doc.build(elements)

        # فتح الملف
        open_pdf(file_path)
        return True

    except Exception as e:
        print(f"خطأ في طباعة إشعار الشهادات الطبية: {str(e)}")
        traceback.print_exc()
        QMessageBox.critical(
            None,
            "خطأ طباعة",
            f"حدث خطأ أثناء إنشاء أو طباعة ملف إشعار الشهادات الطبية:\n{e}"
        )
        return False

# قسم اختباري
if __name__ == "__main__":
    # بيانات تجريبية للاختبار
    test_data = {
        'student_name': 'محمد أحمد',
        'student_code': 'P123456789',
        'class_name': 'TCS-1',
        'certificates': [
            {
                'number': 1,
                'id': '22',
                'start_date': '2025-04-14',
                'end_date': '2025-04-18',
                'duration': '5'
            },
            {
                'number': 2,
                'id': '23',
                'start_date': '2025-05-01',
                'end_date': '2025-05-03',
                'duration': '3'
            }
        ],
        'teachers': [
            {
                'teacher_name': 'عبد الله محمد',
                'subject': 'الرياضيات'
            },
            {
                'teacher_name': 'فاطمة أحمد',
                'subject': 'اللغة العربية'
            }
        ]
    }

    print("اختبار طباعة إشعار الشهادات الطبية...")
    print_medical_certificate_notification(test_data)

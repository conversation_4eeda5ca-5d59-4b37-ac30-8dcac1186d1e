import os
import sys
import sqlite3
import traceback
from datetime import datetime
import subprocess

# ================= إعدادات عامة =================
# الجدول الثاني: 5 أعمدة - تحريك عمود الترتيب للنهاية
COL_WIDTHS_TABLE2 = [50, 40, 40, 40, 20]
# تعديل عناوين الجدول الثاني
TABLE2_HEADERS = ['المستوى', 'تاريخ شهادة المغادرة', 'الاسم الكامل', 'رقم التلميذ', 'الترتيب']

# إضافة ارتفاعات الصفوف
ROW_HEIGHT_TABLE2 = 10   # ارتفاع صفوف الجدول الثاني (جدول المترشحين) - سيتم تعديله ديناميكيًا حسب عدد الصفوف
ROW_HEIGHT_HEADER = 10  # ارتفاع صفوف الرأس
ROW_HEIGHT_TABLE_HEADER = 12  # ارتفاع صف رأس الجدول

# إضافة إعدادات للهوامش (بالملليمتر)
PAGE_MARGIN_TOP = 0.2     # الهامش العلوي للصفحة
PAGE_MARGIN_BOTTOM = 0.2  # الهامش السفلي للصفحة
PAGE_MARGIN_LEFT = 10     # الهامش الأيسر للصفحة
PAGE_MARGIN_RIGHT = 10    # الهامش الأيمن للصفحة

PT_TO_MM = 0.3528
LOGO_W_PT, LOGO_H_PT = 200, 80
BOX1_W_PT, BOX2_W_PT, BOX3_W_PT = 320, 80, 150
TITLE_H_PT = 40
LOGO_W = LOGO_W_PT * PT_TO_MM
LOGO_H = LOGO_H_PT * PT_TO_MM
BOX1_W = BOX1_W_PT * PT_TO_MM
BOX2_W = BOX2_W_PT * PT_TO_MM
BOX3_W = BOX3_W_PT * PT_TO_MM
BOX_H = TITLE_H_PT * PT_TO_MM

try:
    from fpdf import FPDF
    import arabic_reshaper
    from bidi.algorithm import get_display
except ImportError:
    subprocess.check_call([sys.executable, "-m", "pip", "install", "fpdf2", "arabic-reshaper", "python-bidi"])
    from fpdf import FPDF
    import arabic_reshaper
    from bidi.algorithm import get_display

class ArabicPDF(FPDF):
    def __init__(self):
        super().__init__('P','mm','A4')
        # تعيين الهوامش
        self.set_margins(PAGE_MARGIN_LEFT, PAGE_MARGIN_TOP, PAGE_MARGIN_RIGHT)
        self.set_auto_page_break(auto=True, margin=PAGE_MARGIN_BOTTOM)
        fonts_dir = os.path.join(os.path.dirname(__file__), 'fonts')
        # تصحيح تحذير Deprecation Warning بإزالة المعامل uni=True
        self.add_font('Arial', '', os.path.join(fonts_dir, 'arial.ttf'))
        self.add_font('Arial', 'B', os.path.join(fonts_dir, 'arialbd.ttf'))
        self.set_font('Arial', '', 12)
        # تعيين سمك الخط الافتراضي
        self.set_line_width(0.4)

    def ar_text(self, txt: str) -> str:
        """
        تحويل النص العربي ليتم عرضه بشكل صحيح
        إذا كان النص يحتوي على رمز \n سيتم تجاهله وإرجاع نص مباشرة
        لأن fpdf تتعامل مع السطور الجديدة بشكل مختلف
        """
        reshaped = arabic_reshaper.reshape(str(txt))
        return get_display(reshaped)

    def multi_line_ar_text(self, txt: str, cell_width: float, font_size: int = 12) -> list:
        """
        تقسيم النص إلى سطور لتتناسب مع عرض الخلية

        المعاملات:
            txt: النص المراد تقسيمه
            cell_width: عرض الخلية بالملليمتر
            font_size: حجم الخط

        العوائد:
            قائمة بأسطر النص بعد التقسيم
        """
        lines = []
        # تقسيم النص إلى كلمات
        words = txt.split(' ')
        current_line = ""

        for word in words:
            # تقدير عرض السطر الحالي مع الكلمة المضافة
            test_line = current_line + " " + word if current_line else word
            # تحويل مؤقت للنص العربي لحساب العرض بشكل صحيح
            ar_test_line = self.ar_text(test_line)

            # حساب عرض النص التقريبي (استخدام تقدير بسيط)
            self.set_font('Arial', '', font_size)
            width = self.get_string_width(ar_test_line)

            # إذا تجاوز العرض المسموح، نضيف السطر الحالي ونبدأ بسطر جديد
            if width > cell_width and current_line:
                lines.append(current_line)
                current_line = word
            else:
                current_line = test_line

        # إضافة السطر الأخير إذا كان غير فارغ
        if current_line:
            lines.append(current_line)

        return lines


def fetch_records(db_path: str):
    conn = sqlite3.connect(db_path)
    cur = conn.cursor()
    # جلب شعار المؤسسة
    cur.execute("SELECT ImagePath1 FROM بيانات_المؤسسة LIMIT 1")
    logo_row = cur.fetchone()
    logo_path = logo_row[0] if logo_row and os.path.exists(logo_row[0]) else None

    # جلب بيانات سجلات الوافدين والمغادرين مع تصفية الملاحظات وإضافة الحقول المطلوبة
    query = '''
    SELECT رقم_التلميذ, الإسم, النسب, تاريخ_التحويل, المستوى, رقم_الإرسال, رقم_المراسلة, مؤسسة_الإستقبال, المؤسسة_الأصلية, المديرية_الإقليمية_الأصلية, الأكاديمية_الأصلية
    FROM سجلات_الوافدين_والمغادرين
    WHERE ملاحظات = 'لائحة التحويلات (الوافدون)' 
    AND رقم_الطلب IS NOT NULL 
    AND رقم_الطلب != '' 
    AND CAST(رقم_الطلب AS TEXT) GLOB '[0-9]*'
    ORDER BY CAST(رقم_التلميذ AS INTEGER), CAST(رقم_الإرسال AS INTEGER)
    '''
    cur.execute(query)
    cols = [c[0] for c in cur.description]
    recs = []
    for row in cur.fetchall():
        rec = dict(zip(cols, row))
        # دمج الإسم والنسب
        rec['الاسم_الكامل'] = f"{rec.get('الإسم', '')} {rec.get('النسب', '')}".strip()
        recs.append(rec)
    conn.close()
    return logo_path, recs


def generate_report(logo_path, records, output_path, report_title=None, subject_data=None):
    pdf = ArabicPDF()
    margin = 10
    usable_w = pdf.w - 2 * margin

    # جلب بيانات نموذج الطلب من قاعدة البيانات
    template_data = {}
    institution_data = {}
    try:
        db_path = os.path.join(os.path.dirname(__file__), 'data.db')
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # جلب بيانات نموذج الطلب
        cursor.execute("SELECT * FROM نموذج_الطلب LIMIT 1")
        template_row = cursor.fetchone()
        if template_row:
            cursor.execute("PRAGMA table_info(نموذج_الطلب)")
            columns = [col[1] for col in cursor.fetchall()]
            template_data = dict(zip(columns, template_row))
        
        # جلب بيانات المؤسسة
        cursor.execute("SELECT * FROM بيانات_المؤسسة LIMIT 1")
        institution_row = cursor.fetchone()
        if institution_row:
            cursor.execute("PRAGMA table_info(بيانات_المؤسسة)")
            columns = [col[1] for col in cursor.fetchall()]
            institution_data = dict(zip(columns, institution_row))
        
        conn.close()
    except Exception as e:
        print(f"خطأ في جلب بيانات نموذج الطلب: {str(e)}")

    # إذا لم يتم تحديد عنوان التقرير، نبحث عنه في قاعدة البيانات
    if not report_title:
        try:
            # البحث عن العنوان2 في جدول_الامتحان
            db_path = os.path.join(os.path.dirname(__file__), 'data.db')
            conn = sqlite3.connect(db_path)
            cursor = conn.cursor()
            cursor.execute("SELECT العنوان2 FROM جدول_الامتحان LIMIT 1")
            result = cursor.fetchone()
            conn.close()

            if result and result[0]:
                report_title = result[0]
        except Exception as e:
            print(f"خطأ في استرجاع عنوان التقرير من قاعدة البيانات: {str(e)}")

    # تجميع حسب رقم_الإرسال مع الحفاظ على الترتيب حسب رقم التلميذ
    request_groups = {}
    for rec in records:
        request_num = rec.get('رقم_الإرسال','')
        request_groups.setdefault(request_num, []).append(rec)

    # ترتيب مجموعات الإرسال رقمياً
    sorted_request_groups = sorted(request_groups.keys(), key=lambda x: int(x) if str(x).isdigit() else float('inf'))

    # تحديد ارتفاع صفوف جدول المترشحين بناءً على عدد الصفوف
    global ROW_HEIGHT_TABLE2
    max_records_per_group = max([len(request_groups[group]) for group in request_groups]) if request_groups else 0

    # تعديل ارتفاع الصفوف بناءً على عدد المترشحين
    if max_records_per_group <= 20:
        ROW_HEIGHT_TABLE2 = 12  # ارتفاع أكبر للصفوف إذا كان عدد المترشحين 20 أو أقل
    elif 20 < max_records_per_group <= 25:
        ROW_HEIGHT_TABLE2 = 12  # ارتفاع متوسط للصفوف إذا كان عدد المترشحين بين 21 و 25
    elif 25 < max_records_per_group <= 30:
        ROW_HEIGHT_TABLE2 = 12  # ارتفاع أصغر للصفوف إذا كان عدد المترشحين بين 26 و 30
    else:
        ROW_HEIGHT_TABLE2 = 12  # ارتفاع أصغر للصفوف إذا كان عدد المترشحين أكثر من 30

    for request_num in sorted_request_groups:
        recs = request_groups[request_num]
        # ترتيب السجلات داخل كل مجموعة إرسال حسب رقم التلميذ
        recs.sort(key=lambda x: int(x.get('رقم_التلميذ', '0')) if str(x.get('رقم_التلميذ', '0')).isdigit() else 0)

        # حساب العدد الإجمالي للصفحات لهذا الطلب
        # تقدير تقريبي بناءً على المساحة المتاحة
        available_height_first_page = pdf.h - 140  # المساحة المتاحة في الصفحة الأولى (بعد الشعار والبطاقات والنصوص)
        available_height_other_pages = pdf.h - 60   # المساحة في الصفحات الأخرى (بعد الشعار والرأس)
        
        records_per_first_page = int((available_height_first_page - ROW_HEIGHT_TABLE_HEADER - 60) / ROW_HEIGHT_TABLE2)  # 60 للبطاقة الأخيرة
        records_per_other_page = int((available_height_other_pages - ROW_HEIGHT_TABLE_HEADER) / ROW_HEIGHT_TABLE2)
        
        total_records = len(recs)
        if total_records <= records_per_first_page:
            total_pages = 1
        else:
            remaining_records = total_records - records_per_first_page
            additional_pages = (remaining_records + records_per_other_page - 1) // records_per_other_page  # تقريب للأعلى
            total_pages = 1 + additional_pages

        # متغير لترقيم الصفحات لكل طلب
        page_number = 1

        pdf.add_page()
        y = pdf.get_y()
        
        # إضافة ترقيم الصفحة في الزاوية اليسرى السفلى مع رقم الإرسال
        pdf.set_font('Arial', '', 10)
        pdf.set_xy(10, pdf.h - 15)
        pdf.cell(0, 5, pdf.ar_text(f"طلب رقم {request_num} - صفحة {page_number}/{total_pages}"), align='L')
        
        # إضافة الشعار
        if logo_path:
            x_logo = (pdf.w - LOGO_W) / 2
            pdf.image(logo_path, x=x_logo, y=y, w=LOGO_W, h=LOGO_H)
        y += LOGO_H + 10

        # إضافة البطاقتين المتقابلتين تحت الشعار (بطاقتين أصغر فقط)
        card_width = 93  # تقليل العرض بالملليمتر
        card_height = 45  # تقليل الارتفاع لتحسين التوافق مع النصوص
        
        # تحديد المواضع - عكس البطاقتين
        card1_x = pdf.w - margin - card_width  # البطاقة الأولى إلى اليسار الآن
        card2_x = margin  # البطاقة الثانية إلى اليمين الآن
        cards_y = y

        # البطاقة الأولى (اليسار الآن)
        pdf.set_draw_color(0, 0, 0)
        pdf.set_line_width(1)
        # pdf.rect(card1_x, cards_y, card_width, card_height)  # إزالة إطار البطاقة الأولى
        
        # محتوى البطاقة الأولى
        pdf.set_font('Arial', 'B', 13)  # هنا يمكنك تغيير: النوع، النمط (B=عريض، ''=عادي)، الحجم
        pdf.set_text_color(0, 0, 0)    # هنا يمكنك تغيير لون الخط (R, G, B)
        
        # العمود الأول: السيد_المدير + مؤسسة_الإستقبال
        مؤسسة_الإستقبال = recs[0].get('مؤسسة_الإستقبال', '') if recs else ''
        السيد_المدير = template_data.get('السيد_المدير', 'السيد مدير المؤسسة')
        first_line = f"{السيد_المدير} : {مؤسسة_الإستقبال}"
        
        # العمود الثاني: المدينة
        المدينة = template_data.get('المدينة', 'المدينة')
        
        # المدير الإقليمي مقسم إلى سطرين
        المدير_الاقليمي = template_data.get('المدير_الاقليمي', 'السيد المدير الإقليمي')
        words = المدير_الاقليمي.split()
        if len(words) >= 2:
            line1 = ' '.join(words[:len(words)//2])
            line2 = ' '.join(words[len(words)//2:])
        else:
            line1 = المدير_الاقليمي
            line2 = ""
        
        # مصلحة التخطيط
        مصلحة_التخطيط = template_data.get('مصلحة_التخطيط', 'مصلحة التخطيط والخريطة المدرسية')
        
        # المديرية المحلية
        المديرية_المحلية = template_data.get('المديرية_المحلية', 'المديرية المحلية للتربية والتكوين')
        
        # حساب الموضع الوسطي للنصوص داخل البطاقة
        text_margin = 2  # هنا يمكنك تغيير الهامش داخل البطاقة
        available_height = card_height - (2 * text_margin)
        line_height = 7  # هنا يمكنك تغيير ارتفاع كل سطر داخل البطاقة
        total_text_height = 6 * line_height  # 6 أسطر
        start_y = cards_y + text_margin + (available_height - total_text_height) / 2
        
        # رسم النصوص في البطاقة الأولى
        text_x = card1_x + text_margin
        text_width = card_width - (2 * text_margin)
        
        # السطر الأول: السيد المدير + مؤسسة الاستقبال
        pdf.set_xy(text_x, start_y)
        pdf.cell(text_width, line_height, pdf.ar_text(first_line), align='C')
        
        # السطر الثاني: المدينة
        pdf.set_xy(text_x, start_y + line_height)
        pdf.cell(text_width, line_height, pdf.ar_text(المدينة), align='C')
        
        # السطر الثالث: المدير الإقليمي - الجزء الأول
        pdf.set_xy(text_x, start_y + 2 * line_height)
        pdf.cell(text_width, line_height, pdf.ar_text(line1), align='C')
        
        # السطر الرابع: المدير الإقليمي - الجزء الثاني
        pdf.set_xy(text_x, start_y + 3 * line_height)
        pdf.cell(text_width, line_height, pdf.ar_text(line2), align='C')
        
        # السطر الخامس: مصلحة التخطيط
        pdf.set_xy(text_x, start_y + 4 * line_height)
        pdf.cell(text_width, line_height, pdf.ar_text(مصلحة_التخطيط), align='C')
        
        # السطر السادس: المديرية المحلية
        pdf.set_xy(text_x, start_y + 5 * line_height)
        pdf.cell(text_width, line_height, pdf.ar_text(المديرية_المحلية), align='C')

        # البطاقة الثانية (اليمين الآن) - مع المحتوى المطلوب
        card2_y = cards_y + 7  # تحريك البطاقة الثانية أخفض بـ 5 نقاط
        # pdf.rect(card2_x, card2_y, card_width, card_height)  # إزالة إطار البطاقة الثانية
        
        # محتوى البطاقة الثانية
        pdf.set_font('Arial', 'B', 13)  # تصغير الخط لاستيعاب المحتوى الإضافي
        pdf.set_text_color(0, 0, 0)
        
        # السطر الأول: إلى السيد(ة) مدير(ة) + المؤسسة_الأصلية
        المؤسسة_الأصلية = recs[0].get('المؤسسة_الأصلية', '') if recs else ''
        first_line_card2 = f"إلى السيد(ة) مدير(ة) : {المؤسسة_الأصلية}"
        
        # فحص الأكاديمية
        الأكاديمية_الحالية = institution_data.get('الأكاديمية', '')
        الأكاديمية_الأصلية = recs[0].get('الأكاديمية_الأصلية', '') if recs else ''
        مدير_الأكاديمية = template_data.get('مدير_الأكاديمية', 'السيد مدير الأكاديمية')
        
        # إعداد النصوص
        texts_to_display = [first_line_card2]
        
        # إضافة سطر الأكاديمية إذا كانت مختلفة (مقسم إلى سطرين)
        if الأكاديمية_الحالية != الأكاديمية_الأصلية and الأكاديمية_الأصلية:
            academy_full_text = f"{مدير_الأكاديمية} : {الأكاديمية_الأصلية}"
            # تقسيم النص إلى سطرين
            academy_words = academy_full_text.split()
            if len(academy_words) >= 4:  # إذا كان هناك كلمات كافية للتقسيم
                mid_point = len(academy_words) // 2
                academy_line1 = ' '.join(academy_words[:mid_point])
                academy_line2 = ' '.join(academy_words[mid_point:])
            else:
                academy_line1 = academy_full_text
                academy_line2 = ""
            
            texts_to_display.extend([academy_line1, academy_line2])
        
        # المدير الإقليمي مقسم إلى سطرين
        المدير_الاقليمي_2 = template_data.get('المدير_الاقليمي', 'السيد المدير الإقليمي')
        words_2 = المدير_الاقليمي_2.split()
        if len(words_2) >= 2:
            line1_card2 = ' '.join(words_2[:len(words_2)//2])
            line2_card2 = ' '.join(words_2[len(words_2)//2:])
        else:
            line1_card2 = المدير_الاقليمي_2
            line2_card2 = ""
        
        texts_to_display.extend([line1_card2, line2_card2])
        
        # مصلحة التخطيط
        مصلحة_التخطيط_2 = template_data.get('مصلحة_التخطيط', 'مصلحة التخطيط والخريطة المدرسية')
        texts_to_display.append(مصلحة_التخطيط_2)
        
        # المديرية الإقليمية الأصلية
        المديرية_الإقليمية_الأصلية = recs[0].get('المديرية_الإقليمية_الأصلية', '') if recs else ''
        texts_to_display.append(المديرية_الإقليمية_الأصلية)
        
        # حساب الموضع للبطاقة الثانية
        num_lines = len(texts_to_display)
        total_text_height_card2 = num_lines * line_height
        start_y_card2 = card2_y + text_margin + (available_height - total_text_height_card2) / 2
        
        # رسم النصوص في البطاقة الثانية
        text_x_card2 = card2_x + text_margin
        text_width_card2 = card_width - (2 * text_margin)
        
        for i, text in enumerate(texts_to_display):
            if text:  # تجنب رسم النصوص الفارغة
                pdf.set_xy(text_x_card2, start_y_card2 + i * line_height)
                pdf.cell(text_width_card2, line_height, pdf.ar_text(text), align='C')

        # تحديث الموضع y للمحتوى التالي (أخذ الأعلى من البطاقتين + الارتفاع)
        y = max(cards_y + card_height, card2_y + card_height) + 10

        # إضافة النصوص تحت البطاقات
        pdf.set_font('Arial', 'B', 13)
        pdf.set_text_color(0, 0, 0)
        
        # تحديد النص حسب عدد السجلات
        if len(recs) > 1:
            subject_text = "الموضوع : طلب ملفات مدرسية"
            request_text = "وبعد، يشرفني أن أطلب منكم موافاتي بالملفات المدرسية للتلاميذ :"
        else:
            subject_text = "الموضوع : طلب ملف مدرسي"
            request_text = "وبعد، يشرفني أن أطلب منكم موافاتي بالملف المدرسي للتلميذ(ة) :"
        
        # جلب رقم المراسلة من أول سجل
        رقم_المراسلة = recs[0].get('رقم_المراسلة', '') if recs else ''
        
        # رسم النصوص
        pdf.set_xy(margin, y)
        pdf.cell(0, 7, pdf.ar_text(subject_text), ln=True, align='R')
        y += 7
        
        pdf.set_xy(margin, y)
        pdf.cell(0, 7, pdf.ar_text(f"رقم الإرسال : {request_num}"), ln=True, align='R')
        y += 7
        
        pdf.set_xy(margin, y)
        pdf.cell(0, 7, pdf.ar_text(f"المراسلة رقم : {رقم_المراسلة}"), ln=True, align='R')
        y += 7
        
        pdf.set_xy(margin, y)
        pdf.cell(0, 7, pdf.ar_text("سلام تام بوجود مولانا الإمام"), ln=True, align='R')
        y += 7
        
        pdf.set_xy(margin, y)
        pdf.cell(0, 7, pdf.ar_text(request_text), ln=True, align='R')
        y += 12

        # الجدول الثاني: 5 أعمدة
        cols2 = COL_WIDTHS_TABLE2
        pdf.set_font('Arial','B',12)
        pdf.set_fill_color(200,200,200)
        pdf.set_line_width(0.5)  # تحديد سمك حدود الجدول

        # رسم عناوين الجدول
        x = margin
        for i, header in enumerate(TABLE2_HEADERS):
            pdf.set_xy(x, y)
            pdf.cell(cols2[i], ROW_HEIGHT_TABLE_HEADER, pdf.ar_text(header), border=1, align='C', fill=True)
            x += cols2[i]

        y += ROW_HEIGHT_TABLE_HEADER
        pdf.set_font('Arial','',12)

        # رسم الصفوف
        total_records = len(recs)
        for i, rec in enumerate(recs):
            # تحديد ما إذا كان هذا السجل الأخير (يحتاج مساحة للبطاقة)
            is_last_record = (i == total_records - 1)
            
            # اختيار الهامش المناسب حسب الحالة
            if is_last_record:
                # السجل الأخير - احتجاز مساحة للبطاقة
                bottom_margin = 60  # مساحة للبطاقة (40) + هامش أمان (20)
            else:
                # السجلات العادية - استغلال المساحة القصوى
                bottom_margin = 20  # هامش عادي
            
            # التحقق من الحاجة لصفحة جديدة
            if y + ROW_HEIGHT_TABLE2 > pdf.h - bottom_margin:
                page_number += 1  # زيادة رقم الصفحة
                pdf.add_page()
                y = pdf.get_y()
                
                # إضافة ترقيم الصفحة الجديدة مع رقم الإرسال
                pdf.set_font('Arial', '', 10)
                pdf.set_xy(10, pdf.h - 15)
                pdf.cell(0, 5, pdf.ar_text(f"طلب رقم {request_num} - صفحة {page_number}/{total_pages}"), align='L')

                # إضافة الشعار في الصفحة الجديدة
                if logo_path:
                    x_logo = (pdf.w - LOGO_W) / 2
                    pdf.image(logo_path, x=x_logo, y=y, w=LOGO_W, h=LOGO_H)
                y += LOGO_H + 10
                
                # إعادة رسم رأس الجدول في الصفحة الجديدة
                pdf.set_font('Arial','B',12)
                pdf.set_fill_color(200,200,200)
                pdf.set_line_width(0.5)
                
                x = margin
                for j, header in enumerate(TABLE2_HEADERS):
                    pdf.set_xy(x, y)
                    pdf.cell(cols2[j], ROW_HEIGHT_TABLE_HEADER, pdf.ar_text(header), border=1, align='C', fill=True)
                    x += cols2[j]
                
                y += ROW_HEIGHT_TABLE_HEADER
                pdf.set_font('Arial','',12)
            
            x = margin

            # البيانات مع تحريك الترتيب للنهاية
            data = [rec.get('المستوى',''), rec.get('تاريخ_التحويل',''), rec.get('الاسم_الكامل',''), rec.get('رقم_التلميذ',''), str(i + 1)]

            # رسم خلايا الصف
            for j, cell in enumerate(data):
                pdf.set_xy(x, y)
                pdf.cell(cols2[j], ROW_HEIGHT_TABLE2, pdf.ar_text(cell), border=1, align='C')
                x += cols2[j]

            # تحديث الموقع الرأسي للصف التالي
            y += ROW_HEIGHT_TABLE2

        # إضافة البطاقة تحت الجدول
        y += 10  # مسافة صغيرة بين الجدول والبطاقة
        
        # حساب عرض البطاقة (نفس عرض الجدول)
        table_width = sum(cols2)
        card_height = 40  # ارتفاع البطاقة بالملليمتر (تحويل من 100 نقطة تقريباً)
        
        # رسم إطار البطاقة
        pdf.set_line_width(0.5)
        pdf.rect(margin, y, table_width, card_height)
        
        # إضافة النصوص داخل البطاقة
        pdf.set_font('Arial', 'B', 14)
        
        # النص الأول: وتقبلو فائق التقدير والاحترام
        text_y = y + 8
        pdf.set_xy(margin, text_y)
        pdf.cell(table_width, 8, pdf.ar_text("وتقبلو فائق التقدير والاحترام"), align='C')
        
        # النص الثاني: والســـــــلام
        text_y += 8
        pdf.set_xy(margin + 40, text_y)  # محاذاة لليسار
        pdf.cell(60, 8, pdf.ar_text("والســـــــلام"), align='L')
        
        # النصوص السفلية
        text_y += 15
        
        # المدير (يسار)
        pdf.set_xy(margin + 30, text_y)
        pdf.cell(80, 8, pdf.ar_text("المدير"), align='L')
        
        # الحارس العام للخارجية (يمين)
        pdf.set_xy(margin + table_width - 90, text_y)
        pdf.cell(80, 8, pdf.ar_text("الحارس العام للخارجية"), align='R')

    # إضافة صفحة التقرير المفصل في النهاية
    add_detailed_summary_page(pdf, request_groups, sorted_request_groups, logo_path, margin)

    os.makedirs(os.path.dirname(output_path), exist_ok=True)
    pdf.output(output_path)
    print(f"تم إنشاء التقرير: {output_path}")

def add_detailed_summary_page(pdf, request_groups, sorted_request_groups, logo_path, margin):
    """إضافة صفحة التقرير المفصل عن طلبات الملفات"""
    
    pdf.add_page()
    y = pdf.get_y()
    
    # إضافة الشعار
    if logo_path:
        x_logo = (pdf.w - LOGO_W) / 2
        pdf.image(logo_path, x=x_logo, y=y, w=LOGO_W, h=LOGO_H)
    y += LOGO_H + 15
    
    # عنوان التقرير المفصل
    pdf.set_font('Arial', 'B', 16)
    pdf.set_xy(margin, y)
    pdf.cell(0, 12, pdf.ar_text("تقرير مفصل عن طلبات الملفات المدرسية"), ln=True, align='C')
    y += 20
    
    # معلومات إجمالية
    total_requests = len(sorted_request_groups)
    total_students = sum(len(request_groups[req]) for req in sorted_request_groups)
    
    pdf.set_font('Arial', 'B', 14)
    pdf.set_xy(margin, y)
    pdf.cell(0, 10, pdf.ar_text(f"إجمالي عدد الطلبات: {total_requests}"), ln=True, align='R')
    y += 10
    
    pdf.set_xy(margin, y)
    pdf.cell(0, 10, pdf.ar_text(f"إجمالي عدد التلاميذ: {total_students}"), ln=True, align='R')
    y += 15
    
    # جدول تفصيلي للطلبات
    pdf.set_font('Arial', 'B', 12)
    pdf.set_fill_color(200, 200, 200)
    
    # عناوين الجدول
    col_widths = [30, 40, 60, 60]
    headers = ['رقم الطلب', 'عدد التلاميذ', 'المؤسسة الأصلية', 'مؤسسة الاستقبال']
    
    x = margin
    for i, header in enumerate(headers):
        pdf.set_xy(x, y)
        pdf.cell(col_widths[i], 12, pdf.ar_text(header), border=1, align='C', fill=True)
        x += col_widths[i]
    
    y += 12
    pdf.set_font('Arial', '', 11)
    
    # بيانات الجدول
    for request_num in sorted_request_groups:
        recs = request_groups[request_num]
        student_count = len(recs)
        original_institution = recs[0].get('المؤسسة_الأصلية', '') if recs else ''
        receiving_institution = recs[0].get('مؤسسة_الإستقبال', '') if recs else ''
        
        # التحقق من الحاجة لصفحة جديدة
        if y + 10 > pdf.h - 20:
            pdf.add_page()
            y = 20
            
            # إعادة رسم رأس الجدول
            pdf.set_font('Arial', 'B', 12)
            pdf.set_fill_color(200, 200, 200)
            x = margin
            for i, header in enumerate(headers):
                pdf.set_xy(x, y)
                pdf.cell(col_widths[i], 12, pdf.ar_text(header), border=1, align='C', fill=True)
                x += col_widths[i]
            y += 12
            pdf.set_font('Arial', '', 11)
        
        x = margin
        data = [str(request_num), str(student_count), original_institution, receiving_institution]
        
        for i, cell in enumerate(data):
            pdf.set_xy(x, y)
            pdf.cell(col_widths[i], 10, pdf.ar_text(cell), border=1, align='C')
            x += col_widths[i]
        
        y += 10
    
    # إحصائيات إضافية
    y += 15
    pdf.set_font('Arial', 'B', 14)
    pdf.set_xy(margin, y)
    pdf.cell(0, 10, pdf.ar_text("إحصائيات حسب المؤسسات:"), ln=True, align='R')
    y += 15
    
    # تجميع حسب المؤسسات الأصلية
    institution_stats = {}
    for request_num in sorted_request_groups:
        recs = request_groups[request_num]
        for rec in recs:
            original_inst = rec.get('المؤسسة_الأصلية', 'غير محدد')
            if original_inst not in institution_stats:
                institution_stats[original_inst] = 0
            institution_stats[original_inst] += 1
    
    pdf.set_font('Arial', 'B', 12)
    
    # جدول إحصائيات المؤسسات
    stat_col_widths = [100, 30]
    stat_headers = ['المؤسسة الأصلية', 'عدد التلاميذ']
    
    x = margin
    for i, header in enumerate(stat_headers):
        pdf.set_xy(x, y)
        pdf.cell(stat_col_widths[i], 12, pdf.ar_text(header), border=1, align='C', fill=True)
        x += stat_col_widths[i]
    
    y += 12
    pdf.set_font('Arial', '', 11)
    
    # ترتيب المؤسسات حسب عدد التلاميذ (تنازلي)
    sorted_institutions = sorted(institution_stats.items(), key=lambda x: x[1], reverse=True)
    
    for institution, count in sorted_institutions:
        # التحقق من الحاجة لصفحة جديدة
        if y + 10 > pdf.h - 20:
            pdf.add_page()
            y = 20
            
            # إعادة رسم رأس الجدول
            pdf.set_font('Arial', 'B', 12)
            pdf.set_fill_color(200, 200, 200)
            x = margin
            for i, header in enumerate(stat_headers):
                pdf.set_xy(x, y)
                pdf.cell(stat_col_widths[i], 12, pdf.ar_text(header), border=1, align='C', fill=True)
                x += stat_col_widths[i]
            y += 12
            pdf.set_font('Arial', '', 11)
        
        x = margin
        pdf.set_xy(x, y)
        pdf.cell(stat_col_widths[0], 10, pdf.ar_text(institution), border=1, align='C')
        x += stat_col_widths[0]
        pdf.set_xy(x, y)
        pdf.cell(stat_col_widths[1], 10, pdf.ar_text(str(count)), border=1, align='C')
        
        y += 10
    
    # تاريخ إنشاء التقرير
    y += 20
    pdf.set_font('Arial', '', 10)
    pdf.set_xy(margin, y)
    current_date = datetime.now().strftime('%Y/%m/%d %H:%M')
    pdf.cell(0, 8, pdf.ar_text(f"تاريخ إنشاء التقرير: {current_date}"), ln=True, align='L')

def print_exams_report(parent=None, level=None, report_title=None, subject_data=None):
    """
    دالة لإنشاء محضر توقيعات المترشحين، يمكن استدعاؤها من واجهات PyQt5

    المعاملات:
        parent: كائن النافذة الأم (لعرض رسائل)
        level: المستوى لتصفية البيانات (اختياري)
        report_title: عنوان المحضر (اختياري)
        subject_data: بيانات المادة (اختياري)

    العوائد:
        (success, output_path, message): ثلاثية تحدد نجاح العملية ومسار الملف ورسالة النتيجة
    """
    try:
        # تحديد مسار قاعدة البيانات
        db_path = os.path.join(os.path.dirname(__file__), 'data.db')

        # تخصيص استعلام SQL حسب المستوى إذا تم تحديده
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()

        # استعلام مخصص إذا تم تحديد المستوى
        if level:
            query = '''
            SELECT رقم_التلميذ, الإسم, النسب, تاريخ_التحويل, المستوى, رقم_الإرسال, رقم_المراسلة, مؤسسة_الإستقبال, المؤسسة_الأصلية, المديرية_الإقليمية_الأصلية, الأكاديمية_الأصلية
            FROM سجلات_الوافدين_والمغادرين
            WHERE المستوى = ? AND ملاحظات = 'لائحة التحويلات (الوافدون)'
            AND رقم_الطلب IS NOT NULL 
            AND رقم_الطلب != '' 
            AND CAST(رقم_الطلب AS TEXT) GLOB '[0-9]*'
            ORDER BY CAST(رقم_التلميذ AS INTEGER), CAST(رقم_الإرسال AS INTEGER)
            '''
            cursor.execute(query, (level,))
        else:
            query = '''
            SELECT رقم_التلميذ, الإسم, النسب, تاريخ_التحويل, المستوى, رقم_الإرسال, رقم_المراسلة, مؤسسة_الإستقبال, المؤسسة_الأصلية, المديرية_الإقليمية_الأصلية, الأكاديمية_الأصلية
            FROM سجلات_الوافدين_والمغادرين
            WHERE ملاحظات = 'لائحة التحويلات (الوافدون)'
            AND رقم_الطلب IS NOT NULL 
            AND رقم_الطلب != '' 
            AND CAST(رقم_الطلب AS TEXT) GLOB '[0-9]*'
            ORDER BY CAST(رقم_التلميذ AS INTEGER), CAST(رقم_الإرسال AS INTEGER)
            '''
            cursor.execute(query)

        cols = [c[0] for c in cursor.description]
        records = []
        for row in cursor.fetchall():
            rec = dict(zip(cols, row))
            # دمج الإسم والنسب
            rec['الاسم_الكامل'] = f"{rec.get('الإسم', '')} {rec.get('النسب', '')}".strip()
            records.append(rec)

        # جلب شعار المؤسسة
        cursor.execute("SELECT ImagePath1 FROM بيانات_المؤسسة LIMIT 1")
        logo_row = cursor.fetchone()
        logo_path = logo_row[0] if logo_row and os.path.exists(logo_row[0]) else None

        conn.close()

        # التحقق من وجود سجلات
        if not records:
            return False, None, "لم يتم العثور على سجلات مطابقة."

        # إنشاء مجلد التقارير إذا لم يكن موجوداً
        reports_dir = os.path.join(os.path.expanduser('~'), 'Desktop', 'تقارير الامتحانات')
        if not os.path.exists(reports_dir):
            os.makedirs(reports_dir)

        # تحديد اسم الملف بناءً على المستوى
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        level_suffix = f"_{level}" if level else ""
        output_path = os.path.join(reports_dir, f"تقرير_الحضور{level_suffix}_{timestamp}.pdf")

        # إنشاء التقرير مع تمرير عنوان التقرير وبيانات المادة
        generate_report(logo_path, records, output_path, report_title, subject_data)

        # فتح الملف بعد إنشائه
        try:
            if sys.platform == 'win32':
                os.startfile(output_path)
            elif sys.platform == 'darwin':  # macOS
                subprocess.call(['open', output_path])
            else:  # Linux
                subprocess.call(['xdg-open', output_path])
        except Exception as e:
            return True, output_path, f"تم إنشاء التقرير ولكن تعذر فتح الملف: {str(e)}"

        return True, output_path, "تم إنشاء التقرير بنجاح."
    except Exception as e:
        traceback.print_exc()
        return False, None, f"حدث خطأ: {str(e)}"

if __name__=='__main__':
    try:
        db = os.path.join(os.path.dirname(__file__), 'data.db')
        logo, recs = fetch_records(db)
        out = os.path.join(os.path.expanduser('~'),'Desktop','تقارير الامتحانات', f"report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.pdf")
        generate_report(logo, recs, out)
    except Exception as e:
        print(f"خطأ: {e}")
        traceback.print_exc()

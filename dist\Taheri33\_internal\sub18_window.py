import sys
import os
from datetime import datetime  # إضافة هذا الاستيراد
from PyQt5.QtWidgets import (QApplication, QMainWindow, QWidget, QVBoxLayout,
                            QHBoxLayout, QLabel, QPushButton, QTableWidget,
                            QTableWidgetItem, QHeaderView, QLineEdit,
                            QTextEdit, QSizePolicy, QFrame)
from PyQt5.QtGui import QFont
from PyQt5.QtCore import Qt
from PyQt5.QtSql import QSqlQuery
import sqlite3
from sub100_window import ConfirmationDialogs  # استيراد فئة رسائل التأكيد المخصصة

class NewsWindow(QMainWindow):
    def __init__(self, parent=None, db=None):
        super().__init__(parent)
        self.db = db
        self.db_path = os.path.join(os.path.dirname(__file__), "data.db")
        self.using_qsql = bool(db and hasattr(db, 'isOpen') and db.isOpen())

        self.setWindowTitle("إدارة الأخبار والنشاطات")

        # إنشاء كونتينر رئيسي يأخذ كامل المساحة
        container = QWidget()
        self.setCentralWidget(container)

        # تخطيط رئيسي للكونتينر
        main_layout = QVBoxLayout(container)
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.setSpacing(0)

        # إضافة إطار خارجي فارغ مع تخطيط مركزي
        outer_frame = QFrame()
        outer_frame.setStyleSheet("""
            QFrame {
                background-color: #f0f2f5;
                border: none;
            }
        """)
        outer_layout = QVBoxLayout(outer_frame)
        outer_layout.setAlignment(Qt.AlignCenter)
        main_layout.addWidget(outer_frame)

        # إنشاء الإطار الداخلي للمحتوى
        self.content_widget = QWidget()
        self.content_widget.setFixedSize(800, 600)
        self.content_widget.setStyleSheet("""
            QWidget {
                background-color: white;
                border-radius: 8px;
            }
        """)
        outer_layout.addWidget(self.content_widget)

        # باقي الإعدادات للمحتوى تتم على content_widget
        self.setup_ui()
        self.load_data()

    def setup_ui(self):
        # تغيير central_widget إلى content_widget
        layout = QVBoxLayout(self.content_widget)

        # تعيين الخط الرئيسي
        main_font = QFont("Calibri", 13, QFont.Bold)
        self.setFont(main_font)

        # تعيين مؤشر اليد للنافذة الرئيسية
        self.content_widget.setCursor(Qt.ArrowCursor)  # المؤشر الافتراضي للخلفية

        # منطقة الإدخال مع تصميم جديد
        input_group = QWidget()
        input_group.setCursor(Qt.ArrowCursor)  # المؤشر الافتراضي لمنطقة الإدخال
        input_group.setStyleSheet("""
            QWidget {
                background-color: #f0f0f0;
                border: 1px solid #cccccc;
                border-radius: 8px;
                padding: 10px;
            }
        """)
        input_layout = QVBoxLayout(input_group)

        # حقل العنوان
        title_layout = QHBoxLayout()
        title_label = QLabel("العنوان:")
        title_label.setFont(main_font)
        title_label.setCursor(Qt.ArrowCursor)  # المؤشر الافتراضي للنص
        self.title_input = QLineEdit()
        self.title_input.setFont(main_font)
        self.title_input.setMinimumWidth(100)  # تعيين العرض الأدنى
        self.title_input.setCursor(Qt.PointingHandCursor)  # تغيير شكل المؤشر إلى يد
        self.title_input.setStyleSheet("""
            QLineEdit {
                padding: 8px;
                border: 2px solid #3498db;
                border-radius: 6px;
                background-color: white;
            }
            QLineEdit:focus {
                border-color: #2980b9;
            }
        """)
        title_layout.addWidget(title_label)
        title_layout.addWidget(self.title_input)
        input_layout.addLayout(title_layout)

        # حقل النشاط
        activity_layout = QHBoxLayout()
        activity_label = QLabel("النشاط:")
        activity_label.setFont(main_font)
        activity_label.setCursor(Qt.ArrowCursor)  # المؤشر الافتراضي للنص
        self.activity_input = QTextEdit()
        self.activity_input.setFont(main_font)
        self.activity_input.setMaximumHeight(100)
        self.activity_input.setMinimumWidth(200)  # تعيين العرض الأدنى
        self.activity_input.setCursor(Qt.PointingHandCursor)  # تغيير شكل المؤشر إلى يد
        self.activity_input.setStyleSheet("""
            QTextEdit {
                padding: 8px;
                border: 2px solid #3498db;
                border-radius: 6px;
                background-color: white;
            }
            QTextEdit:focus {
                border-color: #2980b9;
            }
        """)
        activity_layout.addWidget(activity_label)
        activity_layout.addWidget(self.activity_input)
        input_layout.addLayout(activity_layout)

        # أزرار التحكم
        buttons_layout = QHBoxLayout()
        button_style = """
            QPushButton {
                padding: 8px 20px;
                font-weight: bold;
                border-radius: 6px;
                color: white;
                min-width: 100px;
            }
            QPushButton:hover {
                opacity: 0.8;
            }
        """

        self.add_btn = QPushButton("إضافة")
        self.add_btn.setFont(main_font)
        self.add_btn.setCursor(Qt.PointingHandCursor)  # تغيير شكل المؤشر إلى يد
        self.add_btn.setStyleSheet(button_style + """
            QPushButton {
                background-color: #2ecc71;
            }
            QPushButton:hover {
                background-color: #27ae60;
            }
        """)

        self.update_btn = QPushButton("تحديث")
        self.update_btn.setFont(main_font)
        self.update_btn.setCursor(Qt.PointingHandCursor)  # تغيير شكل المؤشر إلى يد
        self.update_btn.setStyleSheet(button_style + """
            QPushButton {
                background-color: #3498db;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
        """)

        self.delete_btn = QPushButton("حذف")
        self.delete_btn.setFont(main_font)
        self.delete_btn.setCursor(Qt.PointingHandCursor)  # تغيير شكل المؤشر إلى يد
        self.delete_btn.setStyleSheet(button_style + """
            QPushButton {
                background-color: #e74c3c;
            }
            QPushButton:hover {
                background-color: #c0392b;
            }
        """)

        self.add_btn.clicked.connect(self.add_news)
        self.update_btn.clicked.connect(self.update_news)
        self.delete_btn.clicked.connect(self.delete_news)

        self.print_btn = QPushButton("طباعة النشاطات")
        self.print_btn.setFont(main_font)
        self.print_btn.setCursor(Qt.PointingHandCursor)  # تغيير شكل المؤشر إلى يد
        self.print_btn.setStyleSheet(button_style + """
            QPushButton {
                background-color: #9b59b6;
            }
            QPushButton:hover {
                background-color: #8e44ad;
            }
        """)
        self.print_btn.clicked.connect(self.print_activities)

        buttons_layout.addWidget(self.add_btn)
        buttons_layout.addWidget(self.update_btn)
        buttons_layout.addWidget(self.delete_btn)
        buttons_layout.addWidget(self.print_btn)  # إضافة زر الطباعة
        input_layout.addLayout(buttons_layout)

        layout.addWidget(input_group)

        # تصميم الجدول
        self.table = QTableWidget()
        self.table.setFont(main_font)
        self.table.setCursor(Qt.PointingHandCursor)  # تغيير شكل المؤشر إلى يد للجدول
        self.table.verticalHeader().setVisible(False)  # إخفاء عمود الفهرس
        self.table.setStyleSheet("""
            QTableWidget {
                border: 1px solid #cccccc;
                border-radius: 6px;
                background-color: white;
            }
            QTableWidget::item {
                padding: 5px;
                border-bottom: 1px solid #eeeeee;
            }
            QTableWidget::item:selected {
                background-color: #3498db;
                color: white;
            }
            QHeaderView::section {
                background-color: #3498db;
                color: white;
                padding: 8px;
                border: none;
                font-weight: bold;
            }
        """)
        self.table.setColumnCount(3)
        self.table.setHorizontalHeaderLabels(["ID", "العنوان", "النشاط"])
        self.table.horizontalHeader().setFont(main_font)
        self.table.horizontalHeader().setCursor(Qt.PointingHandCursor)  # تغيير شكل المؤشر إلى يد لرأس الجدول
        self.table.verticalHeader().setCursor(Qt.PointingHandCursor)  # تغيير شكل المؤشر إلى يد للفهرس العمودي
        self.table.horizontalHeader().setSectionResizeMode(1, QHeaderView.Stretch)
        self.table.horizontalHeader().setSectionResizeMode(2, QHeaderView.Stretch)
        self.table.clicked.connect(self.on_table_clicked)
        layout.addWidget(self.table)

    def load_data(self):
        try:
            # تفريغ الجدول أولاً
            self.table.setRowCount(0)

            query_str = "SELECT id, العنوان, النشاط FROM اخبار_بنشاط ORDER BY id DESC"

            if self.using_qsql:
                query = QSqlQuery(self.db)
                if not query.exec_(query_str):
                    raise Exception(f"خطأ في تنفيذ الاستعلام: {query.lastError().text()}")

                # جمع النتائج
                records = []
                while query.next():
                    record = []
                    for i in range(3):  # 3 columns
                        record.append(query.value(i))
                    records.append(record)
            else:
                conn = sqlite3.connect(self.db_path)
                cursor = conn.cursor()
                cursor.execute(query_str)
                records = cursor.fetchall()
                conn.close()

            # إضافة البيانات للجدول
            self.table.setRowCount(len(records))
            for row, record in enumerate(records):
                for col, value in enumerate(record):
                    item = QTableWidgetItem(str(value if value is not None else ''))
                    # إضافة تلميح نصي للعناصر
                    if col == 1:  # عمود العنوان
                        item.setToolTip("انقر للتحرير")
                    elif col == 2:  # عمود النشاط
                        item.setToolTip("انقر للتحرير")
                    self.table.setItem(row, col, item)

        except Exception as e:
            ConfirmationDialogs.show_custom_error_message(self, f"حدث خطأ أثناء تحميل البيانات:\n{str(e)}", "خطأ")

    def add_news(self):
        title = self.title_input.text().strip()
        activity = self.activity_input.toPlainText().strip()

        if not title:
            ConfirmationDialogs.show_custom_warning_message(self, "الرجاء إدخال العنوان", "تنبيه")
            return

        try:
            if self.using_qsql:
                query = QSqlQuery(self.db)
                query.prepare("""
                    INSERT INTO اخبار_بنشاط (العنوان, النشاط)
                    VALUES (?, ?)
                """)
                query.addBindValue(title)
                query.addBindValue(activity)
                if not query.exec_():
                    raise Exception(f"خطأ في إضافة البيانات: {query.lastError().text()}")
            else:
                conn = sqlite3.connect(self.db_path)
                cursor = conn.cursor()
                cursor.execute("""
                    INSERT INTO اخبار_بنشاط (العنوان, النشاط)
                    VALUES (?, ?)
                """, (title, activity))
                conn.commit()
                conn.close()

            self.title_input.clear()
            self.activity_input.clear()
            self.load_data()
            ConfirmationDialogs.show_custom_success_message(self, "تمت إضافة الخبر بنجاح", "نجاح")

        except Exception as e:
            ConfirmationDialogs.show_custom_error_message(self, f"حدث خطأ أثناء إضافة الخبر:\n{str(e)}", "خطأ")

    def on_table_clicked(self):
        current_row = self.table.currentRow()
        if current_row >= 0:
            self.title_input.setText(self.table.item(current_row, 1).text())
            self.activity_input.setText(self.table.item(current_row, 2).text())

    def update_news(self):
        current_row = self.table.currentRow()
        if current_row < 0:
            ConfirmationDialogs.show_custom_warning_message(self, "الرجاء تحديد خبر للتحديث", "تنبيه")
            return

        news_id = self.table.item(current_row, 0).text()
        title = self.title_input.text().strip()
        activity = self.activity_input.toPlainText().strip()

        if not title:
            ConfirmationDialogs.show_custom_warning_message(self, "الرجاء إدخال العنوان", "تنبيه")
            return

        try:
            if self.using_qsql:
                query = QSqlQuery(self.db)
                query.prepare("""
                    UPDATE اخبار_بنشاط
                    SET العنوان = ?, النشاط = ?
                    WHERE id = ?
                """)
                query.addBindValue(title)
                query.addBindValue(activity)
                query.addBindValue(news_id)
                if not query.exec_():
                    raise Exception(f"خطأ في تحديث البيانات: {query.lastError().text()}")
            else:
                conn = sqlite3.connect(self.db_path)
                cursor = conn.cursor()
                cursor.execute("""
                    UPDATE اخبار_بنشاط
                    SET العنوان = ?, النشاط = ?
                    WHERE id = ?
                """, (title, activity, news_id))
                conn.commit()
                conn.close()

            self.title_input.clear()
            self.activity_input.clear()
            self.load_data()
            ConfirmationDialogs.show_custom_success_message(self, "تم تحديث الخبر بنجاح", "نجاح")

        except Exception as e:
            ConfirmationDialogs.show_custom_error_message(self, f"حدث خطأ أثناء تحديث الخبر:\n{str(e)}", "خطأ")

    def delete_news(self):
        current_row = self.table.currentRow()
        if current_row < 0:
            ConfirmationDialogs.show_custom_warning_message(self, "الرجاء تحديد خبر للحذف", "تنبيه")
            return

        news_id = self.table.item(current_row, 0).text()
        title = self.table.item(current_row, 1).text()

        # استخدام نافذة التأكيد المخصصة
        reply = ConfirmationDialogs.show_custom_confirmation_dialog(
            self,
            f"هل أنت متأكد من حذف الخبر: '{title}'؟",
            "تأكيد الحذف"
        )

        if reply:
            try:
                if self.using_qsql:
                    query = QSqlQuery(self.db)
                    query.prepare("DELETE FROM اخبار_بنشاط WHERE id = ?")
                    query.addBindValue(news_id)
                    if not query.exec_():
                        raise Exception(f"خطأ في حذف البيانات: {query.lastError().text()}")
                else:
                    conn = sqlite3.connect(self.db_path)
                    cursor = conn.cursor()
                    cursor.execute("DELETE FROM اخبار_بنشاط WHERE id = ?", (news_id,))
                    conn.commit()
                    conn.close()

                self.title_input.clear()
                self.activity_input.clear()
                self.load_data()
                ConfirmationDialogs.show_custom_success_message(self, "تم حذف الخبر بنجاح", "نجاح")

            except Exception as e:
                ConfirmationDialogs.show_custom_error_message(self, f"حدث خطأ أثناء حذف الخبر:\n{str(e)}", "خطأ")

    def print_activities(self):
        """طباعة النشاط المحدد فقط"""
        try:
            # التحقق من وجود صف محدد
            current_row = self.table.currentRow()
            if current_row < 0:
                ConfirmationDialogs.show_custom_warning_message(self, "الرجاء تحديد نشاط للطباعة", "تنبيه")
                return

            # الحصول على بيانات النشاط المحدد فقط
            title = self.table.item(current_row, 1).text()
            activity = self.table.item(current_row, 2).text()

            # إنشاء قائمة تحتوي على النشاط المحدد فقط
            activities = [{
                'title': title,
                'activity': activity
            }]

            current_date = datetime.now().strftime("%Y/%m/%d")

            # محاولة استخدام الطباعة المباشرة أولاً
            try:
                # استيراد وحدة الطباعة المباشرة
                import activity_image_print
                print("استخدام وحدة الطباعة المباشرة بتقنية تحويل النص إلى صورة")

                # استدعاء دالة الطباعة المباشرة
                success = activity_image_print.print_activities_direct(
                    activities=activities,
                    date_str=current_date
                )

                if success:
                    ConfirmationDialogs.show_custom_success_message(self, f"تمت طباعة النشاط '{title}' بنجاح", "نجاح")
                    return
                else:
                    print("فشلت الطباعة المباشرة، محاولة استخدام الطرق البديلة")
            except ImportError:
                print("وحدة الطباعة المباشرة غير متوفرة، استخدام الطريقة القديمة")
            except Exception as e:
                print(f"خطأ في استخدام وحدة الطباعة المباشرة: {e}")

            # استخدام الطريقة القديمة كاحتياط
            from print_test import print_activity_list
            if print_activity_list(activities, current_date):
                ConfirmationDialogs.show_custom_success_message(self, f"تمت طباعة النشاط '{title}' بنجاح", "نجاح")
            else:
                ConfirmationDialogs.show_custom_warning_message(self, "حدث خطأ أثناء طباعة النشاط", "تنبيه")

        except Exception as e:
            ConfirmationDialogs.show_custom_error_message(self, f"حدث خطأ أثناء طباعة النشاط:\n{str(e)}", "خطأ")

if __name__ == "__main__":
    app = QApplication(sys.argv)
    app.setLayoutDirection(Qt.RightToLeft)
    window = NewsWindow()
    window.show()
    sys.exit(app.exec_())

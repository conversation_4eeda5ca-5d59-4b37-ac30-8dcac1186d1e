import sys
import os
import sqlite3
import traceback
import webbrowser
from datetime import datetime
from PyQt5.QtWidgets import (QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
                            QLabel, QPushButton, QTableView, QFrame, QHeaderView,
                            QAbstractItemView, QDialog, QStyle)
from PyQt5.QtGui import QFont, QPalette, QColor, QIcon
from PyQt5.QtCore import Qt, QSize, QAbstractTableModel
from PyQt5.QtSql import QSqlQueryModel, QSqlQuery, QSqlDatabase

# إضافة استيراد لوظائف طباعة HTML
try:
    import arabic_reshaper
    from bidi.algorithm import get_display
    ARABIC_SUPPORT = True
    print("تم تحميل مكتبات معالجة النصوص العربية بنجاح.")
except ImportError:
    ARABIC_SUPPORT = False
    print("تنبيه: مكتبات معالجة النصوص العربية غير متوفرة.")

class SimpleTableModel(QAbstractTableModel):
    """A simple table model for displaying data when QSqlQueryModel isn't working"""

    def __init__(self, data=None, headers=None, parent=None):
        super().__init__(parent)
        self._data = data if data else []
        self._headers = headers if headers else []

    def rowCount(self, parent=None):
        return len(self._data)

    def columnCount(self, parent=None):
        return len(self._headers) if self._headers else (len(self._data[0]) if self._data else 0)

    def data(self, index, role=Qt.DisplayRole):
        if not index.isValid() or not (0 <= index.row() < len(self._data)):
            return None

        if role == Qt.DisplayRole:
            return str(self._data[index.row()][index.column()])

        return None

    def headerData(self, section, orientation, role=Qt.DisplayRole):
        if role == Qt.DisplayRole and orientation == Qt.Horizontal and section < len(self._headers):
            return self._headers[section]
        return None

class ParentVisitManagementWindow(QMainWindow):
    """نافذة معالجة سجلات زيارات أولياء الأمور"""

    def __init__(self, student_code=None, parent=None):
        super().__init__(parent)

        # تخزين معرف الطالب وإعداد الواجهة
        self.student_code = student_code
        print(f"Creating ParentVisitManagementWindow with student_code = {student_code}")

        # قبل إعداد الواجهة، نتأكد من وجود جدول زيارات أولياء الأمور
        self.ensure_visits_table_exists()

        # إعداد الواجهة الرسومية
        self.setup_ui()

        # تحميل بيانات الزيارات
        self.load_visits()

    def ensure_visits_table_exists(self):
        """التأكد من وجود جدول زيارة_ولي_الأمر وإنشائه إذا لم يكن موجودًا"""
        try:
            conn = sqlite3.connect("data.db")
            cursor = conn.cursor()

            # التحقق من وجود الجدول
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='زيارة_ولي_الأمر'")
            if not cursor.fetchone():
                print("Creating parent visits table...")

                # إنشاء جدول زيارة_ولي_الأمر إذا لم يكن موجودًا
                cursor.execute("""
                    CREATE TABLE زيارة_ولي_الأمر (
                        الرقم INTEGER PRIMARY KEY AUTOINCREMENT,
                        الرمز TEXT NOT NULL,
                        السنة_الدراسية TEXT,
                        اسم_الولي TEXT,
                        رقم_البطاقة TEXT,
                        تاريخ_الزيارة TEXT,
                        وقت_الزيارة TEXT,
                        سبب_الزيارة TEXT,
                        مضمون_الزيارة TEXT,
                        تاريخ_التسجيل TEXT
                    )
                """)

                # إضافة بعض البيانات التجريبية إذا كان الرمز محددًا
                if self.student_code:
                    # إنشاء نموذجين من البيانات التجريبية
                    today = datetime.now().strftime("%Y-%m-%d")
                    time_now = datetime.now().strftime("%H:%M")

                    cursor.execute("""
                        INSERT INTO زيارة_ولي_الأمر
                        (الرمز, السنة_الدراسية, اسم_الولي, رقم_البطاقة, تاريخ_الزيارة, وقت_الزيارة, سبب_الزيارة, مضمون_الزيارة, تاريخ_التسجيل)
                        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                    """, (
                        self.student_code,
                        "2023-2024",
                        "ولي الأمر للتلميذ",
                        "AB123456",
                        today,
                        time_now,
                        "التحقق من المستوى الدراسي",
                        "تمت مناقشة المستوى الدراسي للتلميذ وتم الاتفاق على خطة للتحسين.",
                        today
                    ))

                    # سجل ثاني للاختبار
                    cursor.execute("""
                        INSERT INTO زيارة_ولي_الأمر
                        (الرمز, السنة_الدراسية, اسم_الولي, رقم_البطاقة, تاريخ_الزيارة, وقت_الزيارة, سبب_الزيارة, مضمون_الزيارة, تاريخ_التسجيل)
                        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                    """, (
                        self.student_code,
                        "2023-2024",
                        "ولي الأمر للتلميذ",
                        "AB123456",
                        (datetime.now().replace(day=datetime.now().day-15)).strftime("%Y-%m-%d"),
                        "10:30",
                        "مناقشة نتائج الاختبار",
                        "تمت مناقشة نتائج الاختبار ومستوى الأداء.",
                        today
                    ))

                conn.commit()
                print("Parent visits table created with test data.")

            conn.close()

        except Exception as e:
            print(f"Error ensuring visits table exists: {e}")
            traceback.print_exc()
            if 'conn' in locals() and conn:
                conn.close()

    def setup_ui(self):
        """إعداد واجهة النافذة"""
        # إعدادات النافذة الأساسية
        self.setWindowTitle("سجلات زيارات أولياء الأمور")
        self.setMinimumSize(800, 600)

        # تعيين ويدجت مركزي
        self.central_widget = QWidget(self)
        self.setCentralWidget(self.central_widget)

        # إنشاء التخطيط الرئيسي فقط مرة واحدة
        self.main_layout = QVBoxLayout(self.central_widget)
        self.main_layout.setContentsMargins(10, 10, 10, 10)
        self.main_layout.setSpacing(10)

        # تطبيق نمط النافذة
        self.setStyleSheet("""
            QMainWindow {
                background-color: #f0f0f0;
            }
            QLabel {
                font-size: 12pt;
                font-weight: bold;
            }
            QPushButton {
                background-color: #3498db;
                color: white;
                padding: 6px 12px;
                border-radius: 4px;
                font-size: 13pt;
                font-weight: bold;
                font-family: Calibri;
                min-height: 35px;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
            QTableView {
                background-color: white;
                alternate-background-color: #f0f7ff;
                font-family: Calibri;
                font-size: 13pt;
                font-weight: bold;
                color: black;
                border: 1px solid #3498db;
                border-radius: 5px;
                gridline-color: #d0d0d0;
                selection-background-color: #3498db;
                selection-color: white;
            }
            QTableView::item {
                padding: 5px;
                border-bottom: 1px solid #e0e0e0;
            }
            QTableView::item:selected {
                background-color: #3498db;
                color: white;
            }
            QHeaderView::section {
                background-color: #3498db;
                color: white;
                font-weight: bold;
                padding: 8px;
                font-family: Calibri;
                font-size: 13pt;
                border: none;
                border-right: 1px solid #2980b9;
            }
            QHeaderView::section:first {
                border-top-left-radius: 5px;
            }
            QHeaderView::section:last {
                border-top-right-radius: 5px;
                border-right: none;
            }
            QScrollBar:vertical {
                border: none;
                background: #f0f0f0;
                width: 12px;
                margin: 0px;
            }
            QScrollBar::handle:vertical {
                background: #3498db;
                min-height: 20px;
                border-radius: 6px;
            }
            QScrollBar::add-line:vertical, QScrollBar::sub-line:vertical {
                height: 0px;
            }
        """)

        # إطار العنوان مع اسم التلميذ
        self.header_frame = QFrame(self)
        self.header_frame.setFrameShape(QFrame.StyledPanel)
        self.header_frame.setFrameShadow(QFrame.Raised)
        self.header_frame.setStyleSheet("background-color: #3498db; color: white; border-radius: 5px;")

        header_layout = QHBoxLayout(self.header_frame)
        header_layout.setContentsMargins(10, 10, 10, 10)

        # عنوان النافذة
        self.title_label = QLabel("سجلات زيارات أولياء الأمور", self)
        self.title_label.setFont(QFont("Arial", 14, QFont.Bold))
        self.title_label.setStyleSheet("color: white;")
        header_layout.addWidget(self.title_label)

        # زر الإغلاق
        self.close_button = QPushButton("×", self)
        self.close_button.setFont(QFont("Arial", 14, QFont.Bold))
        self.close_button.setFixedSize(35, 35)
        self.close_button.setStyleSheet("""
            QPushButton {
                background-color: #e74c3c;
                color: white;
                border-radius: 17px;
                min-height: 35px;
                font-size: 14pt;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #c0392b;
            }
        """)
        self.close_button.setCursor(Qt.PointingHandCursor)
        self.close_button.clicked.connect(self.close)
        header_layout.addWidget(self.close_button)

        # إضافة إطار العنوان للتخطيط الرئيسي
        self.main_layout.addWidget(self.header_frame)

        # عنوان جدول الزيارات
        self.records_label = QLabel("سجلات الزيارات", self)
        self.records_label.setAlignment(Qt.AlignCenter)
        self.records_label.setFont(QFont("Calibri", 14, QFont.Bold))
        self.records_label.setStyleSheet("""
            color: #2c3e50;
            background-color: #ecf0f1;
            padding: 8px;
            border-radius: 5px;
            border-bottom: 2px solid #3498db;
        """)
        self.main_layout.addWidget(self.records_label)

        # إطار للجدول لإضافة ظل وتأثيرات بصرية
        table_frame = QFrame(self)
        table_frame.setFrameShape(QFrame.StyledPanel)
        table_frame.setFrameShadow(QFrame.Raised)
        table_frame.setStyleSheet("""
            QFrame {
                background-color: white;
                border-radius: 8px;
                border: 1px solid #d0d0d0;
            }
        """)

        # تخطيط للإطار
        table_layout = QVBoxLayout(table_frame)
        table_layout.setContentsMargins(5, 5, 5, 5)

        # جدول الزيارات
        self.visits_table = QTableView(self)
        self.visits_table.setAlternatingRowColors(True)
        self.visits_table.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.visits_table.setSelectionMode(QAbstractItemView.SingleSelection)
        self.visits_table.setEditTriggers(QAbstractItemView.NoEditTriggers)
        self.visits_table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
        self.visits_table.verticalHeader().setVisible(False)
        self.visits_table.setShowGrid(True)
        self.visits_table.setGridStyle(Qt.SolidLine)

        # تعيين ارتفاع الصفوف
        self.visits_table.verticalHeader().setDefaultSectionSize(35)

        # تطبيق خط Calibri حجم 13 أسود غامق للجدول
        table_font = QFont("Calibri", 13)
        table_font.setBold(True)
        self.visits_table.setFont(table_font)

        # تعيين مؤشر اليد عند المرور فوق الصفوف
        self.visits_table.setCursor(Qt.PointingHandCursor)

        # إضافة الجدول إلى إطار الجدول
        table_layout.addWidget(self.visits_table)

        self.main_layout.addWidget(table_frame)

        # إطار للأزرار
        buttons_frame = QFrame(self)
        buttons_frame.setFrameShape(QFrame.StyledPanel)
        buttons_frame.setFrameShadow(QFrame.Raised)
        buttons_frame.setStyleSheet("""
            QFrame {
                background-color: #f8f9fa;
                border-radius: 8px;
                border: 1px solid #d0d0d0;
            }
        """)

        # تخطيط للأزرار
        self.buttons_layout = QHBoxLayout(buttons_frame)
        self.buttons_layout.setContentsMargins(10, 10, 10, 10)
        self.buttons_layout.setSpacing(15)

        self.delete_button = QPushButton("حذف الزيارة المحددة", self)
        self.delete_button.setStyleSheet("""
            background-color: #e74c3c;
            min-height: 35px;
            font-size: 13pt;
            font-weight: bold;
            font-family: Calibri;
        """)
        self.delete_button.setCursor(Qt.PointingHandCursor)
        self.delete_button.clicked.connect(self.delete_selected_visit)
        self.buttons_layout.addWidget(self.delete_button)

        # إضافة زر طباعة ورقة الزيارة
        self.print_visit_button = QPushButton("طباعة ورقة الزيارة", self)
        self.print_visit_button.setStyleSheet("""
            background-color: #27ae60;
            min-height: 35px;
            font-size: 13pt;
            font-weight: bold;
            font-family: Calibri;
        """)
        self.print_visit_button.setCursor(Qt.PointingHandCursor)
        self.print_visit_button.clicked.connect(self.print_visit_sheet)
        self.buttons_layout.addWidget(self.print_visit_button)

        self.print_button = QPushButton("طباعة السجلات", self)
        self.print_button.setStyleSheet("""
            min-height: 35px;
            font-size: 13pt;
            font-weight: bold;
            font-family: Calibri;
        """)
        self.print_button.setCursor(Qt.PointingHandCursor)
        self.print_button.clicked.connect(self.print_visits)
        self.buttons_layout.addWidget(self.print_button)

        self.main_layout.addWidget(buttons_frame)

    def update_window_title(self, student_name):
        """تحديث عنوان النافذة باسم الطالب"""
        self.setWindowTitle(f"سجلات زيارات أولياء الأمور - {student_name}")
        self.title_label.setText(f"سجلات زيارات أولياء الأمور - {student_name}")

    def update_student_info(self, student_code, student_name):
        """تحديث معلومات الطالب في الواجهة"""
        if student_code:
            self.student_code = student_code

        # تحديث عنوان النافذة باسم الطالب
        self.update_window_title(student_name)

        # إعادة تحميل البيانات بعد تحديث معرف الطالب
        self.load_visits()

    def load_visits(self):
        """تحميل بيانات زيارات أولياء الأمور للطالب الحالي"""
        if not self.student_code:
            # إظهار رسالة إذا لم يتم تحديد طالب
            self.records_label.setText("لم يتم تحديد طالب")
            return

        try:
            print(f"Loading visits for student code: {self.student_code}")

            # استخدام sqlite3 مباشرة (أكثر موثوقية) لتجنب مشاكل QSqlQueryModel
            conn = sqlite3.connect("data.db")
            cursor = conn.cursor()

            # التحقق من وجود الجدول - سيكون موجودًا بالفعل بسبب ensure_visits_table_exists
            cursor.execute("""
                SELECT الرقم, اسم_الولي, رقم_البطاقة, تاريخ_الزيارة, وقت_الزيارة, سبب_الزيارة
                FROM زيارة_ولي_الأمر
                WHERE الرمز = ?
                ORDER BY تاريخ_الزيارة DESC, وقت_الزيارة DESC
            """, (self.student_code,))

            visits = cursor.fetchall()
            conn.close()

            # تعريف أسماء الأعمدة
            headers = ["الرقم", "اسم الولي", "رقم البطاقة", "تاريخ الزيارة", "وقت الزيارة", "سبب الزيارة"]

            # إنشاء نموذج البيانات المخصص وتعيينه للجدول
            model = SimpleTableModel(visits, headers)
            self.visits_table.setModel(model)

            # تحديث عنوان عدد الزيارات
            if len(visits) == 0:
                self.records_label.setText("لا توجد سجلات زيارات")
                self.records_label.setStyleSheet("""
                    color: #7f8c8d;
                    background-color: #ecf0f1;
                    padding: 8px;
                    border-radius: 5px;
                    border-bottom: 2px solid #95a5a6;
                """)
            else:
                self.records_label.setText(f"سجلات الزيارات: {len(visits)} زيارة")
                self.records_label.setStyleSheet("""
                    color: #2c3e50;
                    background-color: #ecf0f1;
                    padding: 8px;
                    border-radius: 5px;
                    border-bottom: 2px solid #3498db;
                """)
            print(f"Successfully loaded {len(visits)} visits for student {self.student_code}")

        except Exception as e:
            print(f"Error loading visits: {e}")
            traceback.print_exc()
            self.records_label.setText(f"خطأ في تحميل البيانات: {str(e)}")

    def show_custom_message(self, title, message, message_type="info", parent=None, width=500, height=250):
        """عرض رسالة مخصصة بتصميم أنيق"""
        dialog = QDialog(parent or self)
        dialog.setWindowTitle(title)
        dialog.setFixedSize(width, height)  # تحديد حجم النافذة
        dialog.setLayoutDirection(Qt.RightToLeft)

        # تعيين أيقونة النافذة
        try:
            app_icon = QIcon("01.ico")
            dialog.setWindowIcon(app_icon)
        except Exception as e:
            print(f"خطأ في تحميل أيقونة البرنامج: {e}")

        # تحديد لون الخلفية والأيقونة حسب نوع الرسالة
        if message_type == "warning":
            bg_color = "#fff3cd"
            border_color = "#ffeeba"
            icon_color = "#f39c12"
            text_color = "#2c3e50"  # لون النص الأزرق الغامق
            button_color = "#f39c12"
            button_hover = "#e67e22"
            message_bg_color = "white"
            message_border_color = "#ffeeba"
        elif message_type == "error":
            bg_color = "#f8d7da"
            border_color = "#f5c6cb"
            icon_color = "#e74c3c"
            text_color = "#2c3e50"  # لون النص الأزرق الغامق
            button_color = "#e74c3c"
            button_hover = "#c0392b"
            message_bg_color = "white"
            message_border_color = "#f5c6cb"
        elif message_type == "question":
            bg_color = "#d1ecf1"
            border_color = "#bee5eb"
            icon_color = "#3498db"
            text_color = "#2c3e50"  # لون النص الأزرق الغامق
            button_color = "#3498db"
            button_hover = "#2980b9"
            message_bg_color = "white"
            message_border_color = "#bee5eb"
        else:  # info
            bg_color = "#d4edda"
            border_color = "#c3e6cb"
            icon_color = "#27ae60"
            text_color = "#2c3e50"  # لون النص الأزرق الغامق
            button_color = "#27ae60"
            button_hover = "#219a52"
            message_bg_color = "white"
            message_border_color = "#c3e6cb"

        # تنسيق النافذة
        dialog.setStyleSheet(f"""
            QDialog {{
                background-color: {bg_color};
                border: 2px solid {border_color};
                border-radius: 5px;
            }}
            QLabel#titleLabel {{
                color: {text_color};
                font-weight: bold;
                font-family: Calibri;
                font-size: 14pt;
            }}
            QLabel#messageLabel {{
                color: {text_color};
                font-weight: bold;
                font-family: Calibri;
                font-size: 12pt;
                padding: 10px;
                background-color: {message_bg_color};
                border: 1px solid {message_border_color};
                border-radius: 5px;
            }}
            QPushButton {{
                background-color: {button_color};
                color: white;
                border-radius: 5px;
                padding: 8px 16px;
                font-weight: bold;
                font-family: Calibri;
                font-size: 13pt;
                min-height: 35px;
            }}
            QPushButton:hover {{
                background-color: {button_hover};
            }}
        """)

        # إنشاء تخطيط النافذة
        layout = QVBoxLayout(dialog)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(15)

        # إضافة عنوان الرسالة
        title_label = QLabel(title)
        title_label.setObjectName("titleLabel")
        title_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(title_label)

        # إضافة أيقونة حسب نوع الرسالة
        icon_label = QLabel()
        icon_label.setAlignment(Qt.AlignCenter)

        # تحديد الأيقونة المناسبة
        if message_type == "warning":
            icon = dialog.style().standardIcon(QStyle.SP_MessageBoxWarning)
        elif message_type == "error":
            icon = dialog.style().standardIcon(QStyle.SP_MessageBoxCritical)
        elif message_type == "question":
            icon = dialog.style().standardIcon(QStyle.SP_MessageBoxQuestion)
        else:  # info
            icon = dialog.style().standardIcon(QStyle.SP_MessageBoxInformation)

        icon_label.setPixmap(icon.pixmap(48, 48))
        layout.addWidget(icon_label)

        # إضافة نص الرسالة في مربع نص واضح
        message_label = QLabel(message)
        message_label.setObjectName("messageLabel")
        message_label.setAlignment(Qt.AlignRight | Qt.AlignVCenter)
        message_label.setWordWrap(True)
        message_label.setMinimumHeight(height - 170)  # تحديد ارتفاع أدنى للنص بناءً على ارتفاع النافذة
        layout.addWidget(message_label)

        # إضافة أزرار حسب نوع الرسالة
        buttons_layout = QHBoxLayout()

        if message_type == "question":
            # زر نعم
            yes_button = QPushButton("نعم")
            yes_button.setCursor(Qt.PointingHandCursor)
            yes_button.clicked.connect(lambda: dialog.done(QDialog.Accepted))
            buttons_layout.addWidget(yes_button)

            # زر لا
            no_button = QPushButton("لا")
            no_button.setCursor(Qt.PointingHandCursor)
            no_button.clicked.connect(lambda: dialog.done(QDialog.Rejected))
            buttons_layout.addWidget(no_button)
        else:
            # زر موافق
            ok_button = QPushButton("موافق")
            ok_button.setCursor(Qt.PointingHandCursor)
            ok_button.clicked.connect(dialog.accept)
            buttons_layout.addWidget(ok_button)

        layout.addLayout(buttons_layout)

        # عرض النافذة وإرجاع النتيجة
        return dialog.exec_()

    def delete_selected_visit(self):
        """حذف الزيارة المحددة"""
        # التحقق من وجود صف محدد
        if not self.visits_table.selectionModel() or not self.visits_table.selectionModel().hasSelection():
            self.show_custom_message(
                "تنبيه",
                "الرجاء تحديد زيارة من الجدول أولاً قبل محاولة الحذف.",
                "warning",
                width=550, height=300
            )
            return

        try:
            # الحصول على الصف المحدد
            index = self.visits_table.selectionModel().selectedRows()[0]
            model = self.visits_table.model()

            if not model or index.row() >= model.rowCount():
                self.show_custom_message(
                    "خطأ",
                    "تعذر تحديد السجل المطلوب. يرجى المحاولة مرة أخرى.",
                    "error",
                    width=550, height=300
                )
                return

            visit_id = model.data(model.index(index.row(), 0), Qt.DisplayRole)
            parent_name = model.data(model.index(index.row(), 1), Qt.DisplayRole) or "غير محدد"
            visit_date = model.data(model.index(index.row(), 3), Qt.DisplayRole) or "غير محدد"

            # تأكيد الحذف
            reply = self.show_custom_message(
                "تأكيد حذف زيارة ولي الأمر",
                f"هل أنت متأكد من رغبتك في حذف زيارة ولي الأمر:\n\nاسم الولي: {parent_name}\nتاريخ الزيارة: {visit_date}\nرقم الزيارة: {visit_id}\n\nتنبيه: لا يمكن التراجع عن هذه العملية بعد تنفيذها.",
                "question",
                width=550, height=300
            )

            if reply == QDialog.Accepted:
                conn = sqlite3.connect("data.db")
                cursor = conn.cursor()
                cursor.execute("DELETE FROM زيارة_ولي_الأمر WHERE الرقم = ?", (visit_id,))
                conn.commit()
                conn.close()

                self.load_visits()  # إعادة تحميل البيانات
                self.show_custom_message(
                    "تم حذف زيارة ولي الأمر بنجاح",
                    f"تم حذف سجل زيارة ولي الأمر '{parent_name}' بتاريخ {visit_date} بنجاح من قاعدة البيانات.",
                    "info",
                    width=550, height=300
                )
        except Exception as e:
            self.show_custom_message(
                "خطأ في الحذف",
                f"حدث خطأ أثناء محاولة حذف الزيارة:\n{str(e)}",
                "error",
                width=550, height=300
            )
            print(f"Error deleting visit: {e}")
            traceback.print_exc()

    def print_visits(self):
        """طباعة تقرير سجلات الزيارات"""
        if not self.student_code:
            self.show_custom_message(
                "تنبيه",
                "الرجاء تحديد طالب أولاً قبل محاولة طباعة سجلات الزيارات.",
                "warning",
                width=550, height=300
            )
            return

        if not self.visits_table.model() or self.visits_table.model().rowCount() == 0:
            self.show_custom_message(
                "معلومات",
                "لا توجد سجلات زيارات متاحة للطباعة لهذا الطالب.\n\nيرجى التأكد من وجود زيارات مسجلة أولاً.",
                "info",
                width=550, height=300
            )
            return

        try:
            # استخدام الدالة من ملف print8.py
            try:
                from print8 import print_parent_visits_report
                success, _ = print_parent_visits_report(self.student_code)  # استخدام _ لتجاهل المتغير غير المستخدم
                if not success:
                    self.show_custom_message(
                        "خطأ",
                        "فشل إنشاء تقرير سجلات الزيارات. يرجى التحقق من السجلات للمزيد من المعلومات.",
                        "error",
                        width=550, height=300
                    )
            except ImportError:
                self.show_custom_message(
                    "خطأ",
                    "لم يتم العثور على ملف print8.py. يرجى التأكد من وجود الملف في نفس المجلد.",
                    "error",
                    width=550, height=300
                )
                print("Error: print8.py module not found")
            except Exception as e:
                self.show_custom_message(
                    "خطأ في إنشاء تقرير الزيارات",
                    f"حدث خطأ أثناء محاولة إنشاء تقرير الزيارات:\n{str(e)}",
                    "error",
                    width=550, height=300
                )
                print(f"Error in print_parent_visits_report: {e}")
                traceback.print_exc()
        except Exception as e:
            self.show_custom_message(
                "خطأ في إنشاء التقرير",
                f"حدث خطأ أثناء محاولة إنشاء تقرير الزيارات:\n{str(e)}",
                "error",
                width=550, height=300
            )
            print(f"Error printing visits: {e}")
            traceback.print_exc()

    def print_visit_sheet(self):
        """طباعة ورقة زيارة ولي الأمر المحددة"""
        # التحقق من وجود زيارة محددة
        if not self.visits_table.selectionModel() or not self.visits_table.selectionModel().hasSelection():
            self.show_custom_message(
                "تنبيه",
                "الرجاء تحديد زيارة من الجدول أولاً قبل محاولة طباعة ورقة الزيارة.",
                "warning",
                width=550, height=300
            )
            return

        try:
            # الحصول على الصف المحدد
            index = self.visits_table.selectionModel().selectedRows()[0]
            model = self.visits_table.model()

            if not model or index.row() >= model.rowCount():
                self.show_custom_message(
                    "خطأ",
                    "تعذر تحديد السجل المطلوب. يرجى المحاولة مرة أخرى.",
                    "error",
                    width=550, height=300
                )
                return

            # استخراج معرف الزيارة المحددة
            visit_id = model.data(model.index(index.row(), 0), Qt.DisplayRole)

            # استخدام الدالة من ملف print7.py
            try:
                from print7 import print_parent_visit_sheet
                success, _ = print_parent_visit_sheet(visit_id)  # استخدام _ لتجاهل المتغير غير المستخدم
                if not success:
                    self.show_custom_message(
                        "خطأ",
                        "فشل إنشاء ورقة الزيارة. يرجى التحقق من السجلات للمزيد من المعلومات.",
                        "error",
                        width=550, height=300
                    )
            except ImportError:
                self.show_custom_message(
                    "خطأ",
                    "لم يتم العثور على ملف print7.py. يرجى التأكد من وجود الملف في نفس المجلد.",
                    "error",
                    width=550, height=300
                )
                print("Error: print7.py module not found")
            except Exception as e:
                self.show_custom_message(
                    "خطأ في إنشاء ورقة الزيارة",
                    f"حدث خطأ أثناء محاولة إنشاء ورقة الزيارة:\n{str(e)}",
                    "error",
                    width=550, height=300
                )
                print(f"Error in print_parent_visit_sheet: {e}")
                traceback.print_exc()

        except Exception as e:
            self.show_custom_message(
                "خطأ في إنشاء ورقة الزيارة",
                f"حدث خطأ أثناء محاولة إنشاء ورقة الزيارة:\n{str(e)}",
                "error",
                width=550, height=300
            )
            print(f"Error printing visit sheet: {e}")
            traceback.print_exc()

    def generate_visit_sheet_html(self, student_info, visit_data):
        """إنشاء محتوى HTML لورقة زيارة ولي الأمر"""
        # تحويل النص العربي
        def fix_arabic_text(text):
            if not text:
                return ""
            text = str(text).strip()
            try:
                if ARABIC_SUPPORT:
                    reshaped = arabic_reshaper.reshape(text)
                    return reshaped
                else:
                    return text
            except Exception:
                return text

        # الحصول على بيانات المؤسسة
        institution_name, school_year, logo_path = self.get_institution_info()

        # تحويل مسار الشعار إلى URL صالح لاستخدامه في HTML
        logo_html = ""
        if logo_path and os.path.exists(logo_path):
            # معالجة المسار أولاً خارج F-string
            path_fixed = os.path.abspath(logo_path).replace('\\', '/')
            logo_url = f"file:///{path_fixed}"
            logo_html = f"""
            <div style="text-align: center; margin-bottom: 20px;">
                <img src="{logo_url}" alt="شعار المؤسسة" style="max-width: 200px; max-height: 100px;">
            </div>
            """

        # إنشاء هيكل HTML
        html_content = f"""<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ورقة زيارة ولي الأمر - {fix_arabic_text(student_info['student_name'])}</title>
    <style>
        @font-face {{
            font-family: 'Calibri';
            src: local('Calibri');
        }}
        body {{
            font-family: 'Calibri', 'Arial', sans-serif;
            margin: 15px;
            direction: rtl;
            text-align: right;
            background-color: #f9f9f9;
        }}
        .header {{
            text-align: center;
            margin-bottom: 10px;
        }}
        .header h1 {{
            color: #2c3e50;
            margin-bottom: 5px;
            font-family: 'Calibri', 'Arial', sans-serif;
            font-size: 14pt;
            font-weight: bold;
        }}
        .header h2 {{
            color: #3498db;
            margin-top: 0;
            margin-bottom: 5px;
            font-family: 'Calibri', 'Arial', sans-serif;
            font-size: 14pt;
            font-weight: bold;
        }}
        .header h3 {{
            margin-top: 0;
            font-family: 'Calibri', 'Arial', sans-serif;
            font-size: 13pt;
            font-weight: bold;
        }}
        .info-box {{
            border: 1px solid #ddd;
            padding: 8px;
            margin-bottom: 10px;
            border-radius: 5px;
            background-color: #f0f7ff;
        }}
        .visit-box {{
            border: 1px solid #3498db;
            padding: 8px;
            margin-bottom: 10px;
            border-radius: 5px;
            background-color: #f8f9fa;
        }}
        .visit-details {{
            border: 1px solid #ddd;
            padding: 8px;
            margin-top: 8px;
            margin-bottom: 10px;
            border-radius: 5px;
            background-color: #fff;
            min-height: 80px;
        }}
        .label {{
            font-weight: bold;
            color: #2980b9;
            margin-bottom: 2px;
            font-family: 'Calibri', 'Arial', sans-serif;
            font-size: 14pt;
        }}
        .data {{
            font-family: 'Calibri', 'Arial', sans-serif;
            font-size: 13pt;
            font-weight: bold;
            margin-bottom: 5px;
        }}
        .footer {{
            margin-top: 15px;
            text-align: center;
            font-size: 0.9em;
            color: #7f8c8d;
        }}
        .signature-area {{
            display: flex;
            justify-content: space-between;
            margin-top: 20px;
        }}
        .signature-box {{
            border-top: 1px solid #2c3e50;
            padding-top: 5px;
            width: 200px;
            text-align: center;
            font-family: 'Calibri', 'Arial', sans-serif;
            font-size: 13pt;
            font-weight: bold;
        }}
        .grid-container {{
            display: grid;
            grid-template-columns: 1fr 1fr;
            grid-gap: 10px;
        }}
        .grid-item {{
            margin-bottom: 2px;
        }}
    </style>
</head>
<body>
    {logo_html}
    <div class="header">
        <h1>{fix_arabic_text(institution_name)}</h1>
        <h2>ورقة زيارة ولي الأمر</h2>
        <h3>السنة الدراسية: {fix_arabic_text(school_year)}</h3>
    </div>

    <div class="info-box">
        <div class="grid-container">
            <div class="grid-item">
                <strong class="label">رمز التلميذ:</strong>
                <span class="data">{fix_arabic_text(student_info['student_code'])}</span>
            </div>
            <div class="grid-item">
                <strong class="label">الاسم والنسب:</strong>
                <span class="data">{fix_arabic_text(student_info['student_name'])}</span>
            </div>
            <div class="grid-item">
                <strong class="label">المستوى:</strong>
                <span class="data">{fix_arabic_text(student_info['level'])}</span>
            </div>
            <div class="grid-item">
                <strong class="label">القسم:</strong>
                <span class="data">{fix_arabic_text(student_info['class_name'])}</span>
            </div>
        </div>
    </div>

    <div class="visit-box">
        <div class="label">بيانات الزيارة:</div>
        <div class="grid-container">
            <div class="grid-item">
                <strong class="label">رقم الزيارة:</strong>
                <span class="data">{fix_arabic_text(visit_data['visit_id'])}</span>
            </div>
            <div class="grid-item">
                <strong class="label">اسم الولي:</strong>
                <span class="data">{fix_arabic_text(visit_data['parent_name'])}</span>
            </div>
            <div class="grid-item">
                <strong class="label">رقم البطاقة:</strong>
                <span class="data">{fix_arabic_text(visit_data['id_number'])}</span>
            </div>
            <div class="grid-item">
                <strong class="label">تاريخ الزيارة:</strong>
                <span class="data">{fix_arabic_text(visit_data['visit_date'])}</span>
            </div>
            <div class="grid-item">
                <strong class="label">وقت الزيارة:</strong>
                <span class="data">{fix_arabic_text(visit_data['visit_time'])}</span>
            </div>
            <div class="grid-item">
                <strong class="label">سبب الزيارة:</strong>
                <span class="data">{fix_arabic_text(visit_data['visit_reason'])}</span>
            </div>
        </div>

        <div class="label">مضمون الزيارة:</div>
        <div class="visit-details data">
            {fix_arabic_text(visit_data['visit_details'])}
        </div>
    </div>

    <div class="signature-area">
        <div class="signature-box">
            <div>توقيع ولي الأمر</div>
        </div>
        <div class="signature-box">
            <div>توقيع الحارس العام</div>
        </div>
    </div>

    <div class="footer">
        <p>تم إنشاء هذا التقرير بتاريخ: {datetime.now().strftime("%Y-%m-%d")}</p>
    </div>
</body>
</html>
"""

        return html_content

    def get_student_info(self):
        """الحصول على بيانات الطالب من قاعدة البيانات"""
        student_info = {
            'student_code': self.student_code,
            'student_name': '',
            'level': '',
            'class_name': ''
        }

        try:
            conn = sqlite3.connect("data.db")
            cursor = conn.cursor()

            # استعلام الحصول على اسم الطالب وصفه من السجل العام واللوائح
            cursor.execute("""
                SELECT sg.الاسم_والنسب, l.المستوى, l.القسم
                FROM السجل_العام sg
                LEFT JOIN اللوائح l ON sg.الرمز = l.الرمز
                WHERE sg.الرمز = ?
                ORDER BY l.السنة_الدراسية DESC
                LIMIT 1
            """, (self.student_code,))

            result = cursor.fetchone()
            conn.close()

            if result:
                student_info['student_name'] = result[0]
                student_info['level'] = result[1]
                student_info['class_name'] = result[2]

        except Exception as e:
            print(f"Error fetching student info: {e}")
            traceback.print_exc()

        return student_info

    def get_institution_info(self):
        """الحصول على بيانات المؤسسة من قاعدة البيانات"""
        institution_name = "المؤسسة التعليمية"
        school_year = ""
        logo_path = None  # إضافة متغير لمسار الشعار

        try:
            conn = sqlite3.connect("data.db")
            cursor = conn.cursor()

            # إضافة استعلام للحصول على مسار الشعار
            cursor.execute("SELECT المؤسسة, السنة_الدراسية, ImagePath1 FROM بيانات_المؤسسة LIMIT 1")
            result = cursor.fetchone()
            conn.close()

            if result:
                institution_name = result[0] or institution_name
                school_year = result[1] or school_year
                logo_path = result[2]  # مسار الشعار من قاعدة البيانات

        except Exception as e:
            print(f"Error fetching institution info: {e}")
            traceback.print_exc()

        return institution_name, school_year, logo_path  # إضافة مسار الشعار إلى القيم المرجعة





    def open_file(self, filepath):
        """فتح الملف باستخدام التطبيق الافتراضي للنظام"""
        try:
            # استخدام الدالة من ملف print8.py
            try:
                from print8 import open_file
                return open_file(filepath)
            except ImportError:
                # استخدام الطريقة القديمة إذا لم يتم العثور على الملف
                file_url = f"file://{os.path.abspath(filepath)}"
                webbrowser.open(file_url)
                return True
        except Exception as e:
            print(f"Error opening file: {e}")
            traceback.print_exc()
            return False

    # Add a method to clean up resources when the window closes
    def clear_references(self):
        """تنظيف المراجع قبل إغلاق النافذة"""
        if hasattr(self, 'visits_table') and self.visits_table:
            # Clear model to prevent memory leaks
            self.visits_table.setModel(None)

        # Remove widgets from layouts explicitly
        if hasattr(self, 'buttons_layout'):
            while self.buttons_layout.count():
                item = self.buttons_layout.takeAt(0)
                if item.widget():
                    item.widget().setParent(None)

    def closeEvent(self, event):
        """معالجة إغلاق النافذة"""
        # Clean up resources
        self.clear_references()
        super().closeEvent(event)

# تنفيذ اختباري للتأكد من عمل النافذة بشكل مستقل
if __name__ == "__main__":
    app = QApplication(sys.argv)
    app.setLayoutDirection(Qt.RightToLeft)  # تعيين الاتجاه من اليمين إلى اليسار

    # اختبار النافذة مع معرف طالب تجريبي
    window = ParentVisitManagementWindow(student_code="A123456")
    window.update_student_info("A123456", "طالب تجريبي للاختبار")
    window.show()
    window.raise_()  # تأكيد أن النافذة ستظهر في المقدمة
    window.activateWindow()  # تنشيط النافذة

    print("Window should be visible now. If not, check if it's behind other windows.")

    sys.exit(app.exec_())

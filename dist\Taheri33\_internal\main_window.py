# main_window.py
# -*- coding: utf-8 -*-



import sys
import os
from PyQt5.QtWidgets import QApplication, QMainWindow
from PyQt5.QtGui import QIcon

# --- المكتبات المطلوبة للتضمين في التطبيق النهائي ---
import simple6_window
import print2_test
import styles
import sub0_window
import sub1_window
import sub2_window
import sub3_window
import sub4_window
import sub5_window
import sub6_window
import sub7_window
import sub8_window
import sub9_window
import sub10_window
import sub11_window
import sub12_window
import sub13_window
import sub14_window
import sub16_window
import sub17_window
import sub18_window
import sub19_window
import sub20_window
import sub23_window
import sub21_window
import sub22_window
import violations_table
import absence_reports
import attendance_report
import default_settings_window
import help_texts
# --- نهاية قائمة المكتبات المطلوبة للتضمين ---

# ـــــــــ إجبار PyInstaller على تضمين الوحدات المفقودة ديناميكيًّا ـــــــــ
try:
    # هذه الوحدات مطلوبة للتضمين في التطبيق النهائي
    # لكن قد لا تكون مستخدمة مباشرة في الكود
    import print0
    import print1
    import print3
    import print4
    import print5
    import print6
    import print7
    import print8
    import print9
    import print10
    import thermal_printer
    import print_student_record_improved
    import print_violation_report
    import custom_messages
    import arabic_pdf_report
    import professional_exam_table
    import second_semester_helper
    import set_default_printer
    import split_attendance_report
    import student_card_ui
    import templates_manager
except ImportError:
    # تجاهل إذا أحدها غير موجود
    pass
# ـــــــــ نهاية إجبار التضمين ـــــــــ

# --- استيراد نافذة طباعة اللوائح ---
try:
    from sub23_window import PrintListsWindow
    PRINT_LISTS_WINDOW_AVAILABLE = True
    print("INFO: تم استيراد PrintListsWindow بنجاح من sub23_window.py.")
except ImportError as e:
    print(f"تحذير: لم يتم العثور على ملف 'sub23_window.py' أو الكلاس PrintListsWindow فيه.\nالخطأ: {e}")
    PRINT_LISTS_WINDOW_AVAILABLE = False

# --- استيراد نافذة بطاقة الأقسام ---
try:
    from sub20_window import RegulationsCardWindow
    SECTIONS_CARD_AVAILABLE = True
    print("INFO: تم استيراد RegulationsCardWindow بنجاح من sub20_window.py.")
except ImportError as e:
    print(f"تحذير: لم يتم العثور على ملف 'sub20_window.py' أو الكلاس RegulationsCardWindow فيه.\nالخطأ: {e}")
    SECTIONS_CARD_AVAILABLE = False
# --- نهاية استيراد نافذة طباعة اللوائح ---

class MyWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("نافذة مع أيقونة")
        icon_path = os.path.join(os.path.dirname(__file__), "01.ico")
        self.setWindowIcon(QIcon(icon_path))
        self.resize(400, 300)



import sys
import os
from PyQt5.QtWidgets import (QApplication, QMainWindow, QWidget, QPushButton, QVBoxLayout,
                             QHBoxLayout, QFrame, QDesktopWidget,
                             QStackedWidget, QMessageBox, QLabel, QSpacerItem, QSizePolicy,
                             QTableWidget, QTabWidget)  # إضافة QTabWidget
from PyQt5.QtGui import QFont, QIcon, QColor
from PyQt5.QtCore import Qt, QTimer, QSize, QCoreApplication

# --- >>> استيراد الدوال والكلاسات من الملف الصحيح (sub4_window.py) <<< ---
try:
    # استيراد الدوال العامة للاتصال وقراءة السنة الدراسية من sub4_window.py
    from sub4_window import create_connection, get_current_academic_year
    # استيراد نافذة اللوائح والأقسام من sub4_window.py
    from sub4_window import Sub4Window, Simple00Window
    SIMPLE_SEARCH_AVAILABLE = True
    DB_FUNCTIONS_AVAILABLE = True
    print("INFO: تم استيراد Sub4Window ودوال الاتصال بنجاح من sub4_window.py.")
except ImportError as e:
    print(f"خطأ فادح: لم يتم العثور على ملف 'sub4_window.py' أو الأسماء المطلوبة فيه.\nالخطأ: {e}")
    # تعريف بدائل وهمية لمنع الانهيار الكامل
    Sub4Window = None
    Simple00Window = None
    SIMPLE_SEARCH_AVAILABLE = False
    def create_connection(): return False, None, "فشل استيراد الدالة 'create_connection' من 'sub4_window.py'"
    def get_current_academic_year(db): return None, "فشل استيراد الدالة 'get_current_academic_year' من 'sub4_window.py'"
    DB_FUNCTIONS_AVAILABLE = False
    # يمكنك إضافة إنهاء إجباري هنا إذا كانت هذه المكونات أساسية
    # sys.exit(f"خطأ حرج: فشل استيراد مكونات أساسية من sub4_window.py")
# --- نهاية استيراد الدوال والكلاسات ---

# --- استيراد النوافذ الفرعية الأخرى (اختياري) ---
try:
    from sub0_window import Sub0Window
    if not hasattr(Sub0Window, 'set_current_tab_by_index'):
        print("تحذير: الدالة 'set_current_tab_by_index' غير موجودة في 'Sub0Window'.")
    SUB0_AVAILABLE = True
    print("INFO: تم العثور على 'sub0_window.py'.")
except ImportError:
    print("تحذير: لم يتم العثور على ملف 'sub0_window.py'.")
    Sub0Window = None
    SUB0_AVAILABLE = False

try:
    from sub3_window import Sub3Window
    SUB3_AVAILABLE = True
    print("INFO: تم العثور على 'sub3_window.py'.")
except ImportError:
    print("تحذير: لم يتم العثور على ملف 'sub3_window.py'. سيتم استخدام محتوى مؤقت لـ 'البنية التربوية'.")
    Sub3Window = None
    SUB3_AVAILABLE = False

# --- استيراد النافذة المؤقتة (Placeholder) ---
try:
    # تعريف بديل لـ PlaceholderWindow إذا لم يتم العثور على components.placeholder
    class PlaceholderWindow(QWidget):
        def __init__(self, title="مؤقت", message="المحتوى غير متوفر", db=None, academic_year=None, parent=None, **kwargs):
            super().__init__(parent)
            layout = QVBoxLayout(self)
            layout.setAlignment(Qt.AlignCenter)
            self.label = QLabel(f"<h2>{title}</h2><p>{message}</p>")
            self.label.setAlignment(Qt.AlignCenter)
            self.label.setFont(QFont("Calibri", 14))
            self.label.setWordWrap(True)
            layout.addWidget(self.label)
            self.setWindowTitle(title)
            print(f"INFO: استخدام PlaceholderWindow المؤقتة لـ '{title}'.")
    print("INFO: تم العثور على 'components/placeholder.py'.")
except ImportError:
    print("تحذير: لم يتم العثور على 'components/placeholder.py'. سيتم استخدام QLabel كمحتوى مؤقت.")
    class PlaceholderWindow(QWidget):
        # زيادة مرونة __init__ لتقبل المعاملات غير المستخدمة
        def __init__(self, title="مؤقت", message="المحتوى غير متوفر", db=None, academic_year=None, parent=None, **kwargs):
            super().__init__(parent)
            layout = QVBoxLayout(self); layout.setAlignment(Qt.AlignCenter)
            self.label = QLabel(f"<h2>{title}</h2><p>{message}</p>")
            self.label.setAlignment(Qt.AlignCenter); self.label.setFont(QFont("Calibri", 14)); self.label.setWordWrap(True)
            layout.addWidget(self.label)
            self.setWindowTitle(title)
            print(f"INFO: استخدام PlaceholderWindow المؤقتة لـ '{title}'.")


# --- >>> استيراد نافذة معالجة تبرير الغياب <<< ---
try:
    from sub14_window import AbsenceManagementWindow
    ABSENCE_MANAGEMENT_WINDOW_AVAILABLE = True
    print("INFO: تم استيراد AbsenceManagementWindow بنجاح من sub14_window.py.")
except ImportError as e:
    print(f"تحذير: لم يتم العثور على ملف 'sub14_window.py' أو الكلاس AbsenceManagementWindow فيه.\nالخطأ: {e}")
    AbsenceManagementWindow = None
    ABSENCE_MANAGEMENT_WINDOW_AVAILABLE = False
# --- نهاية استيراد نافذة معالجة تبرير الغياب ---

# --- >>> استيراد نافذة البحث الجديدة (Simple6Window) <<< ---
try:
    # استيراد نافذة البحث من simple6_window.py
    from simple6_window import Simple6Window
    SIMPLE6_AVAILABLE = True
    print("INFO: تم استيراد Simple6Window بنجاح من simple6_window.py.")
except ImportError as e:
    print(f"تحذير: لم يتم العثور على ملف 'simple6_window.py' أو الكلاس Simple6Window فيه.\nالخطأ: {e}")
    Simple6Window = None
    SIMPLE6_AVAILABLE = False
# --- نهاية استيراد نافذة البحث ---

# --- >>> استيراد نافذة بطاقة التلميذ (StudentCardWindow) <<< ---
try:
    from sub10_window import StudentCardWindow
    STUDENT_CARD_AVAILABLE = True
    print("INFO: تم استيراد StudentCardWindow بنجاح من sub10_window.py.")
except ImportError as e:
    print(f"تحذير: لم يتم العثور على ملف 'sub10_window.py' أو الكلاس StudentCardWindow فيه.\nالخطأ: {e}")
    StudentCardWindow = None
    STUDENT_CARD_AVAILABLE = False
# --- نهاية استيراد نافذة بطاقة التلميذ ---

# --- >>> إضافة فئة مدمجة لبطاقة التلميذ <<< ---
class EmbeddedStudentCardWindow(QWidget):
    """
    نافذة مدمجة تحتوي على StudentCardWindow وتتكيف مع حجم الإطار الأساسي
    """
    def __init__(self, db=None, academic_year=None, parent=None):
        super().__init__(parent)
        self.setObjectName("EmbeddedStudentCard")

        # إنشاء التخطيط الأساسي
        self.main_layout = QVBoxLayout(self)
        self.main_layout.setContentsMargins(0, 0, 0, 0)
        self.main_layout.setSpacing(0)

        # عنوان النافذة المدمجة (اختياري)
        self.header_label = QLabel("بطاقة تلميذ - عرض مدمج")
        self.header_label.setAlignment(Qt.AlignCenter)
        self.header_label.setFont(QFont("Calibri", 14, QFont.Bold))
        self.header_label.setStyleSheet("background-color: #3498db; color: white; padding: 5px;")
        self.main_layout.addWidget(self.header_label)

        # إنشاء نافذة بطاقة التلميذ داخل النافذة المدمجة
        if STUDENT_CARD_AVAILABLE:
            try:
                self.student_card = StudentCardWindow(db=db, academic_year=academic_year, parent=self)
                # تغيير سلوك النافذة لتكون مدمجة داخل الإطار
                self.student_card.setWindowFlags(Qt.Widget)
                # إزالة تحديد الحجم الثابت ليتمدد مع حجم النافذة
                if hasattr(self.student_card, 'setFixedSize'):
                    # استخدام طريقة بديلة لإزالة قيود الحجم الثابت
                    self.student_card.setFixedSize(16777215, 16777215)  # استخدام قيمة كبيرة بدلاً من QWIDGETSIZE_MAX
                self.student_card.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)

                # إضافة نافذة بطاقة التلميذ إلى التخطيط الرئيسي
                self.main_layout.addWidget(self.student_card)
                print("INFO: تم إنشاء نافذة بطاقة التلميذ المدمجة بنجاح")
            except Exception as e:
                error_label = QLabel(f"خطأ في تحميل نافذة بطاقة التلميذ: {str(e)}")
                error_label.setStyleSheet("color: red; font-weight: bold; padding: 20px;")
                error_label.setAlignment(Qt.AlignCenter)
                self.main_layout.addWidget(error_label)
                print(f"ERROR: فشل إنشاء نافذة بطاقة التلميذ المدمجة: {e}")
        else:
            message_label = QLabel("نافذة بطاقة التلميذ غير متوفرة")
            message_label.setStyleSheet("color: orange; font-weight: bold; padding: 20px;")
            message_label.setAlignment(Qt.AlignCenter)
            self.main_layout.addWidget(message_label)
            print("WARNING: نافذة بطاقة التلميذ غير متوفرة لإنشاء النسخة المدمجة")

    def load_student(self, student_id):
        """تحميل بيانات تلميذ محدد في النافذة المدمجة"""
        if hasattr(self, 'student_card') and hasattr(self.student_card, 'load_student_data'):
            try:
                self.student_card.load_student_data(student_id)
                return True
            except Exception as e:
                print(f"ERROR: فشل تحميل بيانات التلميذ {student_id}: {e}")
                return False
        return False
# --- >>> نهاية فئة بطاقة التلميذ المدمجة <<< ---

# --- >>> استيراد نافذة مسك الغياب (Hirasa300Window) <<< ---
try:
    from sub9_window import Hirasa300Window
    HIRASA300_AVAILABLE = True
    print("INFO: تم استيراد Hirasa300Window بنجاح من sub9_window.py.")
except ImportError as e:
    print(f"تحذير: لم يتم العثور على ملف 'sub9_window.py' أو الكلاس Hirasa300Window فيه.\nالخطأ: {e}")
    Hirasa300Window = None
    HIRASA300_AVAILABLE = False
# --- نهاية استيراد نافذة مسك الغياب ---

# --- >>> استيراد نافذة معالجة طلبات الشهادات المدرسية <<< ---
try:
    from sub19_window import SchoolCertificateWindow
    SCHOOL_CERTIFICATE_AVAILABLE = True
    print("INFO: تم استيراد SchoolCertificateWindow بنجاح من sub19_window.py.")
except ImportError as e:
    print(f"تحذير: لم يتم العثور على ملف 'sub19_window.py' أو الكلاس SchoolCertificateWindow فيه.\nالخطأ: {e}")
    SchoolCertificateWindow = None
    SCHOOL_CERTIFICATE_AVAILABLE = False
# --- نهاية استيراد نافذة معالجة طلبات الشهادات المدرسية ---

# --- >>> استيراد نافذة إخبار بنشاط <<< ---
try:
    from sub18_window import NewsWindow
    NEWS_WINDOW_AVAILABLE = True
    print("INFO: تم استيراد NewsWindow بنجاح من sub18_window.py.")
except ImportError as e:
    print(f"تحذير: لم يتم العثور على ملف 'sub18_window.py' أو الكلاس NewsWindow فيه.\nالخطأ: {e}")
    NewsWindow = None
    NEWS_WINDOW_AVAILABLE = False
# --- نهاية استيراد نافذة إخبار بنشاط ---

# --- >>> استيراد نافذة إنشاء جدول مسك_الغياب <<< ---
try:
    # تأكد أن هذا السطر والرسائل أدناه تستخدم الاسم النظيف "sub21_window"
    try:
        from sub21_window import CreateAbsenceTableWindow
        CREATE_ABSENCE_TABLE_AVAILABLE = True
        print("INFO: تم استيراد CreateAbsenceTableWindow بنجاح من sub21_window.py.")
    except ImportError as e:
        print(f"تحذير: لم يتم العثور على ملف 'sub21_window.py' أو الكلاس CreateAbsenceTableWindow فيه.\nالخطأ: {e}")
        CreateAbsenceTableWindow = None
        CREATE_ABSENCE_TABLE_AVAILABLE = False
    CREATE_ABSENCE_TABLE_AVAILABLE = True
    print("INFO: تم استيراد CreateAbsenceTableWindow بنجاح من sub21_window.py.")
except ImportError as e:
    print(f"تحذير: لم يتم العثور على ملف 'sub21_window.py' أو الكلاس CreateAbsenceTableWindow فيه.\nالخطأ: {e}")
    CreateAbsenceTableWindow = None
    CREATE_ABSENCE_TABLE_AVAILABLE = False
# --- نهاية استيراد نافذة إنشاء جدول مسك_الغياب ---

# --- >>> استيراد نافذة تهيئة الامتحانات <<< ---
try:
    from sub22_window import ExamSetupWindow
    EXAM_SETUP_AVAILABLE = True
    print("INFO: تم استيراد ExamSetupWindow بنجاح من sub22_window.py.")
except ImportError as e:
    print(f"تحذير: لم يتم العثور على ملف 'sub22_window.py' أو الكلاس ExamSetupWindow فيه.\nالخطأ: {e}")
    ExamSetupWindow = None
    EXAM_SETUP_AVAILABLE = False
# --- نهاية استيراد نافذة تهيئة الامتحانات ---



# --- >>> إنشاء فئة مخصصة تمتد من Sub4Window لإضافة السلوك المطلوب <<< ---
class EnhancedSimpleSearchWindow(Sub4Window):
    """
    فئة مخصصة تمتد من Sub4Window لإضافة وظيفة معالجة النقر على خلايا الجدول
    وفتح نافذة بطاقة الطالب عند النقر على رموز الطلاب.
    """
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        # تحتفظ الفئة الأساسية بالفعل بمتغير active_student_window
        # كما أن ربط الإشارة table_lists.clicked مع on_table_cell_clicked موجود بالفعل
        print("INFO: تم إنشاء EnhancedSimpleSearchWindow بنجاح")

    def on_table_cell_clicked(self, index, column=None):
        """
        يتجاوز هذا الأسلوب الأسلوب الأصلي في SimpleSearchWindow
        لضمان فتح النافذة الصحيحة عند النقر على رمز في جدول اللوائح
        """
        # استدعاء طريقة الأب للتعامل مع النقر على الخلية بشكل صحيح
        print(f"DEBUG: تم النقر على الخلية {index.row()}, {index.column()}")

        # استخدام طريقة الفئة الأم نفسها (SimpleSearchWindow)
        super().on_table_cell_clicked(index, column)

        # للتشخيص فقط - طباعة حالة النافذة النشطة
        if hasattr(self, 'active_student_window') and self.active_student_window:
            print(f"DEBUG: النافذة النشطة الآن هي: {type(self.active_student_window).__name__}")

# تعديل مستوى التشخيص لتقليل الرسائل
DEBUG_MODE = False  # ضبط هذا على False لتقليل الرسائل

def debug_print(*args, **kwargs):
    """دالة مساعدة للطباعة فقط في وضع التشخيص"""
    if DEBUG_MODE:
        print(*args, **kwargs)

class MainWindow(QMainWindow):
    def __init__(self, auto_show=False):
        super().__init__()
        self.setWindowTitle("نظام إدارة المدرسة")
        self.setLayoutDirection(Qt.RightToLeft)
        self.previous_tab_index = 0 # لتتبع التبويب السابق قبل الضغط على خروج

        # تعيين أيقونة البرنامج
        icon_path = os.path.join(os.path.dirname(__file__), "01.ico")
        if (os.path.exists(icon_path)):
            self.setWindowIcon(QIcon(icon_path))
        else:
            print(f"تنبيه: ملف الأيقونة غير موجود في {icon_path}")

        # --- >>> إنشاء اتصال قاعدة البيانات والسنة الدراسية مبكرًا <<< ---
        # سيتم استخدام الدوال المستوردة من simple00_window.py (أو الوهمية إذا فشل الاستيراد)
        self.db_connection = None
        self.current_academic_year = None
        debug_print("INFO: محاولة الاتصال بقاعدة البيانات باستخدام الدالة المستوردة...")
        connected, db_conn, db_error = create_connection() # <--- تستخدم الدالة المستوردة
        if connected:
            self.db_connection = db_conn
            print("INFO: تم الاتصال بقاعدة البيانات بنجاح.")
            year, year_error = get_current_academic_year(self.db_connection) # <--- تستخدم الدالة المستوردة
            if not year_error:
                self.current_academic_year = year
                print(f"INFO: السنة الدراسية الحالية: {self.current_academic_year}")
            else:
                QMessageBox.warning(self, "خطأ في البيانات", f"لم يتم العثور على السنة الدراسية:\n{year_error}")
                print(f"WARNING: {year_error}")
        else:
            QMessageBox.critical(self, "خطأ فادح", f"فشل الاتصال بقاعدة البيانات:\n{db_error}\nسيتم إغلاق البرنامج.")
            QTimer.singleShot(100, QCoreApplication.instance().quit) # جدولة الخروج
            return # منع استكمال التهيئة
        # ---------------------------------------------------------

        self.menuBar().setVisible(False)

        self.central_widget = QWidget()
        self.setCentralWidget(self.central_widget)

        self.main_layout = QVBoxLayout(self.central_widget)
        self.main_layout.setContentsMargins(0, 0, 0, 0)
        self.main_layout.setSpacing(0) # إزالة المسافة بين العناصر

        self._create_top_navbar()
        self._setup_content_area()

        self.main_layout.addWidget(self.navbar_frame) # إضافة شريط التبويب
        self.main_layout.addWidget(self.content_area, 1) # إضافة منطقة المحتوى مع نسبة تمدد 1

        print("INFO: إنشاء النوافذ الفرعية...")
        self._create_windows()
        print("INFO: ربط أزرار التنقل بالنوافذ...")
        self._link_navbar_buttons()

        self.setup_window_connections()

        # تحديد النافذة والزر الأول
        default_tab_index = 0  # قيمة افتراضية
        if self.content_area.count() > 0:
            # البحث عن مؤشر تبويب "اللوائح والأقسام"
            try:
                regulations_key = "regulations"
                default_tab_index = [i for i, item_tuple in enumerate(self.navbar_items) if item_tuple[1] == regulations_key][0]
            except IndexError:
                print(f"تحذير: مفتاح التبويب الافتراضي '{regulations_key}' غير موجود في navbar_items. سيتم استخدام المؤشر 0.")

            # تأكد من أن default_tab_index صالح لمحتوى content_area
            # content_area لا يحتوي على عنصر لنافذة الخروج
            # لذا، إذا كان default_tab_index يشير إلى تبويب الخروج (وهو غير محتمل هنا)، يجب تعديله
            # لكن بما أن "regulations" ليس تبويب الخروج، هذا آمن.

            # العثور على مؤشر "اللوائح والأقسام" في content_area
            # هذا يفترض أن ترتيب النوافذ في content_area يطابق ترتيبها في navbar_items (باستثناء الخروج)
            # وهو ما يتحقق بواسطة _create_windows

            # نحتاج إلى إيجاد المؤشر الصحيح في content_area إذا كان ترتيب navbar_items يتضمن "logout_action"
            # الذي ليس له ويدجت في content_area.
            # الطريقة الأسهل هي استخدام مفتاح "regulations" مباشرة للعثور على الويدجت.
            if "regulations" in self.windows:
                regulations_widget = self.windows["regulations"]
                content_area_reg_index = self.content_area.indexOf(regulations_widget)
                if content_area_reg_index != -1:
                    self.content_area.setCurrentIndex(content_area_reg_index)
                    # تعيين التبويب المقابل في QTabWidget
                    navbar_reg_index = self.navbar_buttons["regulations"]["tab_index"]
                    self.tabWidget.setCurrentIndex(navbar_reg_index)
                    self.previous_tab_index = navbar_reg_index
                    print(f"INFO: تم تحديد نافذة '{self.navbar_items[navbar_reg_index][0]}' كنافذة افتراضية.")
                else:
                    print(f"WARNING: لم يتم العثور على ويدجت 'regulations' في content_area.")
                    if self.tabWidget.count() > 0: # fallback to first tab if regulations not found
                       self.tabWidget.setCurrentIndex(0)
                       self.previous_tab_index = 0
                       if self.content_area.count() > 0: self.content_area.setCurrentIndex(0)


            else: # fallback if "regulations" window doesn't exist
                print(f"WARNING: لم يتم العثور على نافذة 'regulations'.")
                if self.tabWidget.count() > 0 and self.content_area.count() > 0:
                    self.tabWidget.setCurrentIndex(0)
                    self.content_area.setCurrentIndex(0)
                    self.previous_tab_index = 0


        else: print("تحذير: لا توجد نوافذ في content_area.")

        # عرض النافذة فقط إذا كان مطلوباً (للاختبار المباشر لملف main_window.py)
        if auto_show:
            self.showMaximized()
            print("INFO: تم عرض النافذة الرئيسية (Maximized).")
        else:
            print("INFO: تم إنشاء النافذة الرئيسية بدون عرضها تلقائياً (auto_show=False).")

        # إضافة متغير لتخزين مثيل taheri3.MainWindow مؤقتًا
        self.taheri3_instance = None

        # إضافة متغير لتتبع آخر تحديث لجدول_عام
        self.last_table_update_time = 0

        # إضافة ملصق لعرض حالة التحميل
        self.loading_label = QLabel("جاري التحميل...", self)
        self.loading_label.setAlignment(Qt.AlignCenter)
        self.loading_label.setStyleSheet("background-color: rgba(0, 0, 0, 150); color: white; font-size: 16pt; padding: 20px; border-radius: 10px;")
        self.loading_label.hide()

    def setup_window_connections(self):
        """إعداد الاتصالات بين النوافذ"""
        # ربط إشارات تحديث البيانات
        if "violations" in self.windows:
            violations_window = self.windows["violations"]
            violations_window.load_data_signal = lambda: self.refresh_all_windows()

    def refresh_all_windows(self):
        """تحديث جميع النوافذ التي تحتوي على دالة تحديث"""
        for window_key, window in self.windows.items():
            if hasattr(window, 'load_data'):
                try:
                    window.load_data()
                    print(f"تم تحديث نافذة {window_key}")
                except Exception as e:
                    print(f"خطأ في تحديث نافذة {window_key}: {e}")

    def update_main_table_and_show_window(self, window_widget, window_key):
        """تحديث جدول_عام ثم عرض النافذة المحددة مع تحسين الأداء"""
        try:
            # عرض مؤشر التحميل
            self.show_loading_indicator()

            # استدعاء دالة تحديث جدول_عام من taheri3.py بدون عرض أي رسائل
            try:
                import time
                current_time = time.time()

                # تحقق مما إذا كان قد مر وقت كافٍ منذ آخر تحديث (30 ثانية مثلاً)
                if current_time - self.last_table_update_time > 30:
                    # استيراد taheri3.py مرة واحدة وإعادة استخدام المثيل إذا كان موجودًا
                    if self.taheri3_instance is None:
                        import importlib.util
                        spec = importlib.util.spec_from_file_location("taheri3", "taheri3.py")
                        taheri3 = importlib.util.module_from_spec(spec)
                        spec.loader.exec_module(taheri3)

                        # إنشاء نسخة من MainWindow من taheri3.py وحفظها للاستخدام اللاحق
                        self.taheri3_instance = taheri3.MainWindow()

                    # استدعاء دالة تحديث جدول_عام
                    QApplication.processEvents()  # السماح بتحديث واجهة المستخدم قبل العملية الثقيلة
                    self.taheri3_instance.update_main_table()
                    self.last_table_update_time = current_time
                    print("INFO: تم تحديث جدول_عام")
                else:
                    print("INFO: تجاوز تحديث جدول_عام لأنه تم تحديثه مؤخرًا")

            except Exception as e:
                # طباعة الخطأ في وحدة التحكم فقط بدون عرض رسالة للمستخدم
                print(f"خطأ في تحديث جدول_عام: {e}")

            # إخفاء مؤشر التحميل وعرض النافذة المحددة
            self.hide_loading_indicator()
            self.show_window(window_widget, window_key) # <--- تمرير window_key هنا

        except Exception as e:
            print(f"خطأ في تحديث جدول_عام وعرض النافذة: {e}")
            # إخفاء مؤشر التحميل في حالة حدوث خطأ
            self.hide_loading_indicator()
            # عرض النافذة المحددة في حالة حدوث خطأ
            self.show_window(window_widget, window_key) # <--- وتمرير window_key هنا أيضًا

    def show_loading_indicator(self):
        """عرض مؤشر التحميل"""
        # تحديث حجم وموقع مؤشر التحميل
        self.loading_label.resize(200, 80)
        center_point = self.rect().center()
        self.loading_label.move(center_point.x() - 100, center_point.y() - 40)
        self.loading_label.raise_()
        self.loading_label.show()
        QApplication.processEvents()  # تحديث واجهة المستخدم فورًا

    def hide_loading_indicator(self):
        """إخفاء مؤشر التحميل"""
        self.loading_label.hide()
        QApplication.processEvents()  # تحديث واجهة المستخدم فورًا

    def _create_top_navbar(self):
        """إنشاء شريط التبويب العلوي المميز"""
        self.navbar_frame = QFrame()
        self.navbar_frame.setObjectName("NavBarFrame")
        self.navbar_frame.setStyleSheet("""
            QFrame#NavBarFrame {
                background-color: #00382E; /* لون خلفية شريط التبويب */
                border-bottom: 1px solid #00382E; /* خط فاصل سفلي أرفع */
                height: 60px; /* تحديد الارتفاع بدقة */
                min-height: 50px; /* تقليل الارتفاع */
                max-height: 50px; /* تحديد الحد الأقصى للارتفاع */
                margin: 0; /* إزالة جميع الهوامش */
                padding: 0; /* إزالة جميع الحشوات */
            }
        """)

        navbar_layout = QVBoxLayout(self.navbar_frame) # استخدام QVBoxLayout لوضع التبويب
        navbar_layout.setContentsMargins(0, 0, 0, 0) # إزالة الهوامش
        navbar_layout.setSpacing(0) # إزالة المسافات بين العناصر

        self.tabWidget = QTabWidget()
        self.tabWidget.setObjectName("MainTabBar")
        self.tabWidget.setDocumentMode(True)
        self.tabWidget.setUsesScrollButtons(True)
        self.tabWidget.setElideMode(Qt.ElideNone)
        self.tabWidget.setTabPosition(QTabWidget.North)
        self.tabWidget.setTabShape(QTabWidget.Rounded)
        self.tabWidget.setIconSize(QSize(20, 20)) # تقليل حجم الأيقونات
        self.tabWidget.setFixedHeight(50) # تحديد ارتفاع ثابت للتبويب
        # تعيين ارتفاع ثابت مع السماح بالتمدد الأفقي

        self.tabWidget.setStyleSheet("""
            QTabWidget::pane {
                border: none;
                background-color: #004D40;
                margin: 0;
                padding: 0;
            }

            QTabBar::tab {
                background-color: #87CEEB;
                color: #FFFFFF;
                font-family: 'Calibri';
                font-size: 12pt; /* تقليل حجم الخط */
                font-weight: bold;
                min-width: 95px; /* تقليل العرض الأدنى */
                height: 30px; /* تحديد الارتفاع */
                padding: 1px 10px; /* تقليل الهوامش الداخلية */
                margin: 1px 1px; /* تقليل الهوامش الخارجية */
                border-top-left-radius: 6px; /* تقليل نصف قطر الزوايا */
                border-top-right-radius: 6px;
                text-align: center;
                cursor: pointer; /* تغيير مؤشر الماوس إلى يد عند التحويم */
                transition: background-color 0.3s, color 0.3s; /* إضافة تأثير انتقالي سلس */
            }

            QTabBar::tab:selected {
                background-color: #4682B4;
                color: white;
                border-bottom: 10px solid #B0C4DE; /* تقليل سمك الحدود */
                transition: background-color 0.3s, color 0.3s; /* إضافة تأثير انتقالي سلس */
            }

            QTabBar::tab:hover:!selected {
                background-color: #ADD8E6;
                color: white;
                transition: background-color 0.3s, color 0.3s; /* إضافة تأثير انتقالي سلس */
            }

            QTabBar::tab:disabled {
                color: #A9A9A9;
                background-color: #D3D3D3;
                cursor: not-allowed; /* تغيير مؤشر الماوس إلى غير مسموح للتبويبات المعطلة */
                transition: background-color 0.3s, color 0.3s; /* إضافة تأثير انتقالي سلس */
            }

            /* تخصيص تبويب الخروج - نفترض أنه الأخير دائما */
            QTabBar::tab:last-child {
                background-color: #C62828; /* لون خلفية تبويب الخروج (أحمر غامق) */
                color: white;
                cursor: pointer; /* تغيير مؤشر الماوس إلى يد عند التحويم */
                transition: background-color 0.3s, color 0.3s; /* إضافة تأثير انتقالي سلس */
            }
            QTabBar::tab:last-child:selected { /* عند التحديد (نظريا لن يبقى محددا) */
                background-color: #A71A1A; /* أحمر أغمق */
                transition: background-color 0.3s, color 0.3s; /* إضافة تأثير انتقالي سلس */
            }
            QTabBar::tab:last-child:hover {
                background-color: #B71C1C; /* أحمر عند التحويم */
                transition: background-color 0.3s, color 0.3s; /* إضافة تأثير انتقالي سلس */
            }
        """)

        self.tab_bar = self.tabWidget.tabBar()
        # تعيين مؤشر اليد للتبويبات
        self.tab_bar.setCursor(Qt.PointingHandCursor)

        self.navbar_items = [
            ("تهيئة البرنامج", "setup"),
            ("البنية التربوية", "structure"),
            ("اللوائح والأقسام", "regulations"),
            ("نافذة بحث", "search"),
            ("مسك الغياب", "student_card"),
            ("شواهد مدرسية", "violations"),
            ("إخبار بنشاط", "absence"),
            ("طباعة اللوائح", "statistics"),
            # تم إزالة "بطاقة الأقسام" من التبويب
            ("أوراق الفروض", "create_absence_table"),
            ("تهيئة الامتحانات", "exam_setup"),
            ("تسجيل الخروج", "logout_action") # إضافة تبويب الخروج
        ]

        self.tab_pages = {}
        self.navbar_buttons = {}

        for _, (text, window_key) in enumerate(self.navbar_items): # استخدام _ بدلاً من i لأننا لا نستخدم المتغير
            tab_page = QWidget()
            tab_page.setObjectName(f"TabPage_{window_key}")
            self.tab_pages[window_key] = tab_page

            tab_index = self.tabWidget.addTab(tab_page, text) # استخدام النص مباشرة
            # self.tabWidget.setTabText(tab_index, text) # setTabText ليس ضروريا إذا تم تمرير النص في addTab

            self.tab_bar.setTabData(tab_index, window_key)
            self.navbar_buttons[window_key] = {"tab_index": tab_index}

        # إضافة التبويب إلى التخطيط الرئيسي للإطار
        # تعيين نسبة التمدد إلى 0 لمنع التمدد العمودي مع السماح بالتمدد الأفقي
        navbar_layout.addWidget(self.tabWidget)

        # تم إزالة bottom_bar و logout_btn

        self.tabWidget.currentChanged.connect(self._on_tab_changed)

    def _on_tab_changed(self, index):
        """معالجة حدث تغيير التبويب"""
        if index >= 0 and index < self.tabWidget.count():
            window_key = self.tab_bar.tabData(index)

            if window_key == "logout_action":
                # منع تبويب الخروج من البقاء نشطًا أثناء عرض مربع الحوار
                self.tabWidget.blockSignals(True)
                self.tabWidget.setCurrentIndex(self.previous_tab_index)
                self.tabWidget.blockSignals(False)

                should_quit = self.show_logout_confirmation_dialog()
                if should_quit:
                    print("INFO: طلب إغلاق التطبيق عبر تبويب الخروج...")
                    QCoreApplication.instance().quit()
                # إذا لم يتم الخروج، يبقى التبويب على previous_tab_index

            elif window_key in self.windows:
                window_widget = self.windows[window_key]

                if window_key == "create_absence_table": # --- إضافة تشخيص هنا ---
                    print(f"DEBUG: Tab 'create_absence_table' selected.")
                    print(f"DEBUG:   Window instance from self.windows: {window_widget}")
                    print(f"DEBUG:   Type of window instance: {type(window_widget)}")
                    if CreateAbsenceTableWindow and isinstance(window_widget, CreateAbsenceTableWindow):
                        print("DEBUG:   Instance is CreateAbsenceTableWindow.")
                    elif PlaceholderWindow and isinstance(window_widget, PlaceholderWindow):
                        print("DEBUG:   Instance is PlaceholderWindow.")
                    else:
                        print("DEBUG:   Instance is of an unexpected type.")
                    print(f"DEBUG:   Is CREATE_ABSENCE_TABLE_AVAILABLE: {CREATE_ABSENCE_TABLE_AVAILABLE}")
                 # --- نهاية التشخيص ---

                if window_key == "statistics":
                    # عرض نافذة طباعة اللوائح في منطقة المحتوى الرئيسية
                    try:
                        print("عرض نافذة طباعة اللوائح في منطقة المحتوى الرئيسية...")
                        # استخدام الويدجت الموجود في self.windows
                        self.show_window(window_widget, window_key)
                    except Exception as e:
                        print(f"خطأ في عرض نافذة طباعة اللوائح: {e}")
                        # في حالة الخطأ، استخدم الطريقة القديمة
                        QTimer.singleShot(50, lambda: self.update_main_table_and_show_window(window_widget, window_key))
                elif window_key == "sections_card":
                    # فتح نافذة بطاقة الأقسام كنافذة مستقلة
                    try:
                        # إنشاء نسخة جديدة من النافذة
                        print("فتح نافذة بطاقة الأقسام كنافذة مستقلة...")
                        # استخدام RegulationsCardWindow كما هو محدد في windows_config
                        sections_window = RegulationsCardWindow(
                            parent=None,  # تعيين parent=None لجعلها نافذة مستقلة
                            db=self.db_connection,
                            section="1/1",  # قسم افتراضي
                            academic_year=self.current_academic_year
                        )
                        sections_window.show()  # عرض النافذة المستقلة
                    except Exception as e:
                        print(f"خطأ في فتح نافذة بطاقة الأقسام كنافذة مستقلة: {e}")
                        # في حالة الخطأ، استخدم الطريقة القديمة
                        self.show_window(window_widget, window_key)
                else:
                    self.show_window(window_widget, window_key) # <--- تمرير window_key هنا

                self.previous_tab_index = index # تحديث التبويب السابق فقط للتبويبات العادية
                debug_print(f"تم تبديل التبويب إلى '{window_key}' (Index: {index})")
            else:
                # هذا قد يحدث إذا كان هناك مفتاح في navbar_items ليس له نافذة وليس هو "logout_action"
                # وهو أمر غير متوقع بناءً على _create_windows و _link_navbar_buttons
                print(f"تحذير: تم تغيير التبويب إلى مؤشر {index} بمفتاح غير معالج: {window_key}")
                # الرجوع إلى التبويب السابق كإجراء وقائي
                self.tabWidget.blockSignals(True)
                self.tabWidget.setCurrentIndex(self.previous_tab_index)
                self.tabWidget.blockSignals(False)


    def _link_navbar_buttons(self):
        """تعديل ربط أزرار شريط التنقل لتعمل مع نظام التبويب الجديد"""
        for window_key, tab_data in self.navbar_buttons.items():
            tab_index = tab_data["tab_index"]

            if window_key == "logout_action":
                self.tabWidget.setTabEnabled(tab_index, True)
                self.tabWidget.setTabToolTip(tab_index, "تسجيل الخروج من البرنامج")
            elif window_key not in self.windows:
                self.tabWidget.setTabEnabled(tab_index, False)
                self.tabWidget.setTabToolTip(tab_index, "الوحدة غير متوفرة")
                print(f"تحذير: تم تعطيل التبويب '{window_key}' لعدم توفر النافذة المقابلة.")
            # التبويبات الأخرى تكون مفعلة بشكل افتراضي إذا كانت النافذة موجودة

    def show_window(self, window_widget, window_key_debug=None): # <--- إضافة معامل جديد هنا
        """عرض الويدجت (النافذة الفرعية) المحدد في منطقة المحتوى."""
        if window_key_debug == "create_absence_table": # --- إضافة تشخيص هنا ---
            print(f"DEBUG: show_window called for 'create_absence_table'.")
            print(f"DEBUG:   window_widget: {window_widget}")
        # --- نهاية التشخيص ---

        if window_widget and isinstance(window_widget, QWidget):
            current_index_in_stack = self.content_area.indexOf(window_widget)
            if window_key_debug == "create_absence_table": # --- إضافة تشخيص هنا ---
                print(f"DEBUG:   Index of widget in QStackedWidget: {current_index_in_stack}")
            # --- نهاية التشخيص ---

            if (current_index_in_stack != -1):
                self.content_area.setCurrentIndex(current_index_in_stack)
                if window_key_debug == "create_absence_table": # --- إضافة تشخيص هنا ---
                    print(f"DEBUG:   QStackedWidget current index set to {current_index_in_stack}.")
                # --- نهاية التشخيص ---

                # تحديد علامة التبويب المقابلة
                for window_key, window in self.windows.items():
                    if window == window_widget and window_key in self.navbar_buttons:
                        tab_index = self.navbar_buttons[window_key]["tab_index"]
                        # تعطيل إشارة currentChanged للحظة لتجنب التكرار إذا كان التغيير برمجيًا
                        self.tabWidget.blockSignals(True)
                        self.tabWidget.setCurrentIndex(tab_index)
                        self.tabWidget.blockSignals(False)
                        if window_key_debug == "create_absence_table": # --- إضافة تشخيص هنا ---
                            print(f"DEBUG:   QTabWidget current index synced to {tab_index}.")
                        # --- نهاية التشخيص ---
                        break
        elif window_key_debug == "create_absence_table": # --- إضافة تشخيص هنا ---
            print(f"DEBUG: show_window called for 'create_absence_table', but window_widget is None or not a QWidget.")
        # --- نهاية التشخيص ---


    def _setup_content_area(self):
        """إعداد منطقة عرض المحتوى الرئيسية"""
        self.content_area = QStackedWidget()
        self.content_area.setObjectName("ContentArea")
        self.content_area.setStyleSheet("QWidget#ContentArea { background-color: #ECEFF1; margin-top: 0; }")
        self.content_area.setContentsMargins(0, 0, 0, 0)


    def _create_windows(self):
        """إنشاء جميع النوافذ الفرعية وإضافتها إلى منطقة المحتوى"""
        self.windows = {}

        # اختصار: تحميل النوافذ بشكل متأخر (Lazy Loading)
        # بدلاً من تحميل جميع النوافذ عند البدء، قم بتحميل فقط النافذة الحالية
        # وتحميل البقية عند الحاجة إليها

        # معاملات مشتركة يتم تمريرها لجميع النوافذ الفرعية التي قد تحتاجها
        common_args = {
            "db": self.db_connection,
            "academic_year": self.current_academic_year,
            "parent": self # تمرير النافذة الرئيسية كأب
        }

        # تعريف إعدادات النوافذ (تأكد من تطابق المفاتيح مع navbar_items)
        windows_config = {
            "setup": {"class": Sub0Window, "available": SUB0_AVAILABLE, "title": "تهيئة البرنامج", "module": "sub0_window.py", "pass_args": False}, # نفترض أنها لا تحتاج args
            "structure": {"class": Sub3Window, "available": SUB3_AVAILABLE, "title": "البنية التربوية", "module": "sub3_window.py", "pass_args": False}, # نفترض أنها لا تحتاج args
            "regulations": {"class": EnhancedSimpleSearchWindow, "available": SIMPLE_SEARCH_AVAILABLE, "title": "اللوائح والأقسام", "module": "sub4_window.py", "pass_args": True}, # <-- استخدام الفئة المُحسّنة بدلاً من Sub4Window
            "search": {
                "class": Simple6Window,              # <-- الكلاس الجديد
                "available": SIMPLE6_AVAILABLE,      # <-- العلم الجديد الخاص بتوفره
                "title": "نافذة البحث",
                "module": "simple6_window.py",       # <-- اسم الملف الجديد
                "pass_args": True                    # <-- تحتاج المعاملات (db, academic_year, parent)
            },
            "student_card": {
                "class": Hirasa300Window,
                "available": HIRASA300_AVAILABLE,
                "title": "مسك الغياب ومعالجته",
                "module": "sub9_window.py",
                "pass_args": True,
                "custom_init": lambda: Hirasa300Window(
                    db=self.db_connection,
                    academic_year=self.current_academic_year,
                    parent=self
                )
            },
            "violations": {
                "class": SchoolCertificateWindow,
                "available": SCHOOL_CERTIFICATE_AVAILABLE,
                "title": "معالجة طلبات الشهادات المدرسية",
                "module": "sub19_window.py",
                "pass_args": True,
                "custom_init": lambda: SchoolCertificateWindow(
                    db=self.db_connection,
                    parent=self
                )
            },
            "absence": {
                "class": NewsWindow,              # تغيير من AbsenceManagementWindow إلى NewsWindow
                "available": NEWS_WINDOW_AVAILABLE,
                "title": "إخبار بنشاط",
                "module": "sub18_window.py",
                "pass_args": True,
                "custom_init": lambda: NewsWindow(
                    db=self.db_connection,
                    parent=self
                ),
                 "size": {"width": 800, "height": 600}
            },
            "statistics": {
                "class": PrintListsWindow,
                "available": PRINT_LISTS_WINDOW_AVAILABLE,
                "title": "الإحصائيات وطباعة اللوائح",
                "module": "sub23_window.py",
                "pass_args": True,
                "custom_init": lambda: PrintListsWindow(
                    parent=self,  # تعيين parent=self لجعلها نافذة مدمجة
                    db=self.db_connection,
                    academic_year=self.current_academic_year
                ),
                "size": {"width": 700, "height": 650},
                "fixed_size": True  # إضافة علامة للإشارة إلى أن هذه النافذة يجب أن تحتفظ بحجمها الثابت
            },
            "create_absence_table": {
                "class": CreateAbsenceTableWindow,
                "available": CREATE_ABSENCE_TABLE_AVAILABLE,
                "title": "مسك أوراق الفروض",
                "module": "sub21_window.py",
                "pass_args": True,
                "custom_init": lambda: CreateAbsenceTableWindow(
                    parent=self
                ),
                "size": {"width": 900, "height": 700},
                "fixed_size": True
            },
            "exam_setup": {
                "class": ExamSetupWindow,
                "available": EXAM_SETUP_AVAILABLE,
                "title": "تهيئة الامتحانات",
                "module": "sub22_window.py",
                "pass_args": True,
                "custom_init": lambda: ExamSetupWindow(
                    db=self.db_connection,
                    academic_year=self.current_academic_year,
                    parent=self
                ),
                "size": {"width": 900, "height": 700},
                "fixed_size": True
            },
            "sections_card": {
                "class": RegulationsCardWindow,
                "available": SECTIONS_CARD_AVAILABLE,
                "title": "بطاقة الأقسام",
                "module": "sub20_window.py",
                "pass_args": True,
                "custom_init": lambda: RegulationsCardWindow(
                    parent=None,  # تعيين parent=None لجعلها نافذة مستقلة
                    db=self.db_connection,
                    section="1/1",  # قسم افتراضي
                    academic_year=self.current_academic_year
                ),
                "size": {"width": 800, "height": 600},
                "fixed_size": True
            },
        }

        # إنشاء النوافذ بالترتيب المحدد في navbar_items
        for _, key in self.navbar_items:
            if key == "logout_action": # تخطي مفتاح الخروج لأنه لا يمثل نافذة
                continue

            if key in windows_config:
                config = windows_config[key]
                window_class = config["class"]
                is_available = config["available"]
                title = config["title"]
                module_name = config["module"]
                should_pass_args = config["pass_args"]

                window_instance = None # تهيئة المتغير

                if is_available and window_class is not None:
                    try:
                        if "custom_init" in config:
                            try:
                                window_instance = config["custom_init"]()
                                if "size" in config:  # إضافة تحديد الحجم
                                    window_instance.resize(
                                        config["size"]["width"],
                                        config["size"]["height"]
                                    )
                                print(f"INFO: تم إنشاء '{key}' باستخدام custom_init")
                            except Exception as e:
                                print(f"خطأ في إنشاء النافذة '{key}' باستخدام custom_init: {e}")
                                window_instance = None
                        elif should_pass_args:
                            # إضافة تشخيص أكثر تفصيلاً
                            if key == "student_card":
                               print(f"DEBUG: محاولة إنشاء نافذة بطاقة تلميذ مع المعلمات: {common_args}")

                            # إنشاء النافذة مع تمرير المعاملات المشتركة
                            window_instance = window_class(**common_args)
                            print(f"INFO: تم إنشاء '{key}' ({window_class.__name__}) مع تمرير المعاملات.")

                            # تشخيص إضافي للتأكد من نجاح الإنشاء
                            if key == "student_card":
                                print(f"DEBUG: تم إنشاء نافذة بطاقة تلميذ بنجاح من النوع: {type(window_instance).__name__}")

                        else:
                            # إنشاء النافذة بدون تمرير المعاملات المشتركة (قد تحتاج parent فقط؟)
                            try:
                                window_instance = window_class(parent=self) # محاولة مع parent
                            except TypeError:
                                window_instance = window_class() # محاولة بدون أي شيء
                            print(f"INFO: تم إنشاء '{key}' ({window_class.__name__}) بدون تمرير المعاملات المشتركة.")

                        self.windows[key] = window_instance
                        self.content_area.addWidget(window_instance)

                    except Exception as e:
                        print(f"خطأ فادح عند إنشاء النافذة '{key}' من {module_name}: {e}")
                        # إنشاء Placeholder في حالة فشل إنشاء النسخة الفعلية
                        message = f"خطأ في إنشاء الوحدة\n({module_name})\n{e}"
                        window_instance = PlaceholderWindow(title=title, message=message, **common_args)
                        self.windows[key] = window_instance
                        self.content_area.addWidget(window_instance)
                        # تعطيل علامة التبويب المقابلة
                        if key in self.navbar_buttons and "tab_index" in self.navbar_buttons[key]:
                            tab_idx = self.navbar_buttons[key]["tab_index"]
                            self.tabWidget.setTabEnabled(tab_idx, False)
                            self.tabWidget.setTabToolTip(tab_idx, f"خطأ في تحميل الوحدة: {e}")
                else:
                    # إنشاء Placeholder إذا كانت الوحدة غير متوفرة من البداية
                    message = f"الوحدة غير متوفرة ({module_name})"
                    window_instance = PlaceholderWindow(title=title, message=message, **common_args)
                    self.windows[key] = window_instance
                    self.content_area.addWidget(window_instance)
                    print(f"INFO: تم إضافة PlaceholderWindow للمفتاح '{key}' (الوحدة غير متوفرة).")
                    # تعطيل علامة التبويب المقابلة
                    if key in self.navbar_buttons and "tab_index" in self.navbar_buttons[key]:
                        tab_idx = self.navbar_buttons[key]["tab_index"]
                        self.tabWidget.setTabEnabled(tab_idx, False)
                        self.tabWidget.setTabToolTip(tab_idx, "الوحدة غير متوفرة")
            else:
                print(f"WARNING: المفتاح '{key}' من navbar_items غير موجود في windows_config.")


    def show_logout_confirmation_dialog(self):
        """عرض رسالة تأكيد وإرجاع اختيار المستخدم."""
        msgBox = QMessageBox(self)
        msgBox.setWindowTitle("تأكيد الخروج")
        icon_path = "01.ico"
        if os.path.exists(icon_path):
            msgBox.setWindowIcon(QIcon(icon_path))
        msgBox.setText("<p style='font-family: Calibri; font-size: 13pt; color: #0D47A1; font-weight: bold;'>هل تريد الخروج من البرنامج؟</p>")
        msgBox.setInformativeText("<p style='font-family: Calibri; font-size: 11pt;'>سيتم إغلاق البرنامج وجميع النوافذ المفتوحة.</p>")
        msgBox.setIcon(QMessageBox.Question)
        yesButton = msgBox.addButton("نعم، أريد الخروج", QMessageBox.YesRole)
        noButton = msgBox.addButton("لا، العودة للبرنامج", QMessageBox.NoRole)
        yesButton.setStyleSheet("""
            QPushButton {
                font-family: Calibri; font-size: 13pt; font-weight: bold;
                color: white; background-color: #0D47A1; border: none;
                border-radius: 5px; padding: 5px 15px; min-width: 140px;
            }
            QPushButton:hover { background-color: #1565C0; }
            QPushButton:pressed { background-color: #0D47A1; }
        """)
        noButton.setStyleSheet("""
            QPushButton {
                font-family: Calibri; font-size: 13pt; font-weight: bold;
                color: #0D47A1; background-color: #E3F2FD; border: 1px solid #0D47A1;
                border-radius: 5px; padding: 5px 15px; min-width: 140px;
            }
            QPushButton:hover { background-color: #BBDEFB; }
            QPushButton:pressed { background-color: #E3F2FD; }
        """)
        msgBox.setDefaultButton(noButton)
        msgBox.setStyleSheet("QMessageBox { background-color: white; } QLabel { font-family: Calibri; min-width: 300px; }")
        msgBox.exec_()
        return msgBox.clickedButton() == yesButton

    def closeEvent(self, event):
        """معالجة حدث إغلاق النافذة"""
        try:
            # ضغط قاعدة البيانات قبل الإغلاق
            try:
                from database_utils import compress_database
                compress_result = compress_database()
                if compress_result:
                    print("تم ضغط قاعدة البيانات عند إغلاق التطبيق")
                else:
                    print("تعذر ضغط قاعدة البيانات عند الإغلاق")
            except ImportError:
                print("تعذر استيراد وحدة database_utils لضغط قاعدة البيانات")
            except Exception as e:
                print(f"خطأ عام أثناء ضغط قاعدة البيانات: {e}")

            # استمرار في تنفيذ الإغلاق الطبيعي
            event.accept()
        except Exception as e:
            print(f"خطأ أثناء معالجة إغلاق النافذة: {e}")
            event.accept()

if __name__ == "__main__":
    debug_print("--- بدء تنفيذ التطبيق الرئيسي ---")
    QApplication.setAttribute(Qt.AA_EnableHighDpiScaling, True)
    QApplication.setAttribute(Qt.AA_UseHighDpiPixmaps, True)
    app = QApplication(sys.argv)
    debug_print("1. تم إنشاء QApplication.")
    app.setLayoutDirection(Qt.RightToLeft)
    debug_print("   تم تعيين اتجاه التخطيط العام RTL.")

    # عند تشغيل الملف مباشرةً، نستخدم auto_show=True
    window = MainWindow(auto_show=True) # سيتم إنشاء الاتصال داخل MainWindow
    print("2. تم إنشاء نسخة من MainWindow (أو فشل الاتصال).")

    # التحقق مما إذا كانت النافذة لا تزال موجودة (لم يتم إغلاقها بسبب خطأ فادح)
    # MainWindow قد تعيد return في __init__ إذا فشل الاتصال، لذا قد لا تكون widget صالحة
    # التحقق من وجود اتصال قاعدة البيانات هو مؤشر أفضل على نجاح التهيئة
    if window.db_connection:
        print("3. الاتصال ناجح، عرض النافذة الرئيسية...")
        window.show() # showMaximized يحدث داخل __init__
        print("4. بدء حلقة الأحداث الرئيسية (app.exec_)...")
        exit_code = app.exec_()
        print(f"--- انتهاء تنفيذ التطبيق (رمز الخروج: {exit_code}) ---")
        sys.exit(exit_code)
    else:
        # إذا فشل الاتصال، تكون رسالة الخطأ قد عُرضت والبرنامج سيُغلق
        print("--- فشل الاتصال بقاعدة البيانات أو تهيئة النافذة، سيتم الخروج. ---")
        # لا حاجة لاستدعاء sys.exit(app.exec_()) لأن التطبيق لم يبدأ حلقة الأحداث
        sys.exit(1) # الخروج برمز خطأ
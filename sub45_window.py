#!/usr/bin/env python
# -*- coding: utf-8 -*-

import sys
import os
import sqlite3
from PyQt5.QtWidgets import (
    QApplication, QMainWindow, QTableWidget, QTableWidgetItem, QVBoxLayout,
    QHBoxLayout, QWidget, QHeaderView, QLabel, QPushButton, QMessageBox,
    QFrame, QTextEdit
)
from PyQt5.QtGui import QFont, QIcon
from PyQt5.QtCore import Qt

class RequestFormWindow(QMainWindow):
    """نافذة نموذج طلب الملفات"""

    def __init__(self, parent=None, db_path="data.db"):
        super().__init__(parent)
        print("🔧 تهيئة نافذة نموذج طلب الملفات")
        self.db_path = db_path
        print(f"📁 مسار قاعدة البيانات: {self.db_path}")
        self.initUI()
        self.load_data()

    def initUI(self):
        print("🎨 بناء واجهة المستخدم...")
        # تعيين عنوان النافذة
        self.setWindowTitle("نموذج طلب الملفات")
        self.setWindowIcon(QIcon("01.ico"))

        # تعيين حجم النافذة
        self.setGeometry(100, 100, 800, 600)
        self.setMinimumSize(600, 400)

        # إنشاء ويدجت مركزي
        central_widget = QWidget(self)
        self.setCentralWidget(central_widget)

        # إنشاء التخطيط الرئيسي
        main_layout = QVBoxLayout(central_widget)
        main_layout.setContentsMargins(10, 10, 10, 10)
        main_layout.setSpacing(10)

        # إضافة إطار للعنوان والأزرار
        top_frame = QFrame()
        top_frame.setFrameShape(QFrame.StyledPanel)
        top_frame.setStyleSheet("background-color: white; border: 1px solid #d0d0d0; border-radius: 5px;")
        top_layout = QVBoxLayout(top_frame)

        # العنوان الرئيسي
        self.header_label = QLabel("نموذج طلب الملفات")
        self.header_label.setFont(QFont("Calibri", 16, QFont.Bold))
        self.header_label.setStyleSheet("color: #0066cc; padding: 10px;")
        self.header_label.setAlignment(Qt.AlignCenter)
        top_layout.addWidget(self.header_label)

        # إطار للأزرار
        buttons_frame = QFrame()
        buttons_frame.setStyleSheet("background-color: white; border: none;")
        buttons_layout = QHBoxLayout(buttons_frame)
        buttons_layout.setSpacing(10)

        # زر الحفظ
        self.save_button = QPushButton("حفظ التعديلات")
        self.save_button.setFont(QFont("Calibri", 13, QFont.Bold))
        self.save_button.setStyleSheet("""
            QPushButton {
                background-color: #28a745;
                color: white;
                border: 1px solid #28a745;
                border-radius: 5px;
                padding: 8px 16px;
                margin: 5px;
                min-width: 120px;
            }
            QPushButton:hover {
                background-color: #218838;
                border-color: #218838;
            }
            QPushButton:pressed {
                background-color: #1e7e34;
                border-color: #1e7e34;
            }
        """)
        self.save_button.clicked.connect(self.save_data)
        buttons_layout.addWidget(self.save_button)

        # زر التحديث
        refresh_button = QPushButton("تحديث البيانات")
        refresh_button.setFont(QFont("Calibri", 13, QFont.Bold))
        refresh_button.setStyleSheet("""
            QPushButton {
                background-color: #007bff;
                color: white;
                border: 1px solid #007bff;
                border-radius: 5px;
                padding: 8px 16px;
                margin: 5px;
                min-width: 120px;
            }
            QPushButton:hover {
                background-color: #0069d9;
                border-color: #0069d9;
            }
            QPushButton:pressed {
                background-color: #0056b3;
                border-color: #0056b3;
            }
        """)
        refresh_button.clicked.connect(self.load_data)
        buttons_layout.addWidget(refresh_button)

        buttons_layout.addStretch()
        top_layout.addWidget(buttons_frame)

        main_layout.addWidget(top_frame)

        # إنشاء جدول لعرض البيانات
        self.table = QTableWidget()
        self.table.setFont(QFont("Calibri", 12))

        # تعيين خصائص الجدول
        self.table.setAlternatingRowColors(True)
        self.table.setSelectionBehavior(QTableWidget.SelectItems)
        self.table.setSelectionMode(QTableWidget.SingleSelection)

        # تعيين خلفية الجدول
        self.table.setStyleSheet("""
            QTableWidget {
                background-color: white;
                gridline-color: #d0d0d0;
                selection-background-color: #e6f3ff;
            }
            QTableWidget::item {
                padding: 8px;
                border: none;
            }
            QTableWidget::item:selected {
                background-color: #e6f3ff;
                color: black;
            }
        """)

        # تنسيق رأس الجدول
        header_font = QFont("Calibri", 12, QFont.Bold)
        self.table.horizontalHeader().setFont(header_font)
        self.table.horizontalHeader().setStyleSheet("""
            QHeaderView::section {
                background-color: #4a86e8;
                color: white;
                padding: 10px;
                border: 1px solid #3d71c7;
                font-weight: bold;
            }
        """)
        self.table.horizontalHeader().setDefaultSectionSize(150)
        self.table.horizontalHeader().setMinimumSectionSize(100)

        # إخفاء رؤوس الصفوف
        self.table.verticalHeader().setVisible(False)

        # تنسيق الصفوف
        self.table.verticalHeader().setDefaultSectionSize(50)

        main_layout.addWidget(self.table)

        # تعيين اتجاه النص من اليمين إلى اليسار
        self.setLayoutDirection(Qt.RightToLeft)

        print("✅ تم بناء واجهة المستخدم بنجاح")

    def connect_to_database(self):
        """الاتصال بقاعدة البيانات"""
        print("🔌 محاولة الاتصال بقاعدة البيانات...")
        try:
            conn = sqlite3.connect(self.db_path)
            print("✅ تم الاتصال بقاعدة البيانات بنجاح")
            return conn
        except Exception as e:
            print(f"❌ خطأ في الاتصال بقاعدة البيانات: {e}")
            QMessageBox.critical(self, "خطأ", f"فشل الاتصال بقاعدة البيانات: {e}")
            return None

    def ensure_table_exists(self):
        """التأكد من وجود جدول نموذج_الطلب وإنشاؤه إذا لم يكن موجوداً"""
        print("🔍 التحقق من وجود جدول نموذج_الطلب...")
        conn = self.connect_to_database()
        if not conn:
            return False

        try:
            cursor = conn.cursor()
            
            # التحقق من وجود الجدول
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='نموذج_الطلب'")
            if not cursor.fetchone():
                print("⚠️ جدول نموذج_الطلب غير موجود، سيتم إنشاؤه...")
                
                # إنشاء الجدول مع الأعمدة المطلوبة
                create_table_query = """
                CREATE TABLE نموذج_الطلب (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    السنة_الدراسية TEXT,
                    السيد_المدير TEXT,
                    المدينة TEXT,
                    المدير_الاقليمي TEXT,
                    مصلحة_التخطيط TEXT,
                    المديرية_المحلية TEXT,
                    الى_السيد TEXT,
                    مدير_الأكاديمية TEXT
                )
                """
                cursor.execute(create_table_query)
                
                # إدراج سجل افتراضي
                default_data = """
                INSERT INTO نموذج_الطلب (
                    السنة_الدراسية, السيد_المدير, المدينة, المدير_الاقليمي, 
                    مصلحة_التخطيط, المديرية_المحلية, الى_السيد, مدير_الأكاديمية
                ) VALUES (
                    '2023-2024',
                    'السيد مدير المؤسسة',
                    'المدينة',
                    'السيد المدير الإقليمي',
                    'مصلحة التخطيط والخريطة المدرسية',
                    'المديرية المحلية للتربية والتكوين',
                    'إلى السيد مدير المؤسسة',
                    'السيد مدير الأكاديمية الجهوية للتربية والتكوين'
                )
                """
                cursor.execute(default_data)
                conn.commit()
                print("✅ تم إنشاء جدول نموذج_الطلب مع البيانات الافتراضية")
            else:
                print("✅ جدول نموذج_الطلب موجود بالفعل")
            
            conn.close()
            return True
            
        except Exception as e:
            print(f"❌ خطأ في إنشاء/التحقق من الجدول: {e}")
            QMessageBox.critical(self, "خطأ", f"فشل في إنشاء/التحقق من جدول نموذج_الطلب: {e}")
            if conn:
                conn.close()
            return False

    def load_data(self):
        """تحميل البيانات من جدول نموذج_الطلب"""
        print("📥 بدء تحميل البيانات من جدول نموذج_الطلب...")
        
        # التأكد من وجود الجدول أولاً
        if not self.ensure_table_exists():
            return

        conn = self.connect_to_database()
        if not conn:
            return

        try:
            cursor = conn.cursor()

            # الحصول على أسماء الأعمدة
            cursor.execute("PRAGMA table_info(نموذج_الطلب)")
            columns_info = cursor.fetchall()
            columns = [col[1] for col in columns_info if col[1] != 'id']  # استثناء عمود id
            print(f"📝 الأعمدة الموجودة: {columns}")

            # تحميل البيانات (يجب أن يكون سجل واحد فقط)
            cursor.execute("SELECT * FROM نموذج_الطلب ORDER BY id LIMIT 1")
            data = cursor.fetchone()

            if not data:
                print("⚠️ لا توجد بيانات في جدول نموذج_الطلب")
                QMessageBox.warning(self, "تنبيه", "لا توجد بيانات في جدول نموذج الطلب.")
                conn.close()
                return

            print(f"📋 تم استرجاع سجل واحد من قاعدة البيانات")

            # إعداد الجدول
            self.table.setRowCount(len(columns))
            self.table.setColumnCount(2)
            self.table.setHorizontalHeaderLabels(["الحقل", "القيمة"])

            # ملء الجدول بالبيانات
            for row_idx, (col_name, value) in enumerate(zip(columns, data[1:])):  # تخطي id
                # عمود اسم الحقل (غير قابل للتحرير)
                field_item = QTableWidgetItem(col_name)
                field_item.setFlags(field_item.flags() & ~Qt.ItemIsEditable)
                field_item.setFont(QFont("Calibri", 12, QFont.Bold))
                from PyQt5.QtGui import QColor
                field_item.setBackground(QColor(240, 248, 255))  # خلفية زرقاء فاتحة
                self.table.setItem(row_idx, 0, field_item)

                # جميع الحقول تستخدم QTableWidgetItem عادية
                value_item = QTableWidgetItem(str(value) if value is not None else "")
                value_item.setFont(QFont("Calibri", 13))
                self.table.setItem(row_idx, 1, value_item)
                self.table.setRowHeight(row_idx, 40)

            # تعيين عرض الأعمدة
            self.table.setColumnWidth(0, 200)  # عمود اسم الحقل
            self.table.setColumnWidth(1, 500)  # عمود القيمة

            # منع تغيير حجم الأعمدة
            self.table.horizontalHeader().setSectionResizeMode(QHeaderView.Fixed)

            conn.close()
            print("✅ تم تحميل البيانات بنجاح")

        except Exception as e:
            print(f"❌ خطأ في تحميل البيانات: {e}")
            QMessageBox.critical(self, "خطأ", f"فشل تحميل البيانات: {e}")
            if conn:
                conn.close()

    def save_data(self):
        """حفظ التعديلات في قاعدة البيانات"""
        print("💾 بدء عملية حفظ التعديلات...")
        conn = self.connect_to_database()
        if not conn:
            return

        try:
            cursor = conn.cursor()

            # جمع البيانات من الجدول
            data_dict = {}
            for row in range(self.table.rowCount()):
                field_name = self.table.item(row, 0).text()
                
                # جميع الحقول تستخدم QTableWidgetItem عادية
                item = self.table.item(row, 1)
                field_value = item.text() if item else ""
                
                data_dict[field_name] = field_value
                print(f"📝 {field_name}: {field_value[:50]}{'...' if len(field_value) > 50 else ''}")

            # بناء استعلام التحديث
            columns = list(data_dict.keys())
            placeholders = ', '.join([f"{col} = ?" for col in columns])
            values = list(data_dict.values())

            update_query = f"UPDATE نموذج_الطلب SET {placeholders} WHERE id = 1"
            
            cursor.execute(update_query, values)
            conn.commit()
            
            affected_rows = cursor.rowcount
            print(f"✅ تم تحديث {affected_rows} سجل")

            if affected_rows == 0:
                print("⚠️ لم يتم العثور على سجل للتحديث، محاولة الإدراج...")
                # إذا لم يتم تحديث أي سجل، قم بإدراج سجل جديد
                insert_columns = ', '.join(columns)
                insert_placeholders = ', '.join(['?' for _ in columns])
                insert_query = f"INSERT INTO نموذج_الطلب ({insert_columns}) VALUES ({insert_placeholders})"
                
                cursor.execute(insert_query, values)
                conn.commit()
                print("✅ تم إدراج سجل جديد")

            conn.close()

            # عرض رسالة نجاح
            QMessageBox.information(self, "نجح الحفظ", "تم حفظ التعديلات بنجاح!")
            print("✅ تم حفظ التعديلات بنجاح")

        except Exception as e:
            print(f"❌ خطأ في حفظ البيانات: {e}")
            QMessageBox.critical(self, "خطأ", f"فشل في حفظ البيانات: {e}")
            if conn:
                conn.rollback()
                conn.close()

# تشغيل التطبيق كنافذة مستقلة للاختبار
if __name__ == "__main__":
    print("🚀 بدء تشغيل تطبيق نموذج طلب الملفات...")
    app = QApplication(sys.argv)
    app.setLayoutDirection(Qt.RightToLeft)
    print("🖼️ إنشاء النافذة الرئيسية")
    window = RequestFormWindow()
    print("👁️ عرض النافذة")
    window.show()
    print("⏳ دخول حلقة الأحداث الرئيسية")
    sys.exit(app.exec_())

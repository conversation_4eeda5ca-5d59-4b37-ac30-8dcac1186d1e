# main_window.py
# -*- coding: utf-8 -*-

import sys
import os
from PyQt5.QtWidgets import QApplication, QMainWindow
from PyQt5.QtGui import QIcon

# --- نظام التحكم في مستوى الرسائل ---
VERBOSE_MODE = False  # تعيين False لتقليل الرسائل
SHOW_FONT_REGISTRATION = False  # تعيين False لإخفاء رسائل تسجيل الخطوط
SHOW_CSS_WARNINGS = False  # تعيين False لإخفاء تحذيرات CSS

def debug_print(message, force=False):
    """طباعة الرسائل حسب مستوى التفصيل المحدد"""
    if VERBOSE_MODE or force:
        print(message)

def suppress_qt_warnings():
    """قمع تحذيرات Qt غير المهمة"""
    if not SHOW_CSS_WARNINGS:
        os.environ['QT_LOGGING_RULES'] = '*.debug=false;qt.qpa.*=false'

# تطبيق قمع التحذيرات
suppress_qt_warnings()

# --- المكتبات المطلوبة للتضمين في التطبيق النهائي ---
import simple6_window
import print2_test
import styles
import sub0_window
import sub1_window
import sub2_window
import sub3_window
import sub4_window
import sub5_window
import sub6_window
import sub7_window
import sub8_window
import sub9_window
import sub10_window
import sub11_window
import sub12_window
import sub13_window
import sub14_window
import sub16_window
import sub17_window
import sub18_window
import sub19_window
import sub20_window
import sub23_window
import sub21_window
# استيراد sub37_window تم استبداله باستيراد مباشر للكلاس في قسم الاستيرادات المخصصة
import sub22_window
import sub01_window  # إضافة استيراد sub01_window
import violations_table
import absence_reports
import attendance_report
import default_settings_window
import help_texts
# --- نهاية قائمة المكتبات المطلوبة للتضمين ---

# ـــــــــ إجبار PyInstaller على تضمين الوحدات المفقودة ديناميكيًّا ـــــــــ
try:
    # هذه الوحدات مطلوبة للتضمين في التطبيق النهائي
    # لكن قد لا تكون مستخدمة مباشرة في الكود
    import print0
    import print1
    import print3
    import print4
    import print5
    import print6
    import print7
    import print8
    import print9
    import print10
    import thermal_printer
    import print_student_record_improved
    import print_violation_report
    import custom_messages
    import arabic_pdf_report
    import professional_exam_table
    import second_semester_helper
    import set_default_printer
    import split_attendance_report
    import student_card_ui
    import templates_manager
except ImportError:
    # تجاهل إذا أحدها غير موجود
    pass
# ـــــــــ نهاية إجبار التضمين ـــــــــ

# --- استيراد نافذة طباعة اللوائح ---
try:
    from sub20_window import PrintListsWindow
    PRINT_LISTS_WINDOW_AVAILABLE = True
    debug_print("INFO: تم استيراد PrintListsWindow بنجاح من sub20_window.py.")
except ImportError as e:
    debug_print(f"تحذير: لم يتم العثور على ملف 'sub20_window.py' أو الكلاس PrintListsWindow فيه.\nالخطأ: {e}")
    PRINT_LISTS_WINDOW_AVAILABLE = False

# --- استيراد نافذة بطاقة الأقسام ---
try:
    from sub20_window import RegulationsCardWindow
    SECTIONS_CARD_AVAILABLE = True
    debug_print("INFO: تم استيراد RegulationsCardWindow بنجاح من sub20_window.py.")
except ImportError as e:
    debug_print(f"تحذير: لم يتم العثور على ملف 'sub20_window.py' أو الكلاس RegulationsCardWindow فيه.\nالخطأ: {e}")
    SECTIONS_CARD_AVAILABLE = False
# --- نهاية استيراد نافذة طباعة اللوائح ---

class MyWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("نافذة مع أيقونة")
        icon_path = os.path.join(os.path.dirname(__file__), "01.ico")
        self.setWindowIcon(QIcon(icon_path))
        self.resize(400, 300)



import sys
import os
from PyQt5.QtWidgets import (QApplication, QMainWindow, QWidget, QPushButton, QVBoxLayout,
                             QHBoxLayout, QFrame, QDesktopWidget,
                             QStackedWidget, QMessageBox, QLabel, QSpacerItem, QSizePolicy,
                             QTableWidget, QTabWidget, QTabBar, QDialog, QStyle)  # إضافة QTabWidget و QTabBar و QDialog و QStyle
from PyQt5.QtGui import QFont, QIcon, QColor, QPixmap
from PyQt5.QtCore import Qt, QTimer, QSize, QCoreApplication

# إنشاء كومبوننت لشريط تبويب مزدوج الصف
class TwoRowTabComponent(QWidget):
    """مكون مخصص يستخدم صفين من QTabBar مع QStackedWidget للمحتوى"""
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setObjectName("TwoRowTabComponent")

        # إنشاء التخطيط الرئيسي
        self.layout = QVBoxLayout(self)
        self.layout.setContentsMargins(0, 0, 0, 0)
        self.layout.setSpacing(0)

        # إنشاء إطار لشريط التبويب
        self.tab_frame = QFrame()
        self.tab_frame.setObjectName("TabFrame")
        self.tab_frame.setMinimumHeight(70)
        self.tab_frame.setMaximumHeight(70)

        # تخطيط شريط التبويب
        self.tab_layout = QVBoxLayout(self.tab_frame)
        self.tab_layout.setContentsMargins(0, 0, 0, 0)
        self.tab_layout.setSpacing(5)  # مسافة صغيرة بين الصفين

        # إنشاء شريط التبويب العلوي (أول 10 تبويبات)
        self.top_tab_bar = QTabBar()
        self.top_tab_bar.setObjectName("TopTabBar")
        self.top_tab_bar.setShape(QTabBar.RoundedNorth)
        self.top_tab_bar.setExpanding(False)
        self.top_tab_bar.setDrawBase(False)
        self.top_tab_bar.setCursor(Qt.PointingHandCursor)

        # إنشاء شريط التبويب السفلي (باقي التبويبات)
        self.bottom_tab_bar = QTabBar()
        self.bottom_tab_bar.setObjectName("BottomTabBar")
        self.bottom_tab_bar.setShape(QTabBar.RoundedNorth)
        self.bottom_tab_bar.setExpanding(False)
        self.bottom_tab_bar.setDrawBase(False)
        self.bottom_tab_bar.setCursor(Qt.PointingHandCursor)

        # إضافة أشرطة التبويب إلى التخطيط
        self.tab_layout.addWidget(self.top_tab_bar)
        self.tab_layout.addWidget(self.bottom_tab_bar)

        # إنشاء محتوى الويدجيت المكدس
        self.stacked_widget = QStackedWidget()
        self.stacked_widget.setObjectName("TabContent")

        # ربط إشارات تغيير التبويب بالدوال المعالجة
        self.top_tab_bar.currentChanged.connect(self._on_top_tab_changed)
        self.bottom_tab_bar.currentChanged.connect(self._on_bottom_tab_changed)

        # تخزين الإشارة الخارجية لتغيير التبويب
        self.current_changed_signal = None

        # إضافة المكونات إلى التخطيط الرئيسي
        self.layout.addWidget(self.tab_frame)
        self.layout.addWidget(self.stacked_widget, 1)  # تمدد لملء المساحة المتبقية

        # المتغيرات الداخلية
        self.first_row_count = 10  # عدد التبويبات في الصف الأول
        self.previous_tab_index = 0
        self.tab_data = {}  # لتخزين بيانات التبويبات

    def _on_top_tab_changed(self, index):
        """معالجة تغيير تبويب في الصف العلوي"""
        if index < 0:  # إذا تم إلغاء تحديد جميع التبويبات
            return

        # الحصول على البيانات المرتبطة بالتبويب
        tab_data = self.top_tab_bar.tabData(index)

        if tab_data:
            # حفظ المؤشر السابق
            self.previous_tab_index = self.stacked_widget.currentIndex()

            # تعيين الويدجيت المكدس للتبويب المحدد
            self.stacked_widget.setCurrentIndex(index)

            # إلغاء تحديد جميع التبويبات في الصف السفلي
            self.bottom_tab_bar.blockSignals(True)
            self.bottom_tab_bar.setCurrentIndex(-1)
            self.bottom_tab_bar.blockSignals(False)

            # استدعاء الإشارة الخارجية إذا كانت موجودة
            if self.current_changed_signal:
                self.current_changed_signal(index, tab_data)

    def _on_bottom_tab_changed(self, index):
        """معالجة تغيير تبويب في الصف السفلي"""
        if index < 0:  # إذا تم إلغاء تحديد جميع التبويبات
            return

        # حساب المؤشر الفعلي (مع مراعاة التبويبات في الصف العلوي)
        actual_index = index + self.first_row_count

        # الحصول على البيانات المرتبطة بالتبويب
        tab_data = self.bottom_tab_bar.tabData(index)

        if tab_data:
            # حفظ المؤشر السابق
            self.previous_tab_index = self.stacked_widget.currentIndex()

            # تعيين الويدجيت المكدس للتبويب المحدد
            self.stacked_widget.setCurrentIndex(actual_index)

            # إلغاء تحديد جميع التبويبات في الصف العلوي
            self.top_tab_bar.blockSignals(True)
            self.top_tab_bar.setCurrentIndex(-1)
            self.top_tab_bar.blockSignals(False)

            # استدعاء الإشارة الخارجية إذا كانت موجودة
            if self.current_changed_signal:
                self.current_changed_signal(actual_index, tab_data)

    def addTab(self, page, text):
        """إضافة تبويب جديد وصفحة مرتبطة به"""
        # إضافة الصفحة إلى الويدجيت المكدس
        index = self.stacked_widget.addWidget(page)

        # تحديد أي صف سيتم إضافة التبويب إليه
        if index < self.first_row_count:
            # إضافة التبويب للصف العلوي
            tab_index = self.top_tab_bar.addTab(text)
            self.top_tab_bar.setTabData(tab_index, index)
        else:
            # إضافة التبويب للصف السفلي
            tab_index = self.bottom_tab_bar.addTab(text)
            self.bottom_tab_bar.setTabData(tab_index, index)

        return index

    def setTabData(self, index, data):
        """تعيين بيانات مخصصة للتبويب المحدد"""
        if index < self.first_row_count:
            self.top_tab_bar.setTabData(index, data)
        else:
            self.bottom_tab_bar.setTabData(index - self.first_row_count, data)

        # حفظ البيانات أيضًا للاستخدام الداخلي
        self.tab_data[index] = data

    def tabData(self, index):
        """استرجاع بيانات التبويب المحدد"""
        if index < self.first_row_count:
            return self.top_tab_bar.tabData(index)
        else:
            return self.bottom_tab_bar.tabData(index - self.first_row_count)

    def setCurrentIndex(self, index):
        """تعيين التبويب الحالي"""
        if index < 0 or index >= self.stacked_widget.count():
            return

        # إلغاء تحديد جميع التبويبات في كلا الصفين أولاً
        self.top_tab_bar.blockSignals(True)
        self.bottom_tab_bar.blockSignals(True)
        
        # إلغاء تحديد جميع التبويبات
        self.top_tab_bar.setCurrentIndex(-1)
        self.bottom_tab_bar.setCurrentIndex(-1)

        # تحديد الصف المناسب وتعيين التبويب النشط
        if index < self.first_row_count:
            self.top_tab_bar.setCurrentIndex(index)
        else:
            bottom_index = index - self.first_row_count
            self.bottom_tab_bar.setCurrentIndex(bottom_index)

        # إعادة تفعيل الإشارات
        self.top_tab_bar.blockSignals(False)
        self.bottom_tab_bar.blockSignals(False)

        # تعيين المحتوى المناسب
        self.stacked_widget.setCurrentIndex(index)

    def currentIndex(self):
        """الحصول على مؤشر التبويب الحالي"""
        return self.stacked_widget.currentIndex()

    def setTabEnabled(self, index, enabled):
        """تمكين أو تعطيل تبويب محدد"""
        if index < self.first_row_count:
            self.top_tab_bar.setTabEnabled(index, enabled)
        else:
            self.bottom_tab_bar.setTabEnabled(index - self.first_row_count, enabled)

    def setTabToolTip(self, index, tooltip):
        """تعيين تلميح أداة للتبويب المحدد"""
        if index < self.first_row_count:
            self.top_tab_bar.setTabToolTip(index, tooltip)
        else:
            self.bottom_tab_bar.setTabToolTip(index - self.first_row_count, tooltip)

    def count(self):
        """عدد التبويبات الإجمالي"""
        return self.stacked_widget.count()

    def blockSignals(self, block):
        """تعطيل أو تمكين الإشارات لجميع المكونات"""
        self.top_tab_bar.blockSignals(block)
        self.bottom_tab_bar.blockSignals(block)
        return super().blockSignals(block)

    def setStyleSheet(self, sheet):
        """تعيين ورقة الأنماط مع تنظيف الخصائص غير المدعومة"""
        # إزالة الخصائص غير المدعومة في PyQt5
        cleaned_sheet = sheet.replace('box-shadow:', '/* box-shadow:').replace('cursor:', '/* cursor:')
        super().setStyleSheet(cleaned_sheet)

    def connectCurrentChanged(self, slot):
        """ربط إشارة تغيير التبويب بدالة خارجية"""
        self.current_changed_signal = slot

# --- >>> استيراد الدوال والكلاسات من الملف الصحيح (sub4_window.py) <<< ---
try:
    # استيراد الدوال العامة للاتصال وقراءة السنة الدراسية من sub4_window.py
    from sub4_window import create_connection, get_current_academic_year
    # استيراد نافذة اللوائح والأقسام من sub4_window.py
    from sub4_window import Sub4Window, Simple00Window
    SIMPLE_SEARCH_AVAILABLE = True
    DB_FUNCTIONS_AVAILABLE = True
    debug_print("INFO: تم استيراد Sub4Window ودوال الاتصال بنجاح من sub4_window.py.")
except ImportError as e:
    debug_print(f"خطأ فادح: لم يتم العثور على ملف 'sub4_window.py' أو الأسماء المطلوبة فيه.\nالخطأ: {e}")
    # تعريف بدائل وهمية لمنع الانهيار الكامل
    Sub4Window = None
    Simple00Window = None
    SIMPLE_SEARCH_AVAILABLE = False
    def create_connection(): return False, None, "فشل استيراد الدالة 'create_connection' من 'sub4_window.py'"
    def get_current_academic_year(db): return None, "فشل استيراد الدالة 'get_current_academic_year' من 'sub4_window.py'"
    DB_FUNCTIONS_AVAILABLE = False
    # يمكنك إضافة إنهاء إجباري هنا إذا كانت هذه المكونات أساسية
    # sys.exit(f"خطأ حرج: فشل استيراد مكونات أساسية من sub4_window.py")
# --- نهاية استيراد الدوال والكلاسات ---

# --- استيراد النوافذ الفرعية الأخرى (اختياري) ---
try:
    from sub0_window import Sub0Window
    if not hasattr(Sub0Window, 'set_current_tab_by_index'):
        debug_print("تحذير: الدالة 'set_current_tab_by_index' غير موجودة في 'Sub0Window'.")
    SUB0_AVAILABLE = True
    debug_print("INFO: تم العثور على 'sub0_window.py'.")
except ImportError:
    debug_print("تحذير: لم يتم العثور على ملف 'sub0_window.py'.")
    Sub0Window = None
    SUB0_AVAILABLE = False

try:
    from sub3_window import Sub3Window
    SUB3_AVAILABLE = True
    debug_print("INFO: تم العثور على 'sub3_window.py'.")
except ImportError:
    debug_print("تحذير: لم يتم العثور على ملف 'sub3_window.py'. سيتم استخدام محتوى مؤقت لـ 'البنية التربوية'.")
    Sub3Window = None
    SUB3_AVAILABLE = False

# --- استيراد النافذة المؤقتة (Placeholder) ---
try:
    # تعريف بديل لـ PlaceholderWindow إذا لم يتم العثور على components.placeholder
    class PlaceholderWindow(QWidget):
        def __init__(self, title="مؤقت", message="المحتوى غير متوفر", db=None, academic_year=None, parent=None, **kwargs):
            super().__init__(parent)
            layout = QVBoxLayout(self)
            layout.setAlignment(Qt.AlignCenter)
            self.label = QLabel(f"<h2>{title}</h2><p>{message}</p>")
            self.label.setAlignment(Qt.AlignCenter)
            self.label.setFont(QFont("Calibri", 14))
            self.label.setWordWrap(True)
            layout.addWidget(self.label)
            self.setWindowTitle(title)
            debug_print(f"INFO: استخدام PlaceholderWindow المؤقتة لـ '{title}'.")
    debug_print("INFO: تم العثور على 'components/placeholder.py'.")
except ImportError:
    debug_print("تحذير: لم يتم العثور على 'components/placeholder.py'. سيتم استخدام QLabel كمحتوى مؤقت.")
    class PlaceholderWindow(QWidget):
        # زيادة مرونة __init__ لتقبل المعاملات غير المستخدمة
        def __init__(self, title="مؤقت", message="المحتوى غير متوفر", db=None, academic_year=None, parent=None, **kwargs):
            super().__init__(parent)
            layout = QVBoxLayout(self); layout.setAlignment(Qt.AlignCenter)
            self.label = QLabel(f"<h2>{title}</h2><p>{message}</p>")
            self.label.setAlignment(Qt.AlignCenter); self.label.setFont(QFont("Calibri", 14)); self.label.setWordWrap(True)
            layout.addWidget(self.label)
            self.setWindowTitle(title)
            debug_print(f"INFO: استخدام PlaceholderWindow المؤقتة لـ '{title}'.")


# --- >>> استيراد نافذة معالجة تبرير الغياب <<< ---
try:
    from sub14_window import AbsenceManagementWindow
    ABSENCE_MANAGEMENT_WINDOW_AVAILABLE = True
    debug_print("INFO: تم استيراد AbsenceManagementWindow بنجاح من sub14_window.py.")
except ImportError as e:
    debug_print(f"تحذير: لم يتم العثور على ملف 'sub14_window.py' أو الكلاس AbsenceManagementWindow فيه.\nالخطأ: {e}")
    AbsenceManagementWindow = None
    ABSENCE_MANAGEMENT_WINDOW_AVAILABLE = False
# --- نهاية استيراد نافذة معالجة تبرير الغياب ---

# --- >>> استيراد نافذة البحث الجديدة (Simple6Window) <<< ---
try:
    # استيراد نافذة البحث من simple6_window.py
    from simple6_window import Simple6Window
    SIMPLE6_AVAILABLE = True
    debug_print("INFO: تم استيراد Simple6Window بنجاح من simple6_window.py.")
except ImportError as e:
    debug_print(f"تحذير: لم يتم العثور على ملف 'simple6_window.py' أو الكلاس Simple6Window فيه.\nالخطأ: {e}")
    Simple6Window = None
    SIMPLE6_AVAILABLE = False
# --- نهاية استيراد نافذة البحث ---

# --- >>> استيراد نافذة بطاقة التلميذ (StudentCardWindow) <<< ---
try:
    from sub10_window import StudentCardWindow
    STUDENT_CARD_AVAILABLE = True
    debug_print("INFO: تم استيراد StudentCardWindow بنجاح من sub10_window.py.")
except ImportError as e:
    debug_print(f"تحذير: لم يتم العثور على ملف 'sub10_window.py' أو الكلاس StudentCardWindow فيه.\nالخطأ: {e}")
    StudentCardWindow = None
    STUDENT_CARD_AVAILABLE = False
# --- نهاية استيراد نافذة بطاقة التلميذ ---

# --- >>> إضافة فئة مدمجة لبطاقة التلميذ <<< ---
class EmbeddedStudentCardWindow(QWidget):
    """
    نافذة مدمجة تحتوي على StudentCardWindow وتتكيف مع حجم الإطار الأساسي
    """
    def __init__(self, db=None, academic_year=None, parent=None):
        super().__init__(parent)
        self.setObjectName("EmbeddedStudentCard")

        # إنشاء التخطيط الأساسي
        self.main_layout = QVBoxLayout(self)
        self.main_layout.setContentsMargins(0, 0, 0, 0)
        self.main_layout.setSpacing(0)

        # عنوان النافذة المدمجة (اختياري)
        self.header_label = QLabel("بطاقة تلميذ - عرض مدمج")
        self.header_label.setAlignment(Qt.AlignCenter)
        self.header_label.setFont(QFont("Calibri", 14, QFont.Bold))
        self.header_label.setStyleSheet("background-color: #3498db; color: white; padding: 5px;")
        self.main_layout.addWidget(self.header_label)

        # إنشاء نافذة بطاقة التلميذ داخل النافذة المدمجة
        if STUDENT_CARD_AVAILABLE:
            try:
                self.student_card = StudentCardWindow(db=db, academic_year=academic_year, parent=self)
                # تغيير سلوك النافذة لتكون مدمجة داخل الإطار
                self.student_card.setWindowFlags(Qt.Widget)
                # إزالة تحديد الحجم الثابت ليتمدد مع حجم النافذة
                if hasattr(self.student_card, 'setFixedSize'):
                    # استخدام طريقة بديلة لإزالة قيود الحجم الثابت
                    self.student_card.setFixedSize(16777215, 16777215)  # استخدام قيمة كبيرة بدلاً من QWIDGETSIZE_MAX
                self.student_card.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)

                # إضافة نافذة بطاقة التلميذ إلى التخطيط الرئيسي
                self.main_layout.addWidget(self.student_card)
                debug_print("INFO: تم إنشاء نافذة بطاقة التلميذ المدمجة بنجاح")
            except Exception as e:
                error_label = QLabel(f"خطأ في تحميل نافذة بطاقة التلميذ: {str(e)}")
                error_label.setStyleSheet("color: red; font-weight: bold; padding: 20px;")
                error_label.setAlignment(Qt.AlignCenter)
                self.main_layout.addWidget(error_label)
                debug_print(f"ERROR: فشل إنشاء نافذة بطاقة التلميذ المدمجة: {e}")
        else:
            message_label = QLabel("نافذة بطاقة التلميذ غير متوفرة")
            message_label.setStyleSheet("color: orange; font-weight: bold; padding: 20px;")
            message_label.setAlignment(Qt.AlignCenter)
            self.main_layout.addWidget(message_label)
            debug_print("WARNING: نافذة بطاقة التلميذ غير متوفرة لإنشاء النسخة المدمجة")

    def load_student(self, student_id):
        """تحميل بيانات تلميذ محدد في النافذة المدمجة"""
        if hasattr(self, 'student_card') and hasattr(self.student_card, 'load_student_data'):
            try:
                self.student_card.load_student_data(student_id)
                return True
            except Exception as e:
                print(f"ERROR: فشل تحميل بيانات التلميذ {student_id}: {e}")
                return False
        return False
# --- >>> نهاية فئة بطاقة التلميذ المدمجة <<< ---

# --- >>> استيراد نافذة مسك الغياب (Hirasa300Window) <<< ---
try:
    from sub9_window import Hirasa300Window
    HIRASA300_AVAILABLE = True
    debug_print("INFO: تم استيراد Hirasa300Window بنجاح من sub9_window.py.")
except ImportError as e:
    debug_print(f"تحذير: لم يتم العثور على ملف 'sub9_window.py' أو الكلاس Hirasa300Window فيه.\nالخطأ: {e}")
    Hirasa300Window = None
    HIRASA300_AVAILABLE = False
# --- نهاية استيراد نافذة مسك الغياب ---

# --- >>> استيراد نافذة معالجة طلبات الشهادات المدرسية <<< ---
try:
    from sub19_window import SchoolCertificateWindow
    SCHOOL_CERTIFICATE_AVAILABLE = True
    debug_print("INFO: تم استيراد SchoolCertificateWindow بنجاح من sub19_window.py.")
except ImportError as e:
    debug_print(f"تحذير: لم يتم العثور على ملف 'sub19_window.py' أو الكلاس SchoolCertificateWindow فيه.\nالخطأ: {e}")
    SchoolCertificateWindow = None
    SCHOOL_CERTIFICATE_AVAILABLE = False
# --- نهاية استيراد نافذة معالجة طلبات الشهادات المدرسية ---

# --- >>> استيراد نافذة إخبار بنشاط <<< ---
try:
    from sub18_window import NewsWindow
    NEWS_WINDOW_AVAILABLE = True
    debug_print("INFO: تم استيراد NewsWindow بنجاح من sub18_window.py.")
except ImportError as e:
    debug_print(f"تحذير: لم يتم العثور على ملف 'sub18_window.py' أو الكلاس NewsWindow فيه.\nالخطأ: {e}")
    NewsWindow = None
    NEWS_WINDOW_AVAILABLE = False
# --- نهاية استيراد نافذة إخبار بنشاط ---

# --- >>> استيراد نافذة إنشاء جدول مسك_الغياب <<< ---
try:
    # تأكد أن هذا السطر والرسائل أدناه تستخدم الاسم النظيف "sub21_window"
    try:
        from sub21_window import CreateAbsenceTableWindow
        CREATE_ABSENCE_TABLE_AVAILABLE = True
        debug_print("INFO: تم استيراد CreateAbsenceTableWindow بنجاح من sub21_window.py.")
    except ImportError as e:
        debug_print(f"تحذير: لم يتم العثور على ملف 'sub21_window.py' أو الكلاس CreateAbsenceTableWindow فيه.\nالخطأ: {e}")
        CreateAbsenceTableWindow = None
        CREATE_ABSENCE_TABLE_AVAILABLE = False
    CREATE_ABSENCE_TABLE_AVAILABLE = True
    debug_print("INFO: تم استيراد CreateAbsenceTableWindow بنجاح من sub21_window.py.")
except ImportError as e:
    debug_print(f"تحذير: لم يتم العثور على ملف 'sub21_window.py' أو الكلاس CreateAbsenceTableWindow فيه.\nالخطأ: {e}")
    CreateAbsenceTableWindow = None
    CREATE_ABSENCE_TABLE_AVAILABLE = False
# --- نهاية استيراد نافذة إنشاء جدول مسك_الغياب ---

# --- >>> استيراد نافذة تهيئة الامتحانات <<< ---
try:
    from sub22_window import ExamSetupWindow
    EXAM_SETUP_AVAILABLE = True
    debug_print("INFO: تم استيراد ExamSetupWindow بنجاح من sub22_window.py.")
except ImportError as e:
    debug_print(f"تحذير: لم يتم العثور على ملف 'sub22_window.py' أو الكلاس ExamSetupWindow فيه.\nالخطأ: {e}")
    ExamSetupWindow = None
    EXAM_SETUP_AVAILABLE = False
# --- نهاية استيراد نافذة تهيئة الامتحانات ---

# --- >>> استيراد نافذة أوراق التنقيط (CreateAbsenceTableWindow) <<< ---
try:
    from sub37_window import CreateAbsenceTableWindow as ScoringSheetWindow
    SCORING_SHEET_AVAILABLE = True
    debug_print("INFO: تم استيراد CreateAbsenceTableWindow بنجاح من sub37_window.py.")
except ImportError as e:
    debug_print(f"تحذير: لم يتم العثور على ملف 'sub37_window.py' أو الكلاس CreateAbsenceTableWindow فيه.\nالخطأ: {e}")
    ScoringSheetWindow = None
    SCORING_SHEET_AVAILABLE = False
# --- نهاية استيراد نافذة أوراق التنقيط ---

# --- >>> استيراد نافذة واجهة البرنامج (Sub01Window) <<< ---
try:
    from sub01_window import Sub01Window
    SUB01_AVAILABLE = True
    debug_print("INFO: تم استيراد Sub01Window بنجاح من sub01_window.py.")
except ImportError as e:
    debug_print(f"تحذير: لم يتم العثور على ملف 'sub01_window.py' أو الكلاس Sub01Window فيه.\nالخطأ: {e}")
    Sub01Window = None
    SUB01_AVAILABLE = False
# --- نهاية استيراد نافذة واجهة البرنامج ---



# --- >>> إنشاء فئة مخصصة تمتد من Sub4Window لإضافة السلوك المطلوب <<< ---
class EnhancedSimpleSearchWindow(Sub4Window):
    """
    فئة مخصصة تمتد من Sub4Window لإضافة وظيفة معالجة النقر على خلايا الجدول
    وفتح نافذة بطاقة الطالب عند النقر على رموز الطلاب.
    """
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        # تحتفظ الفئة الأساسية بالفعل بمتغير active_student_window
        # كما أن ربط الإشارة table_lists.clicked مع on_table_cell_clicked موجود بالفعل
        debug_print("INFO: تم إنشاء EnhancedSimpleSearchWindow بنجاح")

    def on_table_cell_clicked(self, index, column=None):
        """
        يتجاوز هذا الأسلوب الأسلوب الأصلي في SimpleSearchWindow
        لضمان فتح النافذة الصحيحة عند النقر على رمز في جدول اللوائح
        """
        # استدعاء طريقة الأب للتعامل مع النقر على الخلية بشكل صحيح
        debug_print(f"DEBUG: تم النقر على الخلية {index.row()}, {index.column()}")

        # استخدام طريقة الفئة الأم نفسها (SimpleSearchWindow)
        super().on_table_cell_clicked(index, column)

        # للتشخيص فقط - طباعة حالة النافذة النشطة
        if hasattr(self, 'active_student_window') and self.active_student_window:
            debug_print(f"DEBUG: النافذة النشطة الآن هي: {type(self.active_student_window).__name__}")

# تعديل مستوى التشخيص لتقليل الرسائل
DEBUG_MODE = False  # ضبط هذا على False لتقليل الرسائل

def debug_print(*args, **kwargs):
    """دالة مساعدة للطباعة فقط في وضع التشخيص"""
    if DEBUG_MODE:
        print(*args, **kwargs)

class MainWindow(QMainWindow):
    def __init__(self, auto_show=False):
        super().__init__()
        self.setWindowTitle("نظام إدارة المدرسة")
        self.setLayoutDirection(Qt.RightToLeft)
        self.previous_tab_index = 0 # لتتبع التبويب السابق قبل الضغط على خروج

        # تقليل رسائل التشخيص
        self.setup_logging()

        # تحديد مسار قاعدة البيانات في مجلد منفصل على سطح المكتب
        self.db_folder = os.path.join(os.path.expanduser("~"), "Desktop", "taheri22")
        # التحقق من وجود المجلد وإنشائه إذا لم يكن موجوداً
        if not os.path.exists(self.db_folder):
            try:
                os.makedirs(self.db_folder)
                print(f"INFO: تم إنشاء مجلد قاعدة البيانات: {self.db_folder}")
            except Exception as e:
                print(f"ERROR: فشل إنشاء مجلد قاعدة البيانات: {e}")
                QMessageBox.critical(self, "خطأ", f"فشل إنشاء مجلد قاعدة البيانات:\n{e}")

        # تعيين أيقونة البرنامج
        icon_path = os.path.join(os.path.dirname(__file__), "01.ico")
        if (os.path.exists(icon_path)):
            self.setWindowIcon(QIcon(icon_path))
        else:
            print(f"تنبيه: ملف الأيقونة غير موجود في {icon_path}")

        # --- >>> إنشاء اتصال قاعدة البيانات والسنة الدراسية مبكرًا <<< ---
        # تعديل دالة إنشاء الاتصال لاستخدام المسار الجديد
        self.db_connection = None
        self.current_academic_year = None
        debug_print("INFO: محاولة الاتصال بقاعدة البيانات باستخدام الدالة المستوردة...")

        # تمرير مسار المجلد الجديد لقاعدة البيانات إلى دالة إنشاء الاتصال
        connected, db_conn, db_error = self.create_db_connection()

        if connected:
            self.db_connection = db_conn
            print("INFO: تم الاتصال بقاعدة البيانات بنجاح.")
            year, year_error = get_current_academic_year(self.db_connection)
            if not year_error:
                self.current_academic_year = year
                print(f"INFO: السنة الدراسية الحالية: {self.current_academic_year}")
            else:
                QMessageBox.warning(self, "خطأ في البيانات", f"لم يتم العثور على السنة الدراسية:\n{year_error}")
                print(f"WARNING: {year_error}")
        else:
            QMessageBox.critical(self, "خطأ فادح", f"فشل الاتصال بقاعدة البيانات:\n{db_error}\nسيتم إغلاق البرنامج.")
            QTimer.singleShot(100, QCoreApplication.instance().quit) # جدولة الخروج
            return # منع استكمال التهيئة
        # ---------------------------------------------------------

        self.menuBar().setVisible(False)

        self.central_widget = QWidget()
        self.setCentralWidget(self.central_widget)

        self.main_layout = QVBoxLayout(self.central_widget)
        self.main_layout.setContentsMargins(0, 0, 0, 0)
        self.main_layout.setSpacing(0) # إزالة المسافة بين العناصر

        self._create_top_navbar()
        self._setup_content_area()

        self.main_layout.addWidget(self.navbar_frame) # إضافة شريط التبويب
        self.main_layout.addWidget(self.content_area, 1) # إضافة منطقة المحتوى مع نسبة تمدد 1

        print("INFO: إنشاء النوافذ الفرعية...")
        self._create_windows()
        print("INFO: ربط أزرار التنقل بالنوافذ...")
        self._link_navbar_buttons()

        self.setup_window_connections()

        # تحديد النافذة والزر الأول
        default_tab_index = 0  # قيمة افتراضية
        if self.content_area.count() > 0:
            # البحث عن مؤشر تبويب "واجهة البرنامج"
            try:
                default_tab_key = "program_interface"
                default_tab_index = [i for i, item_tuple in enumerate(self.navbar_items) if item_tuple[1] == default_tab_key][0]
            except IndexError:
                print(f"تحذير: مفتاح التبويب الافتراضي '{default_tab_key}' غير موجود في navbar_items. سيتم استخدام المؤشر 0.")

            # العثور على مؤشر "واجهة البرنامج" في content_area
            # هذا يفترض أن ترتيب النوافذ في content_area يطابق ترتيبها في navbar_items (باستثناء الخروج)
            # وهو ما يتحقق بواسطة _create_windows

            # نحتاج إلى إيجاد المؤشر الصحيح في content_area إذا كان ترتيب navbar_items يتضمن "logout_action"
            # الذي ليس له ويدجت في content_area.
            # الطريقة الأسهل هي استخدام مفتاح "program_interface" مباشرة للعثور على الويدجت.
            if "program_interface" in self.windows:
                program_interface_widget = self.windows["program_interface"]
                content_area_interface_index = self.content_area.indexOf(program_interface_widget)
                if content_area_interface_index != -1:
                    self.content_area.setCurrentIndex(content_area_interface_index)
                    # تعيين التبويب المقابل في QTabWidget
                    navbar_interface_index = self.navbar_buttons["program_interface"]["tab_index"]
                    self.tabWidget.setCurrentIndex(navbar_interface_index)
                    self.previous_tab_index = navbar_interface_index
                    print(f"INFO: تم تحديد نافذة '{self.navbar_items[navbar_interface_index][0]}' كنافذة افتراضية.")
                else:
                    print(f"WARNING: لم يتم العثور على ويدجت 'program_interface' في content_area.")
                    if self.tabWidget.count() > 0:  # fallback to first tab if program_interface not found
                        self.tabWidget.setCurrentIndex(0)
                        self.previous_tab_index = 0
                        if self.content_area.count() > 0:
                            self.content_area.setCurrentIndex(0)

            else: # fallback if "program_interface" window doesn't exist
                print(f"WARNING: لم يتم العثور على نافذة 'program_interface'.")
                if self.tabWidget.count() > 0 and self.content_area.count() > 0:
                    self.tabWidget.setCurrentIndex(0)
                    self.content_area.setCurrentIndex(0)
                    self.previous_tab_index = 0


        else: print("تحذير: لا توجد نوافذ في content_area.")

        # عرض النافذة فقط إذا كان مطلوباً (للاختبار المباشر لملف main_window.py)
        if auto_show:
            self.showMaximized()
            print("INFO: تم عرض النافذة الرئيسية (Maximized).")
        else:
            print("INFO: تم إنشاء النافذة الرئيسية بدون عرضها تلقائياً (auto_show=False).")

        # إضافة متغير لتخزين مثيل taheri3.MainWindow مؤقتًا
        self.taheri3_instance = None

        # إضافة متغير لتتبع آخر تحديث لجدول_عام
        self.last_table_update_time = 0

        # إضافة ملصق لعرض حالة التحميل
        self.loading_label = QLabel("جاري التحميل...", self)
        self.loading_label.setAlignment(Qt.AlignCenter)
        self.loading_label.setStyleSheet("background-color: rgba(0, 0, 0, 150); color: white; font-size: 16pt; padding: 20px; border-radius: 10px;")
        self.loading_label.hide()

        # تحديد رقم إصدار البرنامج الحالي
        self.app_version = "1.0.0"

        # التحقق من وجود تحديثات عند بدء البرنامج (اختياري)
        QTimer.singleShot(3000, self.check_for_updates)

    def setup_logging(self):
        """إعداد نظام التسجيل لتقليل الرسائل المكررة"""
        self.logged_messages = set()
        
    def log_once(self, message, level="INFO"):
        """طباعة الرسالة مرة واحدة فقط"""
        if message not in self.logged_messages:
            self.logged_messages.add(message)
            if VERBOSE_MODE:
                print(f"{level}: {message}")

    def check_for_updates(self):
        """التحقق من وجود تحديثات للبرنامج"""
        try:
            update_url = "https://your-website.com/updates.json"
            # أو يمكنك استخدام رابط مباشر لملف JSON في GitHub أو Google Drive

            # تعريف UpdateChecker إذا لم يكن معرفاً
            class UpdateChecker:
                def __init__(self, app_version, update_url):
                    self.app_version = app_version
                    self.update_url = update_url

                def check_for_updates(self):
                    # من أجل التصحيح فقط: دائماً لا يوجد تحديث
                    return False, {}

                def prompt_for_update(self, update_info):
                    return False

                def download_and_install_update(self, update_info):
                    pass

            update_checker = UpdateChecker(self.app_version, update_url)
            update_available, update_info = update_checker.check_for_updates()

            if update_available:
                if update_checker.prompt_for_update(update_info):
                    update_checker.download_and_install_update(update_info)
        except Exception as e:
            print(f"خطأ أثناء التحقق من التحديثات: {e}")
            # يمكنك التعامل مع الأخطاء بصمت للحفاظ على تجربة المستخدم

    # يمكنك أيضاً إضافة خيار في القائمة للتحقق من التحديثات يدوياً
    def add_update_action_to_menu(self):
        check_updates_action = self.menuHelp.addAction("التحقق من التحديثات")
        check_updates_action.triggered.connect(self.check_for_updates)

    def setup_window_connections(self):
        """إعداد الاتصالات بين النوافذ"""
        # ربط إشارات تحديث البيانات
        if "violations" in self.windows:
            violations_window = self.windows["violations"]
            violations_window.load_data_signal = lambda: self.refresh_all_windows()

        # ربط إشارة تحديث البيانات من sub2_window إلى sub3_window و sub4_window
        if hasattr(self, 'sub2_window_instance') and hasattr(self.sub2_window_instance, 'data_updated_signal'):
            # ربط الإشارة بدالة تحديث النوافذ
            self.sub2_window_instance.data_updated_signal.connect(self.refresh_windows_on_data_update)
            print("تم ربط إشارة تحديث البيانات من نافذة بيانات المؤسسة إلى النوافذ الأخرى")

    def refresh_all_windows(self):
        """تحديث جميع النوافذ التي تحتوي على دالة تحديث"""
        for window_key, window in self.windows.items():
            if hasattr(window, 'load_data'):
                try:
                    window.load_data()
                    print(f"تم تحديث نافذة {window_key}")
                except Exception as e:
                    print(f"خطأ في تحديث نافذة {window_key}: {e}")

    def refresh_windows_on_data_update(self):
        """تحديث النوافذ المحددة عند تحديث البيانات في نافذة بيانات المؤسسة"""
        print("جاري تحديث النوافذ بعد تحديث بيانات المؤسسة...")

        # تحديث نافذة البنية التربوية (sub3_window)
        if "structure" in self.windows:
            structure_window = self.windows["structure"]
            if hasattr(structure_window, 'refresh_data'):
                try:
                    structure_window.refresh_data()
                    print("تم تحديث نافذة البنية التربوية")
                except Exception as e:
                    print(f"خطأ في تحديث نافذة البنية التربوية: {e}")

        # تحديث نافذة اللوائح والأقسام (sub4_window)
        if "regulations" in self.windows:
            regulations_window = self.windows["regulations"]
            if hasattr(regulations_window, 'refresh_data'):
                try:
                    regulations_window.refresh_data()
                    print("تم تحديث نافذة اللوائح والأقسام")
                except Exception as e:
                    print(f"خطأ في تحديث نافذة اللوائح والأقسام: {e}")

    def update_main_table_and_show_window(self, window_widget, window_key):
        """تحديث جدول_عام ثم عرض النافذة المحددة مع تحسين الأداء"""
        try:
            # عرض مؤشر التحميل
            self.show_loading_indicator()

            # استدعاء دالة تحديث جدول_عام من taheri3.py بدون عرض أي رسائل
            try:
                import time
                current_time = time.time()

                # تحقق مما إذا كان قد مر وقت كافٍ منذ آخر تحديث (30 ثانية مثلاً)
                if current_time - self.last_table_update_time > 30:
                    # استيراد taheri3.py مرة واحدة وإعادة استخدام المثيل إذا كان موجودًا
                    if self.taheri3_instance is None:
                        import importlib.util
                        spec = importlib.util.spec_from_file_location("taheri3", "taheri3.py")
                        taheri3 = importlib.util.module_from_spec(spec)
                        spec.loader.exec_module(taheri3)

                        # إنشاء نسخة من MainWindow من taheri3.py وحفظها للاستخدام اللاحق
                        self.taheri3_instance = taheri3.MainWindow()

                    # استدعاء دالة تحديث جدول_عام
                    QApplication.processEvents()  # السماح بتحديث واجهة المستخدم قبل العملية الثقيلة
                    self.taheri3_instance.update_main_table()
                    self.last_table_update_time = current_time
                    print("INFO: تم تحديث جدول_عام")
                else:
                    print("INFO: تجاوز تحديث جدول_عام لأنه تم تحديثه مؤخرًا")

            except Exception as e:
                # طباعة الخطأ في وحدة التحكم فقط بدون عرض رسالة للمستخدم
                print(f"خطأ في تحديث جدول_عام: {e}")

            # إخفاء مؤشر التحميل وعرض النافذة المحددة
            self.hide_loading_indicator()
            self.show_window(window_widget, window_key) # <--- تمرير window_key هنا

        except Exception as e:
            print(f"خطأ في تحديث جدول_عام وعرض النافذة: {e}")
            # إخفاء مؤشر التحميل في حالة حدوث خطأ
            self.hide_loading_indicator()
            # عرض النافذة المحددة في حالة حدوث خطأ
            self.show_window(window_widget, window_key) # <--- وتمرير window_key هنا أيضًا

    def show_loading_indicator(self):
        """عرض مؤشر التحميل"""
        # تحديث حجم وموقع مؤشر التحميل
        self.loading_label.resize(200, 80)
        center_point = self.rect().center()
        self.loading_label.move(center_point.x() - 100, center_point.y() - 40)
        self.loading_label.raise_()
        self.loading_label.show()
        QApplication.processEvents()  # تحديث واجهة المستخدم فورًا

    def hide_loading_indicator(self):
        """إخفاء مؤشر التحميل"""
        self.loading_label.hide()
        QApplication.processEvents()  # تحديث واجهة المستخدم فورًا

    def _create_top_navbar(self):
        """إنشاء شريط التبويب العلوي المميز"""
        self.navbar_frame = QFrame()
        self.navbar_frame.setObjectName("NavBarFrame")
        
        # تنظيف CSS من الخصائص غير المدعومة
        navbar_style = """
            QFrame#NavBarFrame {
                background-color: #00382E;
                border-bottom: 1px solid #00382E;
                height: 80px;
                min-height: 80px;
                max-height: 80px;
                margin: 0;
                padding: 5;
            }
        """
        self.navbar_frame.setStyleSheet(navbar_style)

        navbar_layout = QVBoxLayout(self.navbar_frame) # استخدام QVBoxLayout لوضع التبويب
        navbar_layout.setContentsMargins(0, 0, 0, 0) # إزالة الهوامش
        navbar_layout.setSpacing(0) # إزالة المسافات بين العناصر

        # استخدام المكون الجديد TwoRowTabComponent
        self.tabWidget = TwoRowTabComponent()
        self.tabWidget.setObjectName("MainTabBar")

        # تعيين أنماط CSS منظفة للمكون الجديد
        clean_tab_style = """
            QTabBar {
                background-color: transparent;
            }

            QTabBar::tab {
                background-color: #B22222;
                color: #FFFFFF;
                font-family: 'Calibri';
                font-size: 11pt;
                font-weight: bold;
                min-width: 120px;
                height: 30px;
                padding: 1px 5px;
                margin: 1px 1px;
                border-top-left-radius: 6px;
                border-top-right-radius: 6px;
                text-align: center;
            }

            QTabBar::tab:selected {
                background-color: #8B0000;
                color: white;
                border-bottom: 3px solid #CD5C5C;
            }

            QTabBar::tab:hover:!selected {
                background-color: #DC143C;
                color: white;
            }

            QTabBar::tab:disabled {
                color: #A9A9A9;
                background-color: #D3D3D3;
            }

            QTabBar#BottomTabBar::tab:last {
                background-color: #C62828;
                color: white;
            }
            QTabBar#BottomTabBar::tab:last:selected {
                background-color: #A71A1A;
            }
            QTabBar#BottomTabBar::tab:last:hover {
                background-color: #B71C1C;
            }
        """
        
        self.tabWidget.setStyleSheet(clean_tab_style)

        # تكوين التبويبات
        self.navbar_items = [
            ("تهيئة البرنامج", "setup"),
            ("البنية التربوية", "structure"),
            ("بيانات المؤسسة", "school_info"),  # إضافة تبويب بيانات المؤسسة
            ("اللوائح والأقسام", "regulations"),
            ("نافذة بحث", "search"),
            ("مسك الغياب", "student_card"),
            ("شواهد مدرسية", "violations"),
            ("إخبار بنشاط", "absence"),
            ("طباعة اللوائح", "statistics"),
            ("أوراق الفروض", "create_absence_table"),
            # تبويبات الصف الثاني
            ("أوراق التنقيط", "scoring_sheets"),  # تبويب أوراق التنقيط في الصف الثاني
            ("تهيئة الامتحانات", "exam_setup"),
            ("واجهة البرنامج", "program_interface"),
            ("طلبات الملفات وإرسالها", "file_requests"),  # التبويب الجديد
            ("تسجيل الخروج", "logout_action") # تبويب الخروج
        ]

        self.tab_pages = {}
        self.navbar_buttons = {}

        for index, (text, window_key) in enumerate(self.navbar_items): # استخدام index بدلاً من _
            tab_page = QWidget()
            tab_page.setObjectName(f"TabPage_{window_key}")
            self.tab_pages[window_key] = tab_page

            # إضافة الصفحة والتبويب
            tab_index = self.tabWidget.addTab(tab_page, text)

            # تخزين البيانات المرتبطة بالتبويب
            self.tabWidget.setTabData(tab_index, window_key)
            self.navbar_buttons[window_key] = {"tab_index": tab_index}

        # إضافة المكون إلى التخطيط الرئيسي للإطار
        navbar_layout.addWidget(self.tabWidget)

        # ربط إشارة تغيير التبويب
        self.tabWidget.connectCurrentChanged(self._on_tab_data_changed)

    def _on_tab_data_changed(self, index, window_key):
        """معالجة حدث تغيير التبويب استنادًا إلى البيانات المخزنة"""
        if window_key == "logout_action":
            # منع تبويب الخروج من البقاء نشطًا أثناء عرض مربع الحوار
            self.tabWidget.blockSignals(True)
            self.tabWidget.setCurrentIndex(self.previous_tab_index)
            self.tabWidget.blockSignals(False)

            should_quit = self.show_logout_confirmation_dialog()
            if should_quit:
                print("INFO: طلب إغلاق التطبيق عبر تبويب الخروج...")
                QCoreApplication.instance().quit()

        elif window_key in self.windows:
            window_widget = self.windows[window_key]

            if window_key == "statistics":
                # عرض نافذة طباعة اللوائح في منطقة المحتوى الرئيسية مع إعداد خاص
                try:
                    print("عرض نافذة طباعة اللوائح المدمجة...")
                    
                    # عرض النافذة أولاً
                    self.show_window(window_widget, window_key)
                    
                    # تطبيق إعدادات خاصة للنافذة المدمجة
                    QTimer.singleShot(300, lambda: self.refresh_embedded_print_window(window_widget))
                    
                except Exception as e:
                    print(f"خطأ في عرض نافذة طباعة اللوائح: {e}")
                    # في حالة الخطأ، استخدم الطريقة القديمة
                    QTimer.singleShot(50, lambda: self.update_main_table_and_show_window(window_widget, window_key))
                    
            elif window_key == "sections_card":
                # فتح نافذة بطاقة الأقسام كنافذة مستقلة
                try:
                    print("فتح نافذة بطاقة الأقسام كنافذة مستقلة...")
                    sections_window = RegulationsCardWindow(
                        parent=None,
                        db=self.db_connection,
                        section="1/1",
                        academic_year=self.current_academic_year
                    )
                    sections_window.show()
                except Exception as e:
                    print(f"خطأ في فتح نافذة بطاقة الأقسام كنافذة مستقلة: {e}")
                    self.show_window(window_widget, window_key)
            else:
                self.show_window(window_widget, window_key)

            self.previous_tab_index = index
            debug_print(f"تم تبديل التبويب إلى '{window_key}' (Index: {index})")
        else:
            print(f"تحذير: تم تغيير التبويب إلى مؤشر {index} بمفتاح غير معالج: {window_key}")
            self.tabWidget.blockSignals(True)
            self.tabWidget.setCurrentIndex(self.previous_tab_index)
            self.tabWidget.blockSignals(False)

    def refresh_embedded_print_window(self, print_window):
        """تحديث خاص للنافذة المدمجة عند عرضها"""
        try:
            print("تحديث النافذة المدمجة...")
            
            # التأكد من أن النافذة مرئية ونشطة
            if print_window and print_window.isVisible():
                # تحديث البيانات
                if hasattr(print_window, 'refresh_data'):
                    print_window.refresh_data()
                elif hasattr(print_window, 'load_data'):
                    print_window.load_data()
                
                # إعادة تطبيق الإعدادات الافتراضية
                self.setup_embedded_print_window(print_window)
                
                print("تم تحديث النافذة المدمجة بنجاح")
            else:
                print("تحذير: النافذة غير مرئية، تخطي التحديث")
                
        except Exception as e:
            print(f"خطأ في تحديث النافذة المدمجة: {e}")

    def _link_navbar_buttons(self):
        """تعديل ربط أزرار شريط التنقل لتعمل مع نظام التبويب الجديد"""
        for window_key, tab_data in self.navbar_buttons.items():
            tab_index = tab_data["tab_index"]

            if window_key == "logout_action":
                self.tabWidget.setTabEnabled(tab_index, True)
                self.tabWidget.setTabToolTip(tab_index, "تسجيل الخروج من البرنامج")
            elif window_key not in self.windows:
                self.tabWidget.setTabEnabled(tab_index, False)
                self.tabWidget.setTabToolTip(tab_index, "الوحدة غير متوفرة")
                print(f"تحذير: تم تعطيل التبويب '{window_key}' لعدم توفر النافذة المقابلة.")
            # التبويبات الأخرى تكون مفعلة بشكل افتراضي إذا كانت النافذة موجودة

    def show_window(self, window_widget, window_key_debug=None): # <--- إضافة معامل جديد هنا
        """عرض الويدجت (النافذة الفرعية) المحدد في منطقة المحتوى."""
        if window_key_debug == "create_absence_table": # --- إضافة تشخيص هنا ---
            print(f"DEBUG: show_window called for 'create_absence_table'.")
            print(f"DEBUG:   window_widget: {window_widget}")
        # --- نهاية التشخيص ---

        if window_widget and isinstance(window_widget, QWidget):
            current_index_in_stack = self.content_area.indexOf(window_widget)
            if window_key_debug == "create_absence_table": # --- إضافة تشخيص هنا ---
                print(f"DEBUG:   Index of widget in QStackedWidget: {current_index_in_stack}")
            # --- نهاية التشخيص ---

            if (current_index_in_stack != -1):
                self.content_area.setCurrentIndex(current_index_in_stack)
                if window_key_debug == "create_absence_table": # --- إضافة تشخيص هنا ---
                    print(f"DEBUG:   QStackedWidget current index set to {current_index_in_stack}.")
                # --- نهاية التشخيص ---

                # تحديد علامة التبويب المقابلة
                for window_key, window in self.windows.items():
                    if window == window_widget and window_key in self.navbar_buttons:
                        tab_index = self.navbar_buttons[window_key]["tab_index"]
                        # تعطيل إشارة currentChanged للحظة لتجنب التكرار إذا كان التغيير برمجيًا
                        self.tabWidget.blockSignals(True)
                        self.tabWidget.setCurrentIndex(tab_index)
                        self.tabWidget.blockSignals(False)
                        if window_key_debug == "create_absence_table": # --- إضافة تشخيص هنا ---
                            print(f"DEBUG:   QTabWidget current index synced to {tab_index}.")
                        # --- نهاية التشخيص ---
                        break
        elif window_key_debug == "create_absence_table": # --- إضافة تشخيص هنا ---
            print(f"DEBUG: show_window called for 'create_absence_table', but window_widget is None or not a QWidget.")
        # --- نهاية التشخيص ---


    def _setup_content_area(self):
        """إعداد منطقة عرض المحتوى الرئيسية"""
        self.content_area = QStackedWidget()
        self.content_area.setObjectName("ContentArea")
        self.content_area.setStyleSheet("QWidget#ContentArea { background-color: #ECEFF1; margin-top: 0; }")
        self.content_area.setContentsMargins(0, 0, 0, 0)


    def _create_windows(self):
        """إنشاء جميع النوافذ الفرعية وإضافتها إلى منطقة المحتوى"""
        self.windows = {}

        # تقليل رسائل التشخيص
        self.log_once("بدء إنشاء النوافذ الفرعية")

        # معاملات مشتركة يتم تمريرها لجميع النوافذ الفرعية التي قد تحتاجها
        common_args = {
            "db": self.db_connection,
            "academic_year": self.current_academic_year,
            "parent": self # تمرير النافذة الرئيسية كأب
        }

        # إنشاء نافذة بيانات المؤسسة (sub2_window) وتخزينها كمتغير عام
        try:
            self.sub2_window_instance = sub2_window.SubWindow(parent=self)
            # إضافة النافذة إلى content_area مباشرة
            self.content_area.addWidget(self.sub2_window_instance)
            # تخزين النافذة في قاموس windows
            self.windows["school_info"] = self.sub2_window_instance
            # ربط إشارة تحديث البيانات بدالة تحديث جميع النوافذ
            self.sub2_window_instance.data_updated_signal.connect(self.refresh_all_windows)
            print("تم إنشاء نافذة بيانات المؤسسة بنجاح وإضافتها إلى content_area")
        except Exception as e:
            print(f"خطأ في إنشاء نافذة بيانات المؤسسة: {e}")
            self.sub2_window_instance = None

        # تعريف إعدادات النوافذ (تأكد من تطابق المفاتيح مع navbar_items)
        windows_config = {
            "setup": {"class": Sub0Window, "available": SUB0_AVAILABLE, "title": "تهيئة البرنامج", "module": "sub0_window.py", "pass_args": False}, # نفترض أنها لا تحتاج args
            "structure": {"class": Sub3Window, "available": SUB3_AVAILABLE, "title": "البنية التربوية", "module": "sub3_window.py", "pass_args": False}, # نفترض أنها لا تحتاج args
            "school_info": {
                "class": sub2_window.SubWindow,
                "available": True,
                "title": "بيانات المؤسسة",
                "module": "sub2_window.py",
                "pass_args": False,
                "custom_init": lambda: self.sub2_window_instance  # استخدام المثيل الذي تم إنشاؤه مسبقاً
            },
            "scoring_sheets": {
                "class": ScoringSheetWindow,
                "available": SCORING_SHEET_AVAILABLE,
                "title": "أوراق التنقيط",
                "module": "sub37_window.py",
                "pass_args": True,
                "custom_init": lambda: ScoringSheetWindow(
                    parent=self
                ),
                "size": {"width": 900, "height": 700}
            },
            "regulations": {"class": EnhancedSimpleSearchWindow, "available": SIMPLE_SEARCH_AVAILABLE, "title": "اللوائح والأقسام", "module": "sub4_window.py", "pass_args": True}, # <-- استخدام الفئة المُحسّنة بدلاً من Sub4Window
            "search": {
                "class": Simple6Window,              # <-- الكلاس الجديد
                "available": SIMPLE6_AVAILABLE,      # <-- العلم الجديد الخاص بتوفره
                "title": "نافذة البحث",
                "module": "simple6_window.py",       # <-- اسم الملف الجديد
                "pass_args": True                    # <-- تحتاج المعاملات (db, academic_year, parent)
            },
            "student_card": {
                "class": Hirasa300Window,
                "available": HIRASA300_AVAILABLE,
                "title": "مسك الغياب ومعالجته",
                "module": "sub9_window.py",
                "pass_args": True,
                "custom_init": lambda: Hirasa300Window(
                    db=self.db_connection,
                    academic_year=self.current_academic_year,
                    parent=self
                )
            },
            "violations": {
                "class": SchoolCertificateWindow,
                "available": SCHOOL_CERTIFICATE_AVAILABLE,
                "title": "معالجة طلبات الشهادات المدرسية",
                "module": "sub19_window.py",
                "pass_args": True,
                "custom_init": lambda: SchoolCertificateWindow(
                    db=self.db_connection,
                    parent=self
                )
            },
            "absence": {
                "class": NewsWindow,              # تغيير من AbsenceManagementWindow إلى NewsWindow
                "available": NEWS_WINDOW_AVAILABLE,
                "title": "إخبار بنشاط",
                "module": "sub18_window.py",
                "pass_args": True,
                "custom_init": lambda: NewsWindow(
                    db=self.db_connection,
                    parent=self
                ),
                 "size": {"width": 800, "height": 600}
            },
            "statistics": {
                "class": PrintListsWindow,
                "available": PRINT_LISTS_WINDOW_AVAILABLE,
                "title": "الإحصائيات وطباعة اللوائح",
                "module": "sub20_window.py",
                "pass_args": True,
                "custom_init": lambda: self.create_enhanced_print_lists_window(),
                "size": {"width": 700, "height": 650},
                "fixed_size": True  # إضافة علامة للإشارة إلى أن هذه النافذة يجب أن تحتفظ بحجمها الثابت
            },
            "create_absence_table": {
                "class": CreateAbsenceTableWindow,
                "available": CREATE_ABSENCE_TABLE_AVAILABLE,
                "title": "مسك أوراق الفروض",
                "module": "sub21_window.py",
                "pass_args": True,
                "custom_init": lambda: CreateAbsenceTableWindow(
                    parent=self
                ),
                "size": {"width": 900, "height": 700},
                "fixed_size": True
            },
            "exam_setup": {
                "class": ExamSetupWindow,
                "available": EXAM_SETUP_AVAILABLE,
                "title": "تهيئة الامتحانات",
                "module": "sub22_window.py",
                "pass_args": True,
                "custom_init": lambda: ExamSetupWindow(
                    db=self.db_connection,
                    academic_year=self.current_academic_year,
                    parent=self
                ),
                "size": {"width": 900, "height": 700},
                "fixed_size": True
            },
            "program_interface": {
                "class": Sub01Window,  # استخدام Sub01Window بدلاً من PlaceholderWindow
                "available": SUB01_AVAILABLE,  # استخدام متغير التحقق من توفر Sub01Window
                "title": "واجهة البرنامج",
                "module": "sub01_window.py",
                "pass_args": True,
                "custom_init": lambda: Sub01Window(
                    parent=self  # إزالة معاملة db لأن Sub01Window لا يقبلها
                ) if SUB01_AVAILABLE else PlaceholderWindow(
                    title="واجهة البرنامج",
                    message="محتوى واجهة البرنامج غير متوفر\nالملف 'sub01_window.py' غير موجود",
                    db=self.db_connection,
                    parent=self
                ),
                "size": {"width": 1200, "height": 650}  # تعديل الحجم ليناسب النافذة الجديدة
            },
            "file_requests": {
                "class": PlaceholderWindow,  # استخدام PlaceholderWindow مؤقتاً
                "available": True,  # متوفر دائماً لأنه PlaceholderWindow
                "title": "طلبات الملفات وإرسالها",
                "module": "file_requests_window.py",  # ملف مستقبلي
                "pass_args": True,
                "custom_init": lambda: PlaceholderWindow(
                    title="طلبات الملفات وإرسالها",
                    message="نافذة طلبات الملفات وإرسالها\n\nهذه النافذة قيد التطوير\nستتيح لك:\n• إنشاء طلبات ملفات جديدة\n• إرسال الملفات عبر البريد الإلكتروني\n• متابعة حالة الطلبات\n• أرشفة الملفات المرسلة",
                    db=self.db_connection,
                    parent=self
                ),
                "size": {"width": 1000, "height": 700}
            },
            "sections_card": {
                "class": RegulationsCardWindow,
                "available": SECTIONS_CARD_AVAILABLE,
                "title": "بطاقة الأقسام",
                "module": "sub20_window.py",
                "pass_args": True,
                "custom_init": lambda: RegulationsCardWindow(
                    parent=None,  # تعيين parent=None لجعلها نافذة مستقلة
                    db=self.db_connection,
                    section="1/1",  # قسم افتراضي
                    academic_year=self.current_academic_year
                ),
                "size": {"width": 800, "height": 600},
                "fixed_size": True
            },
        }

        # إنشاء النوافذ بالترتيب المحدد في navbar_items
        for _, key in self.navbar_items:
            if key == "logout_action": # تخطي مفتاح الخروج لأنه لا يمثل نافذة
                continue

            if key in windows_config:
                config = windows_config[key]
                window_class = config["class"]
                is_available = config["available"]
                title = config["title"]
                module_name = config["module"]
                should_pass_args = config["pass_args"]

                window_instance = None

                if is_available and window_class is not None:
                    try:
                        if "custom_init" in config:
                            try:
                                window_instance = config["custom_init"]()
                                if "size" in config:
                                    window_instance.resize(
                                        config["size"]["width"],
                                        config["size"]["height"]
                                    )
                                self.log_once(f"تم إنشاء '{key}' باستخدام custom_init")
                            except Exception as e:
                                debug_print(f"خطأ في إنشاء النافذة '{key}' باستخدام custom_init: {e}")
                                window_instance = None
                        elif should_pass_args:
                            window_instance = window_class(**common_args)
                            self.log_once(f"تم إنشاء '{key}' ({window_class.__name__}) مع تمرير المعاملات")
                        else:
                            try:
                                window_instance = window_class(parent=self)
                            except TypeError:
                                window_instance = window_class()
                            self.log_once(f"تم إنشاء '{key}' ({window_class.__name__}) بدون تمرير المعاملات المشتركة")

                        self.windows[key] = window_instance
                        self.content_area.addWidget(window_instance)

                    except Exception as e:
                        debug_print(f"خطأ فادح عند إنشاء النافذة '{key}' من {module_name}: {e}", force=True)
                        message = f"خطأ في إنشاء الوحدة\n({module_name})\n{e}"
                        window_instance = PlaceholderWindow(title=title, message=message, **common_args)
                        self.windows[key] = window_instance
                        self.content_area.addWidget(window_instance)
                        
                        if key in self.navbar_buttons and "tab_index" in self.navbar_buttons[key]:
                            tab_idx = self.navbar_buttons[key]["tab_index"]
                            self.tabWidget.setTabEnabled(tab_idx, False)
                            self.tabWidget.setTabToolTip(tab_idx, f"خطأ في تحميل الوحدة: {e}")
                else:
                    message = f"الوحدة غير متوفرة ({module_name})"
                    window_instance = PlaceholderWindow(title=title, message=message, **common_args)
                    self.windows[key] = window_instance
                    self.content_area.addWidget(window_instance)
                    self.log_once(f"تم إضافة PlaceholderWindow للمفتاح '{key}' (الوحدة غير متوفرة)")
                    
                    if key in self.navbar_buttons and "tab_index" in self.navbar_buttons[key]:
                        tab_idx = self.navbar_buttons[key]["tab_index"]
                        self.tabWidget.setTabEnabled(tab_idx, False)
                        self.tabWidget.setTabToolTip(tab_idx, "الوحدة غير متوفرة")
            else:
                debug_print(f"WARNING: المفتاح '{key}' من navbar_items غير موجود في windows_config.")


    def create_automatic_backup(self):
        """عمل نسخة احتياطية تلقائية عند إغلاق البرنامج"""
        try:
            import os
            import datetime
            import zipfile
            import sqlite3

            desktop_path = os.path.join(os.path.expanduser("~"), "Desktop")
            main_folder = os.path.join(desktop_path, "تقارير برنامج المعين في الحراسة العامة")
            backup_folder = os.path.join(main_folder, "النسخ الاحتياطي للبرنامج")

            if not os.path.exists(main_folder):
                os.makedirs(main_folder)
            if not os.path.exists(backup_folder):
                os.makedirs(backup_folder)

            current_date = datetime.datetime.now().strftime("%Y-%m-%d")
            backup_name = f"نسخة_احتياطية_تلقائية_{current_date}"
            backup_sqlite = os.path.join(backup_folder, f"{backup_name}.sqlite")
            backup_zip = os.path.join(backup_folder, f"{backup_name}.zip")

            if os.path.exists(backup_zip):
                debug_print(f"النسخة الاحتياطية التلقائية لهذا اليوم موجودة بالفعل")
                return

            # 3. إصلاح وضغط قاعدة البيانات
            db_path = "data.db"  # مسار قاعدة البيانات الحالية

            # 3.1 فتح اتصال بقاعدة البيانات الأصلية
            conn = sqlite3.connect(db_path)

            # 3.2 إصلاح قاعدة البيانات
            conn.execute("PRAGMA integrity_check")  # التحقق من سلامة قاعدة البيانات
            conn.execute("VACUUM")  # تنظيف وضغط قاعدة البيانات

            # 3.3 إنشاء نسخة احتياطية مؤقتة
            backup_conn = sqlite3.connect(backup_sqlite)
            conn.backup(backup_conn)

            # 3.4 إغلاق الاتصال بقواعد البيانات
            backup_conn.close()
            conn.close()

            # 4. ضغط ملف النسخة الاحتياطية
            with zipfile.ZipFile(backup_zip, 'w', zipfile.ZIP_DEFLATED) as zipf:
                zipf.write(backup_sqlite, os.path.basename(backup_sqlite))

            # 5. حذف الملف المؤقت بعد إنشاء ملف الضغط
            os.remove(backup_sqlite)

            # 6. حذف النسخ الاحتياطية القديمة (الاحتفاظ بآخر 100 نسخة فقط)
            self.cleanup_old_backups(backup_folder, 100)

            debug_print(f"تم عمل نسخة احتياطية تلقائية بنجاح: {backup_zip}")

        except Exception as e:
            debug_print(f"خطأ في عمل نسخة احتياطية تلقائية: {e}")

    def cleanup_old_backups(self, backup_folder, max_backups=100):
        """حذف النسخ الاحتياطية القديمة والاحتفاظ بعدد محدد فقط"""
        try:
            import os

            # الحصول على قائمة ملفات النسخ الاحتياطية
            backup_files = []
            for file in os.listdir(backup_folder):
                if file.startswith("نسخة_احتياطية_تلقائية_") and file.endswith(".zip"):
                    file_path = os.path.join(backup_folder, file)
                    backup_files.append((file_path, os.path.getmtime(file_path)))

            # ترتيب الملفات حسب تاريخ التعديل (الأحدث أولاً)
            backup_files.sort(key=lambda x: x[1], reverse=True)

            # حذف الملفات القديمة إذا تجاوز العدد الحد الأقصى
            if len(backup_files) > max_backups:
                for file_path, _ in backup_files[max_backups:]:
                    os.remove(file_path)
                    print(f"تم حذف نسخة احتياطية قديمة: {file_path}")

        except Exception as e:
            print(f"خطأ في تنظيف النسخ الاحتياطية القديمة: {e}")

    def create_db_connection(self):
        """دالة مخصصة لإنشاء اتصال بقاعدة البيانات في المجلد الجديد"""
        try:
            # استخدام المسار الجديد لقاعدة البيانات
            db_path = os.path.join(self.db_folder, "data.db")

            # إذا لم تكن قاعدة البيانات موجودة ولكن هناك نسخة في مجلد البرنامج، قم بنسخها
            if not os.path.exists(db_path):
                original_db = os.path.join(os.path.dirname(__file__), "data.db")
                if os.path.exists(original_db):
                    import shutil
                    shutil.copy2(original_db, db_path)
                    print(f"INFO: تم نسخ قاعدة البيانات من المجلد الأصلي إلى: {db_path}")

            # استدعاء دالة الاتصال الأصلية بدلاً من إنشاء اتصال مباشر بـ sqlite3
            return create_connection()
        except Exception as e:
            return False, None, str(e)

    def refresh_all_windows(self):
        """تحديث جميع النوافذ في البرنامج"""
        try:
            print("بدء تحديث جميع النوافذ في البرنامج...")

            # تحديث السنة الدراسية الحالية باستخدام الدالة الموحدة
            unified_academic_year = self.get_unified_academic_year()
            if unified_academic_year:
                self.current_academic_year = unified_academic_year
                print(f"🔄 تم تحديث السنة الدراسية الموحدة: {unified_academic_year}")
            
            # تحديث السنة الدراسية من sub4_window أيضاً للمقارنة
            if self.db_connection:
                year, error = get_current_academic_year(self.db_connection)
                if year:
                    print(f"📊 السنة الدراسية من sub4_window: {year}")
                    if year != unified_academic_year:
                        print(f"⚠️ تضارب في السنة الدراسية! الموحدة: {unified_academic_year}, من sub4_window: {year}")
                else:
                    print(f"خطأ في قراءة السنة الدراسية من sub4_window: {error}")

            # تحديث جميع النوافذ
            updated_count = 0
            for key, window in self.windows.items():
                if window:
                    try:
                        # التحقق من وجود دالة refresh_data
                        if hasattr(window, 'refresh_data') and callable(getattr(window, 'refresh_data')):
                            window.refresh_data()
                            updated_count += 1
                            print(f"تم تحديث النافذة: {key}")
                        # التحقق من وجود دالة load_data كبديل
                        elif hasattr(window, 'load_data') and callable(getattr(window, 'load_data')):
                            window.load_data()
                            updated_count += 1
                            print(f"تم تحديث النافذة: {key} باستخدام load_data")
                        # التحقق من وجود دالة update_data كبديل آخر
                        elif hasattr(window, 'update_data') and callable(getattr(window, 'update_data')):
                            window.update_data()
                            updated_count += 1
                            print(f"تم تحديث النافذة: {key} باستخدام update_data")
                    except Exception as e:
                        print(f"خطأ في تحديث النافذة {key}: {str(e)}")

            print(f"تم تحديث {updated_count} نافذة بنجاح")

            # عرض رسالة نجاح
            QMessageBox.information(self, "تم بنجاح", f"✅ تم تحديث {updated_count} نافذة بنجاح\n📅 السنة الدراسية الموحدة: {unified_academic_year}")

        except Exception as e:
            print(f"خطأ في تحديث جميع النوافذ: {str(e)}")
            QMessageBox.warning(self, "خطأ", f"حدث خطأ أثناء تحديث النوافذ:\n{str(e)}")

    def get_customized_academic_year(self, db_conn):
        """نسخة متوافقة من دالة get_current_academic_year تعمل مع اتصال sqlite3.Connection"""
        try:
            if not db_conn:
                return None, "قاعدة البيانات غير متصلة"

            cursor = db_conn.cursor()
            cursor.execute("SELECT السنة_الدراسية FROM بيانات_المؤسسة LIMIT 1")
            result = cursor.fetchone()

            if result and result[0]:
                return result[0], None
            else:
                return None, "لم يتم العثور على السنة الدراسية في قاعدة البيانات"
        except Exception as e:
            return None, f"خطأ في قراءة السنة الدراسية: {str(e)}"

    def show_logout_confirmation_dialog(self):
        """عرض رسالة تأكيد الخروج بالنمط المميز"""
        try:
            # إنشاء نافذة حوار مخصصة
            confirm_dialog = QDialog(self)
            confirm_dialog.setWindowTitle("تأكيد الخروج")
            confirm_dialog.setFixedSize(500, 280)
            confirm_dialog.setLayoutDirection(Qt.RightToLeft)

            # إضافة أيقونة البرنامج
            try:
                app_icon = QIcon("01.ico")
                confirm_dialog.setWindowIcon(app_icon)
            except Exception as e:
                print(f"خطأ في تحميل أيقونة البرنامج: {e}")

            # تنسيق النافذة
            confirm_dialog.setStyleSheet("""
                QDialog {
                    background-color: white;
                    border-radius: 10px;
                }
                QLabel#title_label {
                    color: #2980b9;
                    font-family: 'Calibri';
                    font-size: 16pt;
                    font-weight: bold;
                }
                QLabel#message_label {
                    font-family: 'Calibri';
                    font-size: 13pt;
                }
                QPushButton#yes_btn {
                    background-color: #2980b9;
                    color: white;
                    border: none;
                    border-radius: 5px;
                    padding: 8px 15px;
                    font-family: 'Calibri';
                    font-size: 12pt;
                    font-weight: bold;
                    min-width: 100px;
                }
                QPushButton#yes_btn:hover {
                    background-color: #3498db;
                }
                QPushButton#no_btn {
                    background-color: #e74c3c;
                    color: white;
                    border: none;
                    border-radius: 5px;
                    padding: 8px 15px;
                    font-family: 'Calibri';
                    font-size: 12pt;
                    font-weight: bold;
                    min-width: 100px;
                }
                QPushButton#no_btn:hover {
                    background-color: #c0392b;
                }
            """)

            # إنشاء تخطيط النافذة
            layout = QVBoxLayout(confirm_dialog)
            layout.setContentsMargins(20, 20, 20, 20)
            layout.setSpacing(15)

            # إضافة أيقونة وعنوان
            header_layout = QHBoxLayout()

            # محاولة إضافة أيقونة التأكيد
            icon_label = QLabel()
            icon_label.setAlignment(Qt.AlignCenter)
            try:
                # استخدام أيقونة قياسية
                question_icon = QStyle.standardPixmap(QStyle.SP_MessageBoxQuestion, QStyle.StyleOptionButton(), self)
                question_icon = QPixmap(question_icon).scaled(48, 48, Qt.KeepAspectRatio, Qt.SmoothTransformation)
                icon_label.setPixmap(question_icon)
            except Exception:
                # استخدام نص بديل في حالة الفشل
                icon_label.setText("?")
                icon_label.setFont(QFont("Arial", 24))
                icon_label.setStyleSheet("color: #2980b9;")

            header_layout.addWidget(icon_label)

            # إضافة عنوان النافذة
            title_label = QLabel("تأكيد الخروج")
            title_label.setObjectName("title_label")
            title_label.setAlignment(Qt.AlignCenter)
            header_layout.addWidget(title_label, 1)

            layout.addLayout(header_layout)

            # إضافة رسالة التأكيد
            message_html = """
            <div dir='rtl' style='text-align: right;'>
                <p style='font-family: Calibri; font-size: 14pt; color: #2980b9; font-weight: bold; margin-bottom: 15px;'>
                    هل تريد الخروج من البرنامج؟
                </p>
                <p style='font-family: Calibri; font-size: 13pt; color: #333333; margin-bottom: 10px;'>
                    سيتم إغلاق البرنامج وجميع النوافذ المفتوحة.
                </p>
                <p style='font-family: Calibri; font-size: 13pt; color: #333333; margin-bottom: 10px;'>
                    سيتم عمل نسخة احتياطية تلقائية وضغط قاعدة البيانات قبل الإغلاق.
                </p>
            </div>
            """

            message_label = QLabel(message_html)
            message_label.setObjectName("message_label")
            message_label.setTextFormat(Qt.RichText)
            message_label.setWordWrap(True)
            layout.addWidget(message_label)

            # إضافة أزرار نعم/لا
            button_layout = QHBoxLayout()

            yes_button = QPushButton("نعم")
            yes_button.setObjectName("yes_btn")
            yes_button.setCursor(Qt.PointingHandCursor)
            yes_button.clicked.connect(confirm_dialog.accept)

            no_button = QPushButton("لا")
            no_button.setObjectName("no_btn")
            no_button.setCursor(Qt.PointingHandCursor)
            no_button.clicked.connect(confirm_dialog.reject)

            button_layout.addWidget(yes_button)
            button_layout.addWidget(no_button)

            layout.addLayout(button_layout)

            # عرض النافذة وإرجاع النتيجة
            result = confirm_dialog.exec_() == QDialog.Accepted
            return result

        except Exception as e:
            print(f"خطأ في عرض نافذة تأكيد الخروج: {e}")
            return self._show_traditional_logout_dialog()

    def _show_traditional_logout_dialog(self):
        """عرض رسالة تأكيد تقليدية كاحتياط"""
        msgBox = QMessageBox(self)
        msgBox.setWindowTitle("تأكيد الخروج")
        icon_path = "01.ico"
        if os.path.exists(icon_path):
            msgBox.setWindowIcon(QIcon(icon_path))
        msgBox.setText("<p style='font-family: Calibri; font-size: 13pt; color: #0D47A1; font-weight: bold;'>هل تريد الخروج من البرنامج؟</p>")
        msgBox.setInformativeText("<p style='font-family: Calibri; font-size: 11pt; color: #333333;'>سيتم إغلاق البرنامج وجميع النوافذ المفتوحة.</p>")
        msgBox.setIcon(QMessageBox.Question)
        yesButton = msgBox.addButton("نعم، أريد الخروج", QMessageBox.YesRole)
        noButton = msgBox.addButton("لا، العودة للبرنامج", QMessageBox.NoRole)
        yesButton.setStyleSheet("""
            QPushButton {
                font-family: Calibri; font-size: 13pt; font-weight: bold;
                color: white; background-color: #0D47A1; border: none;
                border-radius: 5px; padding: 5px 15px; min-width: 140px;
            }
            QPushButton:hover { background-color: #1565C0; }
            QPushButton:pressed { background-color: #0D47A1; }
        """)
        noButton.setStyleSheet("""
            QPushButton {
                font-family: Calibri; font-size: 13pt; font-weight: bold;
                color: #0D47A1; background-color: #E3F2FD; border: 1px solid #0D47A1;
                border-radius: 5px; padding: 5px 15px; min-width: 140px;
            }
            QPushButton:hover { background-color: #BBDEFB; }
            QPushButton:pressed { background-color: #E3F2FD; }
        """)
        msgBox.setDefaultButton(noButton)
        msgBox.setStyleSheet("QMessageBox { background-color: white; } QLabel { font-family: Calibri; min-width: 300px; }")
        msgBox.exec_()
        return msgBox.clickedButton() == yesButton

    def closeEvent(self, event):
        """معالجة حدث إغلاق النافذة"""
        try:
            # عرض رسالة تأكيد الخروج
            should_quit = self.show_logout_confirmation_dialog()
            if not should_quit:
                # إذا اختار المستخدم عدم الخروج، نتجاهل حدث الإغلاق
                event.ignore()
                return

            # عمل نسخة احتياطية تلقائية عند الإغلاق
            try:
                self.create_automatic_backup()
            except Exception as e:
                print(f"خطأ أثناء عمل نسخة احتياطية تلقائية: {e}")

            # ضغط قاعدة البيانات بعد النسخ الاحتياطي
            try:
                self.compress_database()
            except Exception as e:
                print(f"خطأ عام أثناء ضغط قاعدة البيانات: {e}")

            # استمرار في تنفيذ الإغلاق الطبيعي
            event.accept()
        except Exception as e:
            print(f"خطأ أثناء معالجة إغلاق النافذة: {e}")
            event.accept()

    def compress_database(self):
        """ضغط قاعدة البيانات وإصلاحها"""
        try:
            import sqlite3
            import shutil
            
            db_path = "data.db"
            backup_path = f"{db_path}.bak"
            
            # إنشاء نسخة احتياطية
            shutil.copy2(db_path, backup_path)
            print(f"تم إنشاء نسخة احتياطية: {backup_path}")
            
            # فتح الاتصال مع قاعدة البيانات
            conn = sqlite3.connect(db_path)
            
            # إجراء فحص سلامة قاعدة البيانات
            integrity_result = conn.execute("PRAGMA integrity_check").fetchone()
            if integrity_result[0] != "ok":
                print(f"تحذير: مشكلة في سلامة قاعدة البيانات: {integrity_result[0]}")
            
            # ضغط قاعدة البيانات
            conn.execute("VACUUM")
            print("تم ضغط قاعدة البيانات بنجاح: " + db_path)
            
            # إغلاق الاتصال
            conn.close()
            
            print("تم ضغط قاعدة البيانات عند إغلاق التطبيق")
            return True
            
        except Exception as e:
            print(f"خطأ في ضغط قاعدة البيانات: {e}")
            return False

    def get_unified_academic_year(self):
        """دالة موحدة لقراءة السنة الدراسية من جدول بيانات_المؤسسة"""
        try:
            import sqlite3
            
            # تحديد مسار قاعدة البيانات
            db_path = "data.db"
            if hasattr(self, 'db_folder') and os.path.exists(os.path.join(self.db_folder, "data.db")):
                db_path = os.path.join(self.db_folder, "data.db")
            elif os.path.exists(os.path.join(os.path.dirname(__file__), "data.db")):
                db_path = os.path.join(os.path.dirname(__file__), "data.db")
            
            # الاتصال بقاعدة البيانات
            conn = sqlite3.connect(db_path)
            cursor = conn.cursor()
            
            # قراءة السنة الدراسية من عمود السنة_الدراسية
            cursor.execute("SELECT السنة_الدراسية FROM بيانات_المؤسسة LIMIT 1")
            result = cursor.fetchone()
            
            conn.close()
            
            if result and result[0]:
                academic_year = result[0]
                print(f"📅 تم توحيد السنة الدراسية من جدول بيانات_المؤسسة: {academic_year}")
                return academic_year
            else:
                print("⚠️ لم يتم العثور على السنة الدراسية في جدول بيانات_المؤسسة")
                return self.current_academic_year if hasattr(self, 'current_academic_year') else "2024/2025"
                
        except Exception as e:
            print(f"❌ خطأ في قراءة السنة الدراسية الموحدة: {e}")
            return self.current_academic_year if hasattr(self, 'current_academic_year') else "2024/2025"

    def create_enhanced_print_lists_window(self):
        """إنشاء نافذة طباعة اللوائح مع حل بسيط لمشكلة قاعدة البيانات"""
        try:
            import sqlite3
            
            # تحديد مسار قاعدة البيانات الصحيح
            db_path = "data.db"  # المسار الافتراضي
            
            # التحقق من وجود قاعدة البيانات في المجلد المخصص أولاً
            if hasattr(self, 'db_folder') and os.path.exists(os.path.join(self.db_folder, "data.db")):
                db_path = os.path.join(self.db_folder, "data.db")
                print(f"استخدام قاعدة البيانات من المجلد المخصص: {db_path}")
            elif os.path.exists(os.path.join(os.path.dirname(__file__), "data.db")):
                db_path = os.path.join(os.path.dirname(__file__), "data.db")
                print(f"استخدام قاعدة البيانات من مجلد البرنامج: {db_path}")
            else:
                print("تحذير: لم يتم العثور على قاعدة البيانات")
            
            sqlite_connection = sqlite3.connect(db_path)
            print(f"تم إنشاء اتصال sqlite3 مع قاعدة البيانات: {db_path}")
            
            # استخدام الدالة الموحدة لقراءة السنة الدراسية
            academic_year = self.get_unified_academic_year()
            
            # إنشاء النافذة مع اتصال sqlite3 مباشرة والسنة الدراسية الموحدة
            print_window = PrintListsWindow(
                parent=self,
                db=sqlite_connection,  # استخدام اتصال sqlite3 مباشرة
                academic_year=academic_year  # استخدام السنة الدراسية الموحدة
            )
            
            # التأكد من أن النافذة تتصرف كويدجت مدمج
            print_window.setWindowFlags(Qt.Widget)
            print_window.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)
            
            print(f"✅ تم إنشاء نافذة طباعة اللوائح المدمجة بنجاح مع السنة الدراسية الموحدة: {academic_year}")
            return print_window
            
        except Exception as e:
            print(f"❌ خطأ في إنشاء نافذة طباعة اللوائح: {e}")
            # إرجاع نافذة مؤقتة في حالة الفشل
            return PlaceholderWindow(
                title="طباعة اللوائح",
                message=f"خطأ في تحميل نافذة طباعة اللوائح:\n{str(e)}",
                parent=self
            )

    # حذف الدوال المعقدة واستبدالها بحل بسيط
    def setup_embedded_print_window(self, print_window):
        """إعداد بسيط للنافذة المدمجة"""
        try:
            print("إعداد نافذة طباعة اللوائح...")
            
            # تحديث البيانات إذا كانت الدالة موجودة
            if hasattr(print_window, 'load_data'):
                print_window.load_data()
                print("تم تحديث بيانات النافذة")
                
        except Exception as e:
            print(f"خطأ في إعداد النافذة: {e}")

    # حذف الدوال المعقدة الأخرى
    def force_update_assigned_classes(self, print_window):
        """تحديث بسيط للأقسام المسندة"""
        try:
            if hasattr(print_window, 'load_assigned_classes'):
                print_window.load_assigned_classes()
            elif hasattr(print_window, 'load_data'):
                print_window.load_data()
        except Exception as e:
            print(f"خطأ في تحديث الأقسام: {e}")

    def connect_print_window_signals(self, print_window):
        """ربط إشارات نافذة طباعة اللوائح إذا لم تكن مربوطة"""
        try:
            # لا حاجة لربط إشارات معقدة - النافذة تتولى ذلك
            print("تم ربط إشارات النافذة")
        except Exception as e:
            print(f"خطأ في ربط الإشارات: {e}")

    def simulate_supervision_change(self, print_window):
        """لا حاجة لمحاكاة تغيير الحراسة"""
        pass

    def set_default_supervision_for_print_window(self, print_window):
        """لا حاجة لتحديد حراسة افتراضية معقدة"""
        pass


if __name__ == "__main__":
    # تقليل الرسائل في وضع التشغيل المباشر
    print("بدء تطبيق نظام إدارة المدرسة...")
    
    QApplication.setAttribute(Qt.AA_EnableHighDpiScaling, True)
    QApplication.setAttribute(Qt.AA_UseHighDpiPixmaps, True)
    app = QApplication(sys.argv)
    app.setLayoutDirection(Qt.RightToLeft)

    window = MainWindow(auto_show=True)
    
    if window.db_connection:
        print("تم تحميل التطبيق بنجاح")
        window.show()
        exit_code = app.exec_()
        print(f"تم إغلاق التطبيق (رمز الخروج: {exit_code})")
        sys.exit(exit_code)
    else:
        print("فشل في تحميل التطبيق - خطأ في قاعدة البيانات")
        sys.exit(1)
from PyQt5.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QLabel, QPushButton,
    QFrame, QRadioButton, QButtonGroup, QGraphicsDropShadowEffect
)
from PyQt5.QtGui import QFont, QPixmap, QColor
from PyQt5.QtCore import Qt

class CustomMessageDialog(QDialog):
    """نافذة حوارية مخصصة للرسائل والتأكيدات"""

    def __init__(self, parent=None, title="", message="", options=None, icon_type="question"):
        super().__init__(parent)
        self.title = title
        self.message = message
        self.options = options or []
        self.icon_type = icon_type
        self.selected_option = None
        self.initUI()

    def initUI(self):
        """تهيئة واجهة المستخدم"""
        self.setWindowTitle(self.title)
        self.setFixedSize(800, 600)
        self.setWindowFlags(Qt.Dialog | Qt.CustomizeWindowHint | Qt.WindowTitleHint | Qt.WindowCloseButtonHint)

        # تعيين نمط CSS للنافذة
        self.setStyleSheet("""
            QDialog {
                background-color: #f0f8ff;
                border: 2px solid #4682b4;
                border-radius: 10px;
            }
        """)

        # تعيين اتجاه النص من اليمين إلى اليسار
        self.setLayoutDirection(Qt.RightToLeft)

        # التخطيط الرئيسي
        layout = QVBoxLayout(self)
        layout.setSpacing(20)
        layout.setContentsMargins(25, 25, 25, 25)

        # إضافة عنوان النافذة
        title_layout = QHBoxLayout()

        # إضافة أيقونة حسب نوع الرسالة
        icon_label = QLabel()
        icon_pixmap = None

        if self.icon_type == "question":
            icon_pixmap = QPixmap("icons/question.png")
            if not icon_pixmap or icon_pixmap.isNull():
                # استخدام أيقونة افتراضية إذا لم يتم العثور على الملف
                icon_label.setText("❓")
                icon_label.setFont(QFont("Arial", 24))
                icon_label.setStyleSheet("color: #4682b4;")
        elif self.icon_type == "information":
            icon_pixmap = QPixmap("icons/info.png")
            if not icon_pixmap or icon_pixmap.isNull():
                icon_label.setText("ℹ️")
                icon_label.setFont(QFont("Arial", 24))
                icon_label.setStyleSheet("color: #4682b4;")
        elif self.icon_type == "error":
            icon_pixmap = QPixmap("icons/error.png")
            if not icon_pixmap or icon_pixmap.isNull():
                icon_label.setText("⚠️")
                icon_label.setFont(QFont("Arial", 24))
                icon_label.setStyleSheet("color: #ff4500;")

        if icon_pixmap and not icon_pixmap.isNull():
            icon_label.setPixmap(icon_pixmap.scaled(48, 48, Qt.KeepAspectRatio, Qt.SmoothTransformation))

        icon_label.setFixedSize(60, 60)
        icon_label.setAlignment(Qt.AlignCenter)

        # إضافة عنوان النافذة
        title_label = QLabel(self.title)
        title_label.setFont(QFont("Calibri", 18, QFont.Bold))
        title_label.setStyleSheet("color: #4682b4;")
        title_label.setAlignment(Qt.AlignRight | Qt.AlignVCenter)

        title_layout.addWidget(title_label)
        title_layout.addWidget(icon_label)

        layout.addLayout(title_layout)

        # إضافة خط فاصل
        separator = QFrame()
        separator.setFrameShape(QFrame.HLine)
        separator.setFrameShadow(QFrame.Sunken)
        separator.setStyleSheet("background-color: #4682b4; min-height: 2px;")
        layout.addWidget(separator)

        # إضافة إطار للرسالة
        message_frame = QFrame()
        message_frame.setFrameShape(QFrame.StyledPanel)
        message_frame.setStyleSheet("""
            QFrame {
                background-color: white;
                border: 1.5px solid #4682b4;
                border-radius: 10px;
            }
        """)

        # إضافة تأثير الظل
        shadow = QGraphicsDropShadowEffect()
        shadow.setBlurRadius(15)
        shadow.setColor(QColor(0, 0, 0, 80))
        shadow.setOffset(3, 3)
        message_frame.setGraphicsEffect(shadow)

        message_layout = QVBoxLayout(message_frame)
        message_layout.setContentsMargins(15, 15, 15, 15)

        # إضافة الرسالة
        message_label = QLabel(self.message)
        message_label.setFont(QFont("Calibri", 14))
        message_label.setStyleSheet("color: #333333; background-color: transparent;")
        message_label.setWordWrap(True)
        message_label.setAlignment(Qt.AlignRight | Qt.AlignVCenter)
        message_layout.addWidget(message_label)

        layout.addWidget(message_frame)

        # إضافة إطار للخيارات إذا كانت موجودة
        if self.options:
            options_frame = QFrame()
            options_frame.setFrameShape(QFrame.StyledPanel)
            options_frame.setStyleSheet("""
                QFrame {
                    background-color: white;
                    border: 1.5px solid #4682b4;
                    border-radius: 10px;
                }
            """)

            # إضافة تأثير الظل
            options_shadow = QGraphicsDropShadowEffect()
            options_shadow.setBlurRadius(15)
            options_shadow.setColor(QColor(0, 0, 0, 80))
            options_shadow.setOffset(3, 3)
            options_frame.setGraphicsEffect(options_shadow)

            options_layout = QVBoxLayout(options_frame)
            options_layout.setContentsMargins(15, 15, 15, 15)

            # إضافة عنوان للخيارات
            options_title = QLabel("الخيارات المتاحة:")
            options_title.setFont(QFont("Calibri", 14, QFont.Bold))
            options_title.setStyleSheet("color: #4682b4; margin-bottom: 10px;")
            options_layout.addWidget(options_title)

            # إنشاء مجموعة أزرار الراديو
            self.button_group = QButtonGroup(self)

            # إضافة خيارات الراديو
            for i, option in enumerate(self.options):
                radio_button = QRadioButton(option)
                radio_button.setFont(QFont("Calibri", 13))
                radio_button.setStyleSheet("""
                    QRadioButton {
                        color: #333333;
                        padding: 8px;
                        margin: 2px 0;
                        border-radius: 5px;
                    }
                    QRadioButton:hover {
                        background-color: #e6f2ff;
                    }
                    QRadioButton::indicator {
                        width: 20px;
                        height: 20px;
                    }
                    QRadioButton::indicator:checked {
                        background-color: #4682b4;
                        border: 2px solid #2c5aa0;
                        border-radius: 10px;
                    }
                """)
                radio_button.setFixedHeight(40)
                self.button_group.addButton(radio_button, i)
                options_layout.addWidget(radio_button)

                # تحديد الخيار الأول افتراضيًا
                if i == 0:
                    radio_button.setChecked(True)
                    self.selected_option = option

            # ربط تغيير الخيار بالدالة
            self.button_group.buttonClicked.connect(self.option_changed)

            layout.addWidget(options_frame)

        # إضافة خط فاصل
        bottom_separator = QFrame()
        bottom_separator.setFrameShape(QFrame.HLine)
        bottom_separator.setFrameShadow(QFrame.Sunken)
        bottom_separator.setStyleSheet("background-color: #4682b4; min-height: 1px;")
        layout.addWidget(bottom_separator)

        # إضافة أزرار موافق وإلغاء
        buttons_layout = QHBoxLayout()
        buttons_layout.setSpacing(15)

        # زر موافق
        self.ok_button = QPushButton("نعم")
        self.ok_button.setFont(QFont("Calibri", 13, QFont.Bold))
        self.ok_button.setStyleSheet("""
            QPushButton {
                background-color: #4682b4;
                color: white;
                border: none;
                border-radius: 5px;
                min-width: 120px;
                min-height: 40px;
            }
            QPushButton:hover {
                background-color: #5c94c5;
            }
            QPushButton:pressed {
                background-color: #3a6d99;
            }
        """)
        self.ok_button.clicked.connect(self.accept)

        # إضافة تأثير الظل للزر
        ok_shadow = QGraphicsDropShadowEffect()
        ok_shadow.setBlurRadius(10)
        ok_shadow.setColor(QColor(0, 0, 0, 80))
        ok_shadow.setOffset(2, 2)
        self.ok_button.setGraphicsEffect(ok_shadow)

        # زر إلغاء
        self.cancel_button = QPushButton("لا")
        self.cancel_button.setFont(QFont("Calibri", 13, QFont.Bold))
        self.cancel_button.setStyleSheet("""
            QPushButton {
                background-color: #e6e6e6;
                color: #333333;
                border: none;
                border-radius: 5px;
                min-width: 120px;
                min-height: 40px;
            }
            QPushButton:hover {
                background-color: #f0f0f0;
            }
            QPushButton:pressed {
                background-color: #d9d9d9;
            }
        """)
        self.cancel_button.clicked.connect(self.reject)

        # إضافة تأثير الظل للزر
        cancel_shadow = QGraphicsDropShadowEffect()
        cancel_shadow.setBlurRadius(10)
        cancel_shadow.setColor(QColor(0, 0, 0, 50))
        cancel_shadow.setOffset(2, 2)
        self.cancel_button.setGraphicsEffect(cancel_shadow)

        buttons_layout.addStretch()
        buttons_layout.addWidget(self.cancel_button)
        buttons_layout.addWidget(self.ok_button)

        layout.addLayout(buttons_layout)

    def option_changed(self, button):
        """تغيير الخيار المحدد"""
        self.selected_option = button.text()

    def get_selected_option(self):
        """الحصول على الخيار المحدد"""
        return self.selected_option

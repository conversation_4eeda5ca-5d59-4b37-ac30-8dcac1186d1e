"""
print1.py - وحدة طباعة نموذج زيارة الطبيب

هذا الملف مسؤول عن طباعة نموذج زيارة الطبيب للتلاميذ.
يتضمن حقل "رأي الطبيب" ويستخدم مكتبة ReportLab لإنشاء ملف PDF.

تاريخ الإنشاء: 2023-10-15
آخر تحديث: 2023-10-15
"""

def print_doctor_visit_form(students, section, date_str=None, time_str=None, font_name=None, font_size=None, font_bold=None, font_italic=None, margin=None, silent_print=None, use_regular_printer=True):
    """طباعة نموذج زيارة الطبيب

    المعلمات:
        students: قائمة الطلاب للطباعة
        section: القسم
        date_str: تاريخ الطباعة (اختياري)
        time_str: وقت الطباعة (اختياري)
        font_name: اس<PERSON> الخط (اختياري)
        font_size: حجم الخط (اختياري)
        font_bold: خط عريض (اختياري)
        font_italic: خط مائل (اختياري)
        margin: الهوامش (اختياري)
        silent_print: طباعة صامتة (اختياري)
        use_regular_printer: استخدام الطابعة العادية بدلاً من الطابعة الحرارية (افتراضي: True)
    """
    try:
        from reportlab.lib.pagesizes import A5  # استخدام حجم A5 بدلاً من A4 لجعل الورقة أصغر
        from reportlab.lib.units import cm
        from reportlab.pdfgen import canvas
        from reportlab.pdfbase import pdfmetrics
        from reportlab.pdfbase.ttfonts import TTFont
        import arabic_reshaper
        from bidi.algorithm import get_display
        import os, sqlite3, time
        import datetime

        # تحديد التاريخ والوقت إذا لم يتم تمريرهما
        if not date_str:
            date_str = datetime.datetime.now().strftime("%Y/%m/%d")
        if not time_str:
            time_str = datetime.datetime.now().strftime("%H:%M")

        # --- إعدادات قابلة للتعديل ---
        settings = {
            "logo_top": 2 * cm,  # تقليل المسافة من 3 إلى 2 سم
            "logo_width": 200,  # تقليل العرض من 300 إلى 200
            "logo_height": 50,  # تقليل الارتفاع من 70 إلى 50

            "title_top": 3 * cm,  # تقليل المسافة من 4 إلى 3 سم
            "institution_font_size": 14,  # تقليل حجم الخط من 18 إلى 14
            "form_title_font_size": 12,  # تقليل حجم الخط من 14 إلى 12

            "year_right": 5 * cm,  # تقليل المسافة من 6.5 إلى 5 سم
            "year_top": 2 * cm,  # تقليل المسافة من 3 إلى 2 سم
            "semester_top": 3 * cm,  # تقليل المسافة من 4 إلى 3 سم

            "box_top": 4 * cm,  # تقليل المسافة من 5 إلى 4 سم
            "box_left": 0.8 * cm,  # تقليل المسافة من 1 إلى 0.8 سم
            "box_width": 13.5 * cm,  # زيادة العرض ليشمل كامل المحتوى
            "box_height": 6 * cm,  # تقليل الارتفاع إلى 6 سم ليناسب نصف الورقة

            "content_font_size": 12,  # تقليل حجم الخط من 14 إلى 12
            "line_spacing": 0.7 * cm,  # تقليل المسافة من 1 إلى 0.8 سم
            "doctor_lines": 8,  # تقليل عدد الأسطر إلى 6
            "doctor_line_spacing": 0.45 * cm,  # زيادة المسافة بين أسطر رأي الطبيب
            "doctor_line_width": 5.5 * cm,  # تحديد عرض سطور رأي الطبيب لتجنب التداخل مع بيانات التلميذ
        }

        # استخدام خط Arial المتوفر في نظام Windows
        try:
            # محاولة تسجيل الخط من المسار المطلق
            arial_path = "C:/Windows/Fonts/arial.ttf"
            pdfmetrics.registerFont(TTFont("Arabic", arial_path))
        except:
            try:
                # محاولة تسجيل الخط من المسار النسبي
                pdfmetrics.registerFont(TTFont("Arabic", "arial.ttf"))
            except:
                # استخدام الخط الافتراضي إذا فشلت المحاولات السابقة
                print("تعذر تحميل الخط العربي، استخدام الخط الافتراضي")

        def reshape(text):
            return get_display(arabic_reshaper.reshape(str(text)))

        student = students[0] if students else {"name": "", "code": ""}

        # استخراج اسم التلميذ ورمزه لاستخدامهما في اسم الملف
        student_name = student.get('name', 'بدون_اسم').replace(' ', '_')
        student_code = student.get('code', 'بدون_رمز')
        current_date = datetime.datetime.now().strftime('%Y%m%d')

        # إنشاء اسم الملف باستخدام اسم التلميذ ورمزه وتاريخ الطلب
        file_name = f"زيارة_طبيب_{student_name}_{student_code}_{current_date}.pdf"

        # إنشاء مجلد رئيسي على سطح المكتب
        desktop_path = os.path.join(os.path.expanduser("~"), "Desktop")
        main_reports_dir = os.path.join(desktop_path, "تقارير برنامج المعين في الحراسة العامة")
        os.makedirs(main_reports_dir, exist_ok=True)

        # إنشاء مجلد تقرير زيارة الطبيب داخل المجلد الرئيسي
        doctor_visit_dir = os.path.join(main_reports_dir, "تقرير زيارة الطبيب")
        os.makedirs(doctor_visit_dir, exist_ok=True)

        # إنشاء مجلد تقارير_زيارة_الطبيب داخل مجلد البرنامج للتوافقية
        local_doctor_visit_dir = os.path.join(os.path.dirname(__file__), "تقارير_زيارة_الطبيب")
        os.makedirs(local_doctor_visit_dir, exist_ok=True)

        # إنشاء مجلد temp للتوافق مع الكود القديم
        temp_dir = os.path.join(os.path.dirname(__file__), "temp")
        os.makedirs(temp_dir, exist_ok=True)

        # حفظ الملف في مجلد تقرير زيارة الطبيب على سطح المكتب
        file_path = os.path.join(doctor_visit_dir, file_name)

        c = canvas.Canvas(file_path, pagesize=A5)  # استخدام حجم A5 بدلاً من A4
        width, height = A5

        # --- استخراج الشعار من قاعدة البيانات ---
        try:
            db_path = os.path.join(os.path.dirname(__file__), "data.db")
            conn = sqlite3.connect(db_path)
            cur = conn.cursor()
            cur.execute("SELECT ImagePath1 FROM بيانات_المؤسسة LIMIT 1")
            logo_row = cur.fetchone()
            if logo_row and logo_row[0]:
                logo_path = logo_row[0]
                if os.path.exists(logo_path):
                    logo_x = (width - settings["logo_width"]) / 2
                    logo_y = height - settings["logo_top"]
                    c.drawImage(logo_path, logo_x, logo_y, width=settings["logo_width"], height=settings["logo_height"], preserveAspectRatio=True, mask='auto')
        except Exception as e:
            print(f"خطأ في استخراج الشعار: {e}")

        # --- استخراج بيانات المؤسسة ---
        institution = "المؤسسة التعليمية"
        year_text = "2024/2025"
        semester = "الأول"
        try:
            conn = sqlite3.connect("data.db")
            cur = conn.cursor()
            cur.execute("SELECT المؤسسة, السنة_الدراسية, الأسدس FROM بيانات_المؤسسة LIMIT 1")
            row = cur.fetchone()
            if row:
                institution = row[0] or institution
                year_text = row[1] or year_text
                semester = row[2] or semester
            conn.close()
        except Exception as e:
            print(f"خطأ في استخراج بيانات المؤسسة: {e}")

        # --- عنوان المؤسسة ---
        c.setFont("Arabic", settings["institution_font_size"])
        c.drawCentredString(width/2, height - settings["title_top"], reshape(institution))

        c.setFont("Arabic", settings["form_title_font_size"])
        c.drawCentredString(width/2, height - (settings["title_top"] + 15), reshape("ورقة زيارة الطبيب"))

        # --- السنة الدراسية والأسدس ---
        c.setFont("Arabic", settings["form_title_font_size"])
        c.drawString(width - settings["year_right"], height - settings["year_top"], reshape(f"السنة الدراسية : {year_text}"))

        # --- الإطار الرئيسي ---
        box_top = height - settings["box_top"]
        box_left = settings["box_left"]
        box_width = settings["box_width"]
        box_height = settings["box_height"]
        c.rect(box_left, box_top - box_height, box_width, box_height)

        # --- محتوى التلميذ ---
        right_x = width - 1 * cm
        y = box_top - 0.7 * cm  # تقليل المسافة من أعلى
        c.setFont("Arabic", settings["content_font_size"])
        c.drawRightString(right_x, y, reshape(f"يسمح للتلميذ(ة) : {student.get('name', '')}"))
        y -= 0.6 * cm  # تقليل المسافة بين الأسطر
        c.drawRightString(right_x, y, reshape(f"قسم : {section}  رقم التسجيل : {student.get('code', '')}"))
        y -= 0.6 * cm  # تقليل المسافة بين الأسطر
        c.drawRightString(right_x, y, reshape(f"بزيارة الطبيب يوم : {date_str}"))
        y -= 0.6 * cm  # تقليل المسافة بين الأسطر
        c.drawRightString(right_x, y, reshape(f"على الساعة : {time_str}"))

        # --- رأي الطبيب ---
        left_x = box_left + 0.5 * cm
        y2 = box_top - 0.6 * cm  # تقليل المسافة من أعلى أكثر
        c.drawString(left_x, y2, reshape("رأي الطبيب"))
        y2 -= 0.4 * cm  # زيادة المسافة بين العنوان والسطر الأول
        
        # رسم أسطر متباعدة لرأي الطبيب (تقليل عدد الأسطر وزيادة المسافة بينها)
        line_count = settings["doctor_lines"]  # عدد الأسطر
        line_spacing = settings["doctor_line_spacing"]  # المسافة بين الأسطر
        line_width = settings["doctor_line_width"]  # عرض السطر
        
        for _ in range(line_count):
            c.line(left_x, y2, left_x + line_width, y2)  # استخدام العرض المحدد في الإعدادات
            y2 -= line_spacing  # استخدام المسافة المحددة المزيدة بين الأسطر

        # --- التوقيعات في نفس الصف ---
        signature_y = box_top - box_height + 1.0 * cm  # موضع التوقيعات في أسفل الجدول

        # توقيع الحراسة العامة (يمين)
        c.drawRightString(right_x, signature_y, reshape("ختم وتوقيع الحراسة العامة"))

        # توقيع الطبيب (يسار)
        c.drawString(left_x, signature_y, reshape("توقيع الطبيب"))

        c.save()

        # --- إعداد الملف للطباعة ---
        # استخدام معلمة use_regular_printer للتحكم في طريقة الطباعة
        print(f"تم إنشاء ملف نموذج زيارة الطبيب باستخدام {'الطابعة العادية' if use_regular_printer else 'الطابعة الحرارية'}")

        # لا نقوم بفتح الملف تلقائياً هنا، بل نترك ذلك لرسالة التأكيد في sub4_window.py
        # سيتم فتح الملف عند الضغط على زر "معاينة الملف" في رسالة التأكيد

        # انتظار لحظة للتأكد من اكتمال إنشاء الملف
        time.sleep(1)
        return True, file_path, doctor_visit_dir

    except Exception as e:
        print(f"خطأ أثناء الطباعة: {e}")
        return False, "", ""

# اختبار الطباعة
if __name__ == "__main__":
    students = [
        {"rt": "1", "name": "محمد أحمد", "code": "A123456"},
        {"rt": "2", "name": "سعيد علي", "code": "B789012"}
    ]
    section = "3-2"

    print("\n=== بدء اختبار طباعة نموذج زيارة الطبيب على الطابعة العادية ===")
    result, filepath, reports_dir = print_doctor_visit_form(
        students, section, silent_print=True, use_regular_printer=True
    )
    print(f"نتيجة طباعة نموذج زيارة الطبيب على الطابعة العادية: {'نجاح' if result else 'فشل'}")
    print(f"تم حفظ الملف في: {filepath}")

    # عرض رسالة تأكيد
    if result:
        print(f"تم إنشاء ملف نموذج زيارة الطبيب بنجاح: {filepath}")

import os
import sqlite3
import logging
import traceback
from datetime import datetime
from PyQt5.QtSql import QSqlQuery, QSqlDatabase

class DatabaseManager:
    """مدير قاعدة البيانات لتسهيل التعامل مع الاستعلامات SQL"""

    def __init__(self, db_path="data.db"):
        """تهيئة مدير قاعدة البيانات مع المسار المحدد"""
        # تحديد مسار قاعدة البيانات في مجلد البرنامج الرئيسي إذا لم يكن مسارًا كاملًا
        if not os.path.isabs(db_path):
            db_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), db_path)

        self.db_path = db_path
        self.connection = None
        try:
            self.connection = sqlite3.connect(db_path)
            print(f"تم الاتصال بقاعدة البيانات بنجاح: {db_path}")
        except sqlite3.Error as e:
            print(f"خطأ في الاتصال بقاعدة البيانات: {e}")

    def execute_query(self, query, params=None, fetch_one=False, fetch_all=False):
        """تنفيذ استعلام SQL واسترجاع النتائج حسب الطلب"""
        try:
            cursor = self.connection.cursor()
            if params:
                cursor.execute(query, params)
            else:
                cursor.execute(query)

            if fetch_one:
                return cursor.fetchone()
            elif fetch_all:
                return cursor.fetchall()
            else:
                self.connection.commit()
                return cursor

        except sqlite3.Error as e:
            print(f"خطأ في تنفيذ الاستعلام: {e}")
            print(f"الاستعلام: {query}")
            if params:
                print(f"المعاملات: {params}")
            return None

    def save_violation(self, violation_data):
        """حفظ بيانات المخالفة في جدول المخالفات وتحديث عدد المخالفات في السجل العام"""
        try:
            # التأكد من أن المفاتيح المطلوبة موجودة (إزالة student_rt من required_keys)
            required_keys = [
                "date", "student_code", "student_name", "level",
                "section", "subject", "teacher", "notes", "procedures",
                "guard_actions", "school_year", "semester" # student_rt removed
            ]
            if not all(key in violation_data for key in required_keys):
                print("خطأ: بيانات المخالفة غير كاملة.")
                # يمكنك إضافة تفاصيل أكثر حول المفاتيح المفقودة إذا لزم الأمر
                missing_keys = [key for key in required_keys if key not in violation_data]
                print(f"المفاتيح المفقودة: {missing_keys}")
                return False

            # بدء معاملة لضمان تنفيذ جميع العمليات أو عدم تنفيذ أي منها
            self.connection.execute("BEGIN TRANSACTION")

            # تعديل الاستعلام لإزالة عمود رت_التلميذ
            query = """
                INSERT INTO المخالفات (
                    التاريخ, رمز_التلميذ, اسم_التلميذ, المستوى, القسم,
                    المادة, الأستاذ, الملاحظات, الإجراءات, إجراءات_الحراسة,
                    السنة_الدراسية, الأسدس
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """
            # تعديل القيم لإزالة student_rt
            values = (
                violation_data["date"],
                violation_data["student_code"],
                # violation_data["student_rt"], # Removed
                violation_data["student_name"],
                violation_data["level"],
                violation_data["section"],
                violation_data["subject"],
                violation_data["teacher"],
                violation_data["notes"],
                violation_data["procedures"],
                violation_data["guard_actions"],
                violation_data["school_year"], # إضافة قيمة السنة الدراسية
                violation_data["semester"]     # إضافة قيمة الأسدس
            )

            # حفظ المخالفة في جدول المخالفات
            self.execute_query(query, values)

            # تحديث عدد المخالفات في السجل العام حسب الأسدس الحالي
            update_query = """
                UPDATE السجل_العام
                SET عدد_المخالفات = (
                    SELECT COUNT(*)
                    FROM المخالفات
                    WHERE رمز_التلميذ = ?
                    AND الأسدس = ?
                    AND السنة_الدراسية = ?
                )
                WHERE الرمز = ?
            """
            update_values = (
                violation_data["student_code"],
                violation_data["semester"],
                violation_data["school_year"],
                violation_data["student_code"]
            )
            self.execute_query(update_query, update_values)

            # تأكيد المعاملة
            self.connection.commit()

            print("تم حفظ المخالفة وتحديث السجل العام بنجاح.")
            return True

        except sqlite3.Error as e:
            # التراجع عن المعاملة في حالة حدوث خطأ
            self.connection.rollback()
            print(f"خطأ SQLite في حفظ المخالفة: {e}")
            # طباعة تفاصيل الخطأ للمساعدة في التشخيص
            import traceback
            traceback.print_exc()
            return False
        except Exception as e:
            # التراجع عن المعاملة في حالة حدوث خطأ
            self.connection.rollback()
            print(f"خطأ عام في حفظ المخالفة: {e}")
            import traceback
            traceback.print_exc()
            return False

    def close(self):
        """إغلاق الاتصال بقاعدة البيانات"""
        if self.connection:
            self.connection.close()
            print("تم إغلاق الاتصال بقاعدة البيانات")

    def check_student_enrollment(self, student_code):
        """التحقق من تسجيل الطالب في السنة الدراسية الحالية"""
        current_year, error = self.get_current_school_year()
        if error:
            return False, None

        query = """
            SELECT l.الرمز, l.السنة_الدراسية, l.المستوى, l.القسم, l.رت, s.الاسم_والنسب
            FROM اللوائح l
            JOIN السجل_العام s ON l.الرمز = s.الرمز
            WHERE l.الرمز = ? AND l.السنة_الدراسية = ?
        """

        result = self.execute_query(query, (student_code, current_year), fetch_one=True)

        if result:
            # إرجاع True وبيانات الطالب إذا كان مسجلاً
            student_data = {
                "code": result[0],
                "year": result[1],
                "level": result[2],
                "class": result[3],
                "rt": result[4],
                "name": result[5]
            }
            return True, student_data
        else:
            # إرجاع False وNone إذا لم يكن الطالب مسجلاً
            return False, None

    def get_current_school_year(self):
        """استرجاع السنة الدراسية الحالية من قاعدة البيانات"""
        try:
            query = "SELECT السنة_الدراسية FROM بيانات_المؤسسة LIMIT 1"
            result = self.execute_query(query, fetch_one=True)

            if result and result[0]:
                return result[0], None
            else:
                return None, "لم يتم العثور على السنة الدراسية في قاعدة البيانات"
        except Exception as e:
            return None, f"خطأ في استرجاع السنة الدراسية: {e}"

    def get_current_semester(self):
        """استرجاع الأسدس الحالي من قاعدة البيانات"""
        try:
            query = "SELECT الأسدس FROM بيانات_المؤسسة LIMIT 1"
            result = self.execute_query(query, fetch_one=True)

            if result and result[0]:
                return result[0], None
            else:
                return None, "لم يتم العثور على الأسدس في قاعدة البيانات"
        except Exception as e:
            return None, f"خطأ في استرجاع الأسدس: {e}"


def get_db_manager(db_path="data.db"):
    """إنشاء وإرجاع نسخة من مدير قاعدة البيانات"""
    # تحديد مسار قاعدة البيانات في مجلد البرنامج الرئيسي
    db_full_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), db_path)
    logging.info(f"مسار قاعدة البيانات لمدير قاعدة البيانات: {db_full_path}")
    return DatabaseManager(db_full_path)

def get_current_school_year(db_manager=None):
    """استرجاع السنة الدراسية الحالية"""
    if db_manager:
        return db_manager.get_current_school_year()
    else:
        db_manager = get_db_manager()
        result = db_manager.get_current_school_year()
        db_manager.close()
        return result

def get_current_semester(db_manager=None):
    """استرجاع الأسدس الحالي"""
    if db_manager:
        return db_manager.get_current_semester()
    else:
        db_manager = get_db_manager()
        result = db_manager.get_current_semester()
        db_manager.close()
        return result

def check_student_enrollment(student_code, db_manager=None):
    """التحقق من تسجيل الطالب في السنة الدراسية الحالية"""
    close_db = False

    if not db_manager:
        db_manager = get_db_manager()
        close_db = True

    result = db_manager.check_student_enrollment(student_code)

    if close_db:
        db_manager.close()

    return result

def initialize_database(db_path="data.db"):
    """تهيئة قاعدة البيانات وإنشاء الجداول إذا لم تكن موجودة"""
    # تحديد مسار قاعدة البيانات في مجلد البرنامج الرئيسي
    db_full_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), db_path)
    logging.info(f"مسار قاعدة البيانات للتهيئة في database_utils: {db_full_path}")

    conn = sqlite3.connect(db_full_path)
    cursor = conn.cursor()

    # إنشاء جدول المخالفات إذا لم يكن موجوداً
    cursor.execute("""
        CREATE TABLE IF NOT EXISTS المخالفات (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            التاريخ TEXT,
            رمز_التلميذ TEXT,
            رت TEXT,
            اسم_التلميذ TEXT,
            المستوى TEXT,
            القسم TEXT,
            المادة TEXT,
            الأستاذ TEXT,
            الملاحظات TEXT,
            الإجراءات TEXT,
            إجراءات_الحراسة TEXT,
            السنة_الدراسية TEXT,
            الأسدس TEXT
        )
    """)

    # التحقق من وجود عمود رت وإضافته إذا لم يكن موجوداً
    try:
        # فحص إذا كان العمود رت موجود
        cursor.execute("SELECT رت FROM المخالفات LIMIT 1")
    except sqlite3.OperationalError:
        # العمود رت غير موجود، نضيفه للجدول
        print("إضافة عمود 'رت' إلى جدول المخالفات...")
        cursor.execute("ALTER TABLE المخالفات ADD COLUMN رت TEXT")

    conn.commit()
    conn.close()
    print("تم تهيئة قاعدة البيانات")

def compress_database(db_path="data.db"):
    """ضغط قاعدة البيانات لتقليل حجمها وتحسين أدائها"""
    try:
        # تحديد مسار قاعدة البيانات في مجلد البرنامج الرئيسي
        db_full_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), db_path)
        logging.info(f"مسار قاعدة البيانات للضغط: {db_full_path}")

        if not os.path.exists(db_full_path):
            logging.warning(f"لا يمكن ضغط قاعدة البيانات: الملف غير موجود: {db_full_path}")
            return False

        conn = sqlite3.connect(db_full_path)
        # تنفيذ أمر VACUUM لضغط قاعدة البيانات
        conn.execute("VACUUM")
        conn.close()
        logging.info(f"تم ضغط قاعدة البيانات بنجاح: {db_path}")
        return True
    except Exception as e:
        logging.error(f"خطأ أثناء ضغط قاعدة البيانات: {e}")
        logging.error(traceback.format_exc())
        try:
            conn.close()
        except:
            pass
        return False

def auto_backup_database(db_path="data.db", max_backups=10):
    """إنشاء نسخة احتياطية من قاعدة البيانات بشكل تلقائي مع حد أقصى للنسخ"""
    try:
        import shutil

        # تحديد مسار قاعدة البيانات في مجلد البرنامج الرئيسي
        db_full_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), db_path)
        logging.info(f"مسار قاعدة البيانات للنسخ الاحتياطي: {db_full_path}")

        if not os.path.exists(db_full_path):
            logging.warning(f"لا يمكن إنشاء نسخة احتياطية: الملف غير موجود: {db_full_path}")
            return False

        # إنشاء مجلد النسخ الاحتياطية إذا لم يكن موجودًا
        backups_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), "Backups")
        if not os.path.exists(backups_dir):
            os.makedirs(backups_dir)

        # تنسيق اسم الملف بتاريخ ووقت حاليين
        current_datetime = datetime.now().strftime("%Y-%m-%d_%H-%M-%S")
        backup_filename = f"data_backup_{current_datetime}.db"
        backup_path = os.path.join(backups_dir, backup_filename)

        # سجل حجم قاعدة البيانات قبل الضغط
        original_size = os.path.getsize(db_full_path) if os.path.exists(db_full_path) else 0
        logging.info(f"حجم قاعدة البيانات قبل الضغط: {original_size} بايت")

        # ضغط قاعدة البيانات قبل النسخ
        compress_result = compress_database(db_path)
        logging.info(f"نتيجة ضغط قاعدة البيانات: {'نجاح' if compress_result else 'فشل'}")

        # سجل حجم قاعدة البيانات بعد الضغط
        compressed_size = os.path.getsize(db_full_path) if os.path.exists(db_full_path) else 0
        if original_size > 0 and compressed_size > 0:
            compression_ratio = ((original_size - compressed_size) / original_size) * 100
            logging.info(f"حجم قاعدة البيانات بعد الضغط: {compressed_size} بايت (بنسبة ضغط {compression_ratio:.1f}%)")

        # نسخ قاعدة البيانات المضغوطة (مع الاحتفاظ بتنسيق .db وعدم تحويلها إلى ZIP)
        shutil.copy2(db_full_path, backup_path)
        logging.info(f"تم إنشاء نسخة احتياطية من قاعدة البيانات: {backup_path}")
        logging.info(f"تنسيق النسخة الاحتياطية: ملف قاعدة بيانات SQLite (.db)")

        # طباعة مسار الملف الكامل والتنسيق للتأكد
        print(f"تم إنشاء نسخة احتياطية في: {backup_path}")
        print(f"تنسيق الملف: SQLite (.db) - وليس (.zip)")

        # حذف النسخ القديمة إذا تجاوزت الحد الأقصى
        backup_files = sorted([os.path.join(backups_dir, f) for f in os.listdir(backups_dir)
                               if f.startswith("data_backup_") and f.endswith(".db")])

        if len(backup_files) > max_backups:
            # حذف أقدم النسخ
            old_files_to_remove = backup_files[:-max_backups]
            for old_file in old_files_to_remove:
                try:
                    os.remove(old_file)
                    logging.info(f"تم حذف نسخة احتياطية قديمة: {old_file}")
                except Exception as e:
                    logging.warning(f"فشل حذف نسخة احتياطية قديمة: {old_file}, السبب: {e}")

            logging.info(f"تم الاحتفاظ بـ {max_backups} نسخة احتياطية فقط وحذف {len(old_files_to_remove)} نسخة قديمة")

        return True
    except Exception as e:
        logging.error(f"خطأ في إنشاء نسخة احتياطية من قاعدة البيانات: {e}")
        logging.error(traceback.format_exc())
        return False

# التهيئة عند استيراد الملف
if __name__ == "__main__":
    print("تهيئة قاعدة البيانات...")
    # استخدام الاسم الافتراضي "data.db" بدون تحديد مسار
    # سيتم وضع قاعدة البيانات في مجلد البرنامج الرئيسي
    initialize_database()
    compress_database()
    print("تم تهيئة قاعدة البيانات بنجاح")

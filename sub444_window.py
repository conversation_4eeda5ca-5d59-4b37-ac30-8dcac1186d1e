#!/usr/bin/env python
# -*- coding: utf-8 -*-

import sys
import sqlite3
from PyQt5.QtWidgets import (
    QApplication, QDialog, QVBoxLayout, QHBoxLayout, QPushButton, 
    QLabel, QDateEdit, QInputDialog, QMessageBox, QFrame, QCheckBox
)
from PyQt5.QtGui import QFont, QIcon, QColor
from PyQt5.QtCore import Qt, QDate

class FileRequestProcessingWindow(QDialog):
    """نافذة معالجة طلبات الملفات"""
    
    def __init__(self, parent=None, db_path="data.db"):
        super().__init__(parent)
        self.parent_window = parent
        self.db_path = db_path
        # تحديد ارتفاع الأزرار من هنا
        self.button_height = 30  # يمكنك تغيير هذا الرقم للتحكم في ارتفاع الأزرار
        self.setupUI()
        
    def setupUI(self):
        # إعداد النافذة الأساسي
        self.setWindowTitle("معالجة طلبات الملفات")
        self.setFixedSize(600, 600)
        self.setLayoutDirection(Qt.RightToLeft)
        
        # محاولة تعيين أيقونة البرنامج
        try:
            self.setWindowIcon(QIcon("01.ico"))
        except Exception:
            pass
        
        # تنسيق النافذة مع تطبيق خط Calibri على جميع العناصر بشكل صريح
        self.setStyleSheet(f"""
            QDialog {{
                background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                          stop: 0 #f8f9fc, stop: 0.3 #e6f0ff, stop: 0.7 #ddeeff, stop: 1 #d4e6ff);
                border: 3px solid #2c5aa0;
                border-radius: 20px;
                font-family: 'Calibri';
                font-size: 12pt;
            }}
            QLabel {{
                color: #2c3e50;
                font-weight: bold;
                font-family: 'Calibri';
                background: transparent;
                border: none;
            }}
            QFrame {{
                background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                          stop: 0 #ffffff, stop: 1 #f5f8fc);
                border: 2px solid #e1e8ed;
                border-radius: 15px;
                margin: 8px;
                padding: 15px;
                font-family: 'Calibri';
            }}
            QPushButton {{
                background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                          stop: 0 #667eea, stop: 0.5 #5a67d8, stop: 1 #4c51bf);
                color: white;
                border: 2px solid #4c51bf;
                border-radius: 12px;
                padding: 8px 16px;
                font-weight: bold;
                font-size: 13pt;
                font-family: 'Calibri';
                min-height: {self.button_height}px;
                max-height: {self.button_height}px;
                margin: 4px;
                text-align: center;
            }}
            QPushButton:hover {{
                background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                          stop: 0 #7c3aed, stop: 0.5 #6d28d9, stop: 1 #5b21b6);
                border: 3px solid #7c3aed;
            }}
            QPushButton:pressed {{
                background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                          stop: 0 #4c51bf, stop: 1 #667eea);
                border: 2px solid #667eea;
                padding: 10px 16px 6px 16px;
            }}
            QPushButton#btn1 {{
                background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                          stop: 0 #ff6b9d, stop: 0.5 #e55a87, stop: 1 #d63384);
                border: 2px solid #d63384;
                font-family: 'Calibri';
                font-weight: bold;
            }}
            QPushButton#btn1:hover {{
                background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                          stop: 0 #ff8fab, stop: 0.5 #ff6b9d, stop: 1 #e55a87);
                border: 3px solid #ff6b9d;
            }}
            QPushButton#btn2 {{
                background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                          stop: 0 #20d9c4, stop: 0.5 #1bb3a1, stop: 1 #0d9488);
                border: 2px solid #0d9488;
                font-family: 'Calibri';
                font-weight: bold;
            }}
            QPushButton#btn2:hover {{
                background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                          stop: 0 #34d9c6, stop: 0.5 #20d9c4, stop: 1 #1bb3a1);
                border: 3px solid #20d9c4;
            }}
            QPushButton#btn3 {{
                background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                          stop: 0 #fb7185, stop: 0.5 #f43f5e, stop: 1 #e11d48);
                border: 2px solid #e11d48;
                font-family: 'Calibri';
                font-weight: bold;
            }}
            QPushButton#btn3:hover {{
                background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                          stop: 0 #fda4af, stop: 0.5 #fb7185, stop: 1 #f43f5e);
                border: 3px solid #fb7185;
            }}
            QPushButton#close_btn {{
                background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                          stop: 0 #9ca3af, stop: 0.5 #6b7280, stop: 1 #4b5563);
                border: 2px solid #4b5563;
                min-height: 35px;
                max-height: 35px;
                min-width: 120px;
                font-size: 12pt;
                font-family: 'Calibri';
                font-weight: bold;
            }}
            QPushButton#close_btn:hover {{
                background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                          stop: 0 #d1d5db, stop: 0.5 #9ca3af, stop: 1 #6b7280);
                border: 3px solid #9ca3af;
            }}
            QDateEdit {{
                font-family: 'Calibri';
                font-size: 12pt;
                font-weight: bold;
            }}
            QTextBrowser {{
                font-family: 'Calibri';
                font-size: 11pt;
            }}
            QMessageBox {{
                font-family: 'Calibri';
                font-size: 12pt;
            }}
            QDialogButtonBox QPushButton {{
                font-family: 'Calibri';
                font-weight: bold;
            }}
        """)
        
        # إنشاء التخطيط الرئيسي
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(25, 25, 25, 25)
        main_layout.setSpacing(20)
        
        # العنوان الرئيسي
        title_label = QLabel("معالجة طلبات الملفات")
        title_label.setFont(QFont("Calibri", 22, QFont.Bold))
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("""
            QLabel {
                color: #1e3a8a;
                background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                          stop: 0 #ffffff, stop: 1 #f1f5f9);
                border: 3px solid #3b82f6;
                border-radius: 15px;
                padding: 20px;
                margin-bottom: 15px;
                font-family: 'Calibri', sans-serif;
            }
        """)
        main_layout.addWidget(title_label)
        
        # إطار للأزرار
        buttons_frame = QFrame()
        buttons_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                          stop: 0 #ffffff, stop: 1 #fafbfc);
                border: 2px solid #d1d9e0;
                border-radius: 18px;
                margin: 5px;
                padding: 20px;
            }
        """)
        buttons_layout = QVBoxLayout(buttons_frame)
        buttons_layout.setSpacing(12)
        buttons_layout.setContentsMargins(15, 15, 15, 15)
        
        # الزر الأول - طلبات الملفات الجماعية
        self.btn_batch = QPushButton("📋  1 - طلبات الملفات الجماعية")
        self.btn_batch.setObjectName("btn1")
        self.btn_batch.setFont(QFont("Calibri", 14, QFont.Bold))
        self.btn_batch.setMinimumHeight(self.button_height)
        self.btn_batch.setMaximumHeight(self.button_height)
        self.btn_batch.clicked.connect(self.batch_file_requests)
        buttons_layout.addWidget(self.btn_batch)
        
        # الزر الثاني - طلبات الملفات الفردية
        self.btn_individual = QPushButton("📄  2 - طلبات الملفات الفردية")
        self.btn_individual.setObjectName("btn2")
        self.btn_individual.setFont(QFont("Calibri", 14, QFont.Bold))
        self.btn_individual.setMinimumHeight(self.button_height)
        self.btn_individual.setMaximumHeight(self.button_height)
        self.btn_individual.clicked.connect(self.individual_file_requests)
        buttons_layout.addWidget(self.btn_individual)
        
        # الزر الثالث - إعادة الطلبات
        self.btn_retry = QPushButton("🔄  3 - إعادة الطلبات")
        self.btn_retry.setObjectName("btn3")
        self.btn_retry.setFont(QFont("Calibri", 14, QFont.Bold))
        self.btn_retry.setMinimumHeight(self.button_height)
        self.btn_retry.setMaximumHeight(self.button_height)
        self.btn_retry.clicked.connect(self.retry_requests)
        buttons_layout.addWidget(self.btn_retry)
        
        main_layout.addWidget(buttons_frame)
        
        # إضافة مساحة مرنة
        main_layout.addStretch()
        
        # زر الإغلاق
        close_button = QPushButton("✖  إغلاق")
        close_button.setObjectName("close_btn")
        close_button.setFont(QFont("Calibri", 12, QFont.Bold))
        close_button.clicked.connect(self.reject)
        
        close_layout = QHBoxLayout()
        close_layout.addStretch()
        close_layout.addWidget(close_button)
        close_layout.addStretch()
        
        main_layout.addLayout(close_layout)
    
    def connect_to_database(self):
        """الاتصال بقاعدة البيانات"""
        print("🔌 محاولة الاتصال بقاعدة البيانات...")
        try:
            conn = sqlite3.connect(self.db_path)
            print("✅ تم الاتصال بقاعدة البيانات بنجاح")
            return conn
        except Exception as e:
            print(f"❌ خطأ في الاتصال بقاعدة البيانات: {e}")
            QMessageBox.critical(self, "خطأ", f"فشل الاتصال بقاعدة البيانات: {e}")
            return None

    def get_double_movement_students(self):
        """الحصول على قائمة التلاميذ ذوي الحركية المزدوجة"""
        conn = self.connect_to_database()
        if not conn:
            return []

        try:
            cursor = conn.cursor()
            
            # البحث عن التلاميذ الذين لديهم حركتان (وافد ومغادر)
            query = """
            SELECT رقم_التلميذ, COUNT(DISTINCT ملاحظات) as movement_count
            FROM سجلات_الوافدين_والمغادرين 
            WHERE رقم_التلميذ IS NOT NULL AND رقم_التلميذ != ''
            AND ملاحظات IN ('لائحة التحويلات (المغادرون)', 'لائحة التحويلات (الوافدون)')
            GROUP BY رقم_التلميذ
            HAVING COUNT(DISTINCT ملاحظات) = 2
            """
            
            cursor.execute(query)
            double_movement_students = [row[0] for row in cursor.fetchall()]
            
            print(f"🔄 عدد التلاميذ ذوي الحركية المزدوجة: {len(double_movement_students)}")
            
            conn.close()
            return double_movement_students
            
        except Exception as e:
            print(f"❌ خطأ في الحصول على التلاميذ ذوي الحركية المزدوجة: {e}")
            if conn:
                conn.close()
            return []

    def ensure_required_columns(self, cursor):
        """التأكد من وجود الأعمدة المطلوبة وإضافتها إذا لم تكن موجودة"""
        print("🔍 التحقق من وجود الأعمدة المطلوبة...")
        
        # قائمة الأعمدة المطلوبة
        required_columns = [
            ("رقم_الطلب", "INTEGER"),
            ("تاريخ_الطلب_الأول", "TEXT"),
            ("تاريخ_الطلب_الثاني", "TEXT"),
            ("تاريخ_الطلب_الثالث", "TEXT"),
            ("رقم_المراسلة", "INTEGER"),
            ("تاريخ_المراسلة_الأولى", "TEXT"),
            ("رقم_المراسلة_الثانية", "INTEGER"),
            ("تاريخ_المراسلة_الثانية", "TEXT"),
            ("تاريخ_استلام_الملف", "TEXT"),
            ("ملاحظات_الملف", "TEXT"),
            ("رقم_الإرسال", "TEXT")
        ]
        
        # الحصول على الأعمدة الموجودة
        cursor.execute("PRAGMA table_info(سجلات_الوافدين_والمغادرين)")
        existing_columns = [col[1] for col in cursor.fetchall()]
        print(f"📋 الأعمدة الموجودة: {existing_columns}")
        
        # إضافة الأعمدة المفقودة
        for column_name, column_type in required_columns:
            if column_name not in existing_columns:
                try:
                    cursor.execute(f"ALTER TABLE سجلات_الوافدين_والمغادرين ADD COLUMN {column_name} {column_type}")
                    print(f"✅ تم إضافة العمود: {column_name}")
                except Exception as e:
                    print(f"❌ خطأ في إضافة العمود {column_name}: {e}")
            else:
                print(f"✓ العمود {column_name} موجود بالفعل")

    def get_date_from_user(self, title):
        """طلب تاريخ من المستخدم - نافذة قياسية مبسطة"""
        from PyQt5.QtWidgets import QDialog, QVBoxLayout, QLabel, QDateEdit, QDialogButtonBox
        
        dialog = QDialog(self)
        dialog.setWindowTitle(title)
        dialog.setFixedSize(350, 200)
        dialog.setLayoutDirection(Qt.RightToLeft)
        
        # تطبيق خط Calibri مع تنسيق مبسط
        dialog.setStyleSheet("""
            QDialog {
                background-color: #f8f9fa;
                border: 2px solid #6c757d;
                border-radius: 10px;
                font-family: 'Calibri';
            }
            QLabel {
                color: #495057;
                font-weight: bold;
                font-size: 14pt;
                font-family: 'Calibri';
                padding: 10px;
                background-color: white;
                border: 1px solid #dee2e6;
                border-radius: 5px;
            }
            QDateEdit {
                border: 2px solid #ced4da;
                border-radius: 5px;
                padding: 8px;
                background-color: white;
                font-weight: bold;
                font-size: 12pt;
                font-family: 'Calibri';
                min-height: 30px;
            }
            QDateEdit:focus {
                border: 2px solid #007bff;
            }
            QDialogButtonBox QPushButton {
                background-color: #007bff;
                color: white;
                border: none;
                border-radius: 5px;
                padding: 8px 16px;
                font-weight: bold;
                font-family: 'Calibri';
                font-size: 11pt;
                min-width: 80px;
                min-height: 30px;
            }
            QDialogButtonBox QPushButton:hover {
                background-color: #0056b3;
            }
        """)
        
        layout = QVBoxLayout(dialog)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(15)
        
        # تسمية
        label = QLabel("📅 اختر التاريخ المطلوب:")
        label.setFont(QFont("Calibri", 14, QFont.Bold))
        label.setAlignment(Qt.AlignCenter)
        layout.addWidget(label)
        
        # محدد التاريخ
        date_edit = QDateEdit()
        date_edit.setDate(QDate.currentDate())
        date_edit.setFont(QFont("Calibri", 12, QFont.Bold))
        date_edit.setCalendarPopup(True)
        date_edit.setDisplayFormat("yyyy-MM-dd")
        layout.addWidget(date_edit)
        
        # أزرار الحوار
        buttons = QDialogButtonBox(QDialogButtonBox.Ok | QDialogButtonBox.Cancel)
        buttons.button(QDialogButtonBox.Ok).setText("تأكيد")
        buttons.button(QDialogButtonBox.Cancel).setText("إلغاء")
        buttons.accepted.connect(dialog.accept)
        buttons.rejected.connect(dialog.reject)
        layout.addWidget(buttons)
        
        if dialog.exec_() == QDialog.Accepted:
            return date_edit.date().toString('yyyy-MM-dd')
        return None

    def accept_with_update(self, dialog, date_edit, update_institution):
        """قبول النافذة مع تحديد نوع العملية"""
        dialog.result_date = date_edit.date().toString('yyyy-MM-dd')
        dialog.update_institution = update_institution
        dialog.accept()

    def batch_file_requests(self):
        """طلبات الملفات الجماعية"""
        print("📋 تم النقر على طلبات الملفات الجماعية")
        
        # الحصول على التلاميذ ذوي الحركية المزدوجة لاستثائهم
        double_movement_students = self.get_double_movement_students()
        print(f"🔄 عدد التلاميذ ذوي الحركية المزدوجة المستثناة: {len(double_movement_students)}")
        
        # الحصول على السجلات المصفاة والتي لم يتم طلب ملفاتها بعد (باستثناء ذوي الحركية المزدوجة)
        conn = self.connect_to_database()
        if not conn:
            return
        
        try:
            cursor = conn.cursor()
            
            # التأكد من وجود الأعمدة المطلوبة وإضافتها إذا لم تكن موجودة
            self.ensure_required_columns(cursor)
            
            # الحصول على أعلى رقم طلب موجود
            cursor.execute("SELECT MAX(رقم_الطلب) FROM سجلات_الوافدين_والمغادرين WHERE رقم_الطلب IS NOT NULL")
            max_request_number = cursor.fetchone()[0]
            
            # تحديد رقم الطلب الجديد
            if max_request_number is None:
                new_request_number = 1
            else:
                new_request_number = max_request_number + 1
            
            print(f"📈 أعلى رقم طلب موجود: {max_request_number}")
            print(f"🆕 رقم الطلب الجديد: {new_request_number}")
            
            # إنشاء استعلام لاستثناء التلاميذ ذوي الحركية المزدوجة
            base_query = """
            SELECT id, رقم_التلميذ, النسب, الإسم
            FROM سجلات_الوافدين_والمغادرين 
            WHERE ملاحظات = 'لائحة التحويلات (الوافدون)' 
            AND (رقم_الطلب IS NULL OR رقم_الطلب = '')
            """
            
            if double_movement_students:
                # إضافة شرط لاستثناء التلاميذ ذوي الحركية المزدوجة
                placeholders = ', '.join(['?' for _ in double_movement_students])
                query = f"{base_query} AND (رقم_التلميذ NOT IN ({placeholders}) OR رقم_التلميذ IS NULL)"
                cursor.execute(query, double_movement_students)
            else:
                # إذا لم توجد حركية مزدوجة، استخدم الاستعلام الأساسي
                cursor.execute(base_query)
            
            pending_records = cursor.fetchall()
            
            print(f"📊 عدد السجلات التي تحتاج لطلب الملفات (بدون الحركية المزدوجة): {len(pending_records)}")
            
            if not pending_records:
                message = "لا توجد ملفات جديدة لطلبها.\n\n"
                if double_movement_students:
                    message += f"تم استثناء {len(double_movement_students)} تلميذ ذو حركية مزدوجة.\n"
                message += "جميع الملفات المتبقية تم طلبها مسبقاً."
                
                QMessageBox.information(self, "معلومات", message)
                conn.close()
                return
            
            # طلب تاريخ الطلب من المستخدم
            date = self.get_date_from_user("طلبات الملفات الجماعية")
            if not date:
                print("❌ تم إلغاء طلبات الملفات الجماعية")
                conn.close()
                return
            
            print(f"📅 التاريخ المحدد للطلبات الجماعية: {date}")
            
            # تحديث السجلات المصفاة
            record_ids = [record[0] for record in pending_records]
            
            try:
                # تحديث البيانات في قاعدة البيانات باستخدام رقم الطلب الجديد
                update_query = """
                UPDATE سجلات_الوافدين_والمغادرين 
                SET رقم_الطلب = ?, 
                    تاريخ_الطلب_الأول = ?, 
                    رقم_المراسلة = 1 
                WHERE id IN ({})
                """.format(','.join(['?' for _ in record_ids]))
                
                cursor.execute(update_query, [new_request_number, date] + record_ids)
                conn.commit()
                
                print(f"✅ تم تحديث {len(record_ids)} سجل بنجاح برقم الطلب {new_request_number}")
                
                # تحديث عرض الجدول في النافذة الأساسية إذا كانت متاحة
                if self.parent_window and hasattr(self.parent_window, 'refresh_data'):
                    self.parent_window.refresh_data()
                
                # عرض رسالة نجاح مع معلومات الاستثناء
                success_message = f"تم طلب الملفات بنجاح!\n\n"
                success_message += f"عدد الملفات المطلوبة: {len(record_ids)}\n"
                success_message += f"تاريخ الطلب: {date}\n"
                success_message += f"رقم الطلب الجديد: {new_request_number}\n"
                success_message += f"رقم المراسلة: 1\n"
                
                if double_movement_students:
                    success_message += f"\n🔄 تم استثناء {len(double_movement_students)} تلميذ ذو حركية مزدوجة"
                
                QMessageBox.information(self, "نجح العملية", success_message)
                
            except Exception as e:
                print(f"❌ خطأ في تحديث البيانات: {e}")
                conn.rollback()
                QMessageBox.critical(self, "خطأ", f"فشل في تحديث البيانات: {e}")
        
            conn.close()
            
        except Exception as e:
            print(f"❌ خطأ في معالجة طلب الملفات: {e}")
            QMessageBox.critical(self, "خطأ", f"فشل في معالجة طلب الملفات: {e}")
            if conn:
                conn.close()
    
    def individual_file_requests(self):
        """طلبات الملفات الفردية"""
        print("📄 تم النقر على طلبات الملفات الفردية")
        
        date = self.get_date_from_user("طلبات الملفات الفردية")
        if date:
            print(f"📅 التاريخ المحدد للطلبات الفردية: {date}")
            
            message = f"تم تحديد التاريخ: {date}\n\nسيتم معالجة الطلبات الفردية..."
            
            QMessageBox.information(self, "طلبات الملفات الفردية", message)
            
            # هنا يمكن إضافة الكود الخاص بمعالجة الطلبات الفردية
        else:
            print("❌ تم إلغاء طلبات الملفات الفردية")
    
    def retry_requests(self):
        """إعادة الطلبات"""
        print("🔄 تم النقر على إعادة الطلبات")
        
        # التحقق من وجود النافذة الأساسية
        if not self.parent_window:
            QMessageBox.warning(self, "خطأ", "لا يمكن الوصول إلى النافذة الأساسية.")
            return
        
        # الحصول على السجلات المحددة من الجدول في النافذة الأساسية
        selected_records = self.get_selected_records_from_parent()
        
        if not selected_records:
            QMessageBox.warning(self, "تنبيه", 
                              "يرجى اختيار سجل واحد على الأقل من الجدول في النافذة الأساسية لإعادة طلبه.\n\n" +
                              "ملاحظة: يجب أن تكون السجلات المختارة قد تم طلب ملفاتها مسبقاً ولم يتم استلامها بعد.")
            return
        
        # فتح نافذة تأكيد إعادة الطلب
        self.open_retry_confirmation_dialog(selected_records)

    def get_selected_records_from_parent(self):
        """الحصول على السجلات المحددة من الجدول في النافذة الأساسية"""
        if not self.parent_window or not hasattr(self.parent_window, 'table'):
            return []
        
        selected_records = []
        table = self.parent_window.table
        
        for row in range(table.rowCount()):
            if table.isRowHidden(row):
                continue
                
            checkbox = table.cellWidget(row, 0)
            if checkbox and checkbox.isChecked():
                # جمع بيانات السجل المحدد
                record_data = {}
                headers = []
                for col in range(table.columnCount()):
                    header_item = table.horizontalHeaderItem(col)
                    if header_item:
                        headers.append(header_item.text())
                
                for col in range(1, table.columnCount()):  # تجاهل عمود الاختيار
                    item = table.item(row, col)
                    if item and col < len(headers):
                        column_name = headers[col]
                        record_data[column_name] = item.text()
                
                # إضافة معرف الصف للمرجعية
                record_data['table_row'] = row
                selected_records.append(record_data)
        
        # تصفية السجلات للاحتفاظ فقط بالسجلات التي يمكن إعادة طلبها
        filtered_records = []
        for record in selected_records:
            # التحقق من أن السجل تم طلب ملفه مسبقاً ولم يتم استلامه
            if self.can_retry_record(record):
                filtered_records.append(record)
        
        return filtered_records

    def can_retry_record(self, record):
        """التحقق من إمكانية إعادة طلب السجل"""
        conn = self.connect_to_database()
        if not conn:
            return False
        
        try:
            cursor = conn.cursor()
            
            name = record.get('الإسم', '')
            surname = record.get('النسب', '')
            student_number = record.get('رقم_التلميذ', '')
            
            # بناء شروط البحث
            where_conditions = []
            params = []
            
            if name:
                where_conditions.append("الإسم = ?")
                params.append(name)
            if surname:
                where_conditions.append("النسب = ?")
                params.append(surname)
            if student_number:
                where_conditions.append("رقم_التلميذ = ?")
                params.append(student_number)
            
            if not where_conditions:
                return False
            
            where_clause = " AND ".join(where_conditions)
            
            # البحث عن السجل في قاعدة البيانات
            query = f"""
            SELECT رقم_الطلب, رقم_المراسلة, تاريخ_استلام_الملف
            FROM سجلات_الوافدين_والمغادرين 
            WHERE {where_clause}
            AND ملاحظات = 'لائحة التحويلات (الوافدون)'
            """
            
            cursor.execute(query, params)
            result = cursor.fetchone()
            
            conn.close()
            
            if result:
                request_num, correspondence_num, receipt_date = result
                # يمكن إعادة الطلب إذا:
                # 1. تم طلب الملف مسبقاً (رقم_الطلب ليس فارغ)
                # 2. لم يتم استلام الملف بعد (تاريخ_استلام_الملف فارغ)
                # 3. رقم المراسلة أقل من 3 (الحد الأقصى للطلبات)
                return (request_num is not None and 
                        (receipt_date is None or receipt_date == '') and
                        (correspondence_num is not None and correspondence_num < 3))
            
            return False
            
        except Exception as e:
            print(f"❌ خطأ في التحقق من إمكانية إعادة طلب السجل: {e}")
            if conn:
                conn.close()
            return False

    def open_retry_confirmation_dialog(self, selected_records):
        """فتح نافذة تأكيد إعادة الطلب - مبسطة"""
        from PyQt5.QtWidgets import QDialog, QVBoxLayout, QHBoxLayout, QLabel, QPushButton, QFrame, QDateEdit, QTextBrowser
        
        dialog = QDialog(self)
        dialog.setWindowTitle("تأكيد إعادة الطلبات")
        dialog.setFixedSize(500, 600)
        dialog.setLayoutDirection(Qt.RightToLeft)
        
        try:
            dialog.setWindowIcon(QIcon("01.ico"))
        except Exception:
            pass
        
        # تنسيق مبسط للنافذة
        dialog.setStyleSheet("""
            QDialog {
                background-color: #f8f9fa;
                border: 2px solid #6c757d;
                border-radius: 10px;
                font-family: 'Calibri';
            }
            QLabel {
                color: #495057;
                font-size: 12pt;
                font-weight: bold;
                font-family: 'Calibri';
                margin-bottom: 5px;
            }
            QLabel#title_label {
                color: #007bff;
                font-size: 16pt;
                font-weight: bold;
                font-family: 'Calibri';
                padding: 15px;
                background-color: white;
                border: 1px solid #dee2e6;
                border-radius: 5px;
                margin-bottom: 15px;
            }
            QFrame {
                background-color: white;
                border: 1px solid #dee2e6;
                border-radius: 5px;
                padding: 15px;
                font-family: 'Calibri';
            }
            QDateEdit {
                background-color: white;
                border: 2px solid #ced4da;
                border-radius: 5px;
                padding: 8px;
                font-size: 11pt;
                font-family: 'Calibri';
                font-weight: bold;
                color: #495057;
                min-height: 25px;
            }
            QDateEdit:focus {
                border: 2px solid #007bff;
            }
            QPushButton {
                background-color: #28a745;
                color: white;
                border: none;
                border-radius: 5px;
                padding: 10px 20px;
                font-size: 11pt;
                font-weight: bold;
                font-family: 'Calibri';
                min-width: 100px;
                min-height: 35px;
            }
            QPushButton:hover {
                background-color: #218838;
            }
            QPushButton#cancel_button {
                background-color: #6c757d;
                font-family: 'Calibri';
                font-weight: bold;
            }
            QPushButton#cancel_button:hover {
                background-color: #5a6268;
            }
            QTextBrowser {
                background-color: white;
                border: 1px solid #ced4da;
                border-radius: 5px;
                padding: 10px;
                font-family: 'Calibri';
                font-size: 10pt;
            }
        """)
        
        layout = QVBoxLayout(dialog)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(15)

        # العنوان
        title_label = QLabel("🔄 تأكيد إعادة الطلبات")
        title_label.setObjectName("title_label")
        title_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(title_label)

        # إطار البيانات
        data_frame = QFrame()
        data_layout = QVBoxLayout(data_frame)
        data_layout.setSpacing(10)

        # تاريخ إعادة الطلب
        date_layout = QHBoxLayout()
        date_label = QLabel("📅 تاريخ إعادة الطلب:")
        date_label.setFont(QFont("Calibri", 11, QFont.Bold))
        
        date_edit = QDateEdit()
        date_edit.setDate(QDate.currentDate())
        date_edit.setFont(QFont("Calibri", 11, QFont.Bold))
        date_edit.setCalendarPopup(True)
        date_edit.setDisplayFormat("yyyy-MM-dd")
        
        date_layout.addWidget(date_label)
        date_layout.addWidget(date_edit)
        date_layout.addStretch()
        data_layout.addLayout(date_layout)

        layout.addWidget(data_frame)

        # عرض السجلات المحددة
        info_label = QLabel(f"📋 السجلات المحددة لإعادة الطلب ({len(selected_records)} سجل):")
        info_label.setFont(QFont("Calibri", 12, QFont.Bold))
        info_label.setStyleSheet("color: #495057; margin-bottom: 10px;")
        layout.addWidget(info_label)

        # عرض تفاصيل السجلات
        records_info = QTextBrowser()
        records_info.setMaximumHeight(120)
        records_content = "<div dir='rtl' style='font-family: Calibri; font-size: 10pt;'>"
        
        for i, record in enumerate(selected_records, 1):
            name = record.get('الإسم', 'غير محدد')
            surname = record.get('النسب', 'غير محدد')
            institution = record.get('المؤسسة_الأصلية', 'غير محدد')
            
            records_content += f"""
            <p><b>{i}.</b> <b>الاسم:</b> {name} <b>النسب:</b> {surname} <b>المؤسسة:</b> {institution}</p>
            """
        
        records_content += "</div>"
        records_info.setHtml(records_content)
        layout.addWidget(records_info)

        # أزرار التحكم
        buttons_layout = QHBoxLayout()
        
        confirm_btn = QPushButton("🔄 تأكيد إعادة الطلب")
        confirm_btn.clicked.connect(lambda: self.confirm_retry_requests(dialog, selected_records, date_edit))
        
        cancel_btn = QPushButton("🚫 إلغاء")
        cancel_btn.setObjectName("cancel_button")
        cancel_btn.clicked.connect(dialog.reject)
        
        buttons_layout.addStretch()
        buttons_layout.addWidget(confirm_btn)
        buttons_layout.addWidget(cancel_btn)
        buttons_layout.addStretch()
        
        layout.addLayout(buttons_layout)
        
        # عرض النافذة
        dialog.exec_()

    def confirm_retry_requests(self, dialog, selected_records, date_edit):
        """تأكيد إعادة الطلبات"""
        if not selected_records:
            QMessageBox.warning(self, "تنبيه", "لا توجد سجلات محددة لإعادة الطلب.")
            return
        
        # تأكيد العملية
        reply = QMessageBox.question(self, "تأكيد إعادة الطلب", 
                                   f"هل أنت متأكد من إعادة طلب {len(selected_records)} ملف؟",
                                   QMessageBox.Yes | QMessageBox.No, 
                                   QMessageBox.No)
        
        if reply == QMessageBox.Yes:
            retry_date = date_edit.date().toString('yyyy-MM-dd')
            self.process_retry_requests_from_parent(selected_records, retry_date)
            dialog.accept()

    def process_retry_requests_from_parent(self, selected_records, retry_date):
        """معالجة إعادة الطلبات للسجلات المحددة من النافذة الأساسية"""
        print(f"🔄 بدء تنفيذ إعادة الطلبات للسجلات المحددة - التاريخ: {retry_date}")
        
        conn = self.connect_to_database()
        if not conn:
            return
        
        try:
            cursor = conn.cursor()
            
            # التأكد من وجود الأعمدة المطلوبة
            self.ensure_required_columns(cursor)
            
            # الحصول على أعلى رقم طلب موجود
            cursor.execute("SELECT MAX(رقم_الطلب) FROM سجلات_الوافدين_والمغادرين WHERE رقم_الطلب IS NOT NULL")
            max_request_number = cursor.fetchone()[0]
            new_request_number = max_request_number + 1 if max_request_number else 1
            
            print(f"🆕 رقم الطلب الجديد: {new_request_number}")
            
            updated_count = 0
            
            # معالجة كل سجل محدد
            for record in selected_records:
                name = record.get('الإسم', '')
                surname = record.get('النسب', '')
                student_number = record.get('رقم_التلميذ', '')
                
                # بناء شروط البحث
                where_conditions = []
                params = []
                
                if name:
                    where_conditions.append("الإسم = ?")
                    params.append(name)
                if surname:
                    where_conditions.append("النسب = ?")
                    params.append(surname)
                if student_number:
                    where_conditions.append("رقم_التلميذ = ?")
                    params.append(student_number)
                
                if not where_conditions:
                    print(f"⚠️ تخطي سجل بدون معرفات كافية")
                    continue
                
                where_clause = " AND ".join(where_conditions)
                
                # الحصول على رقم المراسلة الحالي
                select_query = f"""
                SELECT رقم_المراسلة FROM سجلات_الوافدين_والمغادرين 
                WHERE {where_clause} AND ملاحظات = 'لائحة التحويلات (الوافدون)'
                """
                
                cursor.execute(select_query, params)
                result = cursor.fetchone()
                
                if not result:
                    print(f"⚠️ لم يتم العثور على السجل: {name} {surname}")
                    continue
                
                current_correspondence = result[0]
                
                if current_correspondence == 1:
                    # إذا كان رقم المراسلة = 1، تحديث إلى 2
                    update_query = f"""
                    UPDATE سجلات_الوافدين_والمغادرين 
                    SET رقم_الطلب = ?, 
                        رقم_المراسلة = 2,
                        تاريخ_الطلب_الثاني = ?
                    WHERE {where_clause} AND ملاحظات = 'لائحة التحويلات (الوافدون)'
                    """
                    cursor.execute(update_query, [new_request_number, retry_date] + params)
                    print(f"✅ تم تحديث السجل {name} {surname}: المراسلة 1 → 2")
                    
                elif current_correspondence == 2:
                    # إذا كان رقم المراسلة = 2، تحديث إلى 3
                    update_query = f"""
                    UPDATE سجلات_الوافدين_والمغادرين 
                    SET رقم_الطلب = ?, 
                        رقم_المراسلة = 3,
                        تاريخ_الطلب_الثالث = ?
                    WHERE {where_clause} AND ملاحظات = 'لائحة التحويلات (الوافدون)'
                    """
                    cursor.execute(update_query, [new_request_number, retry_date] + params)
                    print(f"✅ تم تحديث السجل {name} {surname}: المراسلة 2 → 3")
                
                if cursor.rowcount > 0:
                    updated_count += 1
            
            conn.commit()
            
            print(f"✅ تم تحديث {updated_count} سجل لإعادة الطلب")
            
            # تحديث عرض الجدول في النافذة الأساسية
            if self.parent_window and hasattr(self.parent_window, 'refresh_data'):
                self.parent_window.refresh_data()
            
            # عرض رسالة نجاح
            success_message = f"تم إعادة الطلبات بنجاح!\n\n"
            success_message += f"عدد الملفات المعاد طلبها: {updated_count}\n"
            success_message += f"رقم الطلب الجديد: {new_request_number}\n"
            success_message += f"تاريخ إعادة الطلب: {retry_date}\n"
            success_message += f"تم تحديث أرقام المراسلة وفقاً للمتطلبات"
            
            QMessageBox.information(self, "نجح العملية", success_message)
            
        except Exception as e:
            print(f"❌ خطأ في إعادة الطلبات: {e}")
            conn.rollback()
            QMessageBox.critical(self, "خطأ", f"فشل في إعادة الطلبات: {e}")
        finally:
            conn.close()

# تشغيل النافذة كاختبار مستقل
if __name__ == "__main__":
    app = QApplication(sys.argv)
    app.setLayoutDirection(Qt.RightToLeft)
    
    window = FileRequestProcessingWindow()
    window.show()
    
    sys.exit(app.exec_())

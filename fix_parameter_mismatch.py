#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
fix_parameter_mismatch.py - معالجة مشاكل عدم تطابق المعلمات في النوافذ المستقلة
"""

import sys
import inspect
import importlib
import functools
import datetime  # استيراد لفحص الأنواع المدمجة

# قائمة النوافذ التي نحتاج لمعالجة استدعاءاتها
WINDOWS_TO_FIX = [
    'simple00_window',
    'sub00_window',
    'sub1_window',
    'sub11_window',
    'sub2_window',
    'sub4_window',
    'sub5_window',
    'sub55_window',
    'sub6_window',
    'sub7_window',
    'sub8_window',
    'tabbed5_window',
    'tabbed6_window',
]

# قائمة الأنواع المدمجة التي لا يجب تعديلها
IMMUTABLE_TYPES = [
    datetime.datetime,
    datetime.date,
    datetime.time,
    int, 
    float, 
    str, 
    bool, 
    tuple, 
    frozenset
]

def safe_call_wrapper(func):
    """
    مغلف للدوال يتعامل مع أخطاء عدم تطابق المعلمات
    """
    @functools.wraps(func)
    def wrapper(*args, **kwargs):
        try:
            return func(*args, **kwargs)
        except TypeError as e:
            # التقاط خطأ عدم تطابق المعلمات
            if "takes" in str(e) and "got" in str(e) and "positional argument" in str(e):
                print(f"معالجة خطأ عدم تطابق المعلمات: {e}")
                
                # استخراج معلومات المعلمات المطلوبة من الدالة
                sig = inspect.signature(func)
                param_count = len([p for p in sig.parameters.values() 
                               if p.default == inspect.Parameter.empty 
                               and p.kind == inspect.Parameter.POSITIONAL_OR_KEYWORD])
                
                # تعديل المعلمات للتوافق
                if len(args) > param_count:
                    args = args[:param_count]  # أخذ العدد الصحيح من المعلمات
                    print(f"تم تعديل المعلمات لتتناسب مع {func.__name__}")
                
                # محاولة استدعاء الدالة مرة أخرى بالمعلمات المعدلة
                return func(*args, **kwargs)
            else:
                # إذا كان الخطأ ليس بسبب عدم تطابق المعلمات، أعد رفعه
                raise
    return wrapper

def is_immutable_type(obj):
    """
    التحقق مما إذا كان الكائن من نوع غير قابل للتعديل
    """
    for immutable_type in IMMUTABLE_TYPES:
        if obj is immutable_type or isinstance(obj, immutable_type):
            return True
    
    # التحقق من الوحدات المدمجة في Python
    if obj.__module__ in ('builtins', 'datetime'):
        return True
        
    return False

def patch_window_class(module_name):
    """
    تعديل فئات النوافذ في الوحدة المحددة للتعامل مع أخطاء عدم تطابق المعلمات
    """
    try:
        # استيراد الوحدة
        module = importlib.import_module(module_name)
        patched = False
        
        # البحث عن الفئات في الوحدة
        for name, obj in inspect.getmembers(module):
            # تحقق مما إذا كان الكائن فئة ويحتوي على دوال __init__
            if inspect.isclass(obj) and hasattr(obj, '__init__'):
                # تخطي الأنواع المدمجة وغير القابلة للتعديل
                if is_immutable_type(obj):
                    print(f"تخطي نوع غير قابل للتعديل: {name} في {module_name}")
                    continue
                    
                # طبق المغلف على دالة __init__
                if not hasattr(obj.__init__, '_patched'):
                    try:
                        original_init = obj.__init__
                        obj.__init__ = safe_call_wrapper(original_init)
                        obj.__init__._patched = True  # وضع علامة للتجنب تكرار التعديل
                        patched = True
                        print(f"تم تعديل __init__ لـ {name} في {module_name}")
                    except (TypeError, AttributeError) as e:
                        print(f"تعذر تعديل {name} في {module_name}: {e}")
        
        return patched
    except ImportError as e:
        print(f"خطأ في استيراد الوحدة {module_name}: {e}")
        return False

def apply_parameter_fixes():
    """
    تطبيق إصلاحات عدم تطابق المعلمات على جميع النوافذ المستهدفة
    """
    patched_count = 0
    
    for module_name in WINDOWS_TO_FIX:
        if patch_window_class(module_name):
            patched_count += 1
    
    print(f"تم تعديل {patched_count} من أصل {len(WINDOWS_TO_FIX)} وحدة")
    return patched_count > 0

# تنفيذ الإصلاحات عند استيراد الملف
if __name__ == "__main__":
    apply_parameter_fixes()
else:
    # تنفيذ الإصلاحات تلقائياً عند استيراد الوحدة
    apply_parameter_fixes()
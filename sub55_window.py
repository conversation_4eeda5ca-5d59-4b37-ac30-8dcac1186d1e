#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import sqlite3
import datetime
import os
from PyQt5.QtWidgets import (
    QApplication, QMainWindow, QWidget, QFrame, QComboBox, QTableView,
    QGraphicsDropShadowEffect, QPushButton, QMessageBox, QLabel, QVBoxLayout,
    QHBoxLayout, QGroupBox, QLineEdit, QFormLayout, QToolButton, QDialog, QTextEdit, QTextBrowser
)
from PyQt5.QtGui import QFont, QColor, QIcon, QBrush, QTextOption
from PyQt5.QtCore import Qt, QAbstractTableModel, QModelIndex, QVariant, QSettings, QSize
from PyQt5.QtSql import QSqlDatabase, QSqlQuery
from PyQt5.QtWidgets import QHeaderView, QStyledItemDelegate

# استيراد نافذة الإعدادات الافتراضية من الملف الجديد
from default_settings_window import DefaultSettingsDialog

# --- تعديل الاستيراد لدوال تقرير الأسدس ---
try:
    # محاولة استيراد دوال تقرير الأسدس من absence_reports.py
    from absence_reports import print_semester_report, print_semester_report_multiple
    # محاولة استيراد دالة الأسدس الثاني إذا كانت متوفرة
    try:
        from absence_reports import print_second_semester_report
        SECOND_SEMESTER_FUNCTION_AVAILABLE = True
        print("تم استيراد دالة تقرير الأسدس الثاني من absence_reports.py بنجاح.")
    except ImportError:
        # إذا لم تكن دالة الأسدس الثاني متوفرة، نستخدم دالة بديلة
        def print_second_semester_report(parent, section):
            print("دالة طباعة تقرير الأسدس الثاني غير متوفرة بشكل مباشر.")
            print("محاولة استخدام دالة الأسدس العامة لتقرير الأسدس الثاني...")
            # استدعاء الدالة العامة بطريقة تحدد أنها للأسدس الثاني (ربما من خلال بارامتر في الواجهة)
            # هذه مجرد محاولة، وقد لا تنجح اعتمادًا على تنفيذ الدالة الأصلية
            return print_semester_report(parent, section)
        SECOND_SEMESTER_FUNCTION_AVAILABLE = True

    SEMESTER_FUNCTIONS_AVAILABLE = True
    print("تم استيراد دوال تقرير الأسدس من absence_reports.py بنجاح.") # رسالة تأكيد
except ImportError as e:
    # طباعة رسالة الخطأ الفعلية للمساعدة في التشخيص
    print(f"فشل استيراد دوال تقرير الأسدس من absence_reports.py: {e}")
    # إذا لم تنجح محاولة الاستيراد، نعرف دوال فارغة
    def print_semester_report(parent, section):
        print("وظيفة طباعة تقرير الأسدس غير متوفرة بسبب فشل الاستيراد.")
        return False

    def print_second_semester_report(parent, section):
        print("وظيفة طباعة تقرير الأسدس الثاني غير متوفرة بسبب فشل الاستيراد.")
        return False

    def print_semester_report_multiple(parent, sections_list):
        print("وظيفة طباعة تقارير الأسدس المتعددة غير متوفرة بسبب فشل الاستيراد.")
        return False
    SEMESTER_FUNCTIONS_AVAILABLE = False
    SECOND_SEMESTER_FUNCTION_AVAILABLE = False

# استيراد دالة طباعة التقرير الشهري (تبقى كما هي)
try:
    from absence_reports import print_absence_report
    ABSENCE_REPORT_FUNCTION_AVAILABLE = True
    print("تم استيراد دالة تقرير الغياب الشهري من absence_reports.py بنجاح.")
except ImportError as e:
    # طباعة رسالة الخطأ الفعلية للمساعدة في التشخيص
    print(f"فشل استيراد دالة تقرير الغياب الشهري من absence_reports.py: {e}")
    # تعريف دالة بديلة
    def print_absence_report(parent, section, month, model=None):
        print(f"وظيفة طباعة تقرير الغياب الشهري غير متوفرة بسبب فشل الاستيراد.")
        parent.show_message("تنبيه",
            f"لا يمكن إنشاء تقرير الغياب الشهري لقسم '{section}' وشهر '{month}'.\n"
            f"لم يتم العثور على دالة طباعة التقارير المطلوبة.",
            parent.QMessageBox.Warning)
        return False
    ABSENCE_REPORT_FUNCTION_AVAILABLE = False

# مندوب مخصص لعرض الخلايا في الجدول
class StudentCodeDelegate(QStyledItemDelegate):
    def __init__(self, parent=None):
        super(StudentCodeDelegate, self).__init__(parent)

    def paint(self, painter, option, index):
        # التحقق مما إذا كانت الخلية في عمود رمز التلميذ (العمود 3)
        if index.column() == 3:
            # الحصول على قيمة الخلية
            value = index.data(Qt.DisplayRole)
            if value:
                # تعديل خيارات الرسم لتظهر كارتباط تشعبي
                option.palette.setColor(option.palette.Text, QColor('#0000FF'))  # لون أزرق للنص

                # رسم خلفية الخلية
                option.widget.style().drawPrimitive(option.widget.style().PE_PanelItemViewItem, option, painter, option.widget)

                # رسم النص بتنسيق الارتباط التشعبي
                painter.setPen(option.palette.color(option.palette.Text))
                painter.setFont(option.font)
                painter.drawText(option.rect, Qt.AlignCenter, str(value))

                # رسم خط تحت النص
                rect = option.rect
                line_y = rect.bottom() - 3
                painter.drawLine(rect.left() + 2, line_y, rect.right() - 2, line_y)
                return

        # استخدام الرسم الافتراضي للخلايا الأخرى
        super(StudentCodeDelegate, self).paint(painter, option, index)

# نموذج مخصص يدعم التحرير
class EditableTableModel(QAbstractTableModel):
    def __init__(self, data=[], headers=[], parent=None):
        super(EditableTableModel, self).__init__(parent)
        self._data = data
        self._headers = headers
        # إضافة عتبة الغياب الأسبوعي والشهري
        self.threshold = 0
        self.monthly_threshold = 0

    def rowCount(self, parent=QModelIndex()):
        return len(self._data)

    def columnCount(self, parent=QModelIndex()):
        # التأكد من أن هذه السطر يستخدم نفس نوع ومقدار المسافة البادئة مثل السطر أعلاه
        return len(self._headers) if self._data else 0

    def data(self, index, role=Qt.DisplayRole):
        if not index.isValid():
            return QVariant()

        row = index.row()
        col = index.column()

        # التحقق من حدود البيانات (إذا لم يكن موجودًا بالفعل)
        if row >= len(self._data) or col >= len(self._data[row]):
             print(f"Data requested for out-of-bounds index [{row},{col}], role={role}") # تشخيص
             return QVariant()

        value = self._data[row][col]

        if role == Qt.DisplayRole:
            return str(value)
        elif role == Qt.TextAlignmentRole:
            return Qt.AlignCenter
        elif role == Qt.BackgroundRole:
            # التحقق من أعمدة الأسابيع (4 إلى 8)
            if 4 <= col <= 8:
                try:
                    cell_value = str(value).strip()
                    arabic_to_english = {'٠': '0', '١': '1', '٢': '2', '٣': '3', '٤': '4',
                                        '٥': '5', '٦': '6', '٧': '7', '٨': '8', '٩': '9'}
                    normalized_value = ''.join(arabic_to_english.get(c, c) for c in cell_value)

                    # استخدام self.threshold بدلاً من القيمة الثابتة 5
                    if normalized_value.isdigit() and self.threshold > 0:
                        num_value = int(normalized_value)
                        # تلوين الخلية باللون الأصفر عندما تكون القيمة أكبر من أو تساوي عتبة الغياب الأسبوعي
                        if num_value >= self.threshold:
                            return QBrush(QColor('yellow')) # أصفر (استخدام QBrush)
                except (ValueError, TypeError):
                    pass # تجاهل القيم غير الرقمية

            # تلوين المجموع الشهري (العمود 9)
            if col == 9:
                try:
                    cell_value = str(value).strip()
                    # توحيد الأرقام قبل التحقق
                    arabic_to_english = {'٠': '0', '١': '1', '٢': '2', '٣': '3', '٤': '4',
                                        '٥': '5', '٦': '6', '٧': '7', '٨': '8', '٩': '9'}
                    normalized_value = ''.join(arabic_to_english.get(c, c) for c in cell_value)

                    if normalized_value.isdigit() and self.monthly_threshold > 0:
                        num_value = int(normalized_value)
                        if num_value >= self.monthly_threshold:
                            return QBrush(QColor('#FF6347')) # أحمر فاتح (طماطم) (استخدام QBrush)
                except (ValueError, TypeError):
                    pass

        return QVariant() # إرجاع قيمة فارغة للأدوار الأخرى أو إذا لم تتحقق الشروط

    def setData(self, index, value, role=Qt.EditRole):
        if not index.isValid() or role != Qt.EditRole:
            return False

        row = index.row()
        col = index.column()

        # تنظيف وتوحيد القيمة قبل التخزين
        if isinstance(value, str):
            value = value.strip()
            arabic_to_english = {'٠': '0', '١': '1', '٢': '2', '٣': '3', '٤': '4',
                                '٥': '5', '٦': '6', '٧': '7', '٨': '8', '٩': '9'}
            value = ''.join(arabic_to_english.get(c, c) for c in value)

        # التحقق إذا كانت القيمة قد تغيرت بالفعل
        if str(self._data[row][col]) == str(value):
             return False # لا تقم بأي شيء إذا لم تتغير القيمة

        self._data[row][col] = value
        print(f"تم تحديث البيانات الداخلية: [{row},{col}] = {value}")

        # إعادة حساب المجموع الشهري وتحديثه إذا تم تغيير أحد الأسابيع
        if 4 <= col <= 8:
            weekly_values = []
            for c in range(4, 9):
                try:
                    val = str(self._data[row][c]).strip()
                    # تحويل أي أرقام عربية متبقية إلى إنجليزية
                    arabic_to_english = {'٠': '0', '١': '1', '٢': '2', '٣': '3', '٤': '4',
                                       '٥': '5', '٦': '6', '٧': '7', '٨': '8', '٩': '9'}
                    normalized_val = ''.join(arabic_to_english.get(ch, ch) for ch in val)

                    if normalized_val and normalized_val.isdigit():
                        weekly_values.append(int(normalized_val))
                    else:
                        weekly_values.append(0)  # تعامل مع القيم الفارغة أو غير الرقمية كصفر
                except (ValueError, TypeError):
                    weekly_values.append(0)

            # حساب المجموع الشهري
            monthly_total = sum(weekly_values)
            self._data[row][9] = str(monthly_total)
            print(f"تم تحديث المجموع الشهري للصف {row}: {monthly_total}")

            # إعلام الواجهة بتغيير الخلية الحالية وخلية المجموع
            self.dataChanged.emit(index, index) # تحديث الخلية المعدلة
            monthly_index = self.index(row, 9)
            self.dataChanged.emit(monthly_index, monthly_index)  # تحديث خلية المجموع

            # تطبيق التلوين حسب العتبة للخلية المحدثة والمجموع
            if self.threshold > 0 or self.monthly_threshold > 0:
                self.dataChanged.emit(
                    self.index(row, 4),  # بداية من أول خلية في الأسبوع الأول للصف
                    self.index(row, 9)   # نهاية بخلية المجموع الشهري للصف
                )
        else:
            # إذا تم تغيير عمود آخر (مثل الملاحظات أو الغياب المبرر)، فقط أبلغ عن تغيير تلك الخلية
            self.dataChanged.emit(index, index, [Qt.DisplayRole])

        return True

    def headerData(self, section, orientation, role=Qt.DisplayRole):
        if orientation == Qt.Horizontal and role == Qt.DisplayRole:
            return self._headers[section]
        return QVariant()

    def flags(self, index):
        if not index.isValid():
            return Qt.NoItemFlags

        col = index.column()
        # الفهارس في self._data: id=0(مخفي), رت=1, الاسم=2, رمز=3, أسابيع=4-8, مجموع=9, مبرر=10, ملاحظات=11
        # الأعمدة القابلة للتحرير: الأسابيع (4-8), الغياب المبرر (10), الملاحظات (11)
        # تعطيل التحرير لعمود المجموع الشهري (9)
        if (4 <= col <= 8) or col == 10 or col == 11: # العمود 9 لم يعد قابلاً للتحرير
            return Qt.ItemIsEnabled | Qt.ItemIsSelectable | Qt.ItemIsEditable
        # بقية الأعمدة قابلة للتحديد والعرض فقط
        return Qt.ItemIsEnabled | Qt.ItemIsSelectable

def calc_week_dates(start_date_str):
    """حساب تواريخ بداية الأسابيع (حتى خمسة أسابيع) بناءً على تاريخ بداية الشهر"""
    try:
        if isinstance(start_date_str, str) and start_date_str:
            day, month, year = map(int, start_date_str.split('-'))
            start_date = datetime.date(year, month, day)
            week_starts = []
            current_date = start_date
            # البحث عن أول اثنين (بداية الأسبوع)
            while (current_date.weekday() != 0):
                current_date += datetime.timedelta(days=1)
            current_month = start_date.month
            for i in range(5):
                week_start = current_date + datetime.timedelta(days=i*7)
                # إذا كان الأسبوع الخامس خارج الشهر، نعيد سلسلة فارغة
                if i == 4 and week_start.month != current_month:
                    week_starts.append("")
                else:
                    date_str = f"{week_start.day:02d}-{week_start.month:02d}-{week_start.year}"
                    week_starts.append(date_str)
            return week_starts
        return ["" for _ in range(5)]
    except Exception as e:
        print(f"خطأ في حساب تواريخ الأسابيع: {e}")
        return ["" for _ in range(5)]

class Hirasa300Window(QMainWindow):
    def __init__(self):
        super().__init__()
        self.settings = QSettings('Taheri200', 'Sub55Window')
        self.setWindowTitle("مسلك الغياب")
        self.setMinimumWidth(1000)
        # إعداد الخطوط المستخدمة
        self.font_calibri_13_bold = QFont("Calibri", 13)
        self.font_calibri_13_bold.setBold(True)
        self.font_calibri_13_normal = QFont('Calibri', 13)
        self.font_calibri_12_normal = QFont('Calibri', 12)
        # إنشاء الويدجت المركزي
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        # تخطيط بسيط
        main_layout = QVBoxLayout(central_widget)
        main_layout.setContentsMargins(5, 2, 5, 5)  # زيادة الهامش العلوي إلى 20 بكسل
        main_layout.setSpacing(10)  # زيادة المسافة بين العناصر
        # إضافة شريط أزرار بسيط في الأعلى
        button_bar = QHBoxLayout()
        button_bar.setContentsMargins(0, 5, 0, 5)  # إضافة هوامش علوية وسفلية للأزرار
        button_bar.setSpacing(5)  # تقليل المسافة بين الأزرار من 10 إلى 5
        # تم إزالة تعريف الخط غير المستخدم

        # زر الحفظ مع أيقونة مخصصة
        self.btn_save = QPushButton("حفظ")
        self.btn_save.setObjectName("btn_save")  # تعيين معرف للزر لتطبيق النمط الخاص
        # استخدام خط مناسب للزر
        font_save_button = QFont('Calibri', 12)  # حجم خط مناسب
        self.btn_save.setFont(font_save_button)
        self.btn_save.setFixedHeight(30)  # تعيين ارتفاع الزر إلى 30 بكسل
        self.btn_save.setFixedWidth(140)  # تعيين عرض الزر بقيمة 140

        # استخدام أيقونة مخصصة للحفظ
        save_icon_path = "save_icon.svg"
        if os.path.exists(save_icon_path):
            self.btn_save.setIcon(QIcon(save_icon_path))
            # تعيين حجم الأيقونة
            self.btn_save.setIconSize(QSize(24, 24))
        else:
            # استخدام الأيقونة الافتراضية إذا لم يتم العثور على الملف
            self.btn_save.setIcon(QIcon.fromTheme("document-save"))

        button_bar.addWidget(self.btn_save)

        # زر تقرير الغياب الشهري
        self.btn_print = QPushButton("تقرير الغياب الشهري")
        self.btn_print.setObjectName("btn_print")  # تعيين معرف للزر
        self.btn_print.setFont(QFont('Calibri', 12))
        self.btn_print.setFixedHeight(30)  # تعيين ارتفاع الزر إلى 30 بكسل
        self.btn_print.setFixedWidth(140)  # تعيين عرض الزر بقيمة 140
        self.btn_print.setIcon(QIcon.fromTheme("document-print"))  # إضافة أيقونة للزر
        button_bar.addWidget(self.btn_print)

        # تم إزالة زر حساب الغياب الدوري وتم دمج وظيفته في زري تقرير الأسدس الأول والثاني

        # زر تقرير الأسدس الأول - بشكل مستطيل
        self.btn_print_second_semester = QPushButton("تقرير الأسدس الأول")
        self.btn_print_second_semester.setObjectName("btn_print_second_semester")  # تعيين معرف للزر
        self.btn_print_second_semester.setFont(QFont('Calibri', 12))
        self.btn_print_second_semester.setFixedHeight(30)  # تعيين ارتفاع الزر إلى 30 بكسل
        self.btn_print_second_semester.setFixedWidth(140)  # تعيين عرض الزر بقيمة 140
        # إضافة الزر مباشرة إلى شريط الأزرار
        button_bar.addWidget(self.btn_print_second_semester)

        # زر تقرير الأسدس الثاني - بشكل مستطيل
        self.btn_print_first_semester = QPushButton("تقرير الأسدس الثاني")
        self.btn_print_first_semester.setObjectName("btn_print_first_semester")  # تعيين معرف للزر
        self.btn_print_first_semester.setFont(QFont('Calibri', 12))
        self.btn_print_first_semester.setFixedHeight(30)  # تعيين ارتفاع الزر إلى 30 بكسل
        self.btn_print_first_semester.setFixedWidth(140)  # تعيين عرض الزر بقيمة 140
        # إضافة الزر مباشرة إلى شريط الأزرار
        button_bar.addWidget(self.btn_print_first_semester)

        # زر الاعدادات الافتراضية
        self.btn_default_settings = QPushButton("الاعدادات الافتراضية")
        self.btn_default_settings.setObjectName("btn_default_settings")  # تعيين معرف للزر
        self.btn_default_settings.setFont(QFont('Calibri', 12))
        self.btn_default_settings.setFixedHeight(30)  # تعيين ارتفاع الزر إلى 30 بكسل
        self.btn_default_settings.setFixedWidth(140)  # تعيين عرض الزر بقيمة 140
        self.btn_default_settings.setIcon(QIcon.fromTheme("preferences-system"))  # إضافة أيقونة للزر
        button_bar.addWidget(self.btn_default_settings)

        # زر التعليمات
        self.btn_help = QPushButton("تعليمات")
        self.btn_help.setObjectName("btn_help")  # تعيين معرف للزر
        self.btn_help.setFont(QFont('Calibri', 12))
        self.btn_help.setFixedHeight(30)  # تعيين ارتفاع الزر إلى 30 بكسل
        self.btn_help.setFixedWidth(80)  # تعيين عرض الزر بقيمة 80
        self.btn_help.setIcon(QIcon.fromTheme("help-contents"))  # إضافة أيقونة للزر
        # تعيين لون برتقالي للزر
        self.btn_help.setStyleSheet("""
            QPushButton {
                background-color: #FF8C00; /* لون برتقالي */
                color: white;
                border: 1px solid #E67300;
                border-radius: 3px;
                padding: 5px 12px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #FFA333;
            }
            QPushButton:pressed {
                background-color: #E67300;
            }
        """)
        button_bar.addWidget(self.btn_help)
        # إضافة فاصل مرن
        button_bar.addStretch(1)

        # إنشاء تخطيطات عمودية لكل مربع تحرير وسرد مع عنوانه
        # تخطيط المستوى
        level_layout = QVBoxLayout()
        level_layout.setSpacing(5)  # المسافة بين العنوان والمربع
        level_label = QLabel("المستوى:")
        level_label.setFont(self.font_calibri_13_bold)  # تغيير الخط إلى غامق
        level_label.setAlignment(Qt.AlignCenter)  # محاذاة العنوان في الوسط
        self.combo_mustawa = QComboBox()
        self.combo_mustawa.setFont(self.font_calibri_13_bold)  # تغيير الخط إلى غامق
        self.combo_mustawa.setFixedSize(300, 30)  # تعديل عرض المستوى إلى 300
        self.combo_mustawa.setObjectName("combo_mustawa")  # تعيين معرف للتنسيق
        level_layout.addWidget(level_label)
        level_layout.addWidget(self.combo_mustawa)
        button_bar.addLayout(level_layout)

        # تخطيط القسم
        section_layout = QVBoxLayout()
        section_layout.setSpacing(5)  # المسافة بين العنوان والمربع
        section_label = QLabel("القسم:")
        section_label.setFont(self.font_calibri_13_bold)  # تغيير الخط إلى غامق
        section_label.setAlignment(Qt.AlignCenter)  # محاذاة العنوان في الوسط
        self.combo_qism = QComboBox()
        self.combo_qism.setFont(self.font_calibri_13_bold)  # تغيير الخط إلى غامق
        self.combo_qism.setFixedSize(130, 30)  # الإبقاء على عرض القسم 130
        self.combo_qism.setObjectName("combo_qism")  # تعيين معرف للتنسيق
        section_layout.addWidget(section_label)
        section_layout.addWidget(self.combo_qism)
        button_bar.addLayout(section_layout)

        # تخطيط الشهر
        month_layout = QVBoxLayout()
        month_layout.setSpacing(5)  # المسافة بين العنوان والمربع
        month_label = QLabel("الشهر:")
        month_label.setFont(self.font_calibri_13_bold)  # تغيير الخط إلى غامق
        month_label.setAlignment(Qt.AlignCenter)  # محاذاة العنوان في الوسط
        self.combo_shahr = QComboBox()
        self.combo_shahr.setFont(self.font_calibri_13_bold)  # تغيير الخط إلى غامق
        self.combo_shahr.setFixedSize(100, 30)  # الإبقاء على عرض الشهر 100
        self.combo_shahr.setObjectName("combo_shahr")  # تعيين معرف للتنسيق
        month_layout.addWidget(month_label)
        month_layout.addWidget(self.combo_shahr)
        button_bar.addLayout(month_layout)
        # إضافة شريط الأزرار للتخطيط الرئيسي
        main_layout.addLayout(button_bar)
        # إنشاء الجدول
        self.table_view = QTableView()
        self.table_view.setFont(self.font_calibri_13_bold)  # تغيير الخط إلى غامق
        header = self.table_view.horizontalHeader()
        header.setFont(self.font_calibri_13_bold)  # تغيير خط رؤوس الأعمدة إلى غامق
        header.setMinimumHeight(35)
        # إضافة الجدول للتخطيط الرئيسي
        main_layout.addWidget(self.table_view)
        # تطبيق الأنماط
        self.apply_styles()
        # إعداد قاعدة البيانات والنموذج
        self.setupDatabase()
        # تعبئة مربعات التحرير والسرد من قاعدة البيانات باستخدام sqlite3
        self.populateFilters()
        # ربط الإشارات
        self.combo_qism.currentIndexChanged.connect(self.updateModel)
        self.combo_shahr.currentIndexChanged.connect(self.updateModel)
        self.combo_mustawa.currentIndexChanged.connect(self.updateFilterQism)
        self.btn_save.clicked.connect(self.saveData)
        self.btn_print.clicked.connect(self.printAbsenceReport)
        # تم إزالة ربط زر حساب الغياب الدوري
        self.btn_print_second_semester.clicked.connect(self.printFirstSemesterReport)
        self.btn_print_first_semester.clicked.connect(self.printSecondSemesterReport)
        self.btn_default_settings.clicked.connect(self.openDefaultSettings)
        self.btn_help.clicked.connect(self.showHelpDialog)

        # إضافة ربط إشارة النقر على خلايا الجدول
        self.table_view.clicked.connect(self.on_table_cell_clicked)
        # فتح النافذة بملء الشاشة
        self.showMaximized()

    def updateFilterQism(self):
        # تحديث قائمة الأقسام بناءً على اختيار المستوى
        conn = sqlite3.connect("data.db")
        cur = conn.cursor()

        # الحصول على السنة الدراسية ورقم الحراسة من جدول بيانات_المؤسسة
        cur.execute("SELECT السنة_الدراسية, رقم_الحراسة FROM بيانات_المؤسسة LIMIT 1")
        res = cur.fetchone()

        year = res[0] if res and res[0] else ""
        guard_number = res[1] if res and len(res) > 1 else None

        print(f"السنة الدراسية: {year}, رقم الحراسة: {guard_number}")

        # حفظ القيمة الحالية لـ combo_qism قبل التغيير
        current_qism = self.combo_qism.currentText()

        level = self.combo_mustawa.currentText()
        if level:
            # التحقق من وجود عمود الأقسام_المسندة في جدول البنية_التربوية
            has_assigned_column = False
            try:
                cur.execute("PRAGMA table_info(البنية_التربوية)")
                columns = [info[1] for info in cur.fetchall()]
                has_assigned_column = "الأقسام_المسندة" in columns
                print(f"هل يوجد عمود الأقسام_المسندة في البنية_التربوية؟ {has_assigned_column}")
            except Exception as e:
                print(f"خطأ عند التحقق من وجود عمود الأقسام_المسندة: {e}")

            # صياغة استعلام SQL المناسب بناءً على وجود عمود الأقسام_المسندة
            if has_assigned_column and guard_number:
                try:
                    query = """
                        SELECT DISTINCT القسم
                        FROM البنية_التربوية
                        WHERE السنة_الدراسية = ?
                          AND المستوى = ?
                          AND الأقسام_المسندة = ?
                    """
                    print(f"استعلام مع عمود الأقسام_المسندة: {query}")
                    cur.execute(query, (year, level, guard_number))
                except Exception as e:
                    print(f"خطأ في تنفيذ استعلام مع الأقسام_المسندة: {e}")
                    query = """
                        SELECT DISTINCT القسم
                        FROM البنية_التربوية
                        WHERE السنة_الدراسية = ?
                          AND المستوى = ?
                    """
                    cur.execute(query, (year, level))
            else:
                query = """
                    SELECT DISTINCT القسم
                    FROM البنية_التربوية
                    WHERE السنة_الدراسية = ?
                      AND المستوى = ?
                """
                print(f"استعلام بدون عمود الأقسام_المسندة: {query}")
                cur.execute(query, (year, level))
        else:
            # إذا لم يتم اختيار مستوى، نجلب الأقسام للسنة الدراسية فقط
            if has_assigned_column and guard_number:
                try:
                    cur.execute("PRAGMA table_info(البنية_التربوية)")
                    columns = [info[1] for info in cur.fetchall()]
                    if "الأقسام_المسندة" in columns:
                        cur.execute("""
                            SELECT DISTINCT القسم
                            FROM البنية_التربوية
                            WHERE السنة_الدراسية = ?
                              AND الأقسام_المسندة = ?
                        """, (year, guard_number))
                    else:
                        cur.execute("""
                            SELECT DISTINCT القسم
                            FROM البنية_التربوية
                            WHERE السنة_الدراسية = ?
                        """, (year,))
                except Exception as e:
                    print(f"خطأ عند جلب الأقسام: {e}")
                    cur.execute("""
                        SELECT DISTINCT القسم
                        FROM البنية_التربوية
                        WHERE السنة_الدراسية = ?
                    """, (year,))
            else:
                cur.execute("""
                    SELECT DISTINCT القسم
                    FROM البنية_التربوية
                    WHERE السنة_الدراسية = ?
                """, (year,))

        sections = [row[0] for row in cur.fetchall() if row[0] is not None]
        print(f"تم جلب {len(sections)} قسم: {sections}")

        # تذكر: احفظ إشارة currentIndexChanged قبل تغيير مربع الاختيار
        self.combo_qism.blockSignals(True)

        self.combo_qism.clear()
        # إضافة عنصر فارغ في البداية
        self.combo_qism.addItem("")
        self.combo_qism.addItems(sections)

        # تحديد العنصر الفارغ كعنصر افتراضي، أو المحاولة للاحتفاظ بالقيمة السابقة إذا كانت موجودة في القائمة الجديدة
        index = self.combo_qism.findText(current_qism)
        if (index >= 0):
            self.combo_qism.setCurrentIndex(index)
        else:
            self.combo_qism.setCurrentIndex(0)

        # استعادة إشارة currentIndexChanged
        self.combo_qism.blockSignals(False)

        conn.close()

    def setupDatabase(self):
        # إعداد الاتصال بقاعدة بيانات SQLite
        self.db = QSqlDatabase.addDatabase("QSQLITE")
        self.db.setDatabaseName("data.db")
        if not self.db.open():
            print("Database error:", self.db.lastError().text())
        # إنشاء نموذج فارغ باستخدام EditableTableModel مع عناوين مبدئية
        # سنقوم بتحديث العناوين لاحقًا بناءً على تواريخ الأسابيع
        headers = ["رت", "الاسم والنسب", "رمز التلميذ",
                   "الأسبوع الأول", "الأسبوع الثاني", "الأسبوع الثالث",
                   "الأسبوع الرابع", "الأسبوع الخامس", "ملاحظات"]
        self.model = EditableTableModel([], headers)
        self.table_view.setModel(self.model)
        # ضبط محاذاة الجدول من اليمين إلى اليسار
        self.table_view.setLayoutDirection(Qt.RightToLeft)
        # زيادة ارتفاع العناوين للسماح بعرض سطرين
        header = self.table_view.horizontalHeader()
        header.setMinimumHeight(45)  # زيادة ارتفاع العناوين
        # محاذاة النص في العناوين إلى المركز
        header.setDefaultAlignment(Qt.AlignCenter)
        # التأكد من عدم وجود أي تنسيق مطبق هنا
        self.table_view.setStyleSheet("")
        # التأكد من عدم وجود مندوب مخصص قد يتداخل (يمكن إلغاء التعليق إذا لزم الأمر لاحقًا)
        # self.table_view.setItemDelegate(None) # تم التعليق عليه مؤقتًا بناءً على الاقتراح السابق

    def populateFilters(self):
        # تعبئة مربعات التحرير والسرد من قاعدة البيانات باستخدام sqlite3
        conn = sqlite3.connect("data.db")
        cur = conn.cursor()

        # الحصول على السنة الدراسية ورقم الحراسة من جدول بيانات_المؤسسة
        cur.execute("SELECT السنة_الدراسية, رقم_الحراسة FROM بيانات_المؤسسة LIMIT 1")
        res = cur.fetchone()
        year = res[0] if res and res[0] else ""
        guard_number = res[1] if res and len(res) > 1 else None

        # طباعة الاستعلام واظهار السنة الدراسية
        print(f"السنة الدراسية الحالية: {year}, رقم الحراسة: {guard_number}")

        # التحقق من وجود عمود الأقسام_المسندة في جدول البنية_التربوية
        has_assigned_column = False
        try:
            cur.execute("PRAGMA table_info(البنية_التربوية)")
            columns = [info[1] for info in cur.fetchall()]
            has_assigned_column = "الأقسام_المسندة" in columns
            print(f"هل يوجد عمود الأقسام_المسندة في البنية_التربوية؟ {has_assigned_column}")
        except Exception as e:
            print(f"خطأ عند التحقق من وجود عمود الأقسام_المسندة: {e}")

        # تحقق من وجود عمود السنة_الدراسية في جدول البنية_التربوية
        try:
            # بناء استعلام SQL حسب وجود عمود الأقسام_المسندة
            if has_assigned_column and guard_number:
                query = """
                    SELECT DISTINCT المستوى
                    FROM البنية_التربوية
                    WHERE السنة_الدراسية = ?
                      AND الأقسام_المسندة = ?
                    ORDER BY ترتيب_المستويات
                """
                print(f"استعلام جلب المستويات مع الأقسام_المسندة: {query}")
                cur.execute(query, (year, guard_number))
            else:
                query = """
                    SELECT DISTINCT المستوى
                    FROM البنية_التربوية
                    WHERE السنة_الدراسية = ?
                    ORDER BY ترتيب_المستويات
                """
                print(f"استعلام جلب المستويات بدون الأقسام_المسندة: {query}")
                cur.execute(query, (year,))

            levels = [row[0] for row in cur.fetchall() if row[0] is not None]
            print(f"المستويات التي تم جلبها: {levels}")

            # في حالة عدم وجود مستويات، نحاول التحقق من وجود بيانات في الجدول
            if not levels:
                # التحقق من وجود بيانات مع الأقسام المسندة
                if has_assigned_column and guard_number:
                    cur.execute("SELECT COUNT(*) FROM البنية_التربوية WHERE السنة_الدراسية = ? AND الأقسام_المسندة = ?",
                               (year, guard_number))
                    count = cur.fetchone()[0]
                    print(f"عدد السجلات في البنية_التربوية للسنة {year} والأقسام_المسندة {guard_number}: {count}")

                    # إذا لم يكن هناك نتائج مع الأقسام_المسندة، نحاول البحث بدونها
                    if count == 0:
                        cur.execute("SELECT COUNT(*) FROM البنية_التربوية WHERE السنة_الدراسية = ?", (year,))
                        count_without_assigned = cur.fetchone()[0]
                        print(f"عدد السجلات بدون تصفية الأقسام_المسندة: {count_without_assigned}")

                        if count_without_assigned > 0:
                            print("إعادة محاولة جلب المستويات بدون تصفية الأقسام_المسندة")
                            cur.execute("""
                                SELECT DISTINCT المستوى
                                FROM البنية_التربوية
                                WHERE السنة_الدراسية = ?
                                ORDER BY ترتيب_المستويات
                            """, (year,))
                            levels = [row[0] for row in cur.fetchall() if row[0] is not None]
                            print(f"المستويات التي تم جلبها بدون تصفية الأقسام_المسندة: {levels}")
                else:
                    cur.execute("SELECT COUNT(*) FROM البنية_التربوية WHERE السنة_الدراسية = ?", (year,))
                    count = cur.fetchone()[0]
                    print(f"عدد السجلات في جدول البنية_التربوية للسنة {year}: {count}")

                if count == 0:
                    # التحقق من وجود بيانات في جدول البنية_التربوية عموماً
                    cur.execute("SELECT COUNT(*) FROM البنية_التربوية")
                    total_count = cur.fetchone()[0]
                    print(f"إجمالي عدد السجلات في جدول البنية_التربوية: {total_count}")

                    if total_count > 0:
                        # إذا كان هناك بيانات ولكن ليس للسنة المطلوبة، جلب قائمة السنوات المتوفرة
                        cur.execute("SELECT DISTINCT السنة_الدراسية FROM البنية_التربوية")
                        available_years = [row[0] for row in cur.fetchall() if row[0] is not None]
                        print(f"السنوات الدراسية المتوفرة في جدول البنية_التربوية: {available_years}")
        except Exception as e:
            print(f"خطأ أثناء جلب المستويات: {e}")
            levels = []

        # تحديث مربع التحرير والسرد للمستوى
        self.combo_mustawa.clear()
        # إضافة عنصر فارغ في البداية
        self.combo_mustawa.addItem("")
        if levels:
            self.combo_mustawa.addItems(levels)
        else:
            # عرض رسالة تشخيصية إذا لم يتم العثور على مستويات
            print("تحذير: لم يتم العثور على مستويات للسنة الدراسية المحددة")

            # محاولة جلب معلومات إضافية حول جدول البنية_التربوية
            try:
                cur.execute("PRAGMA table_info(البنية_التربوية)")
                columns = [info[1] for info in cur.fetchall()]
                print(f"أعمدة جدول البنية_التربوية: {columns}")
            except Exception as e:
                print(f"خطأ أثناء جلب معلومات الجدول: {e}")

        # تحديد العنصر الفارغ كعنصر افتراضي
        self.combo_mustawa.setCurrentIndex(0)

        # تصفية الأقسام
        # التحقق من وجود عمود الأقسام_المسندة في جدول البنية_التربوية
        has_assigned_column = False
        try:
            cur.execute("PRAGMA table_info(البنية_التربوية)")
            columns = [info[1] for info in cur.fetchall()]
            has_assigned_column = "الأقسام_المسندة" in columns
            print(f"هل يوجد عمود الأقسام_المسندة في البنية_التربوية (للأقسام)؟ {has_assigned_column}")
        except Exception as e:
            print(f"خطأ عند التحقق من وجود عمود الأقسام_المسندة للأقسام: {e}")

        # تطبيق التصفية المناسبة للأقسام
        try:
            if has_assigned_column and guard_number:
                print(f"تصفية الأقسام حسب الأقسام_المسندة '{guard_number}' والسنة الدراسية '{year}'")
                cur.execute("""
                    SELECT DISTINCT القسم
                    FROM البنية_التربوية
                    WHERE السنة_الدراسية = ?
                      AND الأقسام_المسندة = ?
                """, (year, guard_number))

                sections = [row[0] for row in cur.fetchall() if row[0] is not None]

                # إذا لم يكن هناك نتائج، نحاول البحث بدون تصفية الأقسام_المسندة
                if not sections:
                    print("لم يتم العثور على أقسام باستخدام الأقسام_المسندة. محاولة البحث بدون هذا الشرط.")
                    cur.execute("""
                        SELECT DISTINCT القسم
                        FROM البنية_التربوية
                        WHERE السنة_الدراسية = ?
                    """, (year,))
            else:
                print(f"تصفية الأقسام حسب السنة الدراسية '{year}' فقط")
                cur.execute("""
                    SELECT DISTINCT القسم
                    FROM البنية_التربوية
                    WHERE السنة_الدراسية = ?
                """, (year,))

            sections = [row[0] for row in cur.fetchall() if row[0] is not None]
            print(f"الأقسام التي تم جلبها: {sections}")
        except Exception as e:
            print(f"خطأ أثناء جلب الأقسام: {e}")
            sections = []

        # تحديث مربع التحرير والسرد للقسم
        self.combo_qism.clear()
        # إضافة عنصر فارغ في البداية
        self.combo_qism.addItem("")
        if sections:
            self.combo_qism.addItems(sections)
        else:
            print("تحذير: لم يتم العثور على أقسام مطابقة للمعايير")

        # تحديد العنصر الفارغ كعنصر افتراضي
        self.combo_qism.setCurrentIndex(0)

        # جلب الشهور
        try:
            cur.execute("SELECT DISTINCT الشهر FROM جدولة_مسك_الغياب WHERE السنة_الدراسية = ?", (year,))
            months = [str(row[0]) for row in cur.fetchall() if row[0] is not None]
            print(f"الشهور التي تم جلبها: {months}")
        except Exception as e:
            print(f"خطأ أثناء جلب الشهور: {e}")
            months = []

        # تحديث مربع التحرير والسرد للشهر
        self.combo_shahr.clear()
        if months:
            self.combo_shahr.addItems(months)
        else:
            print("تحذير: لم يتم العثور على شهور لجدولة مسك الغياب")

        conn.close()

    def updateModel(self):
        try:
            print("بدء تحديث النموذج updateModel...") # طباعة للتشخيص
            conn = sqlite3.connect("data.db")
            cur = conn.cursor()

            # التحقق من وجود عمود id في جدول مسك_الغياب_الأسبوعي
            cur.execute("PRAGMA table_info(مسك_الغياب_الأسبوعي)")
            columns = [info[1] for info in cur.fetchall()]
            print(f"أعمدة جدول مسك_الغياب_الأسبوعي: {columns}")

            # تحديد عمود المعرّف الصحيح
            id_column = "id"
            if "id" not in columns:
                if "المعرف" in columns:
                    id_column = "المعرف"
                elif "معرف" in columns:
                    id_column = "معرف"
                else:
                    # إذا لم يتم العثور على عمود معرّف، استخدم الرمز كمعرّف بديل
                    id_column = "رمز_التلميذ"
                print(f"استخدام {id_column} كعمود معرّف بديل عن id")

            base_query = f"""
                SELECT
                    m.{id_column} as "id",
                    l.رت as "رت",
                    sg.الاسم_والنسب as "الاسم والنسب",
                    m.رمز_التلميذ as "رمز التلميذ",
                    m.بداية_الشهر as "بداية الشهر",
                    m."1" as "الأسبوع الأول",
                    m."2" as "الأسبوع الثاني",
                    m."3" as "الأسبوع الثالث",
                    m."4" as "الأسبوع الرابع",
                    m."5" as "الأسبوع الخامس",
                    CAST(COALESCE(CAST(m."1" AS INTEGER), 0) +
                        COALESCE(CAST(m."2" AS INTEGER), 0) +
                        COALESCE(CAST(m."3" AS INTEGER), 0) +
                        COALESCE(CAST(m."4" AS INTEGER), 0) +
                        COALESCE(CAST(m."5" AS INTEGER), 0)
                    AS TEXT) as "المجموع الشهري",
                    m.الغياب_المبرر as "الغياب المبرر",
                    m.ملاحظات as "ملاحظات"
                FROM مسك_الغياب_الأسبوعي m
                LEFT JOIN اللوائح l ON m.رمز_التلميذ = l.الرمز
                LEFT JOIN السجل_العام sg ON m.رمز_التلميذ = sg.الرمز
                WHERE l.السنة_الدراسية = (SELECT السنة_الدراسية FROM بيانات_المؤسسة LIMIT 1)
            """
            filters = []
            params = []
            month = self.combo_shahr.currentText()
            section = self.combo_qism.currentText()
            if month:
                filters.append("m.الشهر = ?")
                params.append(month)
            if section:
                filters.append("l.القسم = ?")
                params.append(section)
            if filters:
                base_query += " AND " + " AND ".join(filters)
            base_query += " ORDER BY l.رت"  # ترتيب حسب عمود "رت"
            cur.execute(base_query, params)
            rows = [list(row) for row in cur.fetchall()]
            print(f"تم جلب {len(rows)} صفًا من قاعدة البيانات.") # طباعة للتشخيص

            if not rows:
                print("لا توجد بيانات للعرض، سيتم إنشاء نموذج فارغ.") # طباعة للتشخيص
                headers = ["رت", "الاسم والنسب", "رمز التلميذ",
                           "الأسبوع الأول", "الأسبوع الثاني", "الأسبوع الثالث",
                           "الأسبوع الرابع", "الأسبوع الخامس", "ملاحظات"]
                self.model = EditableTableModel([], headers)
                print("تم تعيين نموذج فارغ للجدول.") # طباعة للتشخيص
                self.table_view.setModel(self.model)
                # تعيين المندوب المخصص لعرض رمز التلميذ كارتباط تشعبي
                self.table_view.setItemDelegate(StudentCodeDelegate(self.table_view))
                print("تم تعيين StudentCodeDelegate للجدول الفارغ.") # طباعة للتشخيص
                # تعيين عرض الأعمدة الثابت (نبدأ من العمود 1 لأن العمود 0 مخفي)
                fixed_widths = [60, 170, 120, 120, 120, 120, 120, 120, 120]
                for col, width in enumerate(fixed_widths):
                    self.table_view.setColumnWidth(col, width)
                self.table_view.setLayoutDirection(Qt.RightToLeft)
                conn.close()
                print("اكتمل تحديث النموذج (فارغ).") # طباعة للتشخيص
                return

            # استخدام عمود "بداية الشهر" (الفهرس 4) لحساب تواريخ الأسابيع
            start_date_str = rows[0][4]
            if not start_date_str:
                start_date_str = "01-01-2023"
            week_dates = calc_week_dates(start_date_str)
            # الآن نحتفظ بعمود id (الفهرس 0) ولكن نخفي عمود "بداية الشهر" (الفهرس 4)
            for row in rows:
                del row[4]  # حذف عمود "بداية الشهر" فقط
            # بناء العناوين الجديدة مع تواريخ الأسابيع
            headers = [
                "id",  # عمود مخفي
                "رت",
                "الاسم والنسب",
                "رمز التلميذ"
            ]
            # إضافة تواريخ الأسابيع كعناوين مع أسماء الأسابيع التقليدية
            for i, week_date in enumerate(week_dates):
                if (week_date):  # إذا كان هناك تاريخ
                    headers.append(f"الأسبوع {i+1}\n{week_date}")
                else:
                    headers.append(f"الأسبوع {i+1}")
            # إضافة بقية العناوين
            headers.extend([
                "المجموع الشهري",
                "الغياب المبرر",
                "ملاحظات"
            ])
            self.model = EditableTableModel(rows, headers)

            self.table_view.setModel(self.model)
            print("تم تعيين النموذج للجدول بنجاح.") # طباعة للتشخيص

            # --- استدعاء update_thresholds بعد تعيين النموذج ---
            self.update_thresholds()
            print(f"تم تحديث العتبات من DB: أسبوعي={self.model.threshold}, شهري={self.model.monthly_threshold}")

            # تعيين المندوب المخصص لعرض رمز التلميذ كارتباط تشعبي
            self.table_view.setItemDelegate(StudentCodeDelegate(self.table_view))
            print("تم تعيين StudentCodeDelegate للجدول.") # طباعة للتشخيص

            # إضافة التالي لإجبار تحديث الجدول (قد لا يكون ضروريًا مع setItemDelegate)
            # self.model.layoutChanged.emit()

            # إخفاء عمود id
            self.table_view.setColumnHidden(0, True)

            # تعيين عرض الأعمدة الثابت (نبدأ من العمود 1 لأن العمود 0 مخفي)
            fixed_widths = [60, 170, 120, 120, 120, 120, 120, 120, 120, 120, 120, 200]
            for i, width in enumerate(fixed_widths):
                if i+1 < self.table_view.model().columnCount():
                    self.table_view.setColumnWidth(i+1, width)
            self.table_view.setLayoutDirection(Qt.RightToLeft)
            conn.close()
            print("اكتمل تحديث النموذج (مع بيانات).") # طباعة للتشخيص
            # قد لا تحتاج layoutChanged هنا إذا كان setModel كافيًا
            # self.model.layoutChanged.emit()

        except Exception as e:
            self.show_message("خطأ", f"حدث خطأ أثناء تحديث النموذج:\n{e}", QMessageBox.Critical)
            import traceback
            traceback.print_exc()

    def insertData(self):
        """
        دالة لإدخال البيانات في جدول مسك_الغياب_الأسبوعي.
        يتم جلب السنة الدراسية من جدول بيانات_المؤسسة،
        واختيار 10 سجلات من جدول جدولة_مسك_الغياب (مرتبة حسب الشهر)
        لكل سجل من جدول اللوائح (بعد تصفية بالسنة) باستخدام Cross Join،
        مع إدخال قيم فارغة للأعمدة "1" إلى "5" وعمود الملاحظات.
        """
        try:
            conn = sqlite3.connect("data.db")
            cur = conn.cursor()
            cur.execute("SELECT السنة_الدراسية FROM بيانات_المؤسسة LIMIT 1")
            res = cur.fetchone()
            if not res:
                self.show_message("خطأ", "لا توجد بيانات في جدول بيانات_المؤسسة", QMessageBox.Warning)
                return
            year = res[0]
            insert_query = """
            WITH limited_schedule AS (
                SELECT *
                FROM (
                    SELECT j.*, ROW_NUMBER() OVER (ORDER BY j.الشهر) as rn
                    FROM جدولة_مسك_الغياب j
                    WHERE j.السنة_الدراسية = ?
                ) sub
                WHERE rn <= 10
            )
            INSERT OR IGNORE INTO مسك_الغياب_الأسبوعي
                (السنة_الدراسية, الأسدس, الشهر, "1", "2", "3", "4", "5", بداية_الشهر, رمز_التلميذ, ملاحظات)
            SELECT
                ls.السنة_الدراسية,
                ls.الأسدس,
                ls.الشهر,
                '', '', '', '', '',
                ls.بداية_الشهر,
                l.الرمز,
                ''
            FROM limited_schedule ls
            CROSS JOIN (SELECT * FROM اللوائح WHERE السنة_الدراسية = ?) l
            """
            cur.execute(insert_query, (year, year))
            conn.commit()
            self.show_message("نجاح", "تم إدخال البيانات في جدول مسك_الغياب_الأسبوعي بنجاح!")
        except Exception as e:
            self.show_message("خطأ", f"حدث خطأ أثناء الإدخال:\n{e}", QMessageBox.Critical)
        finally:
            conn.close()
            self.updateModel()

    def ensure_monthly_total_column(self):
        """إضافة عمود مجموع_شهري وعمود الغياب_المبرر إذا لم يكونا موجودين"""
        try:
            conn = sqlite3.connect("data.db")
            cur = conn.cursor()
            # إضافة عمود مجموع_شهري إذا لم يكن موجوداً
            try:
                cur.execute("""
                    ALTER TABLE مسك_الغياب_الأسبوعي
                    ADD COLUMN مجموع_شهري TEXT
                """)
            except sqlite3.OperationalError as e:
                if "duplicate column name" not in str(e).lower():
                    print(f"خطأ في إضافة عمود مجموع_شهري: {e}")
            # إضافة عمود الغياب_المبرر إذا لم يكن موجوداً
            try:
                cur.execute("""
                    ALTER TABLE مسك_الغياب_الأسبوعي
                    ADD COLUMN الغياب_المبرر TEXT DEFAULT '0'
                """)
                print("تم إضافة عمود الغياب_المبرر بنجاح")
            except sqlite3.OperationalError as e:
                if "duplicate column name" not in str(e).lower():
                    print(f"خطأ في إضافة عمود الغياب_المبرر: {e}")
            conn.commit()
        except Exception as e:
            print(f"خطأ في تعديل الجدول: {e}")
        finally:
            conn.close()

    def ensure_unjustified_absence_column(self):
        """إضافة عمود غياب_غير_مبرر إذا لم يكن موجوداً"""
        try:
            conn = sqlite3.connect("data.db")
            cur = conn.cursor()
            # إضافة عمود غياب_غير_مبرر إذا لم يكن موجوداً
            try:
                cur.execute("""
                    ALTER TABLE مسك_الغياب_الأسبوعي
                    ADD COLUMN غياب_غير_مبرر TEXT DEFAULT '0'
                """)
                print("تم إضافة عمود غياب_غير_مبرر بنجاح")
            except sqlite3.OperationalError as e:
                if "duplicate column name" not in str(e).lower():
                    print(f"خطأ في إضافة عمود غياب_غير_مبرر: {e}")
            conn.commit()
        except Exception as e:
            print(f"خطأ في تعديل الجدول: {e}")
        finally:
            conn.close()

    def saveData(self):
        """دالة لحفظ التغييرات في قاعدة البيانات وتحديث المجموع الشهري والغياب المبرر وغير المبرر"""
        try:
            # التأكد من وجود عمود مجموع_شهري والغياب_المبرر
            self.ensure_monthly_total_column()
            # التأكد من وجود عمود غياب_غير_مبرر
            self.ensure_unjustified_absence_column()

            conn = sqlite3.connect("data.db")
            cur = conn.cursor()
            conn.execute("BEGIN TRANSACTION")
            updates = []
            for row in range(self.model.rowCount()):
                # استخراج معرف id من العمود المخفي (الفهرس 0)
                record_id = self.model.data(self.model.index(row, 0))
                if not record_id:
                    continue
                # استخراج قيم الأسابيع
                week1 = str(self.model.data(self.model.index(row, 4)) or '0')
                week2 = str(self.model.data(self.model.index(row, 5)) or '0')
                week3 = str(self.model.data(self.model.index(row, 6)) or '0')
                week4 = str(self.model.data(self.model.index(row, 7)) or '0')
                week5 = str(self.model.data(self.model.index(row, 8)) or '0')
                justified = str(self.model.data(self.model.index(row, 10)) or '0')  # الغياب المبرر
                notes = str(self.model.data(self.model.index(row, 11)) or '')  # الملاحظات (تحديث الفهرس)

                # حساب المجموع الشهري - تحويل كل قيمة إلى نص أولاً ثم تنظيفها وتحويلها لرقم
                weekly_values = [week1, week2, week3, week4, week5]
                monthly_total = sum(int(str(x).strip() or '0') for x in weekly_values)

                # حساب الغياب غير المبرر (مجموع_شهري ناقص الغياب_المبرر)
                justified_value = int(justified.strip() or '0')
                unjustified_absence = max(0, monthly_total - justified_value)

                # تجميع البيانات للتحديث
                updates.append((
                    week1.strip(), week2.strip(), week3.strip(), week4.strip(), week5.strip(),
                    str(monthly_total), justified.strip(), str(unjustified_absence), notes.strip(), record_id
                ))
            if not updates:
                self.show_message("تنبيه", "لا توجد بيانات للحفظ", QMessageBox.Warning)
                return
            # تحديث البيانات
            update_query = """
            UPDATE مسك_الغياب_الأسبوعي
            SET "1" = ?,
                "2" = ?,
                "3" = ?,
                "4" = ?,
                "5" = ?,
                مجموع_شهري = ?,
                الغياب_المبرر = ?,
                غياب_غير_مبرر = ?,
                ملاحظات = ?
            WHERE id = ?
            """
            cur.executemany(update_query, updates)
            conn.commit()
            self.show_message("نجاح", f"تم حفظ {len(updates)} سجل بنجاح!")
            # تحديث عرض الجدول فوراً
            self.updateModel()
        except Exception as e:
            if conn:
                conn.rollback()
            self.show_message("خطأ", f"حدث خطأ أثناء حفظ البيانات:\n{str(e)}", QMessageBox.Critical)
            import traceback
            traceback.print_exc()
        finally:
            if conn:
                conn.close()

    def update_threshold(self):
        """تحديث عتبة الغياب وإعادة رسم الجدول"""
        try:
            threshold_value = int(self.threshold_box.text() or 0)
            if hasattr(self, 'model'):
                self.model.threshold = threshold_value
                # إصدار إشارة تحديث لكل الخلايا من الأسبوع 1 إلى المجموع الشهري
                self.model.dataChanged.emit(
                    self.model.index(0, 4),  # بداية من أول خلية في الأسبوع الأول
                    self.model.index(self.model.rowCount() - 1, 9)  # نهاية بآخر خلية في المجموع الشهري
                )
        except ValueError:
            if hasattr(self, 'model'):
                self.model.threshold = 0
                self.model.dataChanged.emit(
                    self.model.index(0, 4),
                    self.model.index(self.model.rowCount() - 1, 9)
                )

    def update_monthly_threshold(self):
        """تحديث عتبة الغياب الشهري وإعادة رسم الجدول"""
        try:
            threshold_value = int(self.monthly_threshold_box.text() or 0)
            if hasattr(self, 'model'):
                self.model.monthly_threshold = threshold_value
                # إصدار إشارة تحديث لكل الخلايا من الأسبوع 1 إلى المجموع الشهري
                self.model.dataChanged.emit(
                    self.model.index(0, 4),
                    self.model.index(self.model.rowCount() - 1, 9)
                )
        except ValueError:
            if hasattr(self, 'model'):
                self.model.monthly_threshold = 0
                self.model.dataChanged.emit(
                    self.model.index(0, 4),
                    self.model.index(self.model.rowCount() - 1, 9)
                )

    def save_threshold(self, value):
        """حفظ قيمة عتبة الغياب الأسبوعي"""
        self.settings.setValue('weekly_threshold', value)
        try:
            threshold_value = int(value or 0)
            if hasattr(self, 'model'):
                self.model.threshold = threshold_value
                self.model.layoutChanged.emit()
        except ValueError:
            if hasattr(self, 'model'):
                self.model.threshold = 0
                self.model.layoutChanged.emit()

    def save_monthly_threshold(self, value):
        """حفظ قيمة عتبة الغياب الشهري"""
        self.settings.setValue('monthly_threshold', value)
        try:
            threshold_value = int(value or 0)
            if hasattr(self, 'model'):
                self.model.monthly_threshold = threshold_value
                self.model.layoutChanged.emit()
        except ValueError:
            if hasattr(self, 'model'):
                self.model.monthly_threshold = 0
                self.model.layoutChanged.emit()

    def closeEvent(self, event):
        """حفظ الإعدادات عند إغلاق النافذة"""
        self.settings.sync()
        super().closeEvent(event)

    def show_message(self, title, text, icon=QMessageBox.Information):
        """عرض رسالة للمستخدم بالنمط المطلوب"""
        msg = QMessageBox(self)
        msg.setWindowTitle(title)
        msg.setText(text)
        msg.setIcon(icon)
        msg.setStyleSheet("""
            QMessageBox {
                background-color: white;
                font-family: 'Calibri';
                font-size: 13pt;
            }
            QLabel {
                font-family: 'Calibri';
                font-size: 13pt;
                font-weight: bold;
                color: black;
            }
            QPushButton {
                font-family: 'Calibri';
                font-size: 13pt;
                font-weight: bold;
                min-width: 80px;
                padding: 5px;
            }
        """)
        return msg.exec_()

    def init_ui(self):
        """إنشاء واجهة المستخدم بتصميم عصري"""
        # إنشاء إطار رئيسي مع تأثير الظل
        content_frame = QFrame(self)
        # تعديل الأبعاد لتكون 90% من حجم النافذة
        content_frame.setGeometry(int(self.width() * 0.05), int(self.height() * 0.05),
                                int(self.width() * 0.9), int(self.height() * 0.9))
        content_frame.setFrameShape(QFrame.NoFrame)
        content_frame.setStyleSheet("""
            QFrame {
                background-color: white;
                border-radius: 15px;
            }
        """)
        # إضافة تأثير الظل
        shadow = QGraphicsDropShadowEffect()
        shadow.setBlurRadius(15)
        shadow.setXOffset(5)
        shadow.setYOffset(5)
        shadow.setColor(QColor(0, 0, 0, 150))
        content_frame.setGraphicsEffect(shadow)
        # تعديل أبعاد الجداول لتملأ المساحة المتاحة
        table_x = int(content_frame.width() * 0.05)
        table_width = int(content_frame.width() * 0.9)
        # تعديل عرض وموقع الجداول
        self.general_record_table.setGeometry(table_x, 80, table_width, 300)
        self.lists_table.setGeometry(table_x, 400, table_width, 250)

    def apply_styles(self):
        # إزالة جميع التنسيقات المتعلقة بالنافذة والجدول مؤقتًا
        # يمكن إعادة التنسيقات الأخرى لاحقًا إذا لم تكن هي المشكلة
        self.setStyleSheet("""
            /* QMainWindow { background-color: #f0f2f5; } */ /* تم التعليق */

            QGroupBox {
                background-color: white;
                border-radius: 8px;
                border: 1px solid #ddd;
                margin-top: 10px;
                font-family: 'Calibri';
                font-size: 13pt;
                font-weight: bold;
            }
            QGroupBox::title {
                color: #1976d2;
                subcontrol-position: top center;
                padding: 5px;
            }
            /* تنسيق مربعات التحرير والسرد (QComboBox) */
            QComboBox {
                background-color: white;
                color: #333;
                border: 1px solid #5c9dca;
                border-radius: 3px;
                padding: 5px;
                font-family: 'Calibri';
                font-size: 12pt;
                selection-background-color: #7eb9e3;
                selection-color: white;
            }

            QComboBox:hover {
                border-color: #a1d0f0;
                background-color: #f5f9fc;
            }

            QComboBox:focus {
                border: 2px solid #5c9dca;
            }

            QComboBox::drop-down {
                subcontrol-origin: padding;
                subcontrol-position: top right;
                width: 20px;
                border-left: 1px solid #5c9dca;
                border-top-right-radius: 3px;
                border-bottom-right-radius: 3px;
            }

            QComboBox::down-arrow {
                image: url(down_arrow.png);
                width: 12px;
                height: 12px;
            }
            QPushButton {
                background-color: #7eb9e3;
                color: #333;
                border: 1px solid #5c9dca;
                border-radius: 3px;
                padding: 5px 12px;
                min-height: 30px;
                /* تم إزالة min-width للسماح بتحديد العرض لكل زر على حدة */
                font-family: 'Calibri';
                font-size: 12pt;
                font-weight: bold;
            }

            QPushButton:hover {
                background-color: #a1d0f0;
            }

            QPushButton:pressed {
                background-color: #5c9dca;
            }

            /* تم إزالة تنسيق زر الحفظ لاستخدام النمط العام */

            /* تم إزالة تنسيق أزرار الأسدس لاستخدام النمط العام */

            /* تم إزالة تنسيق أزرار تقرير الغياب الشهري والاعدادات الافتراضية لاستخدام النمط العام */

            /* QTableView { ... } */ /* تم التعليق */
            /* QTableView::item { ... } */ /* تم التعليق */
            /* QHeaderView::section { ... } */ /* تم التعليق */
        """)
        # إزالة التنسيق المباشر لرأس الجدول الأفقي
        self.table_view.horizontalHeader().setStyleSheet("")
        print("تمت إزالة تنسيقات النافذة والجدول.") # طباعة للتشخيص

    def printAbsenceReport(self):
        """طباعة تقرير الغياب للقسم والشهر المحددين"""
        section = self.combo_qism.currentText()
        month = self.combo_shahr.currentText()

        try:
            # استيراد نافذة تقرير الغياب من ملف second_semester_helper.py
            from second_semester_helper import YearlyAbsenceSummaryWindow

            # إنشاء نافذة جديدة لتقرير الغياب
            absence_window = YearlyAbsenceSummaryWindow()

            # تعيين القسم والشهر المحددين
            absence_window.section_combo.setCurrentText(section)
            absence_window.month_combo.setCurrentText(month)

            # توليد التقرير الشهري
            absence_window.generate_monthly_report()

            print(f"تم فتح تقرير الغياب الشهري للقسم {section} والشهر {month}")
        except Exception as e:
            import traceback
            traceback.print_exc()
            self.show_message("خطأ", f"لم يتم فتح تقرير الغياب الشهري:\n{e}", QMessageBox.Critical)
            print(f"خطأ عند فتح تقرير الغياب الشهري: {e}")

            # في حالة حدوث خطأ، نستخدم الدالة القديمة كاحتياط
            try:
                print("محاولة استخدام الدالة القديمة print_absence_report كاحتياط...")
                print_absence_report(self, section, month, self.model)
            except Exception as e2:
                print(f"فشلت الدالة القديمة أيضًا: {e2}")

    def update_current_month(self, new_month):
        """تحديث الشهر الحالي في قاعدة البيانات"""
        try:
            conn = sqlite3.connect("data.db")
            cur = conn.cursor()
            cur.execute("UPDATE اعدادات_البرنامج SET الشهر_الحالي = ?", (new_month,))
            conn.commit()
            conn.close()
        except Exception as e:
            print(f"خطأ في تحديث الشهر الحالي: {e}")

    def get_thresholds(self):
        """استرجاع عتبات الغياب من قاعدة البيانات"""
        try:
            conn = sqlite3.connect("data.db")
            cur = conn.cursor()
            # التحقق من وجود جدول اعدادات_البرنامج وإنشاؤه إذا لم يكن موجودًا
            cur.execute("""
                CREATE TABLE IF NOT EXISTS اعدادات_البرنامج (
                    المعرف INTEGER PRIMARY KEY,
                    الشهر_الحالي TEXT UNIQUE,
                    عتبة_الغياب_الأسبوعي INTEGER,
                    عتبة_الغياب_الشهري INTEGER,
                    نقطة_المواظبة INTEGER,
                    نقطة_السلوك INTEGER,
                    معامل_المواظبة INTEGER,
                    معامل_السلوك INTEGER,
                    المخالفة_الاولى INTEGER,
                    المخالفة_الثانية INTEGER,
                    المخالفة_الثالثة INTEGER,
                    تاريخ_التعديل DATETIME DEFAULT CURRENT_TIMESTAMP
                )
            """)
            # التحقق من وجود سجل وإضافة سجل افتراضي إذا لم يكن موجودًا
            cur.execute("SELECT COUNT(*) FROM اعدادات_البرنامج")
            count = cur.fetchone()[0]
            if count == 0:
                # إنشاء سجل افتراضية
                cur.execute("""
                    INSERT INTO اعدادات_البرنامج (
                        الشهر_الحالي, عتبة_الغياب_الأسبوعي, عتبة_الغياب_الشهري,
                        نقطة_المواظبة, نقطة_السلوك, معامل_المواظبة,
                        معامل_السلوك, المخالفة_الاولى, المخالفة_الثانية, المخالفة_الثالثة
                    ) VALUES ('شتنبر', 3, 8, 20, 20, 2, 1, 2, 3)
                """)
                conn.commit()
            # الآن يمكننا استرجاع القيم بأمان
            cur.execute("SELECT عتبة_الغياب_الأسبوعي, عتبة_الغياب_الشهري FROM اعدادات_البرنامج LIMIT 1")
            result = cur.fetchone()
            conn.close()
            if result:
                return result[0] or 0, result[1] or 0
            return 0, 0
        except Exception as e:
            print(f"خطأ في استرجاع عتبات الغياب: {e}")
            return 0, 0

    def update_thresholds(self):
        """تحديث عتبات الغياب في النموذج"""
        weekly, monthly = self.get_thresholds()
        if hasattr(self, 'model') and self.model: # التأكد من وجود النموذج
            changed = False
            if self.model.threshold != weekly:
                self.model.threshold = weekly
                changed = True
            if self.model.monthly_threshold != monthly:
                self.model.monthly_threshold = monthly
                changed = True

            if changed:
                # استخدام layoutChanged لتحديث شامل للتلوين
                print(f"Thresholds updated in model. Emitting layoutChanged.") # تشخيص
                self.model.layoutChanged.emit()

    def openDefaultSettings(self):
        """فتح نافذة الإعدادات الافتراضية"""
        try:
            from default_settings_window import DefaultSettingsDialog
            dialog = DefaultSettingsDialog(self)
            if dialog.exec_():
                # تحديث عتبات الغياب بعد إغلاق النافذة
                self.update_thresholds()
        except Exception as e:
            self.show_message("خطأ", f"حدث خطأ أثناء فتح نافذة الإعدادات:\n{str(e)}", QMessageBox.Critical)

    def showHelpDialog(self):
        """عرض نافذة التعليمات"""
        # إنشاء نافذة حوار جديدة
        help_dialog = QDialog(self)
        help_dialog.setWindowTitle("تعليمات استخدام نافذة مسك الغياب")
        help_dialog.setMinimumSize(900, 700)
        help_dialog.setLayoutDirection(Qt.RightToLeft)

        # تطبيق التصميم على النافذة
        help_dialog.setStyleSheet("""
            QDialog { background-color: white; }
            QTextBrowser {
                border: 1px solid #E0E0E0;
                border-radius: 5px;
                padding: 10px;
                font-family: Calibri;
                font-size: 14pt;
                line-height: 1.5;
            }
        """)

        layout = QVBoxLayout(help_dialog)

        text_browser = QTextBrowser()
        text_browser.setOpenExternalLinks(True)

        # محتوى التعليمات
        help_text = """
        <h2 style="color: #1976d2; text-align: center;">دليل استخدام نافذة مسك الغياب</h2>

        <h3 style="color: #2196f3;">نظرة عامة</h3>
        <p>هذه النافذة تساعدك على تسجيل ومتابعة غياب التلاميذ بطريقة منظمة وسهلة. إليك شرح مبسط لكيفية استخدام هذه النافذة:</p>

        <h3 style="color: #2196f3;">1. اختيار البيانات الأساسية:</h3>
        <ul>
            <li><b>المستوى:</b> اختر المستوى الدراسي من القائمة المنسدلة.</li>
            <li><b>القسم:</b> بعد اختيار المستوى، ستظهر قائمة الأقسام المتاحة. اختر القسم المطلوب.</li>
            <li><b>الشهر:</b> اختر الشهر الذي تريد تسجيل الغياب فيه.</li>
        </ul>

        <h3 style="color: #2196f3;">2. تسجيل الغياب:</h3>
        <ul>
            <li>بعد اختيار المستوى والقسم والشهر، سيظهر جدول بأسماء التلاميذ.</li>
            <li><b>هام:</b> الغياب يُدخل بالساعة وليس باليوم. أدخل عدد ساعات الغياب لكل تلميذ في كل أسبوع.</li>
            <li>في الأعمدة المخصصة للأسابيع (1-5)، أدخل عدد ساعات الغياب لكل تلميذ في كل أسبوع.</li>
            <li>سيتم حساب المجموع الشهري تلقائيًا.</li>
            <li>يمكنك أيضًا تسجيل الغياب المبرر وإضافة ملاحظات لكل تلميذ.</li>
        </ul>

        <h3 style="color: #2196f3;">3. حفظ البيانات:</h3>
        <ul>
            <li>بعد الانتهاء من تسجيل الغياب، انقر على زر "حفظ" لحفظ البيانات.</li>
            <li>تأكد من حفظ البيانات قبل الانتقال إلى قسم أو شهر آخر.</li>
        </ul>

        <h3 style="color: #2196f3;">4. إنشاء التقارير:</h3>
        <ul>
            <li><b>تقرير الغياب الشهري:</b> لإنشاء تقرير بالغياب الشهري للقسم المحدد.</li>
            <li><b>تقرير الأسدس الأول:</b> لإنشاء تقرير بالغياب للأسدس الأول.</li>
            <li><b>تقرير الأسدس الثاني:</b> لإنشاء تقرير بالغياب للأسدس الثاني.</li>
        </ul>

        <h3 style="color: #2196f3;">5. عرض بيانات غياب تلميذ معين:</h3>
        <ul>
            <li>يمكنك عرض تقرير مفصل لغياب تلميذ معين على مدار السنة الدراسية بالنقر على رمز التلميذ (رقم مسار التلميذ) في العمود الأول من الجدول.</li>
            <li>سيتم فتح نافذة جديدة تعرض تقريرًا سنويًا لغياب التلميذ المحدد، مع تفاصيل الغياب لكل شهر وكل أسبوع.</li>
            <li>يمكنك من خلال هذا التقرير متابعة تطور غياب التلميذ على مدار العام الدراسي بشكل شامل.</li>
        </ul>

        <h3 style="color: #2196f3;">6. حساب نقطة السلوك ونقطة المواظبة:</h3>
        <ul>
            <li><b>نقطة المواظبة:</b> تُحسب كالتالي:
                <ul>
                    <li>القيمة الأساسية لنقطة المواظبة (10 نقاط) ناقص (عدد ساعات الغياب غير المبرر مضروبة في معامل المواظبة).</li>
                    <li>مثال: إذا كانت نقطة المواظبة الأساسية = 10، ومعامل المواظبة = 2، وعدد ساعات الغياب غير المبرر = 3، فإن نقطة المواظبة = 10 - (3 × 2) = 4 نقاط.</li>
                </ul>
            </li>
            <li><b>نقطة السلوك:</b> تُحسب بناءً على عدد المخالفات:
                <ul>
                    <li>إذا لم توجد مخالفات: نقطة السلوك الأساسية (10 نقاط).</li>
                    <li>إذا كانت هناك مخالفة واحدة: نقطة السلوك الأساسية ناقص قيمة المخالفة الأولى.</li>
                    <li>إذا كانت هناك مخالفتان: نقطة السلوك الأساسية ناقص (قيمة المخالفة الأولى + قيمة المخالفة الثانية).</li>
                    <li>إذا كانت هناك ثلاث مخالفات أو أكثر: نقطة السلوك الأساسية ناقص (قيمة المخالفة الأولى + قيمة المخالفة الثانية + قيمة المخالفة الثالثة).</li>
                </ul>
            </li>
        </ul>

        <h3 style="color: #2196f3;">7. الإعدادات الافتراضية:</h3>
        <ul>
            <li>يمكنك تعديل الإعدادات الافتراضية مثل عتبة الغياب الأسبوعي والشهري، ونقطة المواظبة الأساسية، ونقطة السلوك الأساسية، ومعاملات المواظبة والسلوك، وقيم المخالفات من خلال زر "الإعدادات الافتراضية".</li>
        </ul>

        <h3 style="color: #2196f3;">ملاحظات هامة:</h3>
        <ul>
            <li>الخلايا الملونة باللون الأصفر تشير إلى تجاوز عتبة الغياب الأسبوعي.</li>
            <li>الخلايا الملونة باللون الأحمر الفاتح تشير إلى تجاوز عتبة الغياب الشهري.</li>
            <li>تأكد من إدخال أرقام صحيحة فقط في خانات الغياب.</li>
            <li>الغياب غير المبرر = المجموع الشهري للغياب - الغياب المبرر.</li>
        </ul>
        """

        text_browser.setHtml(help_text)
        layout.addWidget(text_browser)

        # زر إغلاق
        close_btn = QPushButton("إغلاق")
        close_btn.setFixedHeight(35)
        close_btn.setMinimumWidth(120)
        close_btn.clicked.connect(help_dialog.accept)
        close_btn.setStyleSheet("""
            QPushButton { background-color: #1976d2; color: white; border: none; border-radius: 4px;
                          padding: 0 15px; font-family: Calibri; font-size: 13pt; font-weight: bold; }
            QPushButton:hover { background-color: #1565c0; }
        """)

        btn_layout = QHBoxLayout()
        btn_layout.addStretch()
        btn_layout.addWidget(close_btn)
        layout.addLayout(btn_layout)

        help_dialog.exec_()

    def update_semester_field(self):
        """تحديث حقل الأسدس في سجلات الغياب بناءً على الشهر"""
        try:
            conn = sqlite3.connect("data.db")
            cur = conn.cursor()

            # تحديث الأسدس الأول (شتنبر، أكتوبر، نونبر، دجنبر، يناير)
            cur.execute("""
                UPDATE مسك_الغياب_الأسبوعي
                SET الأسدس = 1
                WHERE الشهر IN ('شتنبر', 'أكتوبر', 'نونبر', 'دجنبر', 'يناير')
                AND (الأسدس IS NULL OR الأسدس = 0 OR الأسدس = '')
            """)
            first_sem_count = cur.rowcount

            # تحديث الأسدس الثاني (فبراير، مارس، أبريل، ماي، يونيو)
            cur.execute("""
                UPDATE مسك_الغياب_الأسبوعي
                SET الأسدس = 2
                WHERE الشهر IN ('فبراير', 'مارس', 'أبريل', 'ماي', 'يونيو')
                AND (الأسدس IS NULL OR الأسدس = 0 OR الأسدس = '')
            """)
            second_sem_count = cur.rowcount

            conn.commit()
            print(f"تم تحديث {first_sem_count} سجل للأسدس الأول و {second_sem_count} سجل للأسدس الثاني")

            return first_sem_count + second_sem_count
        except Exception as e:
            print(f"خطأ في تحديث حقل الأسدس: {e}")
            return 0
        finally:
            conn.close()

    def printSemesterReport(self):
        """حساب المجموع الشهري للغياب وإنشاء جداول الغياب السنوي"""
        try:
            # استيراد نافذة تقرير الغياب من ملف second_semester_helper.py
            from second_semester_helper import YearlyAbsenceSummaryWindow

            # إنشاء نافذة جديدة لتقرير الغياب
            absence_window = YearlyAbsenceSummaryWindow()

            # الحصول على القسم المحدد من القائمة المنسدلة
            section = self.combo_qism.currentText()

            # تعيين القسم المحدد في نافذة تقرير الغياب
            if section:
                index = absence_window.section_combo.findText(section)
                if index >= 0:
                    absence_window.section_combo.setCurrentIndex(index)

            # إنشاء جداول الغياب السنوي
            absence_window.generate_summary_tables()

            # عرض رسالة نجاح
            self.show_message("نجاح",
                "تم حساب المجموع الشهري للغياب وإنشاء جداول الغياب السنوي بنجاح.",
                QMessageBox.Information)

            print("تم حساب المجموع الشهري للغياب بنجاح!")

        except Exception as e:
            # خطأ عام في تجهيز الطباعة
            error_msg = f"حدث خطأ أثناء حساب المجموع الشهري للغياب:\n{str(e)}"
            print(error_msg)
            import traceback
            traceback.print_exc()
            self.show_message("خطأ", error_msg, QMessageBox.Critical)

    def on_table_cell_clicked(self, index):
        """
        معالجة النقر على خلايا الجدول
        عند النقر على عمود الرمز (العمود 3)، يتم فتح تقرير الغياب الشهري الخاص بالتلميذ
        """
        try:
            # التحقق من أن العمود المنقور عليه هو عمود الرمز (العمود 3)
            if index.column() == 3:  # عمود "رمز التلميذ"
                # الحصول على رمز التلميذ من الخلية المنقور عليها
                student_code = self.model.data(index, Qt.DisplayRole)
                if student_code:
                    print(f"تم النقر على رمز التلميذ: {student_code}")

                    # فتح تقرير الغياب الشهري للتلميذ
                    try:
                        # استيراد نافذة تقرير الغياب السنوي للتلميذ
                        from second_semester_helper import YearlyAbsenceSummaryWindow

                        # إنشاء نافذة جديدة لتقرير الغياب
                        absence_window = YearlyAbsenceSummaryWindow()

                        # تعيين رمز التلميذ في حقل الإدخال
                        absence_window.student_code_input.setText(student_code)

                        # توليد التقرير
                        absence_window.generate_student_absence_report()

                        # عرض النافذة
                        absence_window.show()

                        print(f"تم فتح تقرير الغياب للتلميذ: {student_code}")
                    except Exception as e:
                        import traceback
                        traceback.print_exc()
                        self.show_message("خطأ", f"لم يتم فتح تقرير الغياب للتلميذ:\n{e}", QMessageBox.Critical)
                        print(f"خطأ عند فتح تقرير الغياب للتلميذ: {e}")
        except Exception as e:
            print(f"خطأ في معالجة النقر على خلية الجدول: {e}")
            import traceback
            traceback.print_exc()

    def printFirstSemesterReport(self):
        """طباعة تقرير الأسدس الأول للقسم المحدد في القائمة المنسدلة"""
        try:
            # أولاً، نقوم بحساب المجموع الشهري للغياب وإنشاء جداول الغياب السنوي
            try:
                # استيراد نافذة تقرير الغياب من ملف second_semester_helper.py
                from second_semester_helper import YearlyAbsenceSummaryWindow

                # إنشاء نافذة جديدة لتقرير الغياب
                absence_window = YearlyAbsenceSummaryWindow()

                # الحصول على القسم المحدد من القائمة المنسدلة
                section = self.combo_qism.currentText()

                # تعيين القسم المحدد في نافذة تقرير الغياب
                if section:
                    index = absence_window.section_combo.findText(section)
                    if index >= 0:
                        absence_window.section_combo.setCurrentIndex(index)

                # إنشاء جداول الغياب السنوي
                absence_window.generate_summary_tables()

                print("تم حساب المجموع الشهري للغياب بنجاح!")
            except Exception as e:
                # خطأ عام في تجهيز الطباعة
                error_msg = f"حدث خطأ أثناء حساب المجموع الشهري للغياب:\n{str(e)}"
                print(error_msg)
                import traceback
                traceback.print_exc()
                # لا نعرض رسالة خطأ للمستخدم هنا، نستمر في محاولة طباعة التقرير

            # --- التحقق من توفر دوال تقرير الأسدس ---
            if not SEMESTER_FUNCTIONS_AVAILABLE:
                self.show_message("خطأ", "وظائف طباعة تقرير الأسدس غير متوفرة.", QMessageBox.Critical)
                return

            # الحصول على القسم المحدد من القائمة المنسدلة
            section = self.combo_qism.currentText()

            if not section:
                self.show_message("تنبيه", "الرجاء اختيار قسم أولاً.", QMessageBox.Warning)
                return

            # تحديث حقل الأسدس قبل التحقق من وجود بيانات
            updated_records = self.update_semester_field()
            print(f"تم تحديث {updated_records} سجل في جدول مسك_الغياب_الأسبوعي")

            # تحقق من وجود بيانات للقسم المحدد
            conn = sqlite3.connect("data.db")
            cur = conn.cursor()

            # الحصول على السنة الدراسية الحالية
            cur.execute("SELECT السنة_الدراسية FROM بيانات_المؤسسة LIMIT 1")
            result = cur.fetchone()
            school_year = result[0] if result else ""

            # التحقق من وجود تلاميذ في القسم المحدد
            cur.execute("""
                SELECT COUNT(*) FROM اللوائح
                WHERE القسم = ? AND السنة_الدراسية = ?
            """, (section, school_year))
            students_count = cur.fetchone()[0]

            # استعلام يسمح بإظهار بيانات الأسدس الأول عبر الشهور المرتبطة به
            cur.execute("""
                SELECT COUNT(*) FROM مسك_الغياب_الأسبوعي mag
                JOIN اللوائح l ON mag.رمز_التلميذ = l.الرمز
                WHERE l.القسم = ? AND l.السنة_الدراسية = ?
                AND (mag.الأسدس = 1 OR mag.الشهر IN ('شتنبر', 'أكتوبر', 'نونبر', 'دجنبر', 'يناير'))
            """, (section, school_year))
            attendance_records_count = cur.fetchone()[0]

            # تحقق إضافي للشهور المرتبطة بالأسدس الأول
            cur.execute("""
                SELECT DISTINCT mag.الشهر FROM مسك_الغياب_الأسبوعي mag
                JOIN اللوائح l ON mag.رمز_التلميذ = l.الرمز
                WHERE l.القسم = ? AND l.السنة_الدراسية = ?
                AND mag.الشهر IN ('شتنبر', 'أكتوبر', 'نونبر', 'دجنبر', 'يناير')
            """, (section, school_year))
            first_semester_months = [row[0] for row in cur.fetchall()]

            conn.close()

            # عرض معلومات تشخيصية في وحدة التحكم
            print(f"\n=== معلومات تشخيصية لتقرير الأسدس الأول ===")
            print(f"القسم المحدد: {section}")
            print(f"السنة الدراسية: {school_year}")
            print(f"عدد التلاميذ: {students_count}")
            print(f"عدد سجلات الغياب: {attendance_records_count}")
            print(f"شهور الأسدس الأول المتوفرة: {first_semester_months}")

            if students_count == 0:
                self.show_message("تنبيه",
                    f"لا يوجد تلاميذ مسجلين في القسم {section} للسنة الدراسية {school_year}.",
                    QMessageBox.Warning)
                return

            if attendance_records_count == 0:
                self.show_message("تنبيه",
                    f"لا توجد بيانات غياب مسجلة للقسم {section}.\n"
                    f"يرجى التأكد من تسجيل بيانات الغياب أولاً.",
                    QMessageBox.Warning)
                return

            # --- استدعاء دالة طباعة تقرير الأسدس للقسم الواحد ---
            print(f"محاولة طباعة تقرير الأسدس للقسم: {section}")
            try:
                # استدعاء الدالة المستوردة من absence_reports.py
                print("استدعاء دالة print_semester_report...")
                result = print_semester_report(self, section)
                print(f"نتيجة استدعاء print_semester_report: {result}")

                # عرض رسالة نجاح بغض النظر عن النتيجة، حيث أن التقرير يُنشأ بنجاح
                # حتى لو أرجعت الدالة False في بعض الحالات
                self.show_message("نجاح",
                    f"تم إنشاء تقرير الأسدس الأول للقسم {section} بنجاح.\n"
                    f"يمكنك العثور عليه في مجلد التقارير المؤقتة.",
                    QMessageBox.Information)

                print("تم طباعة تقرير الأسدس الأول بنجاح!")

            except Exception as e:
                error_msg = f"حدث خطأ أثناء طباعة تقرير الأسدس الأول للقسم {section}:\n{str(e)}"
                print(error_msg)
                import traceback
                traceback.print_exc()
                self.show_message("خطأ طباعة الأسدس الأول",
                    f"{error_msg}",
                    QMessageBox.Critical)

        except Exception as e:
            # خطأ عام في تجهيز الطباعة
            error_msg = f"حدث خطأ عام أثناء تجهيز طباعة تقرير الأسدس الأول:\n{str(e)}"
            print(error_msg)
            import traceback
            traceback.print_exc()
            self.show_message("خطأ", error_msg, QMessageBox.Critical)

    def printSecondSemesterReport(self):
        """طباعة تقرير الأسدس الثاني للقسم المحدد في القائمة المنسدلة"""
        try:
            # أولاً، نقوم بحساب المجموع الشهري للغياب وإنشاء جداول الغياب السنوي
            try:
                # استيراد نافذة تقرير الغياب من ملف second_semester_helper.py
                from second_semester_helper import YearlyAbsenceSummaryWindow

                # إنشاء نافذة جديدة لتقرير الغياب
                absence_window = YearlyAbsenceSummaryWindow()

                # الحصول على القسم المحدد من القائمة المنسدلة
                section = self.combo_qism.currentText()

                # تعيين القسم المحدد في نافذة تقرير الغياب
                if section:
                    index = absence_window.section_combo.findText(section)
                    if index >= 0:
                        absence_window.section_combo.setCurrentIndex(index)

                # إنشاء جداول الغياب السنوي
                absence_window.generate_summary_tables()

                print("تم حساب المجموع الشهري للغياب بنجاح!")
            except Exception as e:
                # خطأ عام في تجهيز الطباعة
                error_msg = f"حدث خطأ أثناء حساب المجموع الشهري للغياب:\n{str(e)}"
                print(error_msg)
                import traceback
                traceback.print_exc()
                # لا نعرض رسالة خطأ للمستخدم هنا، نستمر في محاولة طباعة التقرير

            # --- التحقق من توفر دوال تقرير الأسدس ---
            if not SECOND_SEMESTER_FUNCTION_AVAILABLE:
                self.show_message("خطأ", "وظائف طباعة تقرير الأسدس الثاني غير متوفرة.", QMessageBox.Critical)
                return

            # الحصول على القسم المحدد من القائمة المنسدلة
            section = self.combo_qism.currentText()

            if not section:
                self.show_message("تنبيه", "الرجاء اختيار قسم أولاً.", QMessageBox.Warning)
                return

            # تحديث حقل الأسدس قبل التحقق من وجود بيانات
            updated_records = self.update_semester_field()
            print(f"تم تحديث {updated_records} سجل في جدول مسك_الغياب_الأسبوعي")

            # تحقق من وجود بيانات للقسم المحدد
            conn = sqlite3.connect("data.db")
            cur = conn.cursor()

            # الحصول على السنة الدراسية الحالية
            cur.execute("SELECT السنة_الدراسية FROM بيانات_المؤسسة LIMIT 1")
            result = cur.fetchone()
            school_year = result[0] if result else ""

            # التحقق من وجود تلاميذ في القسم المحدد
            cur.execute("""
                SELECT COUNT(*) FROM اللوائح
                WHERE القسم = ? AND السنة_الدراسية = ?
            """, (section, school_year))
            students_count = cur.fetchone()[0]

            # استعلام يسمح بإظهار بيانات الأسدس الثاني عبر الشهور المرتبطة به
            cur.execute("""
                SELECT COUNT(*) FROM مسك_الغياب_الأسبوعي mag
                JOIN اللوائح l ON mag.رمز_التلميذ = l.الرمز
                WHERE l.القسم = ? AND l.السنة_الدراسية = ?
                AND (mag.الأسدس = 2 OR mag.الشهر IN ('فبراير', 'مارس', 'أبريل', 'ماي', 'يونيو'))
            """, (section, school_year))
            attendance_records_count = cur.fetchone()[0]

            # تحقق إضافي للشهور المرتبطة بالأسدس الثاني
            cur.execute("""
                SELECT DISTINCT mag.الشهر FROM مسك_الغياب_الأسبوعي mag
                JOIN اللوائح l ON mag.رمز_التلميذ = l.الرمز
                WHERE l.القسم = ? AND l.السنة_الدراسية = ?
                AND mag.الشهر IN ('فبراير', 'مارس', 'أبريل', 'ماي', 'يونيو')
            """, (section, school_year))
            second_semester_months = [row[0] for row in cur.fetchall()]

            conn.close()

            # عرض معلومات تشخيصية في وحدة التحكم
            print(f"\n=== معلومات تشخيصية لتقرير الأسدس الثاني ===")
            print(f"القسم المحدد: {section}")
            print(f"السنة الدراسية: {school_year}")
            print(f"عدد التلاميذ: {students_count}")
            print(f"عدد سجلات الغياب للأسدس الثاني: {attendance_records_count}")
            print(f"شهور الأسدس الثاني المتوفرة: {second_semester_months}")

            if students_count == 0:
                self.show_message("تنبيه",
                    f"لا يوجد تلاميذ مسجلين في القسم {section} للسنة الدراسية {school_year}.",
                    QMessageBox.Warning)
                return

            if attendance_records_count == 0:
                self.show_message("تنبيه",
                    f"لا توجد بيانات غياب مسجلة للأسدس الثاني للقسم {section}.\n"
                    f"يرجى التأكد من تسجيل بيانات الغياب لشهور الأسدس الثاني أولاً.",
                    QMessageBox.Warning)
                return

            # --- استدعاء دالة طباعة تقرير الأسدس الثاني للقسم المحدد ---
            print(f"محاولة طباعة تقرير الأسدس الثاني للقسم: {section}")
            try:
                # استدعاء الدالة المخصصة للأسدس الثاني مع تمرير معلمة الأسدس
                print("استدعاء دالة print_second_semester_report للأسدس الثاني...")
                result = print_second_semester_report(self, section)
                print(f"نتيجة استدعاء print_second_semester_report: {result}")

                # عرض رسالة نجاح بغض النظر عن النتيجة
                self.show_message("نجاح",
                    f"تم إنشاء تقرير الأسدس الثاني للقسم {section} بنجاح.\n"
                    f"يمكنك العثور عليه في مجلد التقارير المؤقتة.",
                    QMessageBox.Information)

                print("تم طباعة تقرير الأسدس الثاني بنجاح!")

            except Exception as e:
                error_msg = f"حدث خطأ أثناء طباعة تقرير الأسدس الثاني للقسم {section}:\n{str(e)}"
                print(error_msg)
                import traceback
                traceback.print_exc()
                self.show_message("خطأ طباعة الأسدس الثاني",
                    f"{error_msg}",
                    QMessageBox.Critical)

        except Exception as e:
            # خطأ عام في تجهيز الطباعة
            error_msg = f"حدث خطأ عام أثناء تجهيز طباعة تقرير الأسدس الثاني:\n{str(e)}"
            print(error_msg)
            import traceback
            traceback.print_exc()
            self.show_message("خطأ", error_msg, QMessageBox.Critical)

if __name__ == "__main__":
    app = QApplication(sys.argv)
    window = Hirasa300Window()
    window.show()
    sys.exit(app.exec_())





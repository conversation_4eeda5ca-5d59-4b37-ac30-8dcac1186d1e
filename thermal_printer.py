"""
وحدة للتعامل مع الطابعات الحرارية
تحتوي على دوال متخصصة للطباعة على الطابعات الحرارية
"""

import os
import tempfile
import time
import sqlite3
from datetime import datetime

# محاولة استيراد مكتبة escpos إذا كانت متوفرة
ESCPOS_AVAILABLE = False
try:
    from escpos.printer import Usb, Serial, Network, File
    ESCPOS_AVAILABLE = True
    print("تم استيراد مكتبة escpos بنجاح")
except ImportError:
    print("مكتبة escpos غير متوفرة، سيتم استخدام الطرق البديلة")

def get_printer_settings():
    """الحصول على إعدادات الطابعة الحرارية من قاعدة البيانات"""
    # القيم الافتراضية
    settings = {
        'printer_name': None,
        'paper_width': 80,  # عرض الورق بالملم
        'connection_type': 'windows',  # نوع الاتصال (windows, usb, network, serial)
        'vendor_id': None,  # معرف البائع للاتصال USB
        'product_id': None,  # معرف المنتج للاتصال USB
        'ip_address': None,  # عنوان IP للاتصال الشبكي
        'port': 9100,  # المنفذ للاتصال الشبكي
        'serial_port': None,  # المنفذ التسلسلي
        'baudrate': 9600,  # معدل الباود للاتصال التسلسلي
        'cut_paper': True,  # قص الورق بعد الطباعة
        'char_per_line': 42,  # عدد الأحرف في السطر (للورق 80مم)
    }
    
    try:
        conn = sqlite3.connect("data.db")
        cursor = conn.cursor()
        
        # محاولة الحصول على إعدادات الطابعة من جدول إعدادات_الطابعة
        cursor.execute("""
            SELECT الطابعة_الحرارية, عرض_الورق, نوع_الاتصال, 
                   معرف_البائع, معرف_المنتج, عنوان_IP, المنفذ, 
                   المنفذ_التسلسلي, معدل_الباود, قص_الورق, عدد_الأحرف_في_السطر
            FROM إعدادات_الطابعة LIMIT 1
        """)
        result = cursor.fetchone()
        
        if result:
            # تحديث الإعدادات بالقيم من قاعدة البيانات
            fields = ['printer_name', 'paper_width', 'connection_type', 
                      'vendor_id', 'product_id', 'ip_address', 'port', 
                      'serial_port', 'baudrate', 'cut_paper', 'char_per_line']
            
            for i, field in enumerate(fields):
                if result[i] is not None:
                    settings[field] = result[i]
        
        conn.close()
    except Exception as e:
        print(f"خطأ في الحصول على إعدادات الطابعة: {e}")
        
        # محاولة إنشاء جدول إعدادات_الطابعة إذا لم يكن موجودًا
        try:
            conn = sqlite3.connect("data.db")
            cursor = conn.cursor()
            
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS إعدادات_الطابعة (
                    الطابعة_الحرارية TEXT,
                    عرض_الورق INTEGER DEFAULT 80,
                    نوع_الاتصال TEXT DEFAULT 'windows',
                    معرف_البائع TEXT,
                    معرف_المنتج TEXT,
                    عنوان_IP TEXT,
                    المنفذ INTEGER DEFAULT 9100,
                    المنفذ_التسلسلي TEXT,
                    معدل_الباود INTEGER DEFAULT 9600,
                    قص_الورق BOOLEAN DEFAULT 1,
                    عدد_الأحرف_في_السطر INTEGER DEFAULT 42
                )
            """)
            
            # إدراج قيم افتراضية
            cursor.execute("""
                INSERT INTO إعدادات_الطابعة 
                (الطابعة_الحرارية, عرض_الورق, نوع_الاتصال, عدد_الأحرف_في_السطر)
                SELECT ?, ?, ?, ?
                WHERE NOT EXISTS (SELECT 1 FROM إعدادات_الطابعة)
            """, (None, 80, 'windows', 42))
            
            conn.commit()
            conn.close()
            print("تم إنشاء جدول إعدادات_الطابعة")
        except Exception as create_error:
            print(f"خطأ في إنشاء جدول إعدادات_الطابعة: {create_error}")
    
    # تحديد عدد الأحرف في السطر بناءً على عرض الورق إذا لم يتم تحديده
    if not settings['char_per_line']:
        if settings['paper_width'] == 58:
            settings['char_per_line'] = 32
        elif settings['paper_width'] == 80:
            settings['char_per_line'] = 42
        else:
            settings['char_per_line'] = int(settings['paper_width'] / 1.9)  # تقريبي
    
    return settings

def get_windows_printer_name():
    """الحصول على اسم الطابعة الافتراضية في نظام Windows"""
    try:
        import win32print
        return win32print.GetDefaultPrinter()
    except:
        return None

def create_escpos_printer(settings):
    """إنشاء كائن الطابعة باستخدام مكتبة escpos"""
    if not ESCPOS_AVAILABLE:
        return None
    
    try:
        connection_type = settings['connection_type'].lower()
        
        if connection_type == 'usb':
            # اتصال USB
            if settings['vendor_id'] and settings['product_id']:
                vendor_id = int(settings['vendor_id'], 16) if isinstance(settings['vendor_id'], str) else settings['vendor_id']
                product_id = int(settings['product_id'], 16) if isinstance(settings['product_id'], str) else settings['product_id']
                return Usb(vendor_id, product_id)
            else:
                print("معرف البائع أو المنتج غير محدد للاتصال USB")
                return None
        
        elif connection_type == 'network':
            # اتصال شبكي
            if settings['ip_address']:
                return Network(settings['ip_address'], settings['port'])
            else:
                print("عنوان IP غير محدد للاتصال الشبكي")
                return None
        
        elif connection_type == 'serial':
            # اتصال تسلسلي
            if settings['serial_port']:
                return Serial(settings['serial_port'], settings['baudrate'])
            else:
                print("المنفذ التسلسلي غير محدد")
                return None
        
        elif connection_type == 'file':
            # طباعة إلى ملف
            return File("/dev/usb/lp0")
        
        else:
            print(f"نوع اتصال غير معروف: {connection_type}")
            return None
    
    except Exception as e:
        print(f"خطأ في إنشاء كائن الطابعة: {e}")
        return None

def format_content_for_thermal(content, char_per_line=42):
    """تنسيق المحتوى ليتناسب مع عرض الطابعة الحرارية"""
    formatted_lines = []
    
    for line in content.split('\n'):
        if len(line) <= char_per_line:
            formatted_lines.append(line)
        else:
            # تقسيم الأسطر الطويلة
            words = line.split()
            current_line = ""
            
            for word in words:
                if len(current_line) + len(word) + 1 <= char_per_line:
                    if current_line:
                        current_line += " " + word
                    else:
                        current_line = word
                else:
                    formatted_lines.append(current_line)
                    current_line = word
            
            if current_line:
                formatted_lines.append(current_line)
    
    return '\n'.join(formatted_lines)

def print_with_escpos(content, settings):
    """طباعة المحتوى باستخدام مكتبة escpos"""
    if not ESCPOS_AVAILABLE:
        return False
    
    printer = create_escpos_printer(settings)
    if not printer:
        return False
    
    try:
        # تنسيق المحتوى
        formatted_content = format_content_for_thermal(content, settings['char_per_line'])
        
        # طباعة المحتوى
        printer.text(formatted_content)
        
        # قص الورق إذا كان مطلوبًا
        if settings['cut_paper']:
            printer.cut()
        
        printer.close()
        return True
    
    except Exception as e:
        print(f"خطأ في الطباعة باستخدام escpos: {e}")
        return False

def print_with_windows(content, settings):
    """طباعة المحتوى باستخدام طرق Windows"""
    printer_name = settings['printer_name'] or get_windows_printer_name()
    
    if not printer_name:
        print("لم يتم العثور على طابعة متاحة")
        return False
    
    # تنسيق المحتوى
    formatted_content = format_content_for_thermal(content, settings['char_per_line'])
    
    # إنشاء ملف مؤقت
    try:
        with tempfile.NamedTemporaryFile(delete=False, suffix=".txt", mode="w", encoding="utf-8") as f:
            f.write(formatted_content)
            file_path = f.name
        
        print(f"تم إنشاء ملف مؤقت: {file_path}")
        
        success = False
        
        # طباعة الملف باستخدام الطريقة المناسبة
        try:
            # طريقة win32api
            import win32api
            win32api.ShellExecute(0, "printto", file_path, f'"{printer_name}"', ".", 0)
            print(f"تم إرسال الطباعة إلى {printer_name} باستخدام win32api")
            success = True
        except ImportError:
            try:
                # طريقة print command
                import subprocess
                subprocess.run(f'print /d:"{printer_name}" "{file_path}"', shell=True)
                print(f"تم إرسال الطباعة باستخدام أمر print")
                success = True
            except Exception as e:
                try:
                    # طريقة startfile
                    os.startfile(file_path, "print")
                    print("تم إرسال الطباعة باستخدام startfile")
                    success = True
                except Exception as e2:
                    print(f"خطأ في الطباعة: {e2}")
        
        # الانتظار وحذف الملف المؤقت
        time.sleep(2)
        try:
            os.unlink(file_path)
            print("تم حذف الملف المؤقت")
        except:
            print("لم يمكن حذف الملف المؤقت")
        
        return success
        
    except Exception as e:
        print(f"خطأ عام: {e}")
        return False

def print_with_linux(content, settings):
    """طباعة المحتوى باستخدام طرق Linux"""
    printer_name = settings['printer_name']
    
    if not printer_name:
        print("لم يتم تحديد اسم الطابعة")
        return False
    
    # تنسيق المحتوى
    formatted_content = format_content_for_thermal(content, settings['char_per_line'])
    
    # إنشاء ملف مؤقت
    try:
        with tempfile.NamedTemporaryFile(delete=False, suffix=".txt", mode="w", encoding="utf-8") as f:
            f.write(formatted_content)
            file_path = f.name
        
        try:
            import subprocess
            subprocess.run(["lp", "-d", printer_name, file_path])
            print(f"تم إرسال الطباعة باستخدام lp")
            success = True
        except Exception as e:
            print(f"خطأ في الطباعة: {e}")
            success = False
        
        # الانتظار وحذف الملف المؤقت
        time.sleep(2)
        try:
            os.unlink(file_path)
        except:
            pass
        
        return success
        
    except Exception as e:
        print(f"خطأ عام: {e}")
        return False

def thermal_print(content):
    """طباعة المحتوى على الطابعة الحرارية باستخدام الطريقة المناسبة"""
    # الحصول على إعدادات الطابعة
    settings = get_printer_settings()
    
    # محاولة الطباعة باستخدام escpos إذا كانت متوفرة ومدعومة
    if ESCPOS_AVAILABLE and settings['connection_type'] != 'windows':
        success = print_with_escpos(content, settings)
        if success:
            return True
    
    # استخدام طرق الطباعة المناسبة حسب نظام التشغيل
    if os.name == 'nt':  # Windows
        return print_with_windows(content, settings)
    else:  # Linux/Mac
        return print_with_linux(content, settings)

def print_entry_form(students, section, date_str=None, time_str=None, institution_name=None, school_year=None, form_title=None, form_description=None):
    """طباعة نموذج الدخول على الطابعة الحرارية"""
    # تحديد التاريخ والوقت إذا لم يتم تمريرهما
    if not date_str:
        date_str = datetime.now().strftime("%Y/%m/%d")
    if not time_str:
        time_str = datetime.now().strftime("%H:%M")
    
    # الحصول على معلومات النموذج من قاعدة البيانات إذا لم يتم تمريرها
    if not institution_name or not school_year or not form_title:
        try:
            conn = sqlite3.connect("data.db")
            cursor = conn.cursor()
            
            # استخراج اسم المؤسسة والسنة الدراسية
            cursor.execute("SELECT المؤسسة, السنة_الدراسية FROM بيانات_المؤسسة LIMIT 1")
            result = cursor.fetchone()
            if result:
                institution_name = result[0] or "المؤسسة التعليمية"
                school_year = result[1] or "2024/2025"
            
            # استخراج عنوان النموذج وملاحظاته
            cursor.execute("""
                SELECT العنوان, ملاحظات 
                FROM تعديل_المسميات 
                WHERE id = 1
            """)
            result = cursor.fetchone()
            if result:
                form_title = result[0] or "ورقة الدخول"
                form_description = result[1] or ""
            
            conn.close()
        except Exception as e:
            print(f"خطأ في استخراج معلومات النموذج: {e}")
            
            # استخدام القيم الافتراضية
            institution_name = institution_name or "المؤسسة التعليمية"
            school_year = school_year or "2024/2025"
            form_title = form_title or "ورقة الدخول"
            form_description = form_description or ""
    
    # إنشاء محتوى الطباعة
    content = f"{institution_name}\n"
    content += f"السنة الدراسية: {school_year}\n"
    content += f"{form_title}\n"
    
    if form_description:
        content += f"{form_description}\n"
    
    content += "--------------------------------\n"
    content += "رت\t           الاسم الكامل\t\n"
    content += "--------------------------------\n"
    
    # إضافة بيانات الطلاب
    for student in students:
        if isinstance(student, dict) and 'rt' in student and 'name' in student:
            content += f"{student['rt']}\t           {student['name']}\n"
        else:
            content += f"{student}\n"
    
    content += "--------------------------------\n"
    content += f"من قســـــم : {section}\n"
    content += f"التاريخ: {date_str}  الوقت: {time_str}\n\n\n\n"
    
    # طباعة المحتوى
    return thermal_print(content)

def print_late_form(students, section, date_str=None, time_str=None):
    """طباعة نموذج التأخر على الطابعة الحرارية"""
    # تحديد التاريخ والوقت إذا لم يتم تمريرهما
    if not date_str:
        date_str = datetime.now().strftime("%Y/%m/%d")
    if not time_str:
        time_str = datetime.now().strftime("%H:%M")
    
    # الحصول على معلومات النموذج
    try:
        conn = sqlite3.connect("data.db")
        cursor = conn.cursor()
        
        # استخراج اسم المؤسسة والسنة الدراسية
        cursor.execute("SELECT المؤسسة, السنة_الدراسية FROM بيانات_المؤسسة LIMIT 1")
        result = cursor.fetchone()
        institution_name = "المؤسسة التعليمية"
        school_year = "2024/2025"
        if result:
            institution_name = result[0] or institution_name
            school_year = result[1] or school_year
        
        # استخراج عنوان نموذج التأخر وملاحظاته
        cursor.execute("""
            SELECT العنوان, ملاحظات 
            FROM تعديل_المسميات 
            WHERE id = 2
        """)
        result = cursor.fetchone()
        form_title = "ورقة التأخر"
        form_description = ""
        if result:
            form_title = result[0] or form_title
            form_description = result[1] or form_description
        
        conn.close()
    except Exception as e:
        print(f"خطأ في استخراج معلومات نموذج التأخر: {e}")
        institution_name = "المؤسسة التعليمية"
        school_year = "2024/2025"
        form_title = "ورقة التأخر"
        form_description = ""
    
    # إنشاء محتوى الطباعة
    content = f"{institution_name}\n"
    content += f"السنة الدراسية: {school_year}\n"
    content += f"{form_title}\n"
    
    if form_description:
        content += f"{form_description}\n"
    
    content += "--------------------------------\n"
    content += "رت\t           الاسم الكامل\t\n"
    content += "--------------------------------\n"
    
    # إضافة بيانات الطلاب
    for student in students:
        if isinstance(student, dict) and 'rt' in student and 'name' in student:
            content += f"{student['rt']}\t           {student['name']}\n"
        else:
            content += f"{student}\n"
    
    content += "--------------------------------\n"
    content += f"من قســـــم : {section}\n"
    content += f"التاريخ: {date_str}  الوقت: {time_str}\n\n\n\n"
    
    # طباعة المحتوى
    return thermal_print(content)

# يمكن إضافة دوال مماثلة لباقي النماذج (التوجيه، الاستئذان، إلخ)

if __name__ == "__main__":
    # اختبار الطباعة
    students = [
        {"rt": "1", "name": "محمد أحمد"},
        {"rt": "2", "name": "سعيد علي"}
    ]
    section = "3-2"
    
    print("=== بدء اختبار طباعة ورقة الدخول ===")
    result = print_entry_form(students, section)
    print(f"نتيجة طباعة ورقة الدخول: {'نجاح' if result else 'فشل'}")

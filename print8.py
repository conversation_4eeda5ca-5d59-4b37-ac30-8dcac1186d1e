#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
ملف مخصص لطباعة تقرير سجلات زيارات أولياء الأمور (print8.py)
تم فصله من ملف sub17_window.py
تم تحسينه باستخدام تقنية الجداول من ReportLab لإنشاء ملفات PDF

للاستخدام:
    from print8 import print_parent_visits_report
    print_parent_visits_report(student_code)  # حيث student_code هو رمز الطالب
"""

import os
import sys
import sqlite3
import traceback
import subprocess
from datetime import datetime
from PyQt5.QtWidgets import QDialog, QVBoxLayout, QHBoxLayout, QLabel, QPushButton, QStyle
from PyQt5.QtGui import QIcon
from PyQt5.QtCore import Qt
from reportlab.lib.pagesizes import A4
from reportlab.lib.units import cm
from reportlab.pdfgen import canvas
from reportlab.pdfbase import pdfmetrics
from reportlab.pdfbase.ttfonts import TTFont
from reportlab.platypus import SimpleDocTemplate, Table, TableStyle, Paragraph, Spacer, Image
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.lib import colors

# تسجيل الخطوط العربية
try:
    # محاولة تسجيل الخط العربي الأساسي
    pdfmetrics.registerFont(TTFont("Arabic", "arial.ttf"))
    print("تم تسجيل خط Arabic (arial.ttf) بنجاح")
except Exception as font_error:
    print(f"خطأ في تسجيل خط Arabic (arial.ttf): {font_error}")
    try:
        # محاولة استخدام مسار مطلق للخط
        script_dir = os.path.dirname(os.path.abspath(__file__))
        arial_path = os.path.join(script_dir, "arial.ttf")
        if os.path.exists(arial_path):
            pdfmetrics.registerFont(TTFont("Arabic", arial_path))
            print(f"تم تسجيل خط Arabic من المسار المطلق: {arial_path}")
        else:
            print(f"خطأ: ملف الخط غير موجود في المسار: {arial_path}")
    except Exception as alt_font_error:
        print(f"خطأ في تسجيل خط Arabic من المسار المطلق: {alt_font_error}")

# محاولة تسجيل خطوط Amiri و Calibri
try:
    script_dir = os.path.dirname(os.path.abspath(__file__))

    # تحديد مسارات الخطوط
    amiri_regular_path = os.path.join(script_dir, "Amiri-Regular.ttf")
    amiri_bold_path = os.path.join(script_dir, "Amiri-Bold.ttf")
    calibri_regular_path = os.path.join(script_dir, "calibri.ttf")
    calibri_bold_path = os.path.join(script_dir, "calibrib.ttf")

    # البحث عن خطوط Calibri في مجلد الخطوط في Windows
    if os.name == 'nt':  # Windows
        windows_fonts_dir = os.path.join(os.environ.get('SystemRoot', 'C:\\Windows'), 'Fonts')
        if not os.path.exists(calibri_regular_path):
            windows_calibri_path = os.path.join(windows_fonts_dir, 'calibri.ttf')
            if os.path.exists(windows_calibri_path):
                calibri_regular_path = windows_calibri_path
                print(f"تم العثور على خط Calibri في مجلد خطوط Windows: {calibri_regular_path}")

        if not os.path.exists(calibri_bold_path):
            windows_calibri_bold_path = os.path.join(windows_fonts_dir, 'calibrib.ttf')
            if os.path.exists(windows_calibri_bold_path):
                calibri_bold_path = windows_calibri_bold_path
                print(f"تم العثور على خط Calibri Bold في مجلد خطوط Windows: {calibri_bold_path}")

    # تسجيل خطوط Amiri
    if os.path.exists(amiri_regular_path):
        pdfmetrics.registerFont(TTFont("Amiri", amiri_regular_path))
        print(f"تم تسجيل خط Amiri من المسار: {amiri_regular_path}")
    else:
        print(f"تحذير: لم يتم العثور على خط Amiri في المسار: {amiri_regular_path}")

    if os.path.exists(amiri_bold_path):
        pdfmetrics.registerFont(TTFont("Amiri-Bold", amiri_bold_path))
        print(f"تم تسجيل خط Amiri-Bold من المسار: {amiri_bold_path}")
    else:
        print(f"تحذير: لم يتم العثور على خط Amiri-Bold في المسار: {amiri_bold_path}")

    # تسجيل خطوط Calibri
    if os.path.exists(calibri_regular_path):
        pdfmetrics.registerFont(TTFont("Calibri", calibri_regular_path))
        print(f"تم تسجيل خط Calibri من المسار: {calibri_regular_path}")
    else:
        print(f"تحذير: لم يتم العثور على خط Calibri في المسار: {calibri_regular_path}")

    if os.path.exists(calibri_bold_path):
        pdfmetrics.registerFont(TTFont("Calibri-Bold", calibri_bold_path))
        print(f"تم تسجيل خط Calibri-Bold من المسار: {calibri_bold_path}")
    else:
        print(f"تحذير: لم يتم العثور على خط Calibri-Bold في المسار: {calibri_bold_path}")

    # إذا لم يتم العثور على أي خط، استخدم الخط العربي الأساسي
    registered_fonts = pdfmetrics.getRegisteredFontNames()
    if "Amiri" not in registered_fonts and "Amiri-Bold" not in registered_fonts and "Calibri" not in registered_fonts and "Calibri-Bold" not in registered_fonts:
        print("تحذير: لم يتم العثور على خطوط Amiri أو Calibri، سيتم استخدام الخط العربي الأساسي")
except Exception as font_error:
    print(f"خطأ في تسجيل الخطوط: {font_error}")

# Arabic text handling
import arabic_reshaper
from bidi.algorithm import get_display

def fix_arabic(text):
    """إصلاح النص العربي للعرض في PDF"""
    if not text:
        return ""
    try:
        reshaped_text = arabic_reshaper.reshape(str(text))
        return get_display(reshaped_text)
    except Exception as e:
        print(f"خطأ في إعادة تشكيل النص: {e}")
        return str(text)

def get_institution_info(db_path=None):
    """الحصول على معلومات المؤسسة من قاعدة البيانات"""
    try:
        if db_path is None:
            db_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "data.db")

        if not os.path.exists(db_path):
            print(f"تحذير: ملف قاعدة البيانات غير موجود: {db_path}")
            return {}

        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()

        # محاولة قراءة من جدول بيانات_المؤسسة
        try:
            cursor.execute("SELECT المؤسسة, السنة_الدراسية, ImagePath1 FROM بيانات_المؤسسة LIMIT 1")
            institution_row = cursor.fetchone()
            if institution_row:
                institution_data = {
                    'name': institution_row[0],
                    'school_year': institution_row[1],
                    'logo_path': institution_row[2] if institution_row[2] and os.path.exists(institution_row[2]) else None
                }
                return institution_data
        except Exception as e:
            print(f"خطأ في قراءة بيانات المؤسسة: {e}")

        conn.close()
    except Exception as db_error:
        print(f"خطأ في الاتصال بقاعدة البيانات: {db_error}")

    # إرجاع قاموس فارغ في حالة حدوث خطأ
    return {}

def get_student_info(student_code, db_path=None):
    """الحصول على بيانات الطالب من قاعدة البيانات"""
    student_info = {
        'student_code': student_code,
        'student_name': '',
        'level': '',
        'class_name': ''
    }

    try:
        if db_path is None:
            db_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "data.db")

        if not os.path.exists(db_path):
            print(f"تحذير: ملف قاعدة البيانات غير موجود: {db_path}")
            return student_info

        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()

        # استعلام الحصول على اسم الطالب وصفه من السجل العام واللوائح
        cursor.execute("""
            SELECT sg.الاسم_والنسب, l.المستوى, l.القسم
            FROM السجل_العام sg
            LEFT JOIN اللوائح l ON sg.الرمز = l.الرمز
            WHERE sg.الرمز = ?
            ORDER BY l.السنة_الدراسية DESC
            LIMIT 1
        """, (student_code,))

        result = cursor.fetchone()
        conn.close()

        if result:
            student_info['student_name'] = result[0]
            student_info['level'] = result[1]
            student_info['class_name'] = result[2]

    except Exception as e:
        print(f"خطأ في الحصول على بيانات الطالب: {e}")
        traceback.print_exc()

    return student_info

def get_visits_data(student_code, db_path=None):
    """الحصول على بيانات زيارات أولياء الأمور للطالب"""
    try:
        if db_path is None:
            db_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "data.db")

        if not os.path.exists(db_path):
            print(f"تحذير: ملف قاعدة البيانات غير موجود: {db_path}")
            return []

        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()

        # استعلام للحصول على بيانات الزيارات
        cursor.execute("""
            SELECT الرقم, اسم_الولي, رقم_البطاقة, تاريخ_الزيارة, وقت_الزيارة, سبب_الزيارة
            FROM زيارة_ولي_الأمر
            WHERE الرمز = ?
            ORDER BY تاريخ_الزيارة DESC, وقت_الزيارة DESC
        """, (student_code,))

        # ملاحظة: لا نقوم بعكس ترتيب الأعمدة هنا، بل نقوم بذلك عند إنشاء الجدول في PDF

        visits = cursor.fetchall()
        conn.close()

        return visits

    except Exception as e:
        print(f"خطأ في الحصول على بيانات الزيارات: {e}")
        traceback.print_exc()
        return []

def generate_visits_report_pdf(student_info, visits_data, institution_data=None):
    """إنشاء ملف PDF لتقرير زيارات أولياء الأمور"""
    try:
        # إنشاء اسم الملف
        file_name = f"سجلات_زيارات_{student_info['student_code']}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.pdf"

        # إنشاء المجلد الرئيسي على سطح المكتب
        desktop_path = os.path.join(os.path.expanduser("~"), "Desktop")
        main_folder = os.path.join(desktop_path, "تقارير برنامج المعين في الحراسة العامة")
        if not os.path.exists(main_folder):
            os.makedirs(main_folder)

        # إنشاء المجلد الفرعي لتقارير زيارة أولياء الأمور
        reports_folder = os.path.join(main_folder, "تقارير زيارة أولياء الأمور")
        if not os.path.exists(reports_folder):
            os.makedirs(reports_folder)

        # تحديد مسار الملف
        file_path = os.path.join(reports_folder, file_name)

        # إنشاء مجلد temp احتياطي في حالة فشل إنشاء المجلدات على سطح المكتب
        temp_dir = os.path.join(os.path.dirname(__file__), "temp")
        if not os.path.exists(temp_dir):
            os.makedirs(temp_dir)

        # في حالة فشل الوصول إلى سطح المكتب، استخدم المجلد الاحتياطي
        try:
            # اختبار إمكانية الكتابة في المجلد
            test_file = os.path.join(reports_folder, "test.txt")
            with open(test_file, 'w') as f:
                f.write("test")
            os.remove(test_file)
        except Exception as e:
            print(f"تعذر الكتابة في مجلد سطح المكتب: {e}")
            file_path = os.path.join(temp_dir, file_name)

        # الحصول على معلومات المؤسسة إذا لم يتم تمريرها
        if institution_data is None:
            institution_data = get_institution_info()

        # إنشاء مستند PDF
        doc = SimpleDocTemplate(
            file_path,
            pagesize=A4,
            rightMargin=0.2*cm,
            leftMargin=0.2*cm,
            topMargin=0.2*cm,
            bottomMargin=0.2*cm
        )

        # قائمة العناصر التي سيتم إضافتها للمستند
        elements = []

        # إنشاء أنماط الفقرات
        styles = getSampleStyleSheet()

        # إنشاء نمط للنص العربي
        arabic_style = ParagraphStyle(
            'ArabicStyle',
            parent=styles['Normal'],
            fontName='Arabic',
            fontSize=12,
            alignment=1,  # وسط
            leading=14,
            rightIndent=0,
            leftIndent=0
        )

        # إنشاء نمط للعناوين
        title_style = ParagraphStyle(
            'TitleStyle',
            parent=styles['Title'],
            fontName='Calibri-Bold',
            fontSize=17,
            textColor=colors.darkblue,
            alignment=1,  # وسط
            spaceAfter=0.1*cm
        )

        # إنشاء نمط للعناوين الفرعية
        subtitle_style = ParagraphStyle(
            'SubtitleStyle',
            parent=styles['Heading2'],
            fontName='Calibri-Bold',
            fontSize=16,
            textColor=colors.darkblue,
            alignment=1,  # وسط
            spaceAfter=0.1*cm
        )

        # إنشاء نمط للعناوين الفرعية الثانوية
        subsubtitle_style = ParagraphStyle(
            'SubSubtitleStyle',
            parent=styles['Heading3'],
            fontName='Calibri-Bold',
            fontSize=14,
            textColor=colors.darkblue,
            alignment=1,  # وسط
            spaceAfter=0.1*cm
        )

        # إضافة الشعار
        logo_path = institution_data.get('logo_path')
        if logo_path and os.path.exists(logo_path):
            try:
                # إنشاء صورة بعرض 200 وارتفاع 90
                img = Image(logo_path, width=200, height=90)
                img.hAlign = 'CENTER'  # محاذاة الصورة للوسط
                elements.append(img)
                elements.append(Spacer(1, 0.1*cm))  # مسافة 0.1 سم بعد الشعار
                print(f"تم إضافة الشعار بنجاح من: {logo_path}")
            except Exception as logo_error:
                print(f"خطأ في إضافة الشعار: {logo_error}")

        # إضافة اسم المؤسسة
        institution_name = institution_data.get('name', 'المؤسسة التعليمية')
        if institution_name:
            elements.append(Paragraph(fix_arabic(institution_name), title_style))
            elements.append(Spacer(1, 0.1*cm))  # مسافة 0.1 سم بعد اسم المؤسسة

        # إضافة العنوان الرئيسي
        elements.append(Paragraph(fix_arabic("سجلات زيارات أولياء الأمور"), subtitle_style))
        elements.append(Spacer(1, 0.1*cm))  # مسافة 0.1 سم بعد العنوان الرئيسي

        # إضافة العنوان الفرعي (السنة الدراسية)
        school_year = institution_data.get('school_year', '')
        if school_year:
            elements.append(Paragraph(fix_arabic(f"السنة الدراسية: {school_year}"), subsubtitle_style))
            elements.append(Spacer(1, 0.1*cm))  # مسافة 0.1 سم بعد العنوان الفرعي

        # إنشاء جدول معلومات التلميذ
        # تحضير البيانات
        student_data = [
            # الصف الأول: رمز التلميذ واسم التلميذ
            [
                fix_arabic(student_info.get('student_code', '')),
                fix_arabic("رمز التلميذ:"),
                fix_arabic(student_info.get('student_name', '')),
                fix_arabic("اسم التلميذ:")
            ],
            # الصف الثاني: المستوى والقسم
            [
                fix_arabic(student_info.get('level', '')),
                fix_arabic("المستوى:"),
                fix_arabic(student_info.get('class_name', '')),
                fix_arabic("القسم:")
            ]
        ]

        # إنشاء الجدول
        # تحديد عرض موحد لمسميات الأعمدة
        label_width = 3*cm  # عرض موحد لمسميات الأعمدة
        page_width = A4[0]  # عرض صفحة A4
        page_margin = 0.2*cm  # الهامش المستخدم في المستند (0.2 سم)
        available_width = page_width - 2*page_margin
        col_widths = [(available_width - 2*label_width)/2, label_width, (available_width - 2*label_width)/2, label_width]  # عرض الأعمدة
        student_table = Table(student_data, colWidths=col_widths)

        # تنسيق الجدول
        student_table.setStyle(TableStyle([
            # الحدود
            ('GRID', (0, 0), (-1, -1), 1, colors.darkblue),
            # الخط
            ('FONTNAME', (0, 0), (-1, -1), 'Calibri'),
            ('FONTNAME', (1, 0), (1, -1), 'Calibri-Bold'),  # عناوين الأعمدة الأولى
            ('FONTNAME', (3, 0), (3, -1), 'Calibri-Bold'),  # عناوين الأعمدة الثانية
            # حجم الخط
            ('FONTSIZE', (0, 0), (-1, -1), 12),
            # المحاذاة
            ('ALIGN', (0, 0), (0, -1), 'LEFT'),  # قيم العمود الأول محاذاة لليسار
            ('ALIGN', (1, 0), (1, -1), 'RIGHT'),  # عناوين العمود الأول محاذاة لليمين
            ('ALIGN', (2, 0), (2, -1), 'LEFT'),  # قيم العمود الثاني محاذاة لليسار
            ('ALIGN', (3, 0), (3, -1), 'RIGHT'),  # عناوين العمود الثاني محاذاة لليمين
            # لون الخلفية للعناوين
            ('BACKGROUND', (1, 0), (1, -1), colors.lightgrey),
            ('BACKGROUND', (3, 0), (3, -1), colors.lightgrey),
            # المسافات الداخلية
            ('LEFTPADDING', (0, 0), (-1, -1), 6),
            ('RIGHTPADDING', (0, 0), (-1, -1), 6),
            ('TOPPADDING', (0, 0), (-1, -1), 6),
            ('BOTTOMPADDING', (0, 0), (-1, -1), 6),
        ]))

        # إضافة الجدول إلى المستند
        elements.append(student_table)
        elements.append(Spacer(1, 0.5*cm))  # مسافة 0.5 سم بعد جدول معلومات التلميذ

        # إضافة عنوان جدول الزيارات
        elements.append(Paragraph(fix_arabic(f"سجلات الزيارات ({len(visits_data)})"), subsubtitle_style))
        elements.append(Spacer(1, 0.1*cm))  # مسافة 0.1 سم بعد العنوان

        # إنشاء جدول الزيارات
        # تحضير البيانات
        visits_table_data = []

        # إضافة رأس الجدول (بترتيب معكوس)
        header_row = [
            fix_arabic("سبب الزيارة"),
            fix_arabic("وقت الزيارة"),
            fix_arabic("تاريخ الزيارة"),
            fix_arabic("رقم البطاقة"),
            fix_arabic("اسم الولي"),
            fix_arabic("الرقم")
        ]
        visits_table_data.append(header_row)

        # إضافة بيانات الزيارات (بترتيب معكوس)
        for visit in visits_data:
            row = []
            # عكس ترتيب البيانات
            for cell in reversed(visit):
                row.append(fix_arabic(cell))
            visits_table_data.append(row)

        # إنشاء الجدول
        # تحديد عرض الأعمدة (بترتيب معكوس)
        col_widths = [5.5*cm, 2*cm, 3*cm, 3*cm, 4*cm, 1.5*cm]  # عرض الأعمدة
        visits_table = Table(visits_table_data, colWidths=col_widths, repeatRows=1)

        # تنسيق الجدول
        visits_table.setStyle(TableStyle([
            # الحدود
            ('GRID', (0, 0), (-1, -1), 1, colors.darkblue),
            # الخط
            ('FONTNAME', (0, 0), (-1, 0), 'Calibri-Bold'),  # رأس الجدول
            ('FONTNAME', (0, 1), (-1, -1), 'Calibri'),  # بيانات الجدول
            # حجم الخط
            ('FONTSIZE', (0, 0), (-1, -1), 12),
            # المحاذاة
            ('ALIGN', (0, 0), (-1, 0), 'CENTER'),  # محاذاة رأس الجدول للوسط
            ('ALIGN', (0, 1), (-1, -1), 'RIGHT'),  # محاذاة بيانات الجدول لليمين
            # لون الخلفية لرأس الجدول
            ('BACKGROUND', (0, 0), (-1, 0), colors.darkblue),
            ('TEXTCOLOR', (0, 0), (-1, 0), colors.white),
            # لون الخلفية للصفوف البديلة
            ('ROWBACKGROUNDS', (0, 1), (-1, -1), [colors.white, colors.lightgrey]),
            # المسافات الداخلية
            ('LEFTPADDING', (0, 0), (-1, -1), 6),
            ('RIGHTPADDING', (0, 0), (-1, -1), 6),
            ('TOPPADDING', (0, 0), (-1, -1), 6),
            ('BOTTOMPADDING', (0, 0), (-1, -1), 6),
            # ارتفاع الصفوف
            ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
        ]))

        # إضافة الجدول إلى المستند
        elements.append(visits_table)

        # إضافة تاريخ الطبع
        current_date = datetime.now().strftime("%Y-%m-%d")
        elements.append(Spacer(1, 1*cm))
        elements.append(Paragraph(fix_arabic(f"تاريخ الطبع: {current_date}"), arabic_style))

        # بناء المستند
        doc.build(elements)

        return True, file_path

    except Exception as e:
        print(f"خطأ في إنشاء ملف PDF لتقرير زيارات أولياء الأمور: {str(e)}")
        traceback.print_exc()
        return False, None

def open_pdf(filename):
    """Opens the generated PDF file using the default system viewer."""
    try:
        absolute_path = os.path.abspath(filename)
        print(f"محاولة فتح الملف: {absolute_path}")

        try:
            # فتح الملف باستخدام الطريقة المناسبة للنظام
            if sys.platform == "win32":
                os.startfile(absolute_path)
            elif sys.platform == "darwin":  # macOS
                subprocess.run(['open', absolute_path], check=True)
            else:  # Linux وأنظمة Unix الأخرى
                subprocess.run(['xdg-open', absolute_path], check=True)
        except OSError as e:
            # تحقق من رمز الخطأ الخاص بـ "No application associated"
            if hasattr(e, 'winerror') and e.winerror == 1155:  # WinError 1155
                print(f"خطأ فتح الملف: لا يوجد تطبيق مرتبط لفتح ملفات PDF.")

                # إنشاء نافذة حوار مخصصة
                dialog = QDialog()
                dialog.setWindowTitle("فشل فتح الملف")
                dialog.setFixedSize(500, 250)
                dialog.setLayoutDirection(Qt.RightToLeft)

                # تنسيق النافذة
                dialog.setStyleSheet("""
                    QDialog {
                        background-color: #fff3cd;
                        border: 2px solid #ffeeba;
                        border-radius: 5px;
                    }
                    QLabel#messageLabel {
                        color: #856404;
                        font-weight: bold;
                        font-family: Calibri;
                        font-size: 12pt;
                        padding: 10px;
                        background-color: white;
                        border: 1px solid #ffeeba;
                        border-radius: 5px;
                    }
                    QLabel#titleLabel {
                        color: #856404;
                        font-weight: bold;
                        font-family: Calibri;
                        font-size: 14pt;
                    }
                    QPushButton {
                        background-color: #f39c12;
                        color: white;
                        border-radius: 5px;
                        padding: 8px 16px;
                        font-weight: bold;
                        font-family: Calibri;
                        font-size: 13pt;
                        min-height: 35px;
                    }
                    QPushButton:hover {
                        background-color: #e67e22;
                    }
                """)

                # إنشاء تخطيط النافذة
                layout = QVBoxLayout(dialog)
                layout.setContentsMargins(20, 20, 20, 20)
                layout.setSpacing(15)

                # إضافة عنوان الرسالة
                title_label = QLabel("تعذر فتح ملف PDF")
                title_label.setObjectName("titleLabel")
                title_label.setAlignment(Qt.AlignCenter)
                layout.addWidget(title_label)

                # إضافة أيقونة التحذير
                icon_label = QLabel()
                icon_label.setAlignment(Qt.AlignCenter)
                icon = dialog.style().standardIcon(QStyle.SP_MessageBoxWarning)
                icon_label.setPixmap(icon.pixmap(48, 48))
                layout.addWidget(icon_label)

                # إضافة نص الرسالة في مربع نص واضح
                message_label = QLabel(
                    f"تم إنشاء ملف PDF بنجاح في:\n{absolute_path}\n\n"
                    "ولكن، لم يتم العثور على تطبيق افتراضي لفتح ملفات PDF.\n"
                    "الرجاء فتح الملف يدوياً."
                )
                message_label.setObjectName("messageLabel")
                message_label.setAlignment(Qt.AlignRight | Qt.AlignVCenter)
                message_label.setWordWrap(True)
                message_label.setMinimumHeight(100)
                layout.addWidget(message_label)

                # إضافة زر موافق
                ok_button = QPushButton("موافق")
                ok_button.setCursor(Qt.PointingHandCursor)
                ok_button.clicked.connect(dialog.accept)
                layout.addWidget(ok_button)

                # عرض النافذة
                dialog.exec_()

            else:
                # عرض الأخطاء الأخرى
                print(f"خطأ غير متوقع في فتح الملف: {e}")
                traceback.print_exc()

                # إنشاء نافذة حوار مخصصة للخطأ
                dialog = QDialog()
                dialog.setWindowTitle("خطأ")
                dialog.setFixedSize(500, 250)
                dialog.setLayoutDirection(Qt.RightToLeft)

                # تنسيق النافذة
                dialog.setStyleSheet("""
                    QDialog {
                        background-color: #f8d7da;
                        border: 2px solid #f5c6cb;
                        border-radius: 5px;
                    }
                    QLabel#messageLabel {
                        color: #721c24;
                        font-weight: bold;
                        font-family: Calibri;
                        font-size: 12pt;
                        padding: 10px;
                        background-color: white;
                        border: 1px solid #f5c6cb;
                        border-radius: 5px;
                    }
                    QLabel#titleLabel {
                        color: #721c24;
                        font-weight: bold;
                        font-family: Calibri;
                        font-size: 14pt;
                    }
                    QPushButton {
                        background-color: #e74c3c;
                        color: white;
                        border-radius: 5px;
                        padding: 8px 16px;
                        font-weight: bold;
                        font-family: Calibri;
                        font-size: 13pt;
                        min-height: 35px;
                    }
                    QPushButton:hover {
                        background-color: #c0392b;
                    }
                """)

                # إنشاء تخطيط النافذة
                layout = QVBoxLayout(dialog)
                layout.setContentsMargins(20, 20, 20, 20)
                layout.setSpacing(15)

                # إضافة عنوان الرسالة
                title_label = QLabel("خطأ في فتح الملف")
                title_label.setObjectName("titleLabel")
                title_label.setAlignment(Qt.AlignCenter)
                layout.addWidget(title_label)

                # إضافة أيقونة الخطأ
                icon_label = QLabel()
                icon_label.setAlignment(Qt.AlignCenter)
                icon = dialog.style().standardIcon(QStyle.SP_MessageBoxCritical)
                icon_label.setPixmap(icon.pixmap(48, 48))
                layout.addWidget(icon_label)

                # إضافة نص الرسالة في مربع نص واضح
                message_label = QLabel(f"حدث خطأ أثناء محاولة فتح الملف:\n{e}")
                message_label.setObjectName("messageLabel")
                message_label.setAlignment(Qt.AlignRight | Qt.AlignVCenter)
                message_label.setWordWrap(True)
                message_label.setMinimumHeight(80)
                layout.addWidget(message_label)

                # إضافة زر موافق
                ok_button = QPushButton("موافق")
                ok_button.setCursor(Qt.PointingHandCursor)
                ok_button.clicked.connect(dialog.accept)
                layout.addWidget(ok_button)

                # عرض النافذة
                dialog.exec_()

        except Exception as open_error:
            # التعامل مع أي أخطاء أخرى قد تحدث
            print(f"خطأ عام في فتح الملف: {open_error}")
            traceback.print_exc()

            # إنشاء نافذة حوار مخصصة للخطأ العام
            dialog = QDialog()
            dialog.setWindowTitle("خطأ")
            dialog.setFixedSize(500, 250)
            dialog.setLayoutDirection(Qt.RightToLeft)

            # تنسيق النافذة
            dialog.setStyleSheet("""
                QDialog {
                    background-color: #f8d7da;
                    border: 2px solid #f5c6cb;
                    border-radius: 5px;
                }
                QLabel#messageLabel {
                    color: #721c24;
                    font-weight: bold;
                    font-family: Calibri;
                    font-size: 12pt;
                    padding: 10px;
                    background-color: white;
                    border: 1px solid #f5c6cb;
                    border-radius: 5px;
                }
                QLabel#titleLabel {
                    color: #721c24;
                    font-weight: bold;
                    font-family: Calibri;
                    font-size: 14pt;
                }
                QPushButton {
                    background-color: #e74c3c;
                    color: white;
                    border-radius: 5px;
                    padding: 8px 16px;
                    font-weight: bold;
                    font-family: Calibri;
                    font-size: 13pt;
                    min-height: 35px;
                }
                QPushButton:hover {
                    background-color: #c0392b;
                }
            """)

            # إنشاء تخطيط النافذة
            layout = QVBoxLayout(dialog)
            layout.setContentsMargins(20, 20, 20, 20)
            layout.setSpacing(15)

            # إضافة عنوان الرسالة
            title_label = QLabel("خطأ عام في فتح الملف")
            title_label.setObjectName("titleLabel")
            title_label.setAlignment(Qt.AlignCenter)
            layout.addWidget(title_label)

            # إضافة أيقونة الخطأ
            icon_label = QLabel()
            icon_label.setAlignment(Qt.AlignCenter)
            icon = dialog.style().standardIcon(QStyle.SP_MessageBoxCritical)
            icon_label.setPixmap(icon.pixmap(48, 48))
            layout.addWidget(icon_label)

            # إضافة نص الرسالة في مربع نص واضح
            message_label = QLabel(f"حدث خطأ عام أثناء محاولة فتح الملف:\n{open_error}")
            message_label.setObjectName("messageLabel")
            message_label.setAlignment(Qt.AlignRight | Qt.AlignVCenter)
            message_label.setWordWrap(True)
            message_label.setMinimumHeight(80)
            layout.addWidget(message_label)

            # إضافة زر موافق
            ok_button = QPushButton("موافق")
            ok_button.setCursor(Qt.PointingHandCursor)
            ok_button.clicked.connect(dialog.accept)
            layout.addWidget(ok_button)

            # عرض النافذة
            dialog.exec_()

    except Exception as path_error:
        print(f"خطأ في تحديد مسار الملف: {path_error}")
        traceback.print_exc()



def show_success_message(file_path):
    """عرض رسالة نجاح مخصصة"""
    # إنشاء نافذة حوار مخصصة
    dialog = QDialog()
    dialog.setWindowTitle("تم إنشاء التقرير بنجاح")
    dialog.setFixedSize(500, 300)
    dialog.setLayoutDirection(Qt.RightToLeft)

    # تنسيق النافذة
    dialog.setStyleSheet("""
        QDialog {
            background-color: #d4edda;
            border: 2px solid #c3e6cb;
            border-radius: 5px;
        }
        QLabel#titleLabel {
            color: #2c3e50;
            font-weight: bold;
            font-family: Calibri;
            font-size: 14pt;
        }
        QLabel#messageLabel {
            color: #2c3e50;
            font-weight: bold;
            font-family: Calibri;
            font-size: 12pt;
            padding: 10px;
            background-color: white;
            border: 1px solid #c3e6cb;
            border-radius: 5px;
        }
        QPushButton {
            background-color: #27ae60;
            color: white;
            border-radius: 5px;
            padding: 8px 16px;
            font-weight: bold;
            font-family: Calibri;
            font-size: 13pt;
            min-height: 35px;
        }
        QPushButton:hover {
            background-color: #219a52;
        }
    """)

    # إنشاء تخطيط النافذة
    layout = QVBoxLayout(dialog)
    layout.setContentsMargins(20, 20, 20, 20)
    layout.setSpacing(15)

    # إضافة عنوان الرسالة
    title_label = QLabel("تم إنشاء التقرير بنجاح")
    title_label.setObjectName("titleLabel")
    title_label.setAlignment(Qt.AlignCenter)
    layout.addWidget(title_label)

    # إضافة أيقونة النجاح
    icon_label = QLabel()
    icon_label.setAlignment(Qt.AlignCenter)
    icon = dialog.style().standardIcon(QStyle.SP_MessageBoxInformation)
    icon_label.setPixmap(icon.pixmap(48, 48))
    layout.addWidget(icon_label)

    # إضافة نص الرسالة في مربع نص واضح
    file_path_short = os.path.basename(file_path)
    message_label = QLabel(f"تم إنشاء التقرير وحفظه في المسار التالي:\n\n{file_path}\n\nاسم الملف: {file_path_short}\n\nهل تريد فتح الملف الآن؟")
    message_label.setObjectName("messageLabel")
    message_label.setAlignment(Qt.AlignRight | Qt.AlignVCenter)
    message_label.setWordWrap(True)
    message_label.setMinimumHeight(120)
    layout.addWidget(message_label)

    # إضافة أزرار
    buttons_layout = QHBoxLayout()

    # زر نعم
    yes_button = QPushButton("نعم، فتح الآن")
    yes_button.setCursor(Qt.PointingHandCursor)
    yes_button.clicked.connect(lambda: (dialog.accept(), open_pdf(file_path)))
    buttons_layout.addWidget(yes_button)

    # زر لا
    no_button = QPushButton("لا")
    no_button.setCursor(Qt.PointingHandCursor)
    no_button.clicked.connect(dialog.reject)
    buttons_layout.addWidget(no_button)

    layout.addLayout(buttons_layout)

    # عرض النافذة
    dialog.exec_()

def print_parent_visits_report(student_code, db_path=None):
    """الدالة الرئيسية لطباعة تقرير سجلات زيارات أولياء الأمور"""
    try:
        # الحصول على بيانات الطالب
        student_info = get_student_info(student_code, db_path)

        # الحصول على بيانات الزيارات
        visits_data = get_visits_data(student_code, db_path)

        if not visits_data:
            # إنشاء نافذة حوار مخصصة
            dialog = QDialog()
            dialog.setWindowTitle("معلومات")
            dialog.setFixedSize(500, 250)
            dialog.setLayoutDirection(Qt.RightToLeft)

            # تنسيق النافذة
            dialog.setStyleSheet("""
                QDialog {
                    background-color: #d1ecf1;
                    border: 2px solid #bee5eb;
                    border-radius: 5px;
                }
                QLabel#messageLabel {
                    color: #0c5460;
                    font-weight: bold;
                    font-family: Calibri;
                    font-size: 12pt;
                    padding: 10px;
                    background-color: white;
                    border: 1px solid #bee5eb;
                    border-radius: 5px;
                }
                QLabel#titleLabel {
                    color: #0c5460;
                    font-weight: bold;
                    font-family: Calibri;
                    font-size: 14pt;
                }
                QPushButton {
                    background-color: #3498db;
                    color: white;
                    border-radius: 5px;
                    padding: 8px 16px;
                    font-weight: bold;
                    font-family: Calibri;
                    font-size: 13pt;
                    min-height: 35px;
                }
                QPushButton:hover {
                    background-color: #2980b9;
                }
            """)

            # إنشاء تخطيط النافذة
            layout = QVBoxLayout(dialog)
            layout.setContentsMargins(20, 20, 20, 20)
            layout.setSpacing(15)

            # إضافة عنوان الرسالة
            title_label = QLabel("لا توجد سجلات زيارات")
            title_label.setObjectName("titleLabel")
            title_label.setAlignment(Qt.AlignCenter)
            layout.addWidget(title_label)

            # إضافة أيقونة المعلومات
            icon_label = QLabel()
            icon_label.setAlignment(Qt.AlignCenter)
            icon = dialog.style().standardIcon(QStyle.SP_MessageBoxInformation)
            icon_label.setPixmap(icon.pixmap(48, 48))
            layout.addWidget(icon_label)

            # إضافة نص الرسالة في مربع نص واضح
            message_label = QLabel("لا توجد سجلات زيارات متاحة للطباعة لهذا الطالب.\n\nيرجى التأكد من وجود زيارات مسجلة أولاً.")
            message_label.setObjectName("messageLabel")
            message_label.setAlignment(Qt.AlignRight | Qt.AlignVCenter)
            message_label.setWordWrap(True)
            message_label.setMinimumHeight(80)
            layout.addWidget(message_label)

            # إضافة زر موافق
            ok_button = QPushButton("موافق")
            ok_button.setCursor(Qt.PointingHandCursor)
            ok_button.clicked.connect(dialog.accept)
            layout.addWidget(ok_button)

            # عرض النافذة
            dialog.exec_()

            return False, None

        # الحصول على معلومات المؤسسة
        institution_data = get_institution_info(db_path)

        # إنشاء تقرير PDF
        success, file_path = generate_visits_report_pdf(student_info, visits_data, institution_data)

        if success:
            # عرض رسالة نجاح مخصصة
            show_success_message(file_path)
            return True, file_path
        else:
            # إنشاء نافذة حوار مخصصة للخطأ
            dialog = QDialog()
            dialog.setWindowTitle("خطأ في إنشاء التقرير")
            dialog.setFixedSize(500, 250)
            dialog.setLayoutDirection(Qt.RightToLeft)

            # تنسيق النافذة
            dialog.setStyleSheet("""
                QDialog {
                    background-color: #f8d7da;
                    border: 2px solid #f5c6cb;
                    border-radius: 5px;
                }
                QLabel#messageLabel {
                    color: #721c24;
                    font-weight: bold;
                    font-family: Calibri;
                    font-size: 12pt;
                    padding: 10px;
                    background-color: white;
                    border: 1px solid #f5c6cb;
                    border-radius: 5px;
                }
                QLabel#titleLabel {
                    color: #721c24;
                    font-weight: bold;
                    font-family: Calibri;
                    font-size: 14pt;
                }
                QPushButton {
                    background-color: #e74c3c;
                    color: white;
                    border-radius: 5px;
                    padding: 8px 16px;
                    font-weight: bold;
                    font-family: Calibri;
                    font-size: 13pt;
                    min-height: 35px;
                }
                QPushButton:hover {
                    background-color: #c0392b;
                }
            """)

            # إنشاء تخطيط النافذة
            layout = QVBoxLayout(dialog)
            layout.setContentsMargins(20, 20, 20, 20)
            layout.setSpacing(15)

            # إضافة عنوان الرسالة
            title_label = QLabel("خطأ في إنشاء التقرير")
            title_label.setObjectName("titleLabel")
            title_label.setAlignment(Qt.AlignCenter)
            layout.addWidget(title_label)

            # إضافة أيقونة الخطأ
            icon_label = QLabel()
            icon_label.setAlignment(Qt.AlignCenter)
            icon = dialog.style().standardIcon(QStyle.SP_MessageBoxCritical)
            icon_label.setPixmap(icon.pixmap(48, 48))
            layout.addWidget(icon_label)

            # إضافة نص الرسالة في مربع نص واضح
            message_label = QLabel("فشل إنشاء تقرير سجلات الزيارات. يرجى التحقق من السجلات للمزيد من المعلومات.")
            message_label.setObjectName("messageLabel")
            message_label.setAlignment(Qt.AlignRight | Qt.AlignVCenter)
            message_label.setWordWrap(True)
            message_label.setMinimumHeight(80)
            layout.addWidget(message_label)

            # إضافة زر موافق
            ok_button = QPushButton("موافق")
            ok_button.setCursor(Qt.PointingHandCursor)
            ok_button.clicked.connect(dialog.accept)
            layout.addWidget(ok_button)

            # عرض النافذة
            dialog.exec_()

            return False, None

    except Exception as e:
        print(f"خطأ في طباعة تقرير سجلات الزيارات: {str(e)}")
        traceback.print_exc()

        # إنشاء نافذة حوار مخصصة للخطأ
        dialog = QDialog()
        dialog.setWindowTitle("خطأ في إنشاء التقرير")
        dialog.setFixedSize(500, 250)
        dialog.setLayoutDirection(Qt.RightToLeft)

        # تنسيق النافذة
        dialog.setStyleSheet("""
            QDialog {
                background-color: #f8d7da;
                border: 2px solid #f5c6cb;
                border-radius: 5px;
            }
            QLabel#messageLabel {
                color: #721c24;
                font-weight: bold;
                font-family: Calibri;
                font-size: 12pt;
                padding: 10px;
                background-color: white;
                border: 1px solid #f5c6cb;
                border-radius: 5px;
            }
            QLabel#titleLabel {
                color: #721c24;
                font-weight: bold;
                font-family: Calibri;
                font-size: 14pt;
            }
            QPushButton {
                background-color: #e74c3c;
                color: white;
                border-radius: 5px;
                padding: 8px 16px;
                font-weight: bold;
                font-family: Calibri;
                font-size: 13pt;
                min-height: 35px;
            }
            QPushButton:hover {
                background-color: #c0392b;
            }
        """)

        # إنشاء تخطيط النافذة
        layout = QVBoxLayout(dialog)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(15)

        # إضافة عنوان الرسالة
        title_label = QLabel("خطأ في إنشاء التقرير")
        title_label.setObjectName("titleLabel")
        title_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(title_label)

        # إضافة أيقونة الخطأ
        icon_label = QLabel()
        icon_label.setAlignment(Qt.AlignCenter)
        icon = dialog.style().standardIcon(QStyle.SP_MessageBoxCritical)
        icon_label.setPixmap(icon.pixmap(48, 48))
        layout.addWidget(icon_label)

        # إضافة نص الرسالة في مربع نص واضح
        message_label = QLabel(f"حدث خطأ أثناء محاولة إنشاء تقرير الزيارات:\n{str(e)}")
        message_label.setObjectName("messageLabel")
        message_label.setAlignment(Qt.AlignRight | Qt.AlignVCenter)
        message_label.setWordWrap(True)
        message_label.setMinimumHeight(80)
        layout.addWidget(message_label)

        # إضافة زر موافق
        ok_button = QPushButton("موافق")
        ok_button.setCursor(Qt.PointingHandCursor)
        ok_button.clicked.connect(dialog.accept)
        layout.addWidget(ok_button)

        # عرض النافذة
        dialog.exec_()

        return False, None

# تنفيذ اختباري للتأكد من عمل الملف بشكل مستقل
if __name__ == "__main__":
    if len(sys.argv) > 1:
        student_code = sys.argv[1]
        print(f"جاري طباعة تقرير سجلات الزيارات للطالب ذو الرمز {student_code}...")
        print_parent_visits_report(student_code)
    else:
        print("الاستخدام: python print8.py <رمز_الطالب>")
        print("مثال: python print8.py A123456")

from reportlab.lib.pagesizes import A4, landscape
from reportlab.lib.units import cm
from reportlab.pdfgen import canvas
from reportlab.pdfbase import pdfmetrics
from reportlab.pdfbase.ttfonts import TTFont
import arabic_reshaper
from bidi.algorithm import get_display
import os
import sqlite3
import tempfile
from datetime import datetime

class PDFTemplateManager:
    def __init__(self):
        # تسجيل الخط العربي
        pdfmetrics.registerFont(TTFont("Arabic", "arial.ttf"))
        self.temp_dir = tempfile.gettempdir()

        # إعدادات عامة
        self.settings = {
            "logo_top": 3 * cm,
            "logo_width": 300,
            "logo_height": 70,
            "title_top": 4 * cm,
            "institution_font_size": 18,
            "form_title_font_size": 14,
            "content_font_size": 14,
            "line_spacing": 1 * cm
        }

    def reshape(self, text):
        """تنسيق النص العربي"""
        return get_display(arabic_reshaper.reshape(str(text)))

    def get_institution_data(self):
        """جلب بيانات المؤسسة"""
        try:
            conn = sqlite3.connect("data.db")
            cur = conn.cursor()
            cur.execute("""
                SELECT المؤسسة, السنة_الدراسية, الأسدس, ImagePath1 
                FROM بيانات_المؤسسة LIMIT 1
            """)
            row = cur.fetchone()
            conn.close()
            return {
                "name": row[0] if row else "المؤسسة التعليمية",
                "year": row[1] if row else "2024/2025",
                "semester": row[2] if row else "الأول",
                "logo_path": row[3] if row else None
            }
        except Exception as e:
            print(f"خطأ في استخراج بيانات المؤسسة: {e}")
            return None

    def draw_header(self, c, width, height, title=""):
        """رسم رأس الصفحة مع شعار وعنوان المؤسسة"""
        inst_data = self.get_institution_data()
        if not inst_data:
            return height - 5 * cm

        # رسم الشعار
        if inst_data["logo_path"] and os.path.exists(inst_data["logo_path"]):
            logo_x = (width - self.settings["logo_width"]) / 2
            logo_y = height - self.settings["logo_top"]
            c.drawImage(
                inst_data["logo_path"], 
                logo_x, logo_y, 
                width=self.settings["logo_width"],
                height=self.settings["logo_height"],
                preserveAspectRatio=True
            )

        # كتابة عنوان المؤسسة
        y = height - self.settings["title_top"]
        c.setFont("Arabic", self.settings["institution_font_size"])
        c.drawCentredString(width/2, y, self.reshape(inst_data["name"]))
        
        # كتابة عنوان المستند
        if title:
            y -= 20
            c.setFont("Arabic", self.settings["form_title_font_size"])
            c.drawCentredString(width/2, y, self.reshape(title))

        return y - self.settings["line_spacing"]

    def create_violation_template(self, filename):
        """إنشاء قالب المخالفة"""
        filepath = os.path.join(self.temp_dir, filename)
        c = canvas.Canvas(filepath, pagesize=A4)
        width, height = A4

        # رسم الرأس
        y = self.draw_header(c, width, height, "ورقة المخالفة المدرسية")

        # رسم محتوى المخالفة
        c.setFont("Arabic", self.settings["content_font_size"])
        fields = [
            "التاريخ:", "رقم التسجيل:", "اسم التلميذ:", "القسم:",
            "المستوى:", "المادة:", "الأستاذ:", "نوع المخالفة:",
            "الإجراءات المتخذة:", "إجراءات الحراسة:"
        ]

        for field in fields:
            c.drawString(450, y, self.reshape(field))
            y -= 30

        # رسم منطقة التوقيعات
        y -= 30
        c.drawString(450, y, self.reshape("توقيع الحارس العام"))
        c.drawString(100, y, self.reshape("ختم المؤسسة"))

        c.save()
        return filepath

    def fill_template(self, template_path, data):
        """ملء قالب بالبيانات"""
        if not os.path.exists(template_path):
            return None

        output_path = template_path.replace('.pdf', '_filled.pdf')
        c = canvas.Canvas(output_path, pagesize=A4)
        width, height = A4

        # رسم الرأس
        y = self.draw_header(c, width, height, "ورقة المخالفة المدرسية")

        # ملء البيانات
        c.setFont("Arabic", self.settings["content_font_size"])
        field_positions = {
            "date": (450, y),
            "student_code": (450, y-30),
            "student_name": (450, y-60),
            "section": (450, y-90),
            "level": (450, y-120),
            "subject": (450, y-150),
            "teacher": (450, y-180),
            "notes": (450, y-210),
            "procedures": (450, y-240),
            "guard_actions": (450, y-270)
        }

        for key, value in data.items():
            if key in field_positions:
                x, y = field_positions[key]
                c.drawString(x, y, self.reshape(str(value)))

        c.save()
        return output_path

    def create_record_template(self, filename):
        """إنشاء قالب سجل المخالفات"""
        filepath = os.path.join(self.temp_dir, filename)
        c = canvas.Canvas(filepath, pagesize=landscape(A4))
        width, height = landscape(A4)

        # تسجيل الخط العربي
        pdfmetrics.registerFont(TTFont('Arabic', 'arial.ttf'))

        def draw_page_header():
            """رسم رأس كل صفحة"""
            # الشعار والعنوان
            inst_data = self.get_institution_data()
            if inst_data["logo_path"] and os.path.exists(inst_data["logo_path"]):
                c.drawImage(
                    inst_data["logo_path"],
                    width/2 - 100,
                    height - 90,
                    width=200,
                    height=70,
                    preserveAspectRatio=True
                )
            
            c.setFont('Arabic', 22)
            c.drawString(width/2 - 100, height - 120, self.reshape("سجل المخالفات المدرسية"))
            c.setFont('Arabic', 16)
            c.drawString(width - 200, height - 150, self.reshape(f"السنة الدراسية: {inst_data['year']}"))
            c.drawString(100, height - 150, self.reshape(f"الأسدس: {inst_data['semester']}"))
            
            # رسم الجدول
            c.setStrokeColorRGB(0, 0, 0)
            c.setLineWidth(1)
            
            # رسم خطوط الجدول الرئيسية
            table_top = height - 180
            c.rect(50, 50, width - 100, table_top - 50)  # الإطار الخارجي
            
            # رسم رأس الجدول
            header_height = 30
            c.setFillColorRGB(0.2, 0.4, 0.8)  # لون أزرق للخلفية
            c.rect(50, table_top - header_height, width - 100, header_height, fill=1)
            
            # عناوين الأعمدة
            c.setFillColorRGB(1, 1, 1)  # لون أبيض للنص
            c.setFont('Arabic', 14)
            headers = ["التاريخ", "الرمز", "التلميذ", "القسم", "المخالفة", "الإجراءات", "التوقيع"]
            col_widths = [0.1, 0.15, 0.2, 0.1, 0.2, 0.15, 0.1]  # نسب عرض الأعمدة
            
            x = width - 70
            for i, (header, width_ratio) in enumerate(zip(headers, col_widths)):
                col_width = (width - 120) * width_ratio
                c.drawString(x - col_width/2, table_top - 22, self.reshape(header))
                x -= col_width
                # رسم خط فاصل عمودي
                if i < len(headers) - 1:
                    c.line(x, 50, x, table_top)

            return table_top - header_height

        # رسم رأس كل صفحة
        content_top = draw_page_header()

        # حفظ القالب
        c.save()
        return filepath

    def fill_record_template(self, template_path, data, title="سجل المخالفات المدرسية"):
        """ملء قالب السجل بالبيانات"""
        if not os.path.exists(template_path):
            return None

        output_path = template_path.replace('.pdf', '_filled.pdf')
        c = canvas.Canvas(output_path, pagesize=landscape(A4))
        width, height = landscape(A4)

        # متغيرات لتتبع موقع الكتابة
        y = height - 200
        items_per_page = 15
        page = 1

        # حساب عدد الصفحات
        total_pages = (len(data) + items_per_page - 1) // items_per_page

        for i, row in enumerate(data):
            if i % items_per_page == 0:
                if i > 0:
                    c.showPage()
                    y = height - 200
                
                # رسم رأس الصفحة
                self.draw_header(c, width, height, title)
                c.setFont('Arabic', 12)
                c.drawString(100, height - 30, f"صفحة {page} من {total_pages}")
                page += 1

            # كتابة بيانات الصف
            x = width - 70
            c.setFont('Arabic', 12)
            col_widths = [100, 150, 200, 100, 200, 150, 100]
            
            for value, col_width in zip(row, col_widths):
                text = str(value) if value else ""
                if len(text) > 30:  # اختصار النص الطويل
                    text = text[:27] + "..."
                c.drawString(x - col_width + 10, y, self.reshape(text))
                x -= col_width

            # رسم خط أفقي بين الصفوف
            c.line(50, y - 5, width - 50, y - 5)
            y -= 25

        c.save()
        return output_path

# ... باقي الكود بدون تغيير ...

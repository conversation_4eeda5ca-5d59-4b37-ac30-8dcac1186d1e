#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sqlite3

def run_academic_year_filter_test():
    """
    دالة لاختبار تصفية بيانات جدول مسك_الغياب_الأسبوعي حسب السنة الدراسية
    """
    try:
        # الاتصال بقاعدة البيانات
        conn = sqlite3.connect("data.db")
        cur = conn.cursor()
        
        print("=== اختبار تصفية بيانات جدول مسك_الغياب_الأسبوعي حسب السنة الدراسية ===")
        
        # الحصول على السنة الدراسية الحالية
        cur.execute("SELECT السنة_الدراسية FROM بيانات_المؤسسة LIMIT 1")
        school_year = cur.fetchone()[0]
        print(f"السنة الدراسية الحالية: {school_year}")
        
        # القسم والشهر المحددين
        section = "TCLSH-1"
        month = "شتنبر"
        student_code = "H142106728"
        
        print(f"\n=== التحقق من بيانات الطالب {student_code} في جدول اللوائح ===")
        
        # التحقق من وجود الطالب في جدول اللوائح للسنوات الدراسية المختلفة
        cur.execute("""
            SELECT رت, الرمز, القسم, السنة_الدراسية
            FROM اللوائح
            WHERE الرمز = ?
            ORDER BY السنة_الدراسية
        """, (student_code,))
        student_in_lists = cur.fetchall()
        print(f"عدد سجلات الطالب {student_code} في جدول اللوائح: {len(student_in_lists)}")
        for record in student_in_lists:
            print(f"  سجل: {record}")
        
        print(f"\n=== التحقق من بيانات الغياب للطالب {student_code} في جدول مسك_الغياب_الأسبوعي ===")
        
        # التحقق من وجود بيانات الغياب للطالب في جدول مسك_الغياب_الأسبوعي للسنوات الدراسية المختلفة
        cur.execute("""
            SELECT id, السنة_الدراسية, الشهر, رمز_التلميذ, "1", "2", "3", "4", "5", مجموع_شهري
            FROM مسك_الغياب_الأسبوعي
            WHERE رمز_التلميذ = ?
            ORDER BY السنة_الدراسية, الشهر
        """, (student_code,))
        student_absence = cur.fetchall()
        print(f"عدد سجلات الغياب للطالب {student_code} في جدول مسك_الغياب_الأسبوعي: {len(student_absence)}")
        for record in student_absence:
            print(f"  سجل: {record}")
        
        print(f"\n=== اختبار الاستعلام بدون تصفية حسب السنة الدراسية ===")
        
        # استعلام بدون تصفية حسب السنة الدراسية
        query_without_filter = """
            SELECT l.رت, sg.الاسم_والنسب, l.الرمز,
                   mag."1", mag."2", mag."3", mag."4", mag."5",
                   mag.مجموع_شهري, mag.الغياب_المبرر, mag.ملاحظات
            FROM اللوائح l
            LEFT JOIN مسك_الغياب_الأسبوعي mag ON l.الرمز = mag.رمز_التلميذ
                AND mag.الشهر = ?
            LEFT JOIN السجل_العام sg ON l.الرمز = sg.الرمز
            WHERE l.القسم = ? AND l.السنة_الدراسية = ?
            AND l.الرمز = ?
        """
        
        # تنفيذ الاستعلام
        cur.execute(query_without_filter, (month, section, school_year, student_code))
        results_without_filter = cur.fetchall()
        print(f"عدد النتائج بدون تصفية حسب السنة الدراسية: {len(results_without_filter)}")
        for result in results_without_filter:
            print(f"  نتيجة: {result}")
        
        print(f"\n=== اختبار الاستعلام مع تصفية حسب السنة الدراسية ===")
        
        # استعلام مع تصفية حسب السنة الدراسية
        query_with_filter = """
            SELECT l.رت, sg.الاسم_والنسب, l.الرمز,
                   mag."1", mag."2", mag."3", mag."4", mag."5",
                   mag.مجموع_شهري, mag.الغياب_المبرر, mag.ملاحظات
            FROM اللوائح l
            LEFT JOIN مسك_الغياب_الأسبوعي mag ON l.الرمز = mag.رمز_التلميذ
                AND mag.الشهر = ?
                AND mag.السنة_الدراسية = ?
            LEFT JOIN السجل_العام sg ON l.الرمز = sg.الرمز
            WHERE l.القسم = ? AND l.السنة_الدراسية = ?
            AND l.الرمز = ?
        """
        
        # تنفيذ الاستعلام
        cur.execute(query_with_filter, (month, school_year, section, school_year, student_code))
        results_with_filter = cur.fetchall()
        print(f"عدد النتائج مع تصفية حسب السنة الدراسية: {len(results_with_filter)}")
        for result in results_with_filter:
            print(f"  نتيجة: {result}")
        
        # إغلاق الاتصال بقاعدة البيانات
        conn.close()
        print("\nتم إغلاق الاتصال بقاعدة البيانات")
        
    except Exception as e:
        print(f"خطأ: {e}")
        import traceback
        traceback.print_exc()
        if 'conn' in locals() and conn:
            conn.close()

if __name__ == "__main__":
    run_academic_year_filter_test()

from PyQt5.QtWidgets import QWidget, QVBoxLayout, QLabel
from PyQt5.QtCore import Qt

class PlaceholderWindow(QWidget):
    """نافذة مؤقتة تستخدم عندما تكون الوحدة الأصلية غير متوفرة"""
    
    def __init__(self, parent=None, db_path=None, **kwargs):
        super().__init__(parent)
        layout = QVBoxLayout(self)
        
        title = kwargs.get('title', 'نافذة مؤقتة')
        message = kwargs.get('message', 'هذه نافذة مؤقتة. الوحدة الأصلية غير متوفرة.')
        
        info_label = QLabel(f"<h2>{title}</h2><p>{message}</p>")
        info_label.setAlignment(Qt.AlignCenter)
        info_label.setStyleSheet("""
            QLabel {
                color: #555;
                background-color: #f9f9f9;
                border: 1px solid #ddd;
                border-radius: 8px;
                padding: 20px;
                margin: 40px;
            }
        """)
        
        layout.addWidget(info_label)
        layout.addStretch()
        
        # دعم الخصائص المتوقعة من قبل الشفرة الأخرى
        self.student_code = kwargs.get('student_code', '')
        self.student_name = kwargs.get('student_name', '')
    
    def load_student_data(self):
        """تحميل بيانات الطالب (وظيفة وهمية)"""
        print("طلب تحميل بيانات الطالب (وظيفة مؤقتة)")
    
    def search_student(self):
        """البحث عن طالب (وظيفة وهمية)"""
        print("طلب البحث عن طالب (وظيفة مؤقتة)")
    
    def set_student_info(self, student_code=None, student_name=None, student_id=None, level=None, class_name=None):
        """تعيين معلومات الطالب (وظيفة وهمية)"""
        self.student_code = student_code
        self.student_name = student_name
        print(f"طلب تعيين معلومات الطالب: {student_name} (الرمز: {student_code})")

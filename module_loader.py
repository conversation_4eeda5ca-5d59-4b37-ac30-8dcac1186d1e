#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
module_loader.py - مساعد تحميل الوحدات للتأكد من استيراد جميع الوحدات اللازمة عند تحزيم البرنامج
"""

import sys
import os
import importlib
import traceback

# تفعيل رسائل تشخيصية إضافية في حالة البرنامج المحزم
VERBOSE_DEBUG = True

# قائمة بجميع الوحدات التي تحتوي على نوافذ وتنفتح بشكل مستقل
WINDOW_MODULES = [
    'absence_reports',
    'attendance_report',
    'database_utils',
    'default_settings_window',
    'help_guide',
    'hirasa100_window',
    'hirasa102_window',
    'hirasa103_window',
    'hirasa104_window',
    'hirasa400_window',
    'hirasa401_window',
    'hirasa411_window',
    'hirasa412_window',
    'hirasa600_window',
    'simple00_window',
    'simple6_window',
    'split_attendance_report',
    'student_card_ui',
    'sub00_window',
    'sub1_window',
    'sub11_window',
    'sub2_window',
    'sub4_window',
    'sub5_window',
    'sub55_window',
    'sub6_window',
    'sub7_window',
    'sub8_window',
    'tabbed5_window',
    'tabbed6_window',
    'templates_manager',
    'violations_table',
]

# قائمة بالوحدات المساعدة الأخرى
UTILITY_MODULES = [
    'advanced_printer',
    'app_fonts',
    'printer_manager',
    'styles',
    'help_texts',
    # إضافة المكونات الأساسية
    'PyQt5.QtWidgets',
    'PyQt5.QtCore',
    'PyQt5.QtGui',
    'sqlite3',
    'logging',
]

# تحديد الوحدات الضرورية التي يجب أن يفشل البرنامج إذا لم تكن متوفرة
CRITICAL_MODULES = [
    'main_window',
    'database_utils',
    'PyQt5.QtWidgets',
    'PyQt5.QtCore',
    'PyQt5.QtGui',
    'sqlite3',
]

def debug_log(message):
    """طباعة رسالة تشخيصية مع معلومات إضافية في وضع التشخيص"""
    if __debug__ or VERBOSE_DEBUG:
        if getattr(sys, 'frozen', False):
            print(f"[FROZEN] {message}")
        else:
            print(message)
        
        # محاولة كتابة رسالة التشخيص في ملف سجل
        try:
            debug_log_file = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'module_loader_log.txt')
            with open(debug_log_file, 'a', encoding='utf-8') as f:
                f.write(f"{message}\n")
        except:
            pass

def import_modules(module_list):
    """
    استيراد قائمة من الوحدات ديناميكياً
    تعيد: (عدد الوحدات المستوردة بنجاح، عدد الوحدات التي فشل استيرادها)
    """
    imported_count = 0
    failed_count = 0
    imported_modules = []
    failed_modules = []
    
    debug_log("جاري استيراد الوحدات...")
    
    for module_name in module_list:
        try:
            module = importlib.import_module(module_name)
            imported_modules.append(module)
            imported_count += 1
            debug_log(f"تم استيراد {module_name} بنجاح")
            
            # في وضع البرنامج المحزم، اطبع معلومات عن الوحدة المستوردة
            if getattr(sys, 'frozen', False) and VERBOSE_DEBUG:
                try:
                    module_path = getattr(module, '__file__', 'غير معروف')
                    debug_log(f"   - مسار الوحدة: {module_path}")
                except:
                    pass
                
        except ImportError as e:
            failed_modules.append((module_name, str(e)))
            failed_count += 1
            error_msg = f"فشل استيراد {module_name}: {e}"
            debug_log(error_msg)
            
            # في حالة فشل استيراد وحدة ضرورية
            if module_name in CRITICAL_MODULES:
                debug_log(f"خطأ فادح: فشل استيراد وحدة ضرورية: {module_name}")
                if getattr(sys, 'frozen', False):
                    try:
                        import ctypes
                        ctypes.windll.user32.MessageBoxW(0, f"فشل استيراد وحدة ضرورية: {module_name}\n\nالسبب: {str(e)}", "خطأ فادح في البرنامج", 0)
                    except:
                        pass
            
        except Exception as e:  # التقاط أخطاء أخرى محتملة
            failed_modules.append((module_name, str(e)))
            failed_count += 1
            error_msg = f"فشل استيراد {module_name} بسبب خطأ غير متوقع: {e}"
            debug_log(error_msg)
            debug_log(traceback.format_exc())
    
    debug_log(f"اكتمل استيراد الوحدات. تم: {imported_count}, فشل: {failed_count}")
    
    return imported_count, failed_count, imported_modules, failed_modules

def add_subdirs_to_path(base_dir):
    """
    إضافة المجلد الأساسي والمجلدات الفرعية إلى مسار بحث Python
    """
    if os.path.exists(base_dir) and os.path.isdir(base_dir):
        # إضافة المجلد الأساسي نفسه
        sys.path.insert(0, base_dir)
        debug_log(f"تمت إضافة {base_dir} إلى مسارات البحث")
        
        # إضافة المجلدات الفرعية المباشرة
        for subdir in os.listdir(base_dir):
            subdir_path = os.path.join(base_dir, subdir)
            if os.path.isdir(subdir_path):
                sys.path.insert(0, subdir_path)
                debug_log(f"تمت إضافة {subdir_path} إلى مسارات البحث")

# لا تنسى استيراد النوافذ الرئيسية مباشرة لضمان تضمينها
try:
    import main_window
    debug_log("تم استيراد main_window بنجاح بشكل مباشر")
except ImportError as e:
    debug_log(f"فشل استيراد main_window بشكل مباشر: {e}")

# إضافة المجلد الحالي والمجلدات الفرعية إلى مسار البحث
current_dir = os.path.dirname(os.path.abspath(__file__))
debug_log(f"المجلد الحالي: {current_dir}")
add_subdirs_to_path(current_dir)

# محاولة إضافة مجلد البرنامج التنفيذي عند العمل في وضع البرنامج المحزم
if getattr(sys, 'frozen', False):
    executable_dir = os.path.dirname(sys.executable)
    debug_log(f"مجلد الملف التنفيذي: {executable_dir}")
    add_subdirs_to_path(executable_dir)
    
    # محاولة إضافة مجلد MEIPASS أيضًا
    meipass_dir = getattr(sys, '_MEIPASS', None)
    if meipass_dir:
        debug_log(f"مجلد MEIPASS: {meipass_dir}")
        add_subdirs_to_path(meipass_dir)
        
        # طباعة قائمة بمحتويات مجلد MEIPASS
        debug_log("محتويات مجلد MEIPASS:")
        try:
            for item in os.listdir(meipass_dir):
                item_path = os.path.join(meipass_dir, item)
                if os.path.isdir(item_path):
                    debug_log(f"   [مجلد] {item}")
                else:
                    debug_log(f"   [ملف] {item}")
        except Exception as e:
            debug_log(f"خطأ عند قراءة محتويات MEIPASS: {e}")

# استيراد الوحدات
debug_log("بدء استيراد الوحدات المطلوبة...")
imported_count, failed_count, _, _ = import_modules(WINDOW_MODULES + UTILITY_MODULES)

# اطبع معلومات مفيدة للتشخيص
debug_log(f"مسارات البحث الحالية: {sys.path}")
debug_log(f"تم تنفيذ البرنامج من: {sys.executable}")
debug_log(f"حالة التحزيم: {'محزّم' if getattr(sys, 'frozen', False) else 'غير محزّم'}")
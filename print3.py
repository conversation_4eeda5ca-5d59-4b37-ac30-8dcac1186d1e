#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
وحدة طباعة تقرير المخالفة بتنسيق محسن
تستخدم نفس الخطوط والتنسيق المستخدم في ملف print1_test.py
"""

import os
import traceback
import sqlite3
from datetime import datetime
import webbrowser

# استيراد مكتبات ReportLab لإنشاء ملفات PDF
try:
    from reportlab.lib.pagesizes import A4
    from reportlab.pdfgen import canvas
    from reportlab.lib.units import cm
    from reportlab.pdfbase import pdfmetrics
    from reportlab.pdfbase.ttfonts import TTFont
    from reportlab.platypus import Table, TableStyle
    from reportlab.lib import colors
    REPORTLAB_AVAILABLE = True
except ImportError:
    REPORTLAB_AVAILABLE = False
    print("تنبيه: مكتبة ReportLab غير متوفرة. قم بتثبيت: pip install reportlab")

# استيراد مكتبات معالجة النصوص العربية
try:
    import arabic_reshaper
    from bidi.algorithm import get_display
    ARABIC_SUPPORT = True
    print("تم تحميل مكتبات معالجة النصوص العربية بنجاح.")
except ImportError:
    ARABIC_SUPPORT = False
    print("تنبيه: مكتبات معالجة النصوص العربية غير متوفرة. قم بتثبيت: pip install arabic-reshaper python-bidi")

def fix_arabic_text(text):
    """معالجة النص العربي وتجهيزه للعرض في PDF"""
    if not text:
        return ""
    text = str(text).strip()
    if not text:
        return ""
    try:
        if ARABIC_SUPPORT:
            # إعادة تشكيل النص العربي
            reshaped_text = arabic_reshaper.reshape(text)
            # تطبيق خوارزمية BIDI لعرض النص بشكل صحيح
            bidi_text = get_display(reshaped_text)
            return bidi_text
        else:
            return text
    except Exception as e:
        print(f"خطأ في معالجة النص العربي '{text[:20]}...': {e}")
        return text

def register_fonts():
    """تسجيل الخطوط العربية المطلوبة"""
    script_dir = os.path.dirname(os.path.abspath(__file__))

    # قائمة بالخطوط المطلوبة ومساراتها المحتملة - استخدام نفس الخطوط كما في print1_test.py
    fonts_to_register = [
        {
            'name': 'Arabic',
            'paths': [
                'arial.ttf',
                os.path.join(script_dir, 'arial.ttf'),
                os.path.join(script_dir, 'fonts', 'arial.ttf'),
                os.path.join('C:\\Windows\\Fonts', 'arial.ttf')
            ]
        },
        {
            'name': 'Amiri',
            'paths': [
                'Amiri-Regular.ttf',
                os.path.join(script_dir, 'Amiri-Regular.ttf'),
                os.path.join(script_dir, 'fonts', 'Amiri-Regular.ttf')
            ]
        },
        {
            'name': 'Amiri-Bold',
            'paths': [
                'Amiri-Bold.ttf',
                os.path.join(script_dir, 'Amiri-Bold.ttf'),
                os.path.join(script_dir, 'fonts', 'Amiri-Bold.ttf')
            ]
        },
        {
            'name': 'Calibri',
            'paths': [
                'calibri.ttf',
                os.path.join(script_dir, 'calibri.ttf'),
                os.path.join(script_dir, 'fonts', 'calibri.ttf'),
                os.path.join('C:\\Windows\\Fonts', 'calibri.ttf')
            ]
        },
        {
            'name': 'Calibri-Bold',
            'paths': [
                'calibrib.ttf',
                os.path.join(script_dir, 'calibrib.ttf'),
                os.path.join(script_dir, 'fonts', 'calibrib.ttf'),
                os.path.join('C:\\Windows\\Fonts', 'calibrib.ttf')
            ]
        }
    ]

    # تسجيل كل خط من القائمة
    for font in fonts_to_register:
        font_registered = False
        for path in font['paths']:
            try:
                if os.path.exists(path):
                    pdfmetrics.registerFont(TTFont(font['name'], path))
                    print(f"تم تسجيل خط {font['name']} من المسار: {path}")
                    font_registered = True
                    break
            except Exception as font_error:
                print(f"خطأ في تسجيل خط {font['name']} من المسار {path}: {font_error}")

        if not font_registered:
            print(f"تحذير: لم يتم تسجيل خط {font['name']} من أي مسار")

    # إذا لم يتم تسجيل خط Amiri-Bold، استخدم Amiri العادي كبديل
    try:
        if 'Amiri-Bold' not in pdfmetrics.getRegisteredFontNames() and 'Amiri' in pdfmetrics.getRegisteredFontNames():
            # استخدام نفس مسار ملف Amiri العادي لتسجيل Amiri-Bold
            amiri_path = None
            for path in fonts_to_register[1]['paths']:
                if os.path.exists(path):
                    amiri_path = path
                    break

            if amiri_path:
                pdfmetrics.registerFont(TTFont('Amiri-Bold', amiri_path))
                print(f"تم استخدام خط Amiri العادي كبديل لـ Amiri-Bold من المسار: {amiri_path}")
    except Exception as fallback_error:
        print(f"خطأ في استخدام خط Amiri كبديل لـ Amiri-Bold: {fallback_error}")

    # طباعة قائمة الخطوط المسجلة للتشخيص
    print(f"الخطوط المسجلة: {pdfmetrics.getRegisteredFontNames()}")

def get_institution_info(db_path=None):
    """الحصول على معلومات المؤسسة من قاعدة البيانات"""
    try:
        if db_path is None:
            db_path = "data.db"

        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()

        # محاولة قراءة من جدول بيانات_المؤسسة
        try:
            cursor.execute("SELECT المؤسسة, السنة_الدراسية, ImagePath1 FROM بيانات_المؤسسة LIMIT 1")
            institution_row = cursor.fetchone()
            if institution_row:
                institution_data = {
                    'name': institution_row[0],
                    'school_year': institution_row[1],
                    'logo_path': institution_row[2] if institution_row[2] and os.path.exists(institution_row[2]) else None
                }
                return institution_data
        except Exception as e:
            print(f"خطأ في قراءة بيانات المؤسسة: {e}")

        conn.close()
    except Exception as db_error:
        print(f"خطأ في الاتصال بقاعدة البيانات: {db_error}")

    # إرجاع قاموس فارغ في حالة حدوث خطأ
    return {}

def create_violation_pdf(violation_data, file_path, db_path=None):
    """إنشاء ملف PDF للمخالفة بتصميم محسن

    Args:
        violation_data (dict): قاموس يحتوي على معلومات المخالفة
        file_path (str): مسار حفظ ملف PDF
        db_path (str, optional): مسار قاعدة البيانات. Defaults to None.

    Returns:
        bool: True إذا تم إنشاء الملف بنجاح، False في حالة حدوث خطأ
    """
    if not REPORTLAB_AVAILABLE:
        print("مكتبة ReportLab غير متوفرة. لا يمكن إنشاء ملف PDF.")
        return False

    try:
        # تسجيل الخطوط العربية
        register_fonts()

        # الحصول على بيانات المؤسسة
        institution_data = get_institution_info(db_path)

        # إنشاء ملف PDF
        c = canvas.Canvas(file_path, pagesize=A4)
        width, height = A4

        # تعريف الهوامش
        margin = 2 * cm
        right_x = width - margin
        left_x = margin
        center_x = width / 2

        # إضافة الشعار في أعلى الصفحة
        logo_path = institution_data.get('logo_path')
        logo_height = 70  # ارتفاع الشعار بالبكسل
        if logo_path and os.path.exists(logo_path):
            try:
                # ترك هامش أعلى بمقدار 0.3 سم ثم وضع الشعار
                logo_width = 300  # عرض الشعار بالبكسل
                logo_y_position = height - (0.3*cm + logo_height)  # هامش 0.3 سم من أعلى الصفحة
                c.drawImage(logo_path, center_x - (logo_width/2), logo_y_position, width=logo_width, height=logo_height, preserveAspectRatio=True)
                print(f"تم رسم الشعار بنجاح من: {logo_path}")
            except Exception as e:
                print(f"خطأ في رسم الشعار: {e}")
                logo_height = 0  # تعيين ارتفاع الشعار إلى صفر في حالة حدوث خطأ
        else:
            logo_height = 0  # تعيين ارتفاع الشعار إلى صفر إذا لم يكن متوفراً

        # إضافة معلومات المؤسسة في وسط الصفحة بعد مسافة 2.5 سم من الشعار
        y_position = height - (0.3*cm + logo_height + 1.2*cm)

        # اختيار الخط المناسب للعناوين الرئيسية
        try:
            c.setFont('Amiri-Bold', 18)
            print("استخدام خط Amiri-Bold للعنوان الرئيسي")
        except:
            try:
                c.setFont('Amiri', 18)
                print("استخدام خط Amiri للعنوان الرئيسي")
            except:
                c.setFont('Arabic', 18)
                print("استخدام خط Arabic للعنوان الرئيسي")

        # اسم المؤسسة
        institution_name = institution_data.get('name', '')
        c.drawCentredString(center_x, y_position, fix_arabic_text(institution_name))

        # السنة الدراسية
        y_position -= 1.2*cm  # زيادة المسافة
        # اختيار الخط المناسب للعناوين الفرعية
        try:
            c.setFont('Amiri-Bold', 14)
            print("استخدام خط Amiri-Bold للعنوان الفرعي")
        except:
            try:
                c.setFont('Amiri', 14)
                print("استخدام خط Amiri للعنوان الفرعي")
            except:
                c.setFont('Arabic', 14)
                print("استخدام خط Arabic للعنوان الفرعي")

        school_year = institution_data.get('school_year', '')
        c.drawCentredString(center_x, y_position, fix_arabic_text(f"السنة الدراسية: {school_year}"))

        # عنوان التقرير
        y_position -= 1.2*cm  # زيادة المسافة
        # اختيار الخط المناسب لعنوان التقرير
        try:
            c.setFont('Amiri-Bold', 16)
            print("استخدام خط Amiri-Bold لعنوان التقرير")
        except:
            try:
                c.setFont('Amiri', 16)
                print("استخدام خط Amiri لعنوان التقرير")
            except:
                c.setFont('Arabic', 16)
                print("استخدام خط Arabic لعنوان التقرير")

        # إضافة لون أزرق غامق للعنوان كما في print1_test.py
        c.setFillColorRGB(0.2, 0.4, 0.6)  # لون أزرق غامق للعنوان

        c.drawCentredString(center_x, y_position, fix_arabic_text("تقرير المخالفة"))
        c.setFillColorRGB(0, 0, 0)  # إعادة لون النص إلى الأسود

        # إضافة معلومات التلميذ
        y_position -= 1.5*cm  # زيادة المسافة

        # جدول معلومات التلميذ
        student_info_width = 16*cm
        student_info_height = 2*cm
        student_info_x = center_x - (student_info_width / 2)

        # رسم إطار جدول معلومات التلميذ
        c.setStrokeColorRGB(0.2, 0.4, 0.6)  # لون أزرق غامق للإطار
        c.setLineWidth(1.5)  # سمك الخط
        c.rect(student_info_x, y_position - student_info_height, student_info_width, student_info_height)

        # رسم الخطوط الداخلية للجدول - تعديل موقع الخط العمودي ليكون 35% من عرض الجدول للعمود الأول (رمز التلميذ والقسم)
        # وبالتالي يكون عمود اسم التلميذ والمستوى 65% من عرض الجدول
        c.line(student_info_x + student_info_width*0.35, y_position, student_info_x + student_info_width*0.35, y_position - student_info_height)
        c.line(student_info_x, y_position - student_info_height/2, student_info_x + student_info_width, y_position - student_info_height/2)

        # إعادة سمك الخط إلى القيمة الافتراضية
        c.setLineWidth(1)
        c.setStrokeColorRGB(0, 0, 0)  # إعادة لون الخط إلى الأسود

        # كتابة معلومات التلميذ
        # اختيار الخط المناسب لمعلومات التلميذ
        try:
            c.setFont('Calibri-Bold', 14)
            print("استخدام خط Calibri-Bold لمعلومات التلميذ")
        except:
            try:
                c.setFont('Calibri', 14)
                print("استخدام خط Calibri لمعلومات التلميذ")
            except:
                try:
                    c.setFont('Amiri-Bold', 14)
                    print("استخدام خط Amiri-Bold لمعلومات التلميذ")
                except:
                    c.setFont('Arabic', 14)
                    print("استخدام خط Arabic لمعلومات التلميذ")

        # تحسين تنسيق النص داخل الجدول - تعديل موقع النص ليتناسب مع التقسيم الجديد للجدول (65% لعمود اسم التلميذ والمستوى)
        c.drawRightString(student_info_x + student_info_width - 0.3*cm, y_position - 0.6*cm, fix_arabic_text("اسم التلميذ: " + violation_data.get('student_name', '')))
        c.drawRightString(student_info_x + student_info_width*0.35 - 0.3*cm, y_position - 0.6*cm, fix_arabic_text("رمز التلميذ: " + violation_data.get('student_code', '')))
        c.drawRightString(student_info_x + student_info_width - 0.3*cm, y_position - 1.6*cm, fix_arabic_text("المستوى: " + violation_data.get('level', '')))
        c.drawRightString(student_info_x + student_info_width*0.35 - 0.3*cm, y_position - 1.6*cm, fix_arabic_text("القسم: " + violation_data.get('section', '')))

        y_position -= student_info_height + 1.5*cm  # زيادة المسافة

        # تفاصيل المخالفة
        violation_info_width = 16*cm
        violation_info_x = center_x - (violation_info_width / 2)

        # عنوان قسم تفاصيل المخالفة
        # اختيار الخط المناسب لعنوان قسم تفاصيل المخالفة
        try:
            c.setFont('Amiri-Bold', 14)
            print("استخدام خط Amiri-Bold لعنوان قسم تفاصيل المخالفة")
        except:
            try:
                c.setFont('Amiri', 14)
                print("استخدام خط Amiri لعنوان قسم تفاصيل المخالفة")
            except:
                c.setFont('Arabic', 14)
                print("استخدام خط Arabic لعنوان قسم تفاصيل المخالفة")

        # إضافة لون أزرق غامق للعنوان كما في print1_test.py
        c.setFillColorRGB(0.1, 0.3, 0.6)  # لون أزرق غامق للخلفية

        # رسم مستطيل خلفية للعنوان - تحسين المظهر كما في print1_test.py
        title_width = 12*cm  # عرض العنوان
        title_height = 0.9*cm  # ارتفاع العنوان
        c.rect(center_x - title_width/2, y_position - 0.7*cm, title_width, title_height, fill=1, stroke=1)  # إضافة إطار

        # كتابة العنوان بلون أبيض
        c.setFillColorRGB(1, 1, 1)  # لون أبيض للنص
        c.drawCentredString(center_x, y_position - 0.45*cm, fix_arabic_text("تفاصيل المخالفة"))

        # إعادة لون النص إلى الأسود
        c.setFillColorRGB(0, 0, 0)
        y_position -= 1.2*cm

        # تحضير بيانات الجدول
        notes = violation_data.get('notes', '') or "لا توجد"
        procedures = violation_data.get('procedures', '') or "لا توجد"

        # إنشاء جدول كامل لتفاصيل المخالفة
        date_text = violation_data.get('date', '')
        id_text = str(violation_data.get('id', ''))
        subject_text = violation_data.get('subject', '')
        teacher_text = violation_data.get('teacher', '')

        data = [
            [fix_arabic_text("تاريخ المخالفة: " + date_text), "", fix_arabic_text("رقم المخالفة: " + id_text), ""],
            [fix_arabic_text("المادة: " + subject_text), "", fix_arabic_text("الأستاذ(ة): " + teacher_text), ""],
            [fix_arabic_text("وصف المخالفة:"), "", "", ""],
            [fix_arabic_text(notes), "", "", ""],
            [fix_arabic_text("الإجراء المتخذ:"), "", "", ""],
            [fix_arabic_text(procedures), "", "", ""]
        ]

        # تحديد عرض الأعمدة
        col_widths = [4*cm, 4*cm, 4*cm, 4*cm]

        # تحديد ارتفاع الصفوف - زيادة المسافة بين الأسطر
        row_heights = [40, 40, 40, 80, 40, 80]  # زيادة ارتفاع الصفوف لتوفير مساحة أكبر بين الأسطر

        # إنشاء الجدول
        table = Table(
            data,
            colWidths=col_widths,
            rowHeights=row_heights
        )

        # تحديد نمط الجدول
        table_style = TableStyle([
            # الحدود الخارجية
            ('BOX', (0, 0), (-1, -1), 1.5, colors.navy),

            # دمج الخلايا للصفين الأول والثاني
            ('SPAN', (0, 0), (1, 0)),  # دمج الخليتين الأولى والثانية في الصف الأول (تاريخ المخالفة)
            ('SPAN', (2, 0), (3, 0)),  # دمج الخليتين الثالثة والرابعة في الصف الأول (رقم المخالفة)
            ('SPAN', (0, 1), (1, 1)),  # دمج الخليتين الأولى والثانية في الصف الثاني (المادة)
            ('SPAN', (2, 1), (3, 1)),  # دمج الخليتين الثالثة والرابعة في الصف الثاني (الأستاذ)

            # دمج الخلايا لوصف المخالفة والإجراء المتخذ
            ('SPAN', (0, 2), (3, 2)),  # دمج الصف الثالث (وصف المخالفة)
            ('SPAN', (0, 3), (3, 3)),  # دمج الصف الرابع (نص وصف المخالفة)
            ('SPAN', (0, 4), (3, 4)),  # دمج الصف الخامس (الإجراء المتخذ)
            ('SPAN', (0, 5), (3, 5)),  # دمج الصف السادس (نص الإجراء المتخذ)

            # خطوط داخلية
            ('LINEBELOW', (0, 0), (3, 0), 1, colors.navy),  # خط أسفل الصف الأول
            ('LINEBELOW', (0, 1), (3, 1), 1, colors.navy),  # خط أسفل الصف الثاني
            ('LINEBELOW', (0, 2), (3, 2), 1, colors.navy),  # خط أسفل الصف الثالث
            ('LINEBELOW', (0, 3), (3, 3), 1, colors.navy),  # خط أسفل الصف الرابع
            ('LINEBELOW', (0, 4), (3, 4), 1, colors.navy),  # خط أسفل الصف الخامس

            # خطوط عمودية للصفين الأول والثاني فقط
            ('LINEBEFORE', (2, 0), (2, 1), 1, colors.navy),  # خط قبل العمود الثالث

            # تنسيق العناوين
            ('BACKGROUND', (0, 2), (3, 2), colors.lightgrey),  # خلفية رمادية فاتحة لعنوان وصف المخالفة
            ('BACKGROUND', (0, 4), (3, 4), colors.lightgrey),  # خلفية رمادية فاتحة لعنوان الإجراء المتخذ

            # تنسيق الخطوط - استخدام خط Calibri-Bold بحجم 13 داخل الجدول
            ('FONTNAME', (0, 0), (-1, -1), 'Calibri-Bold'),  # استخدام خط Calibri-Bold للجدول بأكمله
            ('FONTNAME', (0, 2), (0, 2), 'Calibri-Bold'),  # خط غامق لعنوان وصف المخالفة
            ('FONTNAME', (0, 4), (0, 4), 'Calibri-Bold'),  # خط غامق لعنوان الإجراء المتخذ

            # حجم الخط - استخدام نفس الحجم كما في print1_test.py
            ('FONTSIZE', (0, 0), (-1, -1), 13),  # حجم الخط 13 لكل الجدول

            # محاذاة النص
            ('ALIGNMENT', (0, 0), (-1, -1), 'RIGHT'),  # محاذاة يمين لكل الجدول
            ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),  # محاذاة وسط عمودية
            ('VALIGN', (0, 3), (3, 3), 'TOP'),  # محاذاة أعلى لوصف المخالفة
            ('VALIGN', (0, 5), (3, 5), 'TOP'),  # محاذاة أعلى للإجراء المتخذ

            # المسافات الداخلية - زيادة المسافات
            ('TOPPADDING', (0, 0), (-1, -1), 15),  # زيادة المسافة العلوية
            ('BOTTOMPADDING', (0, 0), (-1, -1), 15),  # زيادة المسافة السفلية
            ('RIGHTPADDING', (0, 0), (-1, -1), 20),  # زيادة المسافة من اليمين
            ('LEFTPADDING', (0, 0), (-1, -1), 20),  # زيادة المسافة من اليسار
        ])

        # تطبيق النمط على الجدول
        try:
            table.setStyle(table_style)
        except Exception as style_error:
            print(f"خطأ في تطبيق نمط الجدول: {style_error}")
            # استخدام نمط بسيط بدون خطوط مخصصة
            simple_style = TableStyle([
                ('BOX', (0, 0), (-1, -1), 1, colors.black),
                ('GRID', (0, 0), (-1, -1), 0.5, colors.black),
                ('ALIGNMENT', (0, 0), (-1, -1), 'RIGHT'),
                # محاولة استخدام خطوط متعددة للتوافق
                ('FONTNAME', (0, 0), (-1, -1), 'Calibri-Bold'),
                ('SPAN', (0, 0), (1, 0)),  # دمج الخليتين الأولى والثانية في الصف الأول
                ('SPAN', (2, 0), (3, 0)),  # دمج الخليتين الثالثة والرابعة في الصف الأول
                ('SPAN', (0, 1), (1, 1)),  # دمج الخليتين الأولى والثانية في الصف الثاني
                ('SPAN', (2, 1), (3, 1)),  # دمج الخليتين الثالثة والرابعة في الصف الثاني
                ('SPAN', (0, 2), (3, 2)),  # دمج الصف الثالث
                ('SPAN', (0, 3), (3, 3)),  # دمج الصف الرابع
                ('SPAN', (0, 4), (3, 4)),  # دمج الصف الخامس
                ('SPAN', (0, 5), (3, 5)),  # دمج الصف السادس
            ])
            table.setStyle(simple_style)

        # حساب ارتفاع الجدول
        table_height = sum(row_heights)

        # رسم الجدول
        table.wrapOn(c, violation_info_width, table_height)
        table.drawOn(c, violation_info_x, y_position - table_height)

        # تحديث موضع y بعد رسم الجدول
        y_position -= (table_height + 0.5*cm)

        # إضافة التوقيعات
        y_position = margin + 3*cm

        # رسم خط أفقي فوق التوقيع
        c.setStrokeColorRGB(0.1, 0.3, 0.6)  # لون أزرق غامق للخط
        c.setLineWidth(1.5)  # زيادة سمك الخط
        c.line(left_x, y_position + 0.5*cm, right_x, y_position + 0.5*cm)
        c.setStrokeColorRGB(0, 0, 0)  # إعادة لون الخط إلى الأسود

        # اختيار الخط المناسب للتوقيعات - استخدام نفس الخطوط كما في print1_test.py
        try:
            c.setFont('Calibri-Bold', 13)
        except:
            try:
                c.setFont('Calibri', 13)
            except:
                try:
                    c.setFont('Amiri-Bold', 13)
                except:
                    c.setFont('Arabic', 13)

        # كتابة توقيع الأستاذ على اليسار
        c.drawString(left_x, y_position, fix_arabic_text("توقيع الأستاذ(ة)"))

        # كتابة توقيع الحراسة العامة على اليمين
        c.drawRightString(right_x, y_position, fix_arabic_text("توقيع الحراسة العامة"))

        # كتابة تاريخ الطباعة في الوسط
        today = datetime.now().strftime("%Y-%m-%d")
        c.drawCentredString(center_x, margin/2, fix_arabic_text(f"تاريخ الطباعة: {today}"))

        # حفظ الملف
        c.save()
        print(f"تم حفظ ملف PDF بنجاح: {file_path}")
        return True

    except Exception as e:
        print(f"خطأ في إنشاء ملف PDF للمخالفة: {str(e)}")
        traceback.print_exc()
        return False

def open_pdf(file_path):
    """فتح ملف PDF في المتصفح الافتراضي"""
    try:
        print(f"محاولة فتح الملف: {file_path}")

        # التحقق من وجود الملف
        if os.path.exists(file_path):
            print(f"الملف موجود، حجمه: {os.path.getsize(file_path)} بايت")

            # التحقق من امتداد الملف
            if file_path.lower().endswith('.pdf'):
                print("امتداد الملف صحيح: PDF")
            else:
                print(f"تحذير: امتداد الملف غير معتاد: {os.path.splitext(file_path)[1]}")

            # محاولة فتح الملف باستخدام webbrowser
            try:
                absolute_path = os.path.abspath(file_path)
                file_url = f'file:///{absolute_path}'
                print(f"محاولة فتح الملف باستخدام webbrowser: {file_url}")

                # استخدام webbrowser لفتح الملف
                result = webbrowser.open(file_url)

                if result:
                    print(f"تم فتح الملف بنجاح: {file_path}")
                    return True
                else:
                    print(f"فشل في فتح الملف باستخدام webbrowser")

                    # محاولة بديلة باستخدام os.startfile
                    try:
                        if os.name == 'nt':  # Windows
                            print("محاولة فتح الملف باستخدام os.startfile")
                            os.startfile(absolute_path)
                            print("تم فتح الملف بنجاح باستخدام os.startfile")
                            return True
                    except Exception as startfile_error:
                        print(f"فشل في فتح الملف باستخدام os.startfile: {startfile_error}")

                    return False
            except Exception as browser_error:
                print(f"خطأ في فتح الملف باستخدام webbrowser: {browser_error}")
                return False
        else:
            print(f"خطأ: الملف غير موجود: {file_path}")
            return False
    except Exception as e:
        print(f"خطأ في فتح ملف PDF: {str(e)}")
        traceback.print_exc()
        return False

def print_violation_details(violation_data, db_path=None, show_confirmation=True, auto_open=False):
    """طباعة تقرير مخالفة محددة بتنسيق PDF محسن

    Args:
        violation_data (dict): قاموس يحتوي على معلومات المخالفة
        db_path (str, optional): مسار قاعدة البيانات. Defaults to None.
        show_confirmation (bool, optional): عرض رسالة تأكيد. Defaults to True.
        auto_open (bool, optional): فتح الملف تلقائياً. Defaults to False.

    Returns:
        tuple: (bool, str) - نجاح العملية ومسار الملف
    """
    try:
        # إنشاء مجلد رئيسي على سطح المكتب
        desktop_path = os.path.join(os.path.expanduser("~"), "Desktop")
        main_reports_dir = os.path.join(desktop_path, "تقارير برنامج المعين في الحراسة العامة")
        os.makedirs(main_reports_dir, exist_ok=True)

        # إنشاء مجلد فرعي لتقارير سجلات المخالفات
        violations_dir = os.path.join(main_reports_dir, "تقارير سجلات المخالفات")
        os.makedirs(violations_dir, exist_ok=True)

        # تحديد اسم ملف الإخراج
        student_name = violation_data.get('student_name', 'بدون_اسم').replace(' ', '_')
        student_code = violation_data.get('student_code', 'بدون_رمز')
        current_date = datetime.now().strftime("%Y%m%d")

        # إنشاء اسم الملف باستخدام اسم التلميذ ورمزه وتاريخ الطباعة
        filename = os.path.join(violations_dir, f"مخالفة_{student_name}_{student_code}_{current_date}.pdf")

        # إنشاء ملف PDF
        if create_violation_pdf(violation_data, filename, db_path):
            # إذا كان الفتح التلقائي مطلوباً، افتح الملف
            if auto_open:
                if open_pdf(filename):
                    return True, filename
                else:
                    print("فشل في فتح ملف PDF")
                    return False, filename
            else:
                # إرجاع نجاح العملية ومسار الملف
                return True, filename
        else:
            print("فشل في إنشاء ملف PDF للمخالفة")
            return False, ""

    except Exception as e:
        print(f"خطأ في طباعة تقرير المخالفة: {str(e)}")
        traceback.print_exc()
        return False, ""

# قسم اختباري
if __name__ == "__main__":
    # بيانات تجريبية للاختبار
    violation_info_test = {
        "id": 123,
        "date": "2024-03-15",
        "student_name": "أحمد المنصوري",
        "student_code": "A123456",
        "section": "1APIC-1",
        "level": "الأولى إعدادي",
        "subject": "الرياضيات",
        "teacher": "محمد العلوي",
        "notes": "تأخر عن الحصة بدون مبرر وأحدث فوضى في القسم",
        "procedures": "إنذار شفوي وإخبار ولي الأمر"
    }

    # طباعة المخالفة التجريبية
    print_violation_details(violation_info_test)

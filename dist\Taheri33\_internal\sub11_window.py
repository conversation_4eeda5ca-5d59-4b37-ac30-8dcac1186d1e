# نافذة احترافية لتسجيل مخالفات التلاميذ على نمط ViolationsWindow السابقة
import sys
import os
import sqlite3
from datetime import datetime

# PyQt5 Core imports
from PyQt5.QtCore import (
    Qt,
    QDate,
    QDateTime
)

# PyQt5 GUI imports
from PyQt5.QtGui import (
    QFont,
    QColor,
    QIcon,
    QPalette,
    QTextDocument,
    QPageSize
)

# PyQt5 Print Support imports
from PyQt5.QtPrintSupport import (
    QPrinter,
    QPrintDialog,
    QPrintPreviewDialog
)

# PyQt5 Widgets imports
from PyQt5.QtWidgets import (
    QApplication,
    QWidget,
    QVBoxLayout,
    QHBoxLayout,
    QLabel,
    QLineEdit,
    QComboBox,
    QTextEdit,
    QPushButton,
    QFrame,
    QGroupBox,
    QGridLayout,
    QDateEdit,
    QMessageBox,
    QGraphicsDropShadowEffect,
    <PERSON><PERSON><PERSON><PERSON>ter,
    <PERSON><PERSON><PERSON><PERSON>,
    QText<PERSON>rowser
)

# استيراد وحدة الرسائل المخصصة
from custom_messages import show_custom_message

# استيراد مدير قاعدة البيانات
from database_utils import (
    get_db_manager,
    check_student_enrollment
)

# استيراد وحدة الرسائل المخصصة
from custom_messages import show_custom_message

class ViolationsWindow(QWidget):
    def __init__(self, db_path="data.db", window_width=1200, window_height=620, parent=None):
        super().__init__(parent)
        self.db_path = db_path
        self.db_manager = get_db_manager(db_path)

        # تخزين حجم النافذة المطلوب
        self.window_width = window_width
        self.window_height = window_height

        # تعيين النافذة كنافذة مشروطة (Modal)
        self.setWindowModality(Qt.ApplicationModal)

        # إزالة عنوان النافذة وأزرار التحكم
        self.setWindowFlags(Qt.Window | Qt.FramelessWindowHint)

        # إضافة هذه السطور لحل مشكلة الشفافية
        self.setAttribute(Qt.WA_TranslucentBackground, False)
        self.setAttribute(Qt.WA_NoSystemBackground, False)
        self.setAutoFillBackground(True)

        # ضبط لون خلفية النافذة بشكل صريح
        palette = self.palette()
        palette.setColor(QPalette.Window, QColor(255, 255, 255))  # لون أبيض غير شفاف
        self.setPalette(palette)

        # إضافة إطار للنافذة
        self.setStyleSheet("""
            ViolationsWindow {
                border: 2px solid #3498db;
                border-radius: 8px;
                background-color: white;
            }
        """)

        self.initUI()

    def initUI(self):
        # إعداد النافذة الرئيسية
        self.setWindowTitle("مسك المخالفات")

        # تعيين حجم النافذة كما هو مطلوب
        self.setGeometry(100, 70, 1200, 550)

        # تطبيق ستايل النافذة بأسلوب شبيه بـ hirasa102_window.py مع تعديل الخط
        self.setStyleSheet("""
            ViolationsWindow {
                background-color: qlineargradient(
                    x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 #f8f9fa, stop: 1 #e9ecef
                );
                border: 2px solid #3a6ea5;
                border-radius: 8px;
            }
            QWidget {
                font-family: 'Calibri';
                font-size: 13pt;
                font-weight: bold;
                color: black;
            }
            QGroupBox {
                border: 1px solid #ddd;
                border-radius: 5px;
                margin-top: 0px;
                padding-top: 8px;
                background-color: white;
            }
            QLabel {
                color: black;
                font-weight: bold;
                padding: 2px;
            }
            QLineEdit, QComboBox, QTextEdit, QDateEdit {
                background-color: white;
                border: 1px solid #dce0e3;
                border-radius: 4px;
                padding: 4px;
                color: black;
                font-family: 'Calibri';
                font-size: 13pt;
                font-weight: bold;
            }
            QLineEdit:focus, QComboBox:focus, QTextEdit:focus, QDateEdit:focus {
                border: 1px solid #3a6ea5;
            }
            QPushButton {
                background-color: #3a6ea5;
                color: white;
                border-radius: 4px;
                padding: 8px 15px;
                font-weight: bold;
                font-family: 'Calibri';
                font-size: 13pt;
                min-height: 32px;
            }
            QPushButton:hover {
                background-color: #2c5b8e;
            }
            QPushButton:pressed {
                background-color: #1e4a75;
            }
        """)

        self.setLayoutDirection(Qt.RightToLeft)

        # الإطار الرئيسي بهوامش متناسقة
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(12, 8, 12, 12)
        main_layout.setSpacing(6) # تقريب المسافات بين العناصر

        # ----- إنشاء إطار العنوان بتصميم أنيق -----
        header_frame = QFrame()
        header_frame.setStyleSheet("background-color: #3a6ea5; border-radius: 5px;")
        header_frame.setFixedHeight(40)

        header_layout = QHBoxLayout(header_frame)
        header_layout.setContentsMargins(8, 0, 8, 0)



        # زر الإغلاق
        self.close_button = QPushButton("×")
        self.close_button.setFont(QFont("Arial", 14, QFont.Bold))
        self.close_button.setFixedSize(28, 28)
        self.close_button.setStyleSheet("""
            QPushButton {
                background-color: transparent;
                color: white;
                border: none;
                border-radius: 14px;
            }
            QPushButton:hover {
                background-color: rgba(255, 255, 255, 0.3);
            }
            QPushButton:pressed {
                background-color: rgba(0, 0, 0, 0.2);
            }
        """)
        self.close_button.clicked.connect(self.close)

        # إضافة زر التعليمات في شريط العنوان
        self.help_btn = QPushButton("تعليمات")
        self.help_btn.setFont(QFont("Calibri", 12, QFont.Bold))
        self.help_btn.setStyleSheet("""
            QPushButton {
                background-color: #ff9800;
                color: white;
                border: none;
                border-radius: 4px;
                padding: 4px 10px;
            }
            QPushButton:hover {
                background-color: #e65100;
            }
            QPushButton:pressed {
                background-color: #bf360c;
            }
        """)
        self.help_btn.clicked.connect(self.show_help)

        # إضافة العناصر إلى تخطيط العنوان
        header_layout.addWidget(self.help_btn)
        header_layout.addStretch()

        # ---- معلومات التلميذ (إزالة العنوان) ----
        student_group = QGroupBox("")
        student_layout = QGridLayout(student_group)
        student_layout.setVerticalSpacing(8)
        student_layout.setHorizontalSpacing(8)

        # أنماط العناصر المختلفة
        label_style = "QLabel { color: black; padding: 2px; font-weight: bold; font-family: 'Calibri'; font-size: 13pt; }"
        readonly_field_style = """
            QLineEdit {
                background-color: #f8f9fa;
                border: 1px solid #dce0e3;
                border-radius: 4px;
                padding: 4px;
                color: black;
                font-family: 'Calibri';
                font-size: 13pt;
                font-weight: bold;
            }
        """
        editable_field_style = """
            QLineEdit, QDateEdit, QComboBox {
                background-color: white;
                border: 1px solid #dce0e3;
                border-radius: 4px;
                padding: 4px;
                color: black;
                font-family: 'Calibri';
                font-size: 13pt;
                font-weight: bold;
            }
            QLineEdit:focus, QDateEdit:focus, QComboBox:focus {
                border: 1px solid #3a6ea5;
            }
        """

        # التاريخ
        date_label = QLabel("التاريخ:")
        date_label.setStyleSheet(label_style)
        self.date_edit = QDateEdit()
        self.date_edit.setCalendarPopup(True)
        self.date_edit.setDate(QDate.currentDate())
        self.date_edit.setDisplayFormat("dd/MM/yyyy")
        self.date_edit.setStyleSheet(editable_field_style)
        student_layout.addWidget(date_label, 0, 0)
        student_layout.addWidget(self.date_edit, 0, 1)

        # المستوى
        level_label = QLabel("المستوى:")
        level_label.setStyleSheet(label_style)
        self.level_combo = QComboBox()
        self.level_combo.setMinimumWidth(200)
        self.level_combo.setStyleSheet(editable_field_style)
        student_layout.addWidget(level_label, 0, 2)
        student_layout.addWidget(self.level_combo, 0, 3)

        # القسم
        section_label = QLabel("القسم:")
        section_label.setStyleSheet(label_style)
        self.section_combo = QComboBox()
        self.section_combo.setMinimumWidth(200)
        self.section_combo.setStyleSheet(editable_field_style)
        student_layout.addWidget(section_label, 0, 4)
        student_layout.addWidget(self.section_combo, 0, 5)

        # الرمز
        code_label = QLabel("الرمز:")
        code_label.setStyleSheet(label_style)
        self.code_edit = QLineEdit()
        self.code_edit.setStyleSheet(readonly_field_style)
        student_layout.addWidget(code_label, 1, 0)
        student_layout.addWidget(self.code_edit, 1, 1)

        # رت
        rt_label = QLabel("رت:")
        rt_label.setStyleSheet(label_style)
        self.rt_edit = QLineEdit()
        self.rt_edit.setStyleSheet(readonly_field_style)
        student_layout.addWidget(rt_label, 1, 2)
        student_layout.addWidget(self.rt_edit, 1, 3)

        # الاسم والنسب
        name_label = QLabel("الاسم والنسب:")
        name_label.setStyleSheet(label_style)
        self.name_edit = QLineEdit()
        self.name_edit.setStyleSheet(readonly_field_style)
        student_layout.addWidget(name_label, 1, 4)
        student_layout.addWidget(self.name_edit, 1, 5)

        # إضافة الإطار إلى التخطيط الرئيسي
        main_layout.addWidget(student_group)

        # إضافة تأثير الظل
        self.apply_shadow(student_group)

        # ---- المادة والأستاذ (إزالة العنوان) ----
        subject_group = QGroupBox("")
        subject_layout = QGridLayout(subject_group)
        subject_layout.setVerticalSpacing(8)
        subject_layout.setHorizontalSpacing(8)

        # المادة
        subject_label = QLabel("المادة:")
        subject_label.setStyleSheet(label_style)
        self.subject_combo = QComboBox()
        self.subject_combo.setMinimumWidth(250)
        self.subject_combo.setStyleSheet(editable_field_style)
        subject_layout.addWidget(subject_label, 0, 0)
        subject_layout.addWidget(self.subject_combo, 0, 1)

        # الأستاذ
        teacher_label = QLabel("الأستاذ:")
        teacher_label.setStyleSheet(label_style)
        self.teacher_combo = QComboBox()
        self.teacher_combo.setMinimumWidth(250)
        self.teacher_combo.setStyleSheet(editable_field_style)
        subject_layout.addWidget(teacher_label, 0, 2)
        subject_layout.addWidget(self.teacher_combo, 0, 3)

        # إضافة الإطار إلى التخطيط الرئيسي
        main_layout.addWidget(subject_group)

        # إضافة تأثير الظل
        self.apply_shadow(subject_group)

        # ---- مسك المخالفات (إزالة العنوان) ----
        violations_group = QGroupBox("")
        violations_layout = QVBoxLayout(violations_group)
        violations_layout.setSpacing(8) # تقليل المسافة
        violations_layout.setContentsMargins(15, 10, 15, 15) # تقليل المسافة العلوية

        # إنشاء تخطيط أفقي للقوائم المنسدلة
        dropdowns_layout = QHBoxLayout()

        # قائمة الملاحظات
        notes_layout = QVBoxLayout()
        notes_layout.setSpacing(4) # تقليل المسافة
        notes_label = QLabel("قائمة المخالفات:")
        notes_label.setStyleSheet(label_style)
        self.notes_combo = QComboBox()
        self.notes_combo.setMinimumWidth(300)
        self.notes_combo.setStyleSheet(editable_field_style)
        notes_layout.addWidget(notes_label)
        notes_layout.addWidget(self.notes_combo)
        dropdowns_layout.addLayout(notes_layout)

        # قائمة الإجراءات
        procedures_layout = QVBoxLayout()
        procedures_layout.setSpacing(4) # تقليل المسافة
        procedures_label = QLabel("قائمة الإجراءات:")
        procedures_label.setStyleSheet(label_style)
        self.procedures_combo = QComboBox()
        self.procedures_combo.setMinimumWidth(300)
        self.procedures_combo.setStyleSheet(editable_field_style)
        procedures_layout.addWidget(procedures_label)
        procedures_layout.addWidget(self.procedures_combo)
        dropdowns_layout.addLayout(procedures_layout)

        violations_layout.addLayout(dropdowns_layout)

        # تنسيق مربع النص
        textedit_style = """
            QTextEdit {
                background-color: white;
                border: 1px solid #dce0e3;
                border-radius: 4px;
                padding: 4px;
                color: black;
                font-family: 'Calibri';
                font-size: 13pt;
                font-weight: bold;
            }
            QTextEdit:focus {
                border: 1px solid #3a6ea5;
            }
        """

        # إنشاء تخطيط للمربعات النصية
        textareas_layout = QGridLayout()
        textareas_layout.setVerticalSpacing(8)
        textareas_layout.setHorizontalSpacing(8)

        # المخالفات المرتكبة
        notes_text_label = QLabel("المخالفات المرتكبة:")
        notes_text_label.setStyleSheet(label_style)
        self.notes_text = QTextEdit()
        self.notes_text.setMinimumHeight(120)
        self.notes_text.setMaximumHeight(200)
        self.notes_text.setStyleSheet(textedit_style)
        textareas_layout.addWidget(notes_text_label, 0, 0)
        textareas_layout.addWidget(self.notes_text, 1, 0)

        # الإجراءات المطلوبة
        procedures_text_label = QLabel("الإجراءات المطلوبة:")
        procedures_text_label.setStyleSheet(label_style)
        self.procedures_text = QTextEdit()
        self.procedures_text.setMinimumHeight(120)
        self.procedures_text.setMaximumHeight(200)
        self.procedures_text.setStyleSheet(textedit_style)
        textareas_layout.addWidget(procedures_text_label, 0, 1)
        textareas_layout.addWidget(self.procedures_text, 1, 1)

        # الإجراءات التي اتخذتها الحراسة العامة
        guard_actions_label = QLabel("الإجراءات التي اتخذتها الحراسة العامة:")
        guard_actions_label.setStyleSheet(label_style)
        self.guard_actions_text = QTextEdit()
        self.guard_actions_text.setMinimumHeight(120)
        self.guard_actions_text.setMaximumHeight(200)
        self.guard_actions_text.setStyleSheet(textedit_style)
        textareas_layout.addWidget(guard_actions_label, 0, 2)
        textareas_layout.addWidget(self.guard_actions_text, 1, 2)

        violations_layout.addLayout(textareas_layout)

        # --- تحسين أنماط الأزرار ---
        button_style = """
            QPushButton {{
                padding: 8px 20px;
                border-radius: 5px;
                font-size: 13pt;
                font-weight: bold;
                font-family: 'Calibri';
                min-height: 32px;
                color: {text_color};
                background-color: {bg_color};
                border: 1px solid {border_color};
            }}
            QPushButton:hover {{
                background-color: {hover_color};
            }}
            QPushButton:pressed {{
                background-color: {pressed_color};
            }}
        """

        # أزرار التحكم
        buttons_layout = QHBoxLayout()
        buttons_layout.setSpacing(10)

        self.save_btn = QPushButton("حفظ المخالفة")
        self.save_btn.setMinimumWidth(150)
        self.save_btn.setFont(QFont("Calibri", 13, QFont.Bold))
        self.save_btn.setCursor(Qt.PointingHandCursor)  # تغيير المؤشر إلى شكل يد
        self.save_btn.setStyleSheet(button_style.format(
            text_color="#ffffff",
            bg_color="#3498db",
            border_color="#2980b9",
            hover_color="#2c5b8e",
            pressed_color="#1e4a75"))

        self.clear_btn = QPushButton("مسح البيانات")
        self.clear_btn.setMinimumWidth(150)
        self.clear_btn.setFont(QFont("Calibri", 13, QFont.Bold))
        self.clear_btn.setCursor(Qt.PointingHandCursor)  # تغيير المؤشر إلى شكل يد
        self.clear_btn.setStyleSheet(button_style.format(
            text_color="#333333",
            bg_color="#f0f0f0",
            border_color="#ccc",
            hover_color="#e0e0e0",
            pressed_color="#cccccc"))

        buttons_layout.addStretch()
        buttons_layout.addWidget(self.clear_btn)
        buttons_layout.addWidget(self.save_btn)

        violations_layout.addLayout(buttons_layout)

        # إضافة الإطار إلى التخطيط الرئيسي
        main_layout.addWidget(violations_group)

        # إضافة تأثير الظل
        self.apply_shadow(violations_group)

        # اتصالات الإشارات
        self.connect_signals()

        # تحميل البيانات
        self.load_data()

    def connect_signals(self):
        """ربط الإشارات بالوظائف"""
        # تحديث القسم عند تغيير المستوى
        self.level_combo.currentIndexChanged.connect(self.update_sections)

        # تحديث الأساتذة عند تغيير المادة
        self.subject_combo.currentIndexChanged.connect(self.update_teachers)

        # إضافة الملاحظة المحددة إلى مربع النص
        self.notes_combo.activated.connect(self.add_note_to_text)

        # إضافة الإجراء المحدد إلى مربع النص
        self.procedures_combo.activated.connect(self.add_procedure_to_text)

        # زر الحفظ
        self.save_btn.clicked.connect(self.save_violation)

        # زر المسح
        self.clear_btn.clicked.connect(self.clear_fields)

    def load_data(self):
        """تحميل البيانات من قاعدة البيانات"""
        try:
            # تحميل المستويات
            cursor = self.db_manager.execute_query(
                "SELECT DISTINCT المستوى FROM البنية_التربوية ORDER BY المستوى",
                fetch_all=True
            )
            levels = [row[0] for row in cursor]
            self.level_combo.addItems(levels)

            # تحميل المواد
            cursor = self.db_manager.execute_query(
                "SELECT DISTINCT المادة FROM الأساتذة ORDER BY المادة",
                fetch_all=True
            )
            subjects = [row[0] for row in cursor]
            self.subject_combo.addItems(subjects)

            # تحميل المخالفات من جدول تعديل_المسميات حيث ID من 7 إلى 16
            violations_query = self.db_manager.execute_query(
                "SELECT العنوان FROM تعديل_المسميات WHERE ID BETWEEN 7 AND 16 ORDER BY ID",
                fetch_all=True
            )
            if violations_query:
                violations = [row[0] for row in violations_query]
                self.notes_combo.addItems(violations)

            # تحميل الإجراءات من جدول تعديل_المسميات حيث ID من 17 إلى 20
            procedures_query = self.db_manager.execute_query(
                "SELECT العنوان FROM تعديل_المسميات WHERE ID BETWEEN 17 AND 20 ORDER BY ID",
                fetch_all=True
            )
            if procedures_query:
                procedures = [row[0] for row in procedures_query]
                self.procedures_combo.addItems(procedures)

            # تحديث الأقسام بناءً على المستوى الأول
            self.update_sections()

            # تحديث الأساتذة بناءً على المادة الأولى
            self.update_teachers()

        except Exception as e:
            QMessageBox.critical(self, "خطأ في تحميل البيانات", f"حدث خطأ أثناء تحميل البيانات: {str(e)}")

    def update_sections(self):
        """تحديث قائمة الأقسام بناءً على المستوى المحدد"""
        try:
            level = self.level_combo.currentText()
            if not level:
                return

            sections = self.db_manager.execute_query(
                "SELECT DISTINCT القسم FROM البنية_التربوية WHERE المستوى = ? ORDER BY القسم",
                (level,),
                fetch_all=True
            )

            self.section_combo.clear()
            self.section_combo.addItems([row[0] for row in sections])
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء تحديث الأقسام: {str(e)}")

    def update_teachers(self):
        """تحديث قائمة الأساتذة بناءً على المادة المحددة"""
        try:
            subject = self.subject_combo.currentText()
            if not subject:
                return

            teachers = self.db_manager.execute_query(
                "SELECT DISTINCT اسم_الأستاذ FROM الأساتذة WHERE المادة = ? ORDER BY اسم_الأستاذ",
                (subject,),
                fetch_all=True
            )

            self.teacher_combo.clear()
            self.teacher_combo.addItems([row[0] for row in teachers])
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء تحديث الأساتذة: {str(e)}")

    def add_note_to_text(self):
        """إضافة الملاحظة المحددة إلى مربع النص"""
        selected_note = self.notes_combo.currentText()
        if selected_note:
            current_text = self.notes_text.toPlainText()
            if current_text:
                self.notes_text.setPlainText(f"{current_text}\n- {selected_note}")
            else:
                self.notes_text.setPlainText(f"- {selected_note}")

    def add_procedure_to_text(self):
        """إضافة الإجراء المحدد إلى مربع النص"""
        selected_procedure = self.procedures_combo.currentText()
        if selected_procedure:
            current_text = self.procedures_text.toPlainText()
            if current_text:
                self.procedures_text.setPlainText(f"{current_text}\n- {selected_procedure}")
            else:
                self.procedures_text.setPlainText(f"- {selected_procedure}")

    def search_student(self):
        """البحث عن تلميذ بناءً على الرمز أو الاسم"""
        try:
            code = self.code_edit.text().strip()
            name = self.name_edit.text().strip()

            if not code and not name:
                QMessageBox.warning(self, "تنبيه", "الرجاء إدخال الرمز أو الاسم للبحث")
                return

            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # استرجاع السنة الدراسية الحالية من جدول بيانات_المؤسسة
            cursor.execute("SELECT السنة_الدراسية FROM بيانات_المؤسسة LIMIT 1")
            current_school_year = cursor.fetchone()
            current_school_year = current_school_year[0] if current_school_year else "2023/2024"

            if code:
                cursor.execute("""
                    SELECT s.الرمز, s.رت, s.الاسم, s.النسب, l.المستوى, l.القسم
                    FROM السجل_العام s
                    JOIN اللوائح l ON s.الرمز = l.الرمز
                    WHERE s.الرمز = ? AND l.السنة_الدراسية = ?
                """, (code, current_school_year))
            else:
                # البحث بالاسم (نفترض أن الاسم يشمل الاسم والنسب)
                cursor.execute("""
                    SELECT s.الرمز, s.رت, s.الاسم, s.النسب, l.المستوى, l.القسم
                    FROM السجل_العام s
                    JOIN اللوائح l ON s.الرمز = l.الرمز
                    WHERE (s.الاسم || ' ' || s.النسب LIKE ?) AND l.السنة_الدراسية = ?
                """, (f"%{name}%", current_school_year))

            result = cursor.fetchone()
            conn.close()

            if result:
                # تعبئة البيانات
                self.code_edit.setText(str(result[0]))
                self.rt_edit.setText(str(result[1]))
                self.name_edit.setText(f"{result[2]} {result[3]}")

                # تحديد المستوى والقسم في القوائم المنسدلة
                level_index = self.level_combo.findText(result[4])
                if level_index >= 0:
                    self.level_combo.setCurrentIndex(level_index)
                    # تحديث الأقسام ثم اختيار القسم
                    self.update_sections()
                    section_index = self.section_combo.findText(result[5])
                    if section_index >= 0:
                        self.section_combo.setCurrentIndex(section_index)
            else:
                QMessageBox.information(self, "نتيجة البحث", "لم يتم العثور على التلميذ في السنة الدراسية الحالية")

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء البحث: {str(e)}")

    def save_violation(self):
        """حفظ المخالفة في قاعدة البيانات مع تضمين السنة الدراسية والأسدس"""
        try:
            # التحقق من إدخال البيانات الضرورية
            if not self.code_edit.text() or not self.name_edit.text():
                show_custom_message(self, "الرجاء إدخال بيانات التلميذ", "تنبيه", "warning")
                return

            if not self.notes_text.toPlainText():
                show_custom_message(self, "الرجاء إدخال الملاحظات المرتكبة", "تنبيه", "warning")
                return

            # --- استرجاع السنة الدراسية والأسدس الحاليين ---
            # Assuming get_current_school_year/semester return (value, error) tuple
            year_result = self.db_manager.get_current_school_year()
            semester_result = self.db_manager.get_current_semester()

            # Check for errors during retrieval
            if year_result[1] or semester_result[1]:
                error_msg = year_result[1] or semester_result[1]
                show_custom_message(self, f"لم يتم العثور على السنة الدراسية أو الأسدس في إعدادات المؤسسة.\n{error_msg}", "خطأ", "error")
                print(f"Error retrieving settings: Year Error='{year_result[1]}', Semester Error='{semester_result[1]}'")
                return

            # Extract the actual values
            current_school_year = year_result[0]
            current_semester = semester_result[0]

            # Verify the extracted values (for debugging)
            print(f"DEBUG: Retrieved School Year: {current_school_year}")
            print(f"DEBUG: Retrieved Semester: {current_semester}")

            # Check if values are valid before proceeding
            if not current_school_year or not current_semester:
                 show_custom_message(self, "فشل استرداد السنة الدراسية أو الأسدس (قيمة فارغة).", "خطأ", "error")
                 print("ERROR: Retrieved school_year or semester is None or empty.")
                 return
            # ------------------------------------------------

            # جمع البيانات
            violation_data = {
                "date": self.date_edit.date().toString("yyyy-MM-dd"),
                "student_code": self.code_edit.text(),
                "student_name": self.name_edit.text(),
                "level": self.level_combo.currentText(),
                "section": self.section_combo.currentText(),
                "subject": self.subject_combo.currentText(),
                "teacher": self.teacher_combo.currentText(),
                "notes": self.notes_text.toPlainText(),
                "procedures": self.procedures_text.toPlainText(),
                "guard_actions": self.guard_actions_text.toPlainText(),
                "school_year": current_school_year, # Use the extracted value
                "semester": current_semester        # Use the extracted value
            }

            # Debug: Print the dictionary before sending it
            print(f"DEBUG: Data being sent to save_violation: {violation_data}")

            # استخدام مدير قاعدة البيانات لحفظ المخالفة
            print("محاولة حفظ المخالفة...")
            success = self.db_manager.save_violation(violation_data)

            if success:
                print("تم حفظ المخالفة بنجاح!")
                # استخدام رسالة نجاح مخصصة مع تصميم جميل
                show_custom_message(
                    self,
                    "تم حفظ المخالفة بنجاح\n\nتم تسجيل المخالفة في قاعدة البيانات بنجاح للطالب: " + self.name_edit.text(),
                    "تم الحفظ بنجاح",
                    "success"
                )

                # مسح الحقول النصية
                self.notes_text.clear()
                self.procedures_text.clear()
                self.guard_actions_text.clear()
            else:
                print("فشل حفظ المخالفة!")
                show_custom_message(self, "حدث خطأ أثناء حفظ المخالفة", "خطأ", "error")

        except Exception as e:
            print(f"خطأ في حفظ المخالفة: {e}")
            import traceback
            traceback.print_exc()
            show_custom_message(
                self,
                f"حدث خطأ أثناء حفظ المخالفة:\n{str(e)}",
                "خطأ في الحفظ",
                "error"
            )

    def clear_fields(self):
        """مسح جميع الحقول"""
        # استخدام رسالة مخصصة للتأكيد
        reply = show_custom_message(
            self,
            "هل تريد مسح جميع البيانات؟",
            "تأكيد المسح",
            "question"
        )

        if reply:
            self.code_edit.clear()
            self.rt_edit.clear()
            self.name_edit.clear()
            self.notes_text.clear()
            self.procedures_text.clear()
            self.guard_actions_text.clear()
            self.date_edit.setDate(QDate.currentDate())

            # إعادة تعيين القوائم المنسدلة للقيم الأولى
            if self.level_combo.count() > 0:
                self.level_combo.setCurrentIndex(0)
            if self.subject_combo.count() > 0:
                self.subject_combo.setCurrentIndex(0)

            # عرض رسالة نجاح المسح
            show_custom_message(
                self,
                "تم مسح جميع البيانات بنجاح",
                "تم المسح",
                "success"
            )

    def apply_shadow(self, widget):
        """إضافة تأثير الظل للعنصر"""
        shadow = QGraphicsDropShadowEffect()
        shadow.setBlurRadius(15)
        shadow.setXOffset(5)
        shadow.setYOffset(5)
        shadow.setColor(QColor(0, 0, 0, 150))
        widget.setGraphicsEffect(shadow)

    def set_student_info(self, code, name, id_num, level, class_name):
        """تعيين معلومات الطالب برمجياً مع التحقق من السنة الدراسية الحالية"""
        try:
            print(f"محاولة تعيين معلومات الطالب في نافذة المخالفات: {name}, المستوى: {level}, القسم: {class_name}")

            # استخدام مدير قاعدة البيانات للتحقق من تسجيل التلميذ
            is_enrolled, student_data = self.db_manager.check_student_enrollment(code)

            if not is_enrolled:
                current_school_year = self.db_manager.get_current_school_year()
                print(f"التلميذ {name} غير مسجل في السنة الدراسية الحالية: {current_school_year}")
                QMessageBox.warning(
                    self,
                    "تنبيه",
                    f"التلميذ {name} غير مسجل في السنة الدراسية الحالية: {current_school_year}"
                )
                return False

            # تعيين رمز التلميذ
            self.code_edit.setText(code)

            # تعيين رقم التلميذ الترتيبي
            self.rt_edit.setText(id_num)

            # تعيين اسم التلميذ
            self.name_edit.setText(name)

            # تحديد المستوى في القائمة المنسدلة
            level_index = self.level_combo.findText(level)
            if level_index >= 0:
                self.level_combo.setCurrentIndex(level_index)
                # تحديث الأقسام ثم اختيار القسم
                self.update_sections()
                section_index = self.section_combo.findText(class_name)
                if section_index >= 0:
                    self.section_combo.setCurrentIndex(section_index)

            print(f"تم تعيين معلومات الطالب بنجاح في نافذة المخالفات: {name}")
            return True

        except Exception as e:
            print(f"خطأ في تعيين معلومات الطالب في نافذة المخالفات: {e}")
            import traceback
            traceback.print_exc()
            return False

    def show_help(self):
        """عرض نافذة التعليمات للمستخدم"""
        help_dialog = QDialog(self)
        help_dialog.setWindowTitle("تعليمات استخدام نافذة المخالفات")
        help_dialog.setMinimumSize(900, 700)
        help_dialog.setLayoutDirection(Qt.RightToLeft)

        # تطبيق التصميم على النافذة
        help_dialog.setStyleSheet("""
            QDialog { background-color: white; }
            QTextBrowser {
                border: 1px solid #E0E0E0;
                border-radius: 5px;
                padding: 10px;
                font-family: Calibri;
                font-size: 14pt;
                line-height: 1.5;
            }
        """)

        layout = QVBoxLayout(help_dialog)

        text_browser = QTextBrowser()
        text_browser.setOpenExternalLinks(True)

        # محتوى التعليمات
        help_text = """
        <h2 style="color: #1976d2; text-align: center;">تعليمات استخدام نافذة المخالفات</h2>

        <h3 style="color: #2196f3;">نظرة عامة</h3>
        <p>هذه النافذة تساعدك على تسجيل وإدارة مخالفات التلاميذ بطريقة منظمة وسهلة.</p>

        <h3 style="color: #2196f3;">مكونات النافذة</h3>
        <ul>
            <li><b>بيانات التلميذ:</b> تحتوي على معلومات التلميذ الأساسية (الرمز، الرقم الترتيبي، الاسم والنسب).</li>
            <li><b>معلومات المخالفة:</b> تحتوي على المستوى، القسم، المادة، والأستاذ.</li>
            <li><b>تفاصيل المخالفة:</b> تحتوي على الملاحظات، الإجراءات المتخذة، وإجراءات الحراسة العامة.</li>
        </ul>

        <h3 style="color: #2196f3;">خطوات تسجيل مخالفة</h3>
        <ol>
            <li><b>إدخال بيانات التلميذ:</b> أدخل رمز التلميذ أو اسمه للبحث عنه في قاعدة البيانات.</li>
            <li><b>اختيار المستوى والقسم:</b> سيتم تعبئتهما تلقائياً بعد البحث، أو يمكنك اختيارهما يدوياً.</li>
            <li><b>اختيار المادة والأستاذ:</b> اختر المادة أولاً ثم الأستاذ المعني.</li>
            <li><b>إدخال الملاحظات:</b> يمكنك اختيار ملاحظة من القائمة المنسدلة أو كتابتها يدوياً.</li>
            <li><b>إدخال الإجراءات:</b> يمكنك اختيار إجراء من القائمة المنسدلة أو كتابته يدوياً.</li>
            <li><b>إدخال إجراءات الحراسة:</b> أدخل الإجراءات التي اتخذتها الحراسة العامة.</li>
            <li><b>حفظ المخالفة:</b> انقر على زر "حفظ المخالفة" لتسجيلها في قاعدة البيانات.</li>
        </ol>

        <h3 style="color: #2196f3;">ميزات إضافية</h3>
        <ul>
            <li><b>إضافة ملاحظات متعددة:</b> يمكنك إضافة أكثر من ملاحظة باختيارها من القائمة المنسدلة.</li>
            <li><b>إضافة إجراءات متعددة:</b> يمكنك إضافة أكثر من إجراء باختياره من القائمة المنسدلة.</li>
            <li><b>مسح البيانات:</b> يمكنك مسح جميع الحقول بالنقر على زر "مسح البيانات".</li>
        </ul>

        <h3 style="color: #2196f3;">ملاحظات هامة</h3>
        <ul>
            <li>تأكد من إدخال بيانات التلميذ بشكل صحيح قبل حفظ المخالفة.</li>
            <li>يجب إدخال الملاحظات على الأقل قبل حفظ المخالفة.</li>
            <li>يتم حفظ المخالفة مع السنة الدراسية والأسدس الحاليين تلقائياً.</li>
            <li>يمكنك طباعة المخالفة بعد حفظها من خلال نافذة معاينة الطباعة.</li>
        </ul>
        """

        text_browser.setHtml(help_text)
        layout.addWidget(text_browser)

        # زر إغلاق
        close_btn = QPushButton("إغلاق")
        close_btn.setFixedHeight(35)
        close_btn.setMinimumWidth(120)
        close_btn.clicked.connect(help_dialog.accept)
        close_btn.setStyleSheet("""
            QPushButton { background-color: #1976d2; color: white; border: none; border-radius: 4px;
                          padding: 0 15px; font-family: Calibri; font-size: 13pt; font-weight: bold; }
            QPushButton:hover { background-color: #1565c0; }
        """)

        btn_layout = QHBoxLayout()
        btn_layout.addStretch()
        btn_layout.addWidget(close_btn)
        layout.addLayout(btn_layout)

        help_dialog.setLayout(layout)
        help_dialog.exec_()

# إضافة فئة جديدة لنافذة معاينة الطباعة
class ViolationPrintPreviewWindow(QWidget):
    def __init__(self, violation_data, institution_data, parent=None):
        super().__init__(parent)
        self.violation_data = violation_data
        self.institution_data = institution_data
        self.setWindowTitle("معاينة طباعة المخالفة")
        self.setWindowModality(Qt.ApplicationModal)
        self.setup_ui()

    def setup_ui(self):
        # إعداد الواجهة
        self.setMinimumSize(800, 600)
        layout = QVBoxLayout(self)

        # إنشاء مربع عنوان
        title_frame = QFrame()
        title_frame.setStyleSheet("background-color: #3498db; color: white; border-radius: 5px;")
        title_layout = QHBoxLayout(title_frame)

        title_label = QLabel("معاينة طباعة المخالفة")
        title_label.setFont(QFont("Calibri", 16, QFont.Bold))
        title_label.setStyleSheet("color: white;")
        title_label.setAlignment(Qt.AlignCenter)

        title_layout.addWidget(title_label)
        layout.addWidget(title_frame)

        # إنشاء مربع معاينة
        preview_frame = QFrame()
        preview_frame.setStyleSheet("background-color: white; border: 1px solid #bdc3c7; border-radius: 5px;")
        preview_layout = QVBoxLayout(preview_frame)

        # إنشاء مستند HTML للمعاينة
        preview_document = self.create_preview_document()

        # إنشاء عنصر لعرض المستند (يمكن استخدام QTextBrowser)
        from PyQt5.QtWidgets import QTextBrowser
        self.preview_browser = QTextBrowser()
        self.preview_browser.setHtml(preview_document)
        self.preview_browser.setStyleSheet("border: none;")

        preview_layout.addWidget(self.preview_browser)
        layout.addWidget(preview_frame, 1)  # تمتد هذه الأداة

        # إضافة أزرار التحكم
        buttons_layout = QHBoxLayout()

        self.close_btn = QPushButton("إغلاق")
        self.close_btn.setFont(QFont("Calibri", 13, QFont.Bold))
        self.close_btn.setMinimumWidth(120)
        self.close_btn.setStyleSheet("background-color: #e74c3c; color: white;")
        self.close_btn.clicked.connect(self.close)

        self.print_btn = QPushButton("طباعة")
        self.print_btn.setFont(QFont("Calibri", 13, QFont.Bold))
        self.print_btn.setMinimumWidth(120)
        self.print_btn.setStyleSheet("background-color: #27ae60; color: white;")
        self.print_btn.clicked.connect(self.print_document)

        buttons_layout.addStretch()
        buttons_layout.addWidget(self.close_btn)
        buttons_layout.addWidget(self.print_btn)

        layout.addLayout(buttons_layout)

    def create_preview_document(self):
        """إنشاء مستند HTML للمعاينة والطباعة"""
        # معالجة مسار الصورة
        logo_html = ""
        if self.institution_data.get("logo_path") and os.path.exists(self.institution_data.get("logo_path")):
            logo_html = f'<img src="{self.institution_data.get("logo_path")}" alt="شعار المؤسسة" style="height: 80px; display: block; margin: 0 auto;">'

        # معالجة النصوص لتجنب مشاكل f-string
        notes_formatted = self.violation_data.get("notes", "").replace('\n', '<br>')
        procedures_formatted = self.violation_data.get("procedures", "").replace('\n', '<br>')
        guard_actions_formatted = self.violation_data.get("guard_actions", "").replace('\n', '<br>')

        # إنشاء محتوى المستند بتنسيق HTML
        html_content = f"""
        <html dir="rtl">
        <head>
            <style>
                @page {{ size: A4 portrait; margin: 2cm; }}
                body {{ font-family: 'Calibri'; font-size: 12pt; text-align: right; }}
                h1 {{ text-align: center; font-size: 16pt; margin-bottom: 10px; }}
                h2 {{ font-size: 14pt; margin-top: 15px; margin-bottom: 10px; }}
                table {{ width: 100%; border-collapse: collapse; margin-bottom: 15px; }}
                th, td {{ border: 1px solid #000; padding: 8px; text-align: right; }}
                th {{ background-color: #f2f2f2; }}
                .section {{ margin-top: 15px; border-top: 1px solid #ccc; padding-top: 10px; }}
                .footer {{ text-align: center; margin-top: 30px; font-size: 10pt; }}
                .header {{ padding: 10px; }}
                .school-info {{ text-align: right; font-weight: bold; }}
                .logo {{ text-align: center; }}
            </style>
        </head>
        <body>
            <div class="header">
                <table border="0" style="border: none;">
                    <tr style="border: none;">
                        <td style="border: none; text-align: right; width: 33%;">
                            <div class="school-info">
                                <p>الأكاديمية: {self.institution_data.get("academy")}</p>
                                <p>المديرية: {self.institution_data.get("directorate")}</p>
                                <p>السنة الدراسية: {self.institution_data.get("school_year")}</p>
                            </div>
                        </td>
                        <td style="border: none; text-align: center; width: 33%;">
                            <div class="logo">
                                {logo_html}
                            </div>
                        </td>
                        <td style="border: none; width: 33%;"></td>
                    </tr>
                </table>
            </div>

            <h1>تقرير مخالفة</h1>
            <p style="text-align: center;">المملكة المغربية - وزارة التربية الوطنية</p>

            <h2>معلومات التلميذ</h2>
            <table>
                <tr>
                    <th>التاريخ</th>
                    <td>{self.violation_data.get("date")}</td>
                    <th>المستوى</th>
                    <td>{self.violation_data.get("level")}</td>
                </tr>
                <tr>
                    <th>القسم</th>
                    <td>{self.violation_data.get("section")}</td>
                    <th>الرمز</th>
                    <td>{self.violation_data.get("student_code")}</td>
                </tr>
                <tr>
                    <th>رت</th>
                    <td>{self.violation_data.get("student_rt")}</td>
                    <th>الاسم والنسب</th>
                    <td>{self.violation_data.get("student_name")}</td>
                </tr>
            </table>

            <h2>معلومات المخالفة</h2>
            <table>
                <tr>
                    <th>المادة</th>
                    <td>{self.violation_data.get("subject")}</td>
                    <th>الأستاذ</th>
                    <td>{self.violation_data.get("teacher")}</td>
                </tr>
            </table>

            <div class="section">
                <h2>المخالفات المرتكبة</h2>
                <p>{notes_formatted}</p>
            </div>

            <div class="section">
                <h2>الإجراءات المطلوبة</h2>
                <p>{procedures_formatted}</p>
            </div>

            <div class="section">
                <h2>الإجراءات التي اتخذتها الحراسة العامة</h2>
                <p>{guard_actions_formatted}</p>
            </div>

            <div class="footer">
                <p>وقت الطباعة: {QDateTime.currentDateTime().toString("dd/MM/yyyy hh:mm")}</p>
            </div>
        </body>
        </html>
        """

        return html_content

    def print_document(self):
        """طباعة المستند"""
        try:
            # إنشاء مستند HTML للطباعة
            document = QTextDocument()
            document.setHtml(self.create_preview_document())

            # إعداد الطابعة بحجم A4 عمودي
            printer = QPrinter(QPrinter.HighResolution)
            printer.setPageSize(QPageSize(QPageSize.A4))
            printer.setOrientation(QPrinter.Portrait)

            # إنشاء مربع حوار الطباعة
            print_dialog = QPrintDialog(printer, self)
            print_dialog.setWindowTitle("طباعة المخالفة")

            if print_dialog.exec_() == QPrintDialog.Accepted:
                # طباعة المستند مباشرة
                document.print_(printer)
                QMessageBox.information(self, "طباعة", "تمت الطباعة بنجاح!")

        except Exception as e:
            QMessageBox.critical(self, "خطأ في الطباعة", f"حدث خطأ أثناء طباعة المخالفة:\n{str(e)}")
            import traceback
            traceback.print_exc()

# إضافة فئة جديدة ليتم استخدامها كعنصر واجهة داخل تبويب
class ViolationsWidget(QWidget):
    def __init__(self, db_path="data.db", window_width=1200, window_height=620, parent=None):
        super().__init__(parent)
        self.db_path = db_path
        self.db_manager = get_db_manager(db_path)

        # إضافة هذه السطور لحل مشكلة الشفافية للويدجت
        self.setAttribute(Qt.WA_TranslucentBackground, False)
        self.setAttribute(Qt.WA_NoSystemBackground, False)
        self.setAutoFillBackground(True)

        # ضبط لون خلفية الويدجت
        palette = self.palette()
        palette.setColor(QPalette.Window, QColor(245, 245, 245))
        self.setPalette(palette)

        # إضافة إطار للويدجت
        self.setStyleSheet("""
            QWidget {
                border-radius: 8px;
                background-color: #f5f5f5;
            }
        """)

        # إنشاء التخطيط الرئيسي للعنصر
        self.layout = QVBoxLayout(self)
        self.layout.setContentsMargins(0, 0, 0, 0)  # إزالة الهوامش لاستخدام كامل المساحة المتاحة

        # بدلاً من إنشاء كائن ViolationsWindow، سننشئ واجهة المخالفات مباشرة هنا
        # استعارة الكود من فئة ViolationsWindow ولكن مع إزالة خصائص النافذة المنفصلة
        self.initUI()

    def initUI(self):
        # إنشاء التخطيط الرئيسي
        main_layout = QVBoxLayout()
        main_layout.setContentsMargins(12, 8, 12, 12)
        main_layout.setSpacing(6) # تقريب المسافات بين العناصر

        # ----- إنشاء إطار العنوان بتصميم أنيق -----
        header_frame = QFrame()
        header_frame.setStyleSheet("background-color: #3a6ea5; border-radius: 5px;")
        header_frame.setFixedHeight(40)

        header_layout = QHBoxLayout(header_frame)
        header_layout.setContentsMargins(8, 0, 8, 0)

        # عنوان النافذة
        title_label = QLabel("نظام تسجيل ومتابعة المخالفات")
        title_label.setFont(QFont("Calibri", 14, QFont.Bold))
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("color: white;")

        # لا نحتاج إلى زر الإغلاق في التبويب
        header_layout.addWidget(title_label)

        # إضافة إطار العنوان إلى التخطيط الرئيسي
        main_layout.addWidget(header_frame)

        # ---- معلومات التلميذ (إزالة العنوان) ----
        student_group = QGroupBox("")
        student_layout = QGridLayout(student_group)
        student_layout.setVerticalSpacing(8)
        student_layout.setHorizontalSpacing(8)

        # أنماط العناصر المختلفة
        label_style = "QLabel { color: black; padding: 2px; font-weight: bold; font-family: 'Calibri'; font-size: 13pt; }"
        readonly_field_style = """
            QLineEdit {
                background-color: #f8f9fa;
                border: 1px solid #dce0e3;
                border-radius: 4px;
                padding: 4px;
                color: black;
                font-family: 'Calibri';
                font-size: 13pt;
                font-weight: bold;
            }
        """
        editable_field_style = """
            QLineEdit, QDateEdit, QComboBox {
                background-color: white;
                border: 1px solid #dce0e3;
                border-radius: 4px;
                padding: 4px;
                color: black;
                font-family: 'Calibri';
                font-size: 13pt;
                font-weight: bold;
            }
            QLineEdit:focus, QDateEdit:focus, QComboBox:focus {
                border: 1px solid #3a6ea5;
            }
        """

        # التاريخ
        date_label = QLabel("التاريخ:")
        date_label.setStyleSheet(label_style)
        self.date_edit = QDateEdit()
        self.date_edit.setCalendarPopup(True)
        self.date_edit.setDate(QDate.currentDate())
        self.date_edit.setDisplayFormat("dd/MM/yyyy")
        self.date_edit.setStyleSheet(editable_field_style)
        student_layout.addWidget(date_label, 0, 0)
        student_layout.addWidget(self.date_edit, 0, 1)

        # المستوى
        level_label = QLabel("المستوى:")
        level_label.setStyleSheet(label_style)
        self.level_combo = QComboBox()
        self.level_combo.setMinimumWidth(200)
        self.level_combo.setStyleSheet(editable_field_style)
        student_layout.addWidget(level_label, 0, 2)
        student_layout.addWidget(self.level_combo, 0, 3)

        # القسم
        section_label = QLabel("القسم:")
        section_label.setStyleSheet(label_style)
        self.section_combo = QComboBox()
        self.section_combo.setMinimumWidth(200)
        self.section_combo.setStyleSheet(editable_field_style)
        student_layout.addWidget(section_label, 0, 4)
        student_layout.addWidget(self.section_combo, 0, 5)

        # الرمز
        code_label = QLabel("الرمز:")
        code_label.setStyleSheet(label_style)
        self.code_edit = QLineEdit()
        self.code_edit.setStyleSheet(readonly_field_style)
        student_layout.addWidget(code_label, 1, 0)
        student_layout.addWidget(self.code_edit, 1, 1)

        # رت
        rt_label = QLabel("رت:")
        rt_label.setStyleSheet(label_style)
        self.rt_edit = QLineEdit()
        self.rt_edit.setStyleSheet(readonly_field_style)
        student_layout.addWidget(rt_label, 1, 2)
        student_layout.addWidget(self.rt_edit, 1, 3)

        # الاسم والنسب
        name_label = QLabel("الاسم والنسب:")
        name_label.setStyleSheet(label_style)
        self.name_edit = QLineEdit()
        self.name_edit.setStyleSheet(readonly_field_style)
        student_layout.addWidget(name_label, 1, 4)
        student_layout.addWidget(self.name_edit, 1, 5)

        # إضافة الإطار إلى التخطيط الرئيسي
        main_layout.addWidget(student_group)

        # إضافة تأثير الظل
        self.apply_shadow(student_group)

        # ---- المادة والأستاذ (إزالة العنوان) ----
        subject_group = QGroupBox("")
        subject_layout = QGridLayout(subject_group)
        subject_layout.setVerticalSpacing(8)
        subject_layout.setHorizontalSpacing(8)

        # المادة
        subject_label = QLabel("المادة:")
        subject_label.setStyleSheet(label_style)
        self.subject_combo = QComboBox()
        self.subject_combo.setMinimumWidth(250)
        self.subject_combo.setStyleSheet(editable_field_style)
        subject_layout.addWidget(subject_label, 0, 0)
        subject_layout.addWidget(self.subject_combo, 0, 1)

        # الأستاذ
        teacher_label = QLabel("الأستاذ:")
        teacher_label.setStyleSheet(label_style)
        self.teacher_combo = QComboBox()
        self.teacher_combo.setMinimumWidth(250)
        self.teacher_combo.setStyleSheet(editable_field_style)
        subject_layout.addWidget(teacher_label, 0, 2)
        subject_layout.addWidget(self.teacher_combo, 0, 3)

        # إضافة الإطار إلى التخطيط الرئيسي
        main_layout.addWidget(subject_group)

        # إضافة تأثير الظل
        self.apply_shadow(subject_group)

        # ---- مسك المخالفات (إزالة العنوان) ----
        violations_group = QGroupBox("")
        violations_layout = QVBoxLayout(violations_group)
        violations_layout.setSpacing(8) # تقليل المسافة
        violations_layout.setContentsMargins(15, 10, 15, 15) # تقليل المسافة العلوية

        # إنشاء تخطيط أفقي للقوائم المنسدلة
        dropdowns_layout = QHBoxLayout()

        # قائمة الملاحظات
        notes_layout = QVBoxLayout()
        notes_layout.setSpacing(4) # تقليل المسافة
        notes_label = QLabel("قائمة المخالفات:")
        notes_label.setStyleSheet(label_style)
        self.notes_combo = QComboBox()
        self.notes_combo.setMinimumWidth(300)
        self.notes_combo.setStyleSheet(editable_field_style)
        notes_layout.addWidget(notes_label)
        notes_layout.addWidget(self.notes_combo)
        dropdowns_layout.addLayout(notes_layout)

        # قائمة الإجراءات
        procedures_layout = QVBoxLayout()
        procedures_layout.setSpacing(4) # تقليل المسافة
        procedures_label = QLabel("قائمة الإجراءات:")
        procedures_label.setStyleSheet(label_style)
        self.procedures_combo = QComboBox()
        self.procedures_combo.setMinimumWidth(300)
        self.procedures_combo.setStyleSheet(editable_field_style)
        procedures_layout.addWidget(procedures_label)
        procedures_layout.addWidget(self.procedures_combo)
        dropdowns_layout.addLayout(procedures_layout)

        violations_layout.addLayout(dropdowns_layout)

        # تنسيق مربع النص
        textedit_style = """
            QTextEdit {
                background-color: white;
                border: 1px solid #dce0e3;
                border-radius: 4px;
                padding: 4px;
                color: black;
                font-family: 'Calibri';
                font-size: 13pt;
                font-weight: bold;
            }
            QTextEdit:focus {
                border: 1px solid #3a6ea5;
            }
        """

        # إنشاء تخطيط للمربعات النصية
        textareas_layout = QGridLayout()
        textareas_layout.setVerticalSpacing(8)
        textareas_layout.setHorizontalSpacing(8)

        # المخالفات المرتكبة
        notes_text_label = QLabel("المخالفات المرتكبة:")
        notes_text_label.setStyleSheet(label_style)
        self.notes_text = QTextEdit()
        self.notes_text.setMinimumHeight(120)
        self.notes_text.setMaximumHeight(200)
        self.notes_text.setStyleSheet(textedit_style)
        textareas_layout.addWidget(notes_text_label, 0, 0)
        textareas_layout.addWidget(self.notes_text, 1, 0)

        # الإجراءات المطلوبة
        procedures_text_label = QLabel("الإجراءات المطلوبة:")
        procedures_text_label.setStyleSheet(label_style)
        self.procedures_text = QTextEdit()
        self.procedures_text.setMinimumHeight(120)
        self.procedures_text.setMaximumHeight(200)
        self.procedures_text.setStyleSheet(textedit_style)
        textareas_layout.addWidget(procedures_text_label, 0, 1)
        textareas_layout.addWidget(self.procedures_text, 1, 1)

        # الإجراءات التي اتخذتها الحراسة العامة
        guard_actions_label = QLabel("الإجراءات التي اتخذتها الحراسة العامة:")
        guard_actions_label.setStyleSheet(label_style)
        self.guard_actions_text = QTextEdit()
        self.guard_actions_text.setMinimumHeight(120)
        self.guard_actions_text.setMaximumHeight(200)
        self.guard_actions_text.setStyleSheet(textedit_style)
        textareas_layout.addWidget(guard_actions_label, 0, 2)
        textareas_layout.addWidget(self.guard_actions_text, 1, 2)

        violations_layout.addLayout(textareas_layout)

        # --- تحسين أنماط الأزرار ---
        button_style = """
            QPushButton {{
                padding: 8px 20px;
                border-radius: 5px;
                font-size: 13pt;
                font-weight: bold;
                font-family: 'Calibri';
                min-height: 32px;
                color: {text_color};
                background-color: {bg_color};
                border: 1px solid {border_color};
            }}
            QPushButton:hover {{
                background-color: {hover_color};
            }}
            QPushButton:pressed {{
                background-color: {pressed_color};
            }}
        """

        # أزرار التحكم
        buttons_layout = QHBoxLayout()
        buttons_layout.setSpacing(10)

        self.save_btn = QPushButton("حفظ المخالفة")
        self.save_btn.setMinimumWidth(150)
        self.save_btn.setFont(QFont("Calibri", 13, QFont.Bold))
        self.save_btn.setStyleSheet(button_style.format(
            text_color="#ffffff",
            bg_color="#3498db",
            border_color="#2980b9",
            hover_color="#2c5b8e",
            pressed_color="#1e4a75"))

        self.clear_btn = QPushButton("مسح البيانات")
        self.clear_btn.setMinimumWidth(150)
        self.clear_btn.setFont(QFont("Calibri", 13, QFont.Bold))
        self.clear_btn.setStyleSheet(button_style.format(
            text_color="#333333",
            bg_color="#f0f0f0",
            border_color="#ccc",
            hover_color="#e0e0e0",
            pressed_color="#cccccc"))

        buttons_layout.addStretch()
        buttons_layout.addWidget(self.clear_btn)
        buttons_layout.addWidget(self.save_btn)

        violations_layout.addLayout(buttons_layout)

        # إضافة الإطار إلى التخطيط الرئيسي
        main_layout.addWidget(violations_group)

        # إضافة تأثير الظل
        self.apply_shadow(violations_group)

        # إضافة التخطيط الرئيسي للويدجت
        self.layout.addLayout(main_layout)

        # اتصالات الإشارات
        self.connect_signals()

        # تحميل البيانات
        self.load_data()

        # ضبط اتجاه التخطيط لدعم اللغة العربية
        self.setLayoutDirection(Qt.RightToLeft)

    def connect_signals(self):
        """ربط الإشارات بالوظائف"""
        # تحديث القسم عند تغيير المستوى
        self.level_combo.currentIndexChanged.connect(self.update_sections)

        # تحديث الأساتذة عند تغيير المادة
        self.subject_combo.currentIndexChanged.connect(self.update_teachers)

        # إضافة الملاحظة المحددة إلى مربع النص
        self.notes_combo.activated.connect(self.add_note_to_text)

        # إضافة الإجراء المحدد إلى مربع النص
        self.procedures_combo.activated.connect(self.add_procedure_to_text)

        # زر الحفظ
        self.save_btn.clicked.connect(self.save_violation)

        # زر المسح
        self.clear_btn.clicked.connect(self.clear_fields)

    def load_data(self):
        """تحميل البيانات من قاعدة البيانات"""
        try:
            # تحميل المستويات
            cursor = self.db_manager.execute_query(
                "SELECT DISTINCT المستوى FROM البنية_التربوية ORDER BY المستوى",
                fetch_all=True
            )
            levels = [row[0] for row in cursor]
            self.level_combo.addItems(levels)

            # تحميل المواد
            cursor = self.db_manager.execute_query(
                "SELECT DISTINCT المادة FROM الأساتذة ORDER BY المادة",
                fetch_all=True
            )
            subjects = [row[0] for row in cursor]
            self.subject_combo.addItems(subjects)

            # تحميل المخالفات من جدول تعديل_المسميات حيث ID من 7 إلى 16
            violations_query = self.db_manager.execute_query(
                "SELECT العنوان FROM تعديل_المسميات WHERE ID BETWEEN 7 AND 16 ORDER BY ID",
                fetch_all=True
            )
            if violations_query:
                violations = [row[0] for row in violations_query]
                self.notes_combo.addItems(violations)

            # تحميل الإجراءات من جدول تعديل_المسميات حيث ID من 17 إلى 20
            procedures_query = self.db_manager.execute_query(
                "SELECT العنوان FROM تعديل_المسميات WHERE ID BETWEEN 17 AND 20 ORDER BY ID",
                fetch_all=True
            )
            if procedures_query:
                procedures = [row[0] for row in procedures_query]
                self.procedures_combo.addItems(procedures)

            # تحديث الأقسام بناءً على المستوى الأول
            self.update_sections()

            # تحديث الأساتذة بناءً على المادة الأولى
            self.update_teachers()

        except Exception as e:
            QMessageBox.critical(self, "خطأ في تحميل البيانات", f"حدث خطأ أثناء تحميل البيانات: {str(e)}")

    def update_sections(self):
        """تحديث قائمة الأقسام بناءً على المستوى المحدد"""
        try:
            level = self.level_combo.currentText()
            if not level:
                return

            sections = self.db_manager.execute_query(
                "SELECT DISTINCT القسم FROM البنية_التربوية WHERE المستوى = ? ORDER BY القسم",
                (level,),
                fetch_all=True
            )

            self.section_combo.clear()
            self.section_combo.addItems([row[0] for row in sections])
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء تحديث الأقسام: {str(e)}")

    def update_teachers(self):
        """تحديث قائمة الأساتذة بناءً على المادة المحددة"""
        try:
            subject = self.subject_combo.currentText()
            if not subject:
                return

            teachers = self.db_manager.execute_query(
                "SELECT DISTINCT اسم_الأستاذ FROM الأساتذة WHERE المادة = ? ORDER BY اسم_الأستاذ",
                (subject,),
                fetch_all=True
            )

            self.teacher_combo.clear()
            self.teacher_combo.addItems([row[0] for row in teachers])
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء تحديث الأساتذة: {str(e)}")

    def add_note_to_text(self):
        """إضافة الملاحظة المحددة إلى مربع النص"""
        selected_note = self.notes_combo.currentText()
        if selected_note:
            current_text = self.notes_text.toPlainText()
            if current_text:
                self.notes_text.setPlainText(f"{current_text}\n- {selected_note}")
            else:
                self.notes_text.setPlainText(f"- {selected_note}")

    def add_procedure_to_text(self):
        """إضافة الإجراء المحدد إلى مربع النص"""
        selected_procedure = self.procedures_combo.currentText()
        if selected_procedure:
            current_text = self.procedures_text.toPlainText()
            if current_text:
                self.procedures_text.setPlainText(f"{current_text}\n- {selected_procedure}")
            else:
                self.procedures_text.setPlainText(f"- {selected_procedure}")

    def search_student(self):
        """البحث عن تلميذ بناءً على الرمز أو الاسم"""
        try:
            code = self.code_edit.text().strip()
            name = self.name_edit.text().strip()

            if not code and not name:
                QMessageBox.warning(self, "تنبيه", "الرجاء إدخال الرمز أو الاسم للبحث")
                return

            # استخدام مدير قاعدة البيانات لاستعلام البيانات
            # استرجاع السنة الدراسية الحالية
            year_result = self.db_manager.get_current_school_year()
            current_school_year = year_result[0]

            query_params = []
            if code:
                query = """
                    SELECT s.الرمز, s.رت, s.الاسم, s.النسب, l.المستوى, l.القسم
                    FROM السجل_العام s
                    JOIN اللوائح l ON s.الرمز = l.الرمز
                    WHERE s.الرمز = ? AND l.السنة_الدراسية = ?
                """
                query_params = [code, current_school_year]
            else:
                query = """
                    SELECT s.الرمز, s.رت, s.الاسم, s.النسب, l.المستوى, l.القسم
                    FROM السجل_العام s
                    JOIN اللوائح l ON s.الرمز = l.الرمز
                    WHERE (s.الاسم || ' ' || s.النسب LIKE ?) AND l.السنة_الدراسية = ?
                """
                query_params = [f"%{name}%", current_school_year]

            result = self.db_manager.execute_query(query, query_params, fetch_one=True)

            if result:
                # تعبئة البيانات
                self.code_edit.setText(str(result[0]))
                self.rt_edit.setText(str(result[1]))
                self.name_edit.setText(f"{result[2]} {result[3]}")

                # تحديد المستوى والقسم في القوائم المنسدلة
                level_index = self.level_combo.findText(result[4])
                if level_index >= 0:
                    self.level_combo.setCurrentIndex(level_index)
                    # تحديث الأقسام ثم اختيار القسم
                    self.update_sections()
                    section_index = self.section_combo.findText(result[5])
                    if section_index >= 0:
                        self.section_combo.setCurrentIndex(section_index)
            else:
                QMessageBox.information(self, "نتيجة البحث", "لم يتم العثور على التلميذ في السنة الدراسية الحالية")

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء البحث: {str(e)}")

    def save_violation(self):
        """حفظ المخالفة في قاعدة البيانات مع تضمين السنة الدراسية والأسدس"""
        try:
            # التحقق من إدخال البيانات الضرورية
            if not self.code_edit.text() or not self.name_edit.text():
                show_custom_message(self, "الرجاء إدخال بيانات التلميذ", "تنبيه", "warning")
                return

            if not self.notes_text.toPlainText():
                show_custom_message(self, "الرجاء إدخال الملاحظات المرتكبة", "تنبيه", "warning")
                return

            # استرجاع السنة الدراسية والأسدس الحاليين
            year_result = self.db_manager.get_current_school_year()
            semester_result = self.db_manager.get_current_semester()

            # التحقق من وجود أخطاء أثناء الاسترجاع
            if year_result[1] or semester_result[1]:
                error_msg = year_result[1] or semester_result[1]
                show_custom_message(self, f"لم يتم العثور على السنة الدراسية أو الأسدس في إعدادات المؤسسة.\n{error_msg}", "خطأ", "error")
                print(f"Error retrieving settings: Year Error='{year_result[1]}', Semester Error='{semester_result[1]}'")
                return

            # استخراج القيم الفعلية
            current_school_year = year_result[0]
            current_semester = semester_result[0]

            # التحقق من القيم المستخرجة (للتصحيح)
            print(f"DEBUG: Retrieved School Year: {current_school_year}")
            print(f"DEBUG: Retrieved Semester: {current_semester}")

            # التحقق من صحة القيم قبل المتابعة
            if not current_school_year or not current_semester:
                 show_custom_message(self, "فشل استرداد السنة الدراسية أو الأسدس (قيمة فارغة).", "خطأ", "error")
                 print("ERROR: Retrieved school_year or semester is None or empty.")
                 return

            # جمع البيانات
            violation_data = {
                "date": self.date_edit.date().toString("yyyy-MM-dd"),
                "student_code": self.code_edit.text(),
                "student_name": self.name_edit.text(),
                "student_rt": self.rt_edit.text(),
                "level": self.level_combo.currentText(),
                "section": self.section_combo.currentText(),
                "subject": self.subject_combo.currentText(),
                "teacher": self.teacher_combo.currentText(),
                "notes": self.notes_text.toPlainText(),
                "procedures": self.procedures_text.toPlainText(),
                "guard_actions": self.guard_actions_text.toPlainText(),
                "school_year": current_school_year,
                "semester": current_semester
            }

            # Debug: طباعة البيانات قبل إرسالها
            print(f"DEBUG: Data being sent to save_violation: {violation_data}")

            # استخدام مدير قاعدة البيانات لحفظ المخالفة
            print("محاولة حفظ المخالفة...")
            success = self.db_manager.save_violation(violation_data)

            if success:
                print("تم حفظ المخالفة بنجاح!")
                # استخدام رسالة نجاح مخصصة مع تصميم جميل
                show_custom_message(
                    self,
                    "تم حفظ المخالفة بنجاح\n\nتم تسجيل المخالفة في قاعدة البيانات بنجاح للطالب: " + self.name_edit.text(),
                    "تم الحفظ بنجاح",
                    "success"
                )

                # مسح الحقول النصية
                self.notes_text.clear()
                self.procedures_text.clear()
                self.guard_actions_text.clear()
            else:
                print("فشل حفظ المخالفة!")
                show_custom_message(self, "حدث خطأ أثناء حفظ المخالفة", "خطأ", "error")

        except Exception as e:
            print(f"خطأ في حفظ المخالفة: {e}")
            import traceback
            traceback.print_exc()
            show_custom_message(
                self,
                f"حدث خطأ أثناء حفظ المخالفة:\n{str(e)}",
                "خطأ في الحفظ",
                "error"
            )

    def apply_shadow(self, widget):
        """إضافة تأثير الظل للعنصر"""
        shadow = QGraphicsDropShadowEffect()
        shadow.setBlurRadius(15)
        shadow.setXOffset(5)
        shadow.setYOffset(5)
        shadow.setColor(QColor(0, 0, 0, 150))
        widget.setGraphicsEffect(shadow)

    def set_student_info(self, code, name, id_num, level, class_name):
        """تعيين معلومات الطالب برمجياً مع التحقق من السنة الدراسية الحالية"""
        try:
            print(f"محاولة تعيين معلومات الطالب في نافذة المخالفات: {name}, المستوى: {level}, القسم: {class_name}")

            # استخدام مدير قاعدة البيانات للتحقق من تسجيل التلميذ
            is_enrolled, student_data = self.db_manager.check_student_enrollment(code)

            if not is_enrolled:
                current_school_year = self.db_manager.get_current_school_year()
                print(f"التلميذ {name} غير مسجل في السنة الدراسية الحالية: {current_school_year}")
                QMessageBox.warning(
                    self,
                    "تنبيه",
                    f"التلميذ {name} غير مسجل في السنة الدراسية الحالية: {current_school_year}"
                )
                return False

            # تعيين رمز التلميذ
            self.code_edit.setText(code)

            # تعيين رقم التلميذ الترتيبي
            self.rt_edit.setText(id_num)

            # تعيين اسم التلميذ
            self.name_edit.setText(name)

            # تحديد المستوى في القائمة المنسدلة
            level_index = self.level_combo.findText(level)
            if level_index >= 0:
                self.level_combo.setCurrentIndex(level_index)
                # تحديث الأقسام ثم اختيار القسم
                self.update_sections()
                section_index = self.section_combo.findText(class_name)
                if section_index >= 0:
                    self.section_combo.setCurrentIndex(section_index)

            print(f"تم تعيين معلومات الطالب بنجاح في نافذة المخالفات: {name}")
            return True

        except Exception as e:
            print(f"خطأ في تعيين معلومات الطالب في نافذة المخالفات: {e}")
            import traceback
            traceback.print_exc()
            return False

if __name__ == "__main__":
    app = QApplication(sys.argv)
    app.setStyle('Fusion')

    # تحديد حجم ثابت للنافذة عند استخدامها مستقلة
    window_width = 1200
    window_height = 620

    window = ViolationsWindow(window_width=window_width, window_height=window_height)
    window.show()
    sys.exit(app.exec_())



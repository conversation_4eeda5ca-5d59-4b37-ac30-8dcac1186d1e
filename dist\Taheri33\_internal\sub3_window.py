# -*- coding: utf-8 -*-

import sys
import os
import sqlite3
from PyQt5.QtWidgets import (QWidget, QComboBox, QTableWidget, QTableWidgetItem,
                             QApplication, QHeaderView, QPushButton, QFrame,
                             QGraphicsDropShadowEffect, QMessageBox, QLabel,
                             QVBoxLayout, QHBoxLayout, QSplitter, QSizePolicy,
                             QTextBrowser, QDialog) # أضفنا QTextBrowser و QDialog
from PyQt5.QtGui import QFont, QColor
from PyQt5.QtCore import Qt, QTimer


class Sub3Window(QWidget):
    def __init__(self, parent=None): # Added parent parameter
        super().__init__(parent)

        # استخدام QTimer لتأجيل تحميل البيانات لتحسين سرعة فتح النافذة
        self._setup_ui_only()
        QTimer.singleShot(100, self._load_initial_data)

    def _setup_ui_only(self):
        """إنشاء واجهة المستخدم فقط دون تحميل البيانات"""
        # إعداد النافذة الرئيسية
        self.setStyleSheet("background-color: white;")
        self.setLayoutDirection(Qt.RightToLeft)

        self.db_path = "data.db"

        # --- تعريف الخط المطلوب ---
        self.main_font = QFont("Calibri", 13)
        self.main_font.setBold(True)
        self.main_text_color = QColor('black')
        # ------------------------

        # تطبيق الخط العام
        self.setFont(self.main_font)

        # إنشاء التخطيط الرئيسي
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(10, 10, 10, 10)
        main_layout.setSpacing(10)

        # إنشاء وإضافة الشريط العلوي
        self._setup_top_controls(main_layout)

        # إنشاء وإضافة منطقة الجداول
        self._setup_tables_area(main_layout)

    def _load_initial_data(self):
        """تحميل البيانات الأولية بعد عرض الواجهة"""
        # ✅ تحميل البيانات الأولية
        self.load_years() # تحميل السنوات أولاً
        self.load_levels() # تحميل المستويات للسنة المختارة مبدئياً
        # جدول الأقسام يبقى فارغًا حتى يتم النقر على مستوى
        self.filter_assigned_sections() # تصفية الأقسام المسندة مبدئيًا للحراسة المبدئية

    def _setup_top_controls(self, parent_layout):
        """إنشاء وتنسيق شريط الأدوات العلوي"""
        top_frame = QFrame()
        top_frame.setFixedHeight(55)
        top_frame.setStyleSheet("""
            QFrame {
                background-color: #F8F8F8; border-radius: 8px;
                border: 1px solid #E0E0E0; padding: 5px;
            }
        """)
        self.apply_shadow(top_frame, blur=12, x=0, y=3, alpha=60)

        controls_layout = QHBoxLayout(top_frame)
        controls_layout.setContentsMargins(10, 0, 10, 0)
        controls_layout.setSpacing(10)

        # عناصر التحكم
        self.year_label = QLabel("السنة الدراسية:")
        self.year_label.setFont(self.main_font)
        self.year_combo = QComboBox()
        self.year_combo.setFont(self.main_font)
        self.year_combo.setMinimumWidth(180); self.year_combo.setFixedHeight(35)
        self.year_combo.setStyleSheet("QComboBox { padding-left: 10px; }")
        # سيتم ربط الإشارة بعد تحميل السنوات في load_years

        self.guard_label = QLabel("الحراسة العامة:")
        self.guard_label.setFont(self.main_font)
        self.guard_combo = QComboBox()
        self.guard_combo.setFont(self.main_font)
        self.guard_combo.addItems(["حراسة رقم 1", "حراسة رقم 2", "حراسة رقم 3", "حراسة رقم 4", "حراسة رقم 5"])
        self.guard_combo.setMinimumWidth(180); self.guard_combo.setFixedHeight(35)
        self.guard_combo.setStyleSheet("QComboBox { padding-left: 10px; }")
        self.guard_combo.currentTextChanged.connect(self.filter_assigned_sections)

        self.sort_levels_btn = QPushButton("تحديث ترتيب المستويات")
        self.sort_levels_btn.setFixedHeight(35)
        self.sort_levels_btn.clicked.connect(self.update_levels_order)
        self.sort_levels_btn.setStyleSheet("""
            QPushButton { background-color: #388e3c; color: white; border: none; border-radius: 4px;
                          padding: 0 15px; font-family: Calibri; font-size: 13pt; font-weight: bold; }
            QPushButton:hover { background-color: #2e7d32; }
        """)

        self.assign_btn = QPushButton("تعيين الكل لحراسة 1")
        self.assign_btn.setFixedHeight(35)
        self.assign_btn.clicked.connect(self.assign_all_to_guard1)
        self.assign_btn.setStyleSheet("""
            QPushButton { background-color: #1976d2; color: white; border: none; border-radius: 4px;
                          padding: 0 15px; font-family: Calibri; font-size: 13pt; font-weight: bold; }
            QPushButton:hover { background-color: #1565c0; }
        """)

        # إضافة زر التعليمات
        self.help_btn = QPushButton("تعليمات")
        self.help_btn.setFixedHeight(35)
        self.help_btn.clicked.connect(self.show_help)
        self.help_btn.setStyleSheet("""
            QPushButton { background-color: #ff9800; color: white; border: none; border-radius: 4px;
                          padding: 0 15px; font-family: Calibri; font-size: 13pt; font-weight: bold; }
            QPushButton:hover { background-color: #f57c00; }
        """)

        # إضافة العناصر للتخطيط (RTL)
        controls_layout.addWidget(self.year_label)
        controls_layout.addWidget(self.year_combo)
        controls_layout.addSpacing(20)
        controls_layout.addWidget(self.guard_label)
        controls_layout.addWidget(self.guard_combo)
        controls_layout.addStretch()
        controls_layout.addWidget(self.help_btn)  # إضافة زر التعليمات
        controls_layout.addWidget(self.sort_levels_btn)
        controls_layout.addWidget(self.assign_btn)

        parent_layout.addWidget(top_frame)

    def _setup_tables_area(self, parent_layout):
        """إنشاء وتنسيق منطقة الجداول"""
        # استخدام QSplitter بدلاً من QHBoxLayout للسماح بتغيير عرض الجداول يدويًا
        tables_splitter = QSplitter(Qt.Horizontal)
        tables_splitter.setChildrenCollapsible(False)  # منع انهيار الجداول عند السحب

        # إنشاء الجداول
        self.levels_table = self.create_table(["السنة الدراسية", "المستوى", "مجموع التلاميذ"])
        self.levels_table.setEditTriggers(QTableWidget.NoEditTriggers)
        self.levels_table.cellClicked.connect(self.filter_sections)
        self.levels_table.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)

        self.sections_table = self.create_table(["السنة الدراسية", "القسم", "مجموع التلاميذ"])
        self.sections_table.setEditTriggers(QTableWidget.NoEditTriggers)
        self.sections_table.cellClicked.connect(self.update_assigned_sections)
        self.sections_table.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)
        # تعيين العرض الأولي لأعمدة جدول الأقسام عند بدء التشغيل
        self.sections_table.setColumnWidth(0, 110) # السنة
        self.sections_table.setColumnWidth(1, 140) # القسم
        self.sections_table.setColumnWidth(2, 112) # المجموع

        # تعديل: إضافة عمود السنة الدراسية للجدول الثالث
        self.assigned_sections_table = self.create_table(["السنة الدراسية", "القسم", "مجموع التلاميذ"])
        self.assigned_sections_table.setEditTriggers(QTableWidget.NoEditTriggers)
        self.assigned_sections_table.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)

        # إضافة الجداول إلى QSplitter
        tables_splitter.addWidget(self.levels_table)
        tables_splitter.addWidget(self.sections_table)
        tables_splitter.addWidget(self.assigned_sections_table)

        # ضبط نسب العرض الأولية (يمكن للمستخدم تغييرها لاحقًا)
        # الجدول الثاني (الأقسام) أكبر من البقية (300)، والجدول الأول (المستويات) هو 100، والجدول الثالث (الأقسام المسندة) هو 200
        tables_splitter.setSizes([320, 220, 250])

        # تنسيق مقابض السحب لتكون أكثر وضوحًا
        tables_splitter.setHandleWidth(6)
        tables_splitter.setStyleSheet("""
            QSplitter::handle {
                background-color: #E0E0E0;
                border: 1px solid #CCCCCC;
                border-radius: 2px;
                margin: 1px;
            }
            QSplitter::handle:hover {
                background-color: #BBBBBB;
            }
        """)

        # إضافة QSplitter إلى التخطيط الرئيسي
        parent_layout.addWidget(tables_splitter)

    def apply_shadow(self, widget, blur=15, x=5, y=5, alpha=150):
        """🔹 إضافة تأثير الظل للعنصر"""
        shadow = QGraphicsDropShadowEffect()
        shadow.setBlurRadius(blur); shadow.setXOffset(x); shadow.setYOffset(y)
        shadow.setColor(QColor(0, 0, 0, alpha))
        widget.setGraphicsEffect(shadow)

    def create_table(self, headers):
        """🔹 إنشاء جدول مع ضبط خصائصه (مع تمكين تعديل عرض الأعمدة وارتفاع الصفوف)"""
        table = QTableWidget()
        table.setColumnCount(len(headers))
        table.setHorizontalHeaderLabels(headers)
        table.setFont(self.main_font) # تطبيق الخط الرئيسي للخلايا
        table.verticalHeader().setVisible(False)
        # --- السماح للمستخدم بتعديل ارتفاع الصفوف وعرض الأعمدة ---
        table.verticalHeader().setSectionResizeMode(QHeaderView.Interactive)
        header = table.horizontalHeader()
        header.setFixedHeight(30)
        header.setSectionResizeMode(QHeaderView.Interactive) # <-- السماح بتعديل عرض الأعمدة
        # --------------------------------------------------
        # الخط الخاص بالرأس
        header_font = QFont("Calibri", 13, QFont.Bold) # خط الرأس
        header.setFont(header_font)
        # تنسيق الجدول
        table.setStyleSheet("""
            QTableWidget {
                border: 1px solid #E0E0E0; border-radius: 5px;
                background-color: white; gridline-color: #F0F0F0;
            }
            QTableWidget::item { padding: 5px; color: black; }
            QTableWidget::item:selected { background-color: #D6EAF8; color: black; }
        """)
        # تنسيق رأس الجدول
        header.setStyleSheet("""
            QHeaderView::section {
                background-color: #1976d2; color: white;
                font-size: 13pt; font-weight: bold; /* استخدام font-size هنا */
                padding: 6px 10px; border-radius: 4px;
                border: 1px solid #1565C0; margin: 0px;
            }
        """)
        return table

    def set_table_item(self, table, row, col, value):
        """ إنشاء عنصر جدول مع تطبيق اللون الأسود """
        item = QTableWidgetItem(str(value))
        item.setTextAlignment(Qt.AlignCenter)
        item.setForeground(self.main_text_color) # تطبيق اللون الأسود
        table.setItem(row, col, item)

    def load_years(self):
        """🔹 تحميل السنوات الدراسية الفريدة"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            cursor.execute("SELECT DISTINCT السنة_الدراسية FROM البنية_التربوية ORDER BY السنة_الدراسية DESC")
            years = cursor.fetchall()
            self.year_combo.blockSignals(True)
            self.year_combo.clear()
            self.year_combo.addItems([year[0] for year in years if year[0]])
            self.year_combo.blockSignals(False)
            conn.close()
            # --- ربط الإشارة بعد التحميل وتعبئة القائمة ---
            # التأكد من إزالة أي ربط قديم إذا تم استدعاء الدالة أكثر من مرة (نادر)
            try: self.year_combo.currentTextChanged.disconnect(self.load_levels)
            except TypeError: pass # Ignorer l'erreur si le signal n'était pas connecté
            self.year_combo.currentTextChanged.connect(self.load_levels)
            # -------------------------------------------
        except Exception as e:
            print(f"خطأ في تحميل السنوات الدراسية: {e}")
            QMessageBox.warning(self, "خطأ", f"لا يمكن تحميل السنوات الدراسية:\n{e}")

    def load_levels(self):
        """🔹 تحميل بيانات المستويات مع ضبط عرض أولي للأعمدة"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            selected_year = self.year_combo.currentText()
            if not selected_year:
                self.levels_table.setRowCount(0); self.sections_table.setRowCount(0)
                conn.close(); return
            try: cursor.execute("ALTER TABLE البنية_التربوية ADD COLUMN ترتيب_المستويات INTEGER DEFAULT 99")
            except: pass
            # تعديل استعلام SQL لترتيب المستويات حسب ترتيب_المستويات أولاً
            query = """ SELECT السنة_الدراسية, المستوى, SUM(مجموع_التلاميذ) as مجموع FROM البنية_التربوية
                        WHERE السنة_الدراسية = ? GROUP BY السنة_الدراسية, المستوى
                        ORDER BY ترتيب_المستويات, المستوى """
            cursor.execute(query, (selected_year,))
            levels = cursor.fetchall()
            self.levels_table.setRowCount(0)
            self.levels_table.setRowCount(len(levels))
            for row, level in enumerate(levels):
                for col, value in enumerate(level):
                    self.set_table_item(self.levels_table, row, col, value)
                self.levels_table.setRowHeight(row, 30)
            # --- ضبط العرض الأولي للأعمدة (مع السماح للمستخدم بالتعديل) ---
            if self.levels_table.columnCount() >= 3:
                self.levels_table.setColumnWidth(0, 110)  # السنة الدراسية
                self.levels_table.setColumnWidth(1, 280)  # المستوى
                self.levels_table.setColumnWidth(2, 112)  # مجموع التلاميذ
            # ------------------------------------------------------------
            conn.close()
            self.sections_table.setRowCount(0)
        except Exception as e:
            print(f"خطأ في تحميل المستويات: {e}")
            QMessageBox.warning(self, "خطأ", f"لا يمكن تحميل المستويات:\n{e}")

    def section_sort_key(self, section_name):
        """🔹 دالة مساعدة لترتيب الأقسام رقميًا"""
        try:
            parts = section_name.split('-'); num_part = parts[-1] # أخذ الجزء الأخير
            if num_part.isdigit(): return int(num_part)
            if section_name.isdigit(): return int(section_name)
        except: pass
        return section_name # إرجاع النص للمقارنة إذا فشل التحويل

    def filter_sections(self, row, col):
        """🔹 عند الضغط على مستوى، تصفية جدول الأقسام مع ضبط العرض الأولي"""
        if row < 0 or col < 0 or row >= self.levels_table.rowCount(): return
        level_item = self.levels_table.item(row, 1); year_item = self.levels_table.item(row, 0)
        if not level_item or not year_item: return
        level = level_item.text(); year = year_item.text()
        try:
            conn = sqlite3.connect(self.db_path); cursor = conn.cursor()
            cursor.execute("SELECT ب.السنة_الدراسية, ب.القسم, ب.مجموع_التلاميذ FROM البنية_التربوية ب WHERE ب.المستوى = ? AND ب.السنة_الدراسية = ? ORDER BY ب.ترتيب_المستويات, ب.المستوى, ب.القسم", (level, year))
            sections = cursor.fetchall()
            sections = sorted(sections, key=lambda x: self.section_sort_key(x[1])) # الترتيب حسب الرقم في اسم القسم
            self.sections_table.setRowCount(0)
            self.sections_table.setRowCount(len(sections))
            for row_idx, section in enumerate(sections):
                for col_idx, value in enumerate(section):
                    self.set_table_item(self.sections_table, row_idx, col_idx, value)
                self.sections_table.setRowHeight(row_idx, 25)
            # --- ضبط العرض الأولي للأعمدة (مع السماح للمستخدم بالتعديل) ---
            if self.sections_table.columnCount() >= 3:
                self.sections_table.setColumnWidth(0, 110) # السنة
                self.sections_table.setColumnWidth(1, 140) # القسم
                self.sections_table.setColumnWidth(2, 112) # المجموع
            # ------------------------------------------------------------
            conn.close()
        except Exception as e:
            print(f"خطأ في تصفية الأقسام: {e}")
            QMessageBox.warning(self, "خطأ", f"لا يمكن تصفية الأقسام:\n{e}")

    def update_assigned_sections(self, row, col):
        """🔹 تحديث الأقسام المسندة عند النقر على قسم"""
        if row < 0 or col < 0 or row >= self.sections_table.rowCount(): return
        section_item = self.sections_table.item(row, 1); year_item = self.sections_table.item(row, 0)
        if not section_item or not year_item: return
        section = section_item.text(); year = year_item.text()
        guard_number = self.guard_combo.currentText()
        try:
            conn = sqlite3.connect(self.db_path); cursor = conn.cursor()
            cursor.execute("UPDATE البنية_التربوية SET الأقسام_المسندة = ? WHERE القسم = ? AND السنة_الدراسية = ?", (guard_number, section, year))
            conn.commit(); conn.close()
            self.filter_assigned_sections()
        except Exception as e:
            print(f"خطأ في تحديث الأقسام المسندة: {e}")
            QMessageBox.warning(self, "خطأ", f"لا يمكن تحديث القسم المسند:\ن{e}")

    def filter_assigned_sections(self):
        """🔹 تصفية جدول الأقسام المسندة مع ضبط العرض الأولي"""
        guard_number = self.guard_combo.currentText(); selected_year = self.year_combo.currentText()
        try:
            conn = sqlite3.connect(self.db_path); cursor = conn.cursor()
            params = [guard_number]
            # تعديل الاستعلام لإضافة السنة الدراسية
            query = "SELECT السنة_الدراسية, القسم, مجموع_التلاميذ FROM البنية_التربوية WHERE الأقسام_المسندة = ?"
            if selected_year: query += " AND السنة_الدراسية = ?"; params.append(selected_year)
            query += " ORDER BY ترتيب_المستويات, المستوى, القسم"
            cursor.execute(query, params)
            assigned_sections = cursor.fetchall()
            self.assigned_sections_table.setRowCount(0)
            self.assigned_sections_table.setRowCount(len(assigned_sections))
            for row, section in enumerate(assigned_sections):
                for col, value in enumerate(section):
                     self.set_table_item(self.assigned_sections_table, row, col, value)
                self.assigned_sections_table.setRowHeight(row, 25)
            # تعديل عرض الأعمدة لتناسب المحتوى الجديد
            if self.assigned_sections_table.columnCount() >= 3:
                 self.assigned_sections_table.setColumnWidth(0, 110)  # السنة الدراسية
                 self.assigned_sections_table.setColumnWidth(1, 120)  # القسم
                 self.assigned_sections_table.setColumnWidth(2, 100)  # المجموع
            self.assigned_sections_table.horizontalHeader().setStretchLastSection(True)
            # ------------------------------------------------------------
            conn.close()
        except Exception as e:
            print(f"خطأ في تصفية الأقسام المسندة: {e}")
            QMessageBox.warning(self, "خطأ", f"لا يمكن تصفية الأقسام المسندة:\ن{e}")

    def assign_all_to_guard1(self):
        """🔹 تعيين جميع الأقسام للسنة المختارة إلى حراسة رقم 1"""
        selected_year = self.year_combo.currentText()
        if not selected_year:
            QMessageBox.warning(self, "خطأ", "الرجاء اختيار سنة دراسية أولاً.")
            return
        
        # إنشاء نافذة تأكيد مخصصة
        confirm_dialog = QDialog(self)
        confirm_dialog.setWindowTitle("تعيين جميع الأقسام للحراسة")
        confirm_dialog.setFixedSize(550, 380)
        confirm_dialog.setLayoutDirection(Qt.RightToLeft)
        confirm_dialog.setStyleSheet("""
            QDialog { background-color: white; border: 1px solid #cccccc; border-radius: 5px; }
            QPushButton { height: 40px; border-radius: 4px; font-size: 13pt; font-weight: bold; }
        """)

        # التخطيط الرئيسي
        layout = QVBoxLayout(confirm_dialog)
        layout.setContentsMargins(15, 15, 15, 15)
        layout.setSpacing(10)

        # العنوان مع أيقونة
        title_layout = QHBoxLayout()
        
        # أيقونة العملية
        icon_label = QLabel()
        icon_label.setFixedSize(50, 50)
        icon_label.setText("🔄")  # رمز الدوران للإشارة إلى العملية
        icon_label.setStyleSheet("""
            QLabel { 
                background-color: #1976D2; 
                color: white; 
                font-size: 24pt; 
                border-radius: 5px; 
                qproperty-alignment: AlignCenter;
            }
        """)

        # عنوان النافذة - خط Calibri 14 أزرق غامق
        title_label = QLabel(f"تعيين جميع أقسام سنة {selected_year}")
        title_font = QFont("Calibri", 14)
        title_font.setBold(True)
        title_label.setFont(title_font)
        title_label.setStyleSheet("color: #0D47A1;")
        
        title_layout.addWidget(title_label)
        title_layout.addStretch()
        title_layout.addWidget(icon_label)
        
        layout.addLayout(title_layout)
        
        # إضافة فاصل أفقي
        separator = QFrame()
        separator.setFrameShape(QFrame.HLine)
        separator.setFrameShadow(QFrame.Sunken)
        separator.setStyleSheet("background-color: #e0e0e0;")
        layout.addWidget(separator)
        
        # نص الوصف - خط Calibri 13 أسود غامق
        desc_font = QFont("Calibri", 13)
        desc_font.setBold(True)
        desc_label = QLabel(f"سيتم تعيين جميع أقسام السنة الدراسية <b>{selected_year}</b> إلى الحراسة المحددة.")
        desc_label.setFont(desc_font)
        desc_label.setWordWrap(True)
        desc_label.setStyleSheet("color: black; margin: 5px 0; padding: 5px;")
        layout.addWidget(desc_label)
        
        # استخراج معلومات المستويات والأقسام من قاعدة البيانات
        levels_info = self.get_levels_sections_count(selected_year)
        
        # عرض ملخص للأقسام التي سيتم تعيينها
        summary_label = QLabel("ملخص المستويات والأقسام التي سيتم تعيينها:")
        summary_label.setFont(desc_font)
        summary_label.setStyleSheet("color: #333; margin-top: 10px;")
        layout.addWidget(summary_label)
        
        # جدول عرض المعلومات
        table_widget = QTableWidget()
        table_widget.setColumnCount(3)
        table_widget.setRowCount(len(levels_info) if levels_info else 0)
        
        # تعيين عناوين الأعمدة مع خط Calibri 14 أزرق غامق
        headers = ["المستوى", "عدد الأقسام", "مجموع التلاميذ"]
        table_widget.setHorizontalHeaderLabels(headers)
        header_font = QFont("Calibri", 14)
        header_font.setBold(True)
        
        # تطبيق الخط والألوان على رأس الجدول
        header = table_widget.horizontalHeader()
        for column in range(3):
            header_item = table_widget.horizontalHeaderItem(column)
            header_item.setFont(header_font)
            header_item.setForeground(QColor("#0D47A1"))  # أزرق غامق
        
        # تنسيق رأس الجدول
        header.setStyleSheet("""
            QHeaderView::section { 
                background-color: #E3F2FD; 
                color: #0D47A1; 
                font-weight: bold; 
                padding: 6px; 
                border: 1px solid #BBDEFB;
            }
        """)
        
        # إضافة البيانات للجدول
        cell_font = QFont("Calibri", 13)
        cell_font.setBold(True)
        
        total_sections = 0
        total_students = 0
        
        if levels_info:
            for row, (level, sections_count, students_count) in enumerate(levels_info):
                level_item = QTableWidgetItem(level)
                level_item.setFont(cell_font)
                level_item.setTextAlignment(Qt.AlignCenter)
                
                sections_item = QTableWidgetItem(str(sections_count))
                sections_item.setFont(cell_font)
                sections_item.setTextAlignment(Qt.AlignCenter)
                
                students_item = QTableWidgetItem(str(students_count))
                students_item.setFont(cell_font)
                students_item.setTextAlignment(Qt.AlignCenter)
                
                table_widget.setItem(row, 0, level_item)
                table_widget.setItem(row, 1, sections_item)
                table_widget.setItem(row, 2, students_item)
                
                total_sections += sections_count
                total_students += students_count
        
        # تعديل حجم الجدول وعرض الأعمدة
        table_widget.horizontalHeader().setSectionResizeMode(0, QHeaderView.Stretch)
        table_widget.horizontalHeader().setSectionResizeMode(1, QHeaderView.Fixed)
        table_widget.horizontalHeader().setSectionResizeMode(2, QHeaderView.Fixed)
        table_widget.horizontalHeader().resizeSection(1, 100)
        table_widget.horizontalHeader().resizeSection(2, 120)
        table_widget.setStyleSheet("""
            QTableWidget { 
                border: 1px solid #d0d0d0;
                gridline-color: #e0e0e0;
            }
            QTableWidget::item {
                padding: 5px;
            }
            QTableWidget::item:selected {
                background-color: #E3F2FD;
                color: black;
            }
        """)
        
        table_widget.setMinimumHeight(150)
        layout.addWidget(table_widget)
        
        # إضافة معلومات المجموع
        total_label = QLabel(f"المجموع الكلي: <b>{total_sections}</b> قسم، <b>{total_students}</b> تلميذ")
        total_label.setFont(desc_font)
        total_label.setStyleSheet("color: #333; margin-top: 5px; padding: 5px; background-color: #F5F5F5; border-radius: 3px;")
        layout.addWidget(total_label)
        
        # القائمة المنسدلة لاختيار الحراسة
        guard_layout = QHBoxLayout()
        
        guard_label = QLabel("اختر الحراسة:")
        guard_label.setFont(desc_font)
        
        guard_combo = QComboBox()
        guard_combo.setFont(desc_font)
        guard_combo.addItems(["حراسة رقم 1", "حراسة رقم 2", "حراسة رقم 3", "حراسة رقم 4", "حراسة رقم 5"])
        guard_combo.setMinimumWidth(200)
        guard_combo.setMinimumHeight(35)
        guard_combo.setStyleSheet("""
            QComboBox {
                border: 1px solid #BBDEFB;
                border-radius: 4px;
                padding: 5px 10px;
                background-color: white;
            }
            QComboBox:hover {
                border: 1px solid #2196F3;
            }
        """)
        
        guard_layout.addWidget(guard_label)
        guard_layout.addWidget(guard_combo)
        layout.addLayout(guard_layout)
        
        # أزرار التأكيد والإلغاء
        buttons_layout = QHBoxLayout()
        
        cancel_btn = QPushButton("إلغاء")
        cancel_btn.setStyleSheet("""
            QPushButton { 
                background-color: #e74c3c; 
                color: white; 
                border: none; 
                min-width: 120px;
            }
            QPushButton:hover { 
                background-color: #c0392b; 
            }
        """)
        cancel_btn.clicked.connect(confirm_dialog.reject)
        
        confirm_btn = QPushButton("تأكيد")
        confirm_btn.setStyleSheet("""
            QPushButton { 
                background-color: #2ecc71; 
                color: white; 
                border: none; 
                min-width: 120px;
            }
            QPushButton:hover { 
                background-color: #27ae60; 
            }
        """)
        confirm_btn.clicked.connect(confirm_dialog.accept)
        
        buttons_layout.addWidget(cancel_btn)
        buttons_layout.addWidget(confirm_btn)
        layout.addLayout(buttons_layout)
        
        # عرض النافذة وتنفيذ الإجراء عند التأكيد
        if confirm_dialog.exec_() != QDialog.Accepted:
            return
        
        # استخراج الحراسة المختارة
        selected_guard = guard_combo.currentText()
        
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            cursor.execute("UPDATE البنية_التربوية SET الأقسام_المسندة = ? WHERE السنة_الدراسية = ?", 
                          (selected_guard, selected_year))
            updated_count = cursor.rowcount
            conn.commit()
            conn.close()
            
            # تحديث القائمة المنسدلة للحراسة في النافذة الرئيسية
            index = self.guard_combo.findText(selected_guard)
            if index >= 0:
                self.guard_combo.setCurrentIndex(index)
            
            # رسالة نجاح مميزة
            success_dialog = QDialog(self)
            success_dialog.setWindowTitle("نجاح العملية")
            success_dialog.setFixedSize(450, 220)
            success_dialog.setLayoutDirection(Qt.RightToLeft)
            success_dialog.setStyleSheet("""
                QDialog { background-color: white; border: 1px solid #cccccc; border-radius: 5px; }
            """)

            success_layout = QVBoxLayout(success_dialog)
            success_layout.setContentsMargins(15, 15, 15, 15)
            
            # عنوان مع أيقونة نجاح
            success_title_layout = QHBoxLayout()
            
            success_icon = QLabel("✓")
            success_icon.setFixedSize(40, 40)
            success_icon.setStyleSheet("""
                QLabel { 
                    background-color: #2ecc71; 
                    color: white; 
                    font-size: 20pt; 
                    border-radius: 20px; 
                    qproperty-alignment: AlignCenter;
                }
            """)
            
            # عنوان النجاح بخط Calibri 14 أزرق غامق
            success_title = QLabel("تم تعيين الأقسام بنجاح")
            title_font = QFont("Calibri", 14)
            title_font.setBold(True)
            success_title.setFont(title_font)
            success_title.setStyleSheet("color: #0D47A1;")
            
            success_title_layout.addWidget(success_title)
            success_title_layout.addStretch()
            success_title_layout.addWidget(success_icon)
            
            success_layout.addLayout(success_title_layout)
            
            # رسالة النجاح بخط Calibri 13 أسود غامق
            details_font = QFont("Calibri", 13)
            details_font.setBold(True)
            
            success_msg = QLabel(f"تم تعيين <b>{updated_count}</b> قسم من سنة <b>{selected_year}</b> إلى <b>{selected_guard}</b> بنجاح.")
            success_msg.setFont(details_font)
            success_msg.setStyleSheet("color: black; margin: 15px 0;")
            success_msg.setWordWrap(True)
            success_layout.addWidget(success_msg)
            
            # معلومات إضافية
            additional_info = QLabel(f"تم تحديث قائمة الأقسام المسندة. يمكنك الآن مشاهدة الأقسام المسندة في الجدول.")
            additional_info.setFont(details_font)
            additional_info.setStyleSheet("color: #555;")
            additional_info.setWordWrap(True)
            success_layout.addWidget(additional_info)
            
            # زر إغلاق
            close_btn = QPushButton("إغلاق")
            close_btn.setFont(details_font)
            close_btn.setStyleSheet("""
                QPushButton { 
                    background-color: #1976d2; 
                    color: white; 
                    border: none; 
                    min-width: 120px;
                    min-height: 40px;
                    border-radius: 4px;
                }
                QPushButton:hover { 
                    background-color: #1565c0; 
                }
            """)
            close_btn.clicked.connect(success_dialog.accept)
            
            close_layout = QHBoxLayout()
            close_layout.addStretch()
            close_layout.addWidget(close_btn)
            
            success_layout.addLayout(close_layout)
            success_dialog.exec_()
            
        except Exception as e:
            print(f"خطأ في تعيين الكل للحراسة: {e}")
            QMessageBox.critical(self, "خطأ في العملية", f"حدث خطأ أثناء تعيين الأقسام:\n{str(e)}")
    
    def get_levels_sections_count(self, year):
        """استخراج معلومات المستويات وعدد الأقسام ومجموع التلاميذ للسنة المحددة"""
        levels_info = []
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # استخراج جميع المستويات مع عدد الأقسام وعدد التلاميذ
            query = """
                SELECT المستوى, 
                       COUNT(*) as عدد_الأقسام, 
                       SUM(مجموع_التلاميذ) as مجموع_التلاميذ
                FROM البنية_التربوية
                WHERE السنة_الدراسية = ?
                GROUP BY المستوى
                ORDER BY ترتيب_المستويات, المستوى
            """
            cursor.execute(query, (year,))
            levels_info = cursor.fetchall()
            conn.close()
            
            return levels_info
        except Exception as e:
            print(f"خطأ في استخراج معلومات المستويات: {e}")
            return []

    def update_levels_order(self):
        """🔹 تحديث ترتيب_المستويات في قاعدة البيانات"""
        # إنشاء نافذة تأكيد مخصصة
        confirm_dialog = QDialog(self)
        confirm_dialog.setWindowTitle("تأكيد تحديث ترتيب المستويات")
        confirm_dialog.setFixedSize(550, 400)
        confirm_dialog.setLayoutDirection(Qt.RightToLeft)
        confirm_dialog.setStyleSheet("""
            QDialog { background-color: white; border: 1px solid #cccccc; border-radius: 5px; }
            QPushButton { height: 40px; border-radius: 4px; font-size: 13pt; font-weight: bold; }
        """)

        # التخطيط الرئيسي
        layout = QVBoxLayout(confirm_dialog)
        layout.setContentsMargins(15, 15, 15, 15)
        layout.setSpacing(10)

        # العنوان مع أيقونة
        title_layout = QHBoxLayout()
        
        # صورة الأيقونة
        icon_label = QLabel()
        icon_label.setFixedSize(50, 50)
        icon_label.setText("🗃️")
        icon_label.setStyleSheet("""
            QLabel { 
                background-color: #0D47A1; 
                color: white; 
                font-size: 24pt; 
                border-radius: 5px; 
                qproperty-alignment: AlignCenter;
            }
        """)

        # عنوان النافذة - خط Calibri 14 أزرق غامق
        title_label = QLabel("تحديث ترتيب المستويات")
        title_font = QFont("Calibri", 14)
        title_font.setBold(True)
        title_label.setFont(title_font)
        title_label.setStyleSheet("color: #0D47A1;")
        
        title_layout.addWidget(title_label)
        title_layout.addStretch()
        title_layout.addWidget(icon_label)
        
        layout.addLayout(title_layout)
        
        # إضافة فاصل أفقي
        separator = QFrame()
        separator.setFrameShape(QFrame.HLine)
        separator.setFrameShadow(QFrame.Sunken)
        separator.setStyleSheet("background-color: #e0e0e0;")
        layout.addWidget(separator)
        
        # نص الوصف - خط Calibri 13 أسود غامق
        desc_font = QFont("Calibri", 13)
        desc_font.setBold(True)
        desc_label = QLabel("يمكنك تعديل قيم الترتيب لكل مستوى دراسي في الجدول أدناه:")
        desc_label.setFont(desc_font)
        desc_label.setWordWrap(True)
        desc_label.setStyleSheet("color: black; margin: 5px 0;")
        layout.addWidget(desc_label)
        
        # استخراج الترتيب الحالي من قاعدة البيانات
        levels_data = self.get_current_levels_order()
        
        # جدول المعلومات القابل للتحرير
        table_widget = QTableWidget()
        table_widget.setColumnCount(2)
        table_widget.setRowCount(len(levels_data) if levels_data else 3)
        
        # تعيين عناوين الأعمدة مع خط Calibri 14 أزرق غامق
        headers = ["المستوى", "الترتيب"]
        table_widget.setHorizontalHeaderLabels(headers)
        header_font = QFont("Calibri", 14)
        header_font.setBold(True)
        
        # تطبيق الخط والألوان على رأس الجدول
        header = table_widget.horizontalHeader()
        for column in range(2):
            header_item = table_widget.horizontalHeaderItem(column)
            header_item.setFont(header_font)
            header_item.setForeground(QColor("#0D47A1"))  # أزرق غامق
        
        # تنسيق رأس الجدول
        header.setStyleSheet("""
            QHeaderView::section { 
                background-color: #E3F2FD; 
                color: #0D47A1; 
                font-weight: bold; 
                padding: 6px; 
                border: 1px solid #BBDEFB;
            }
        """)
        
        # إضافة البيانات للجدول
        if not levels_data:
            # القيم الافتراضية إذا لم تكن هناك بيانات
            levels_data = [
                ["جذع مشترك", 1],
                ["أولى بكالوريا", 2],
                ["ثانية بكالوريا", 3]
            ]
        
        # تعيين قيم خلايا الجدول مع تطبيق الخط Calibri 13 لمحتوى الجدول
        cell_font = QFont("Calibri", 13)
        cell_font.setBold(True)
        
        for row, (level, order) in enumerate(levels_data):
            # عمود المستوى (للقراءة فقط)
            level_item = QTableWidgetItem(level)
            level_item.setFlags(level_item.flags() & ~Qt.ItemIsEditable)  # جعله غير قابل للتحرير
            level_item.setFont(cell_font)
            level_item.setTextAlignment(Qt.AlignCenter)
            
            # عمود الترتيب (قابل للتحرير)
            order_item = QTableWidgetItem(str(order))
            order_item.setFont(cell_font)
            order_item.setTextAlignment(Qt.AlignCenter)
            
            table_widget.setItem(row, 0, level_item)
            table_widget.setItem(row, 1, order_item)
            table_widget.setRowHeight(row, 30)  # ارتفاع الصف
        
        # تعديل حجم الجدول وعرض الأعمدة
        table_widget.horizontalHeader().setSectionResizeMode(0, QHeaderView.Stretch)
        table_widget.horizontalHeader().setSectionResizeMode(1, QHeaderView.Fixed)
        table_widget.horizontalHeader().resizeSection(1, 100)  # عرض عمود الترتيب
        table_widget.setStyleSheet("""
            QTableWidget { 
                border: 1px solid #d0d0d0;
                gridline-color: #e0e0e0;
            }
            QTableWidget::item {
                padding: 5px;
            }
            QTableWidget::item:selected {
                background-color: #E3F2FD;
                color: black;
            }
        """)
        
        table_widget.setMinimumHeight(200)
        layout.addWidget(table_widget)
        
        # إضافة نص توضيحي إضافي
        note_label = QLabel("ملاحظة: القيم الأصغر (1, 2, 3) تظهر أولاً في الترتيب، ثم القيم الأكبر.")
        note_font = QFont("Calibri", 13)
        note_font.setBold(True)
        note_label.setFont(note_font)
        note_label.setStyleSheet("color: #555;")
        note_label.setWordWrap(True)
        layout.addWidget(note_label)
        
        # أزرار التأكيد والإلغاء
        buttons_layout = QHBoxLayout()
        
        cancel_btn = QPushButton("إلغاء")
        cancel_btn.setStyleSheet("""
            QPushButton { 
                background-color: #e74c3c; 
                color: white; 
                border: none; 
                min-width: 120px;
            }
            QPushButton:hover { 
                background-color: #c0392b; 
            }
        """)
        cancel_btn.clicked.connect(confirm_dialog.reject)
        
        confirm_btn = QPushButton("تأكيد")
        confirm_btn.setStyleSheet("""
            QPushButton { 
                background-color: #2ecc71; 
                color: white; 
                border: none; 
                min-width: 120px;
            }
            QPushButton:hover { 
                background-color: #27ae60; 
            }
        """)
        confirm_btn.clicked.connect(confirm_dialog.accept)
        
        buttons_layout.addWidget(cancel_btn)
        buttons_layout.addWidget(confirm_btn)
        layout.addLayout(buttons_layout)
        
        # عرض النافذة وتنفيذ الإجراء عند التأكيد
        if confirm_dialog.exec_() != QDialog.Accepted:
            return
            
        try:
            # استرجاع القيم التي أدخلها المستخدم
            updated_levels = []
            for row in range(table_widget.rowCount()):
                level_item = table_widget.item(row, 0)
                order_item = table_widget.item(row, 1)
                
                if level_item and order_item:
                    level = level_item.text()
                    order_text = order_item.text().strip()
                    
                    # التحقق من أن القيمة المدخلة هي رقم صحيح
                    try:
                        order = int(order_text)
                        updated_levels.append((level, order))
                    except ValueError:
                        QMessageBox.warning(self, "خطأ في القيمة", f"القيمة '{order_text}' للمستوى '{level}' ليست رقمًا صحيحًا.")
                        return
            
            # تطبيق التحديثات على قاعدة البيانات
            conn = sqlite3.connect(self.db_path); cursor = conn.cursor()
            
            # التأكد من وجود عمود ترتيب_المستويات
            try: cursor.execute("ALTER TABLE البنية_التربوية ADD COLUMN ترتيب_المستويات INTEGER DEFAULT 99")
            except: pass
            
            # تعيين القيمة الافتراضية لجميع المستويات
            cursor.execute("UPDATE البنية_التربوية SET ترتيب_المستويات = 99")
            
            # تحديث قيم الترتيب للمستويات التي تم تعديلها
            for level, order in updated_levels:
                cursor.execute("UPDATE البنية_التربوية SET ترتيب_المستويات = ? WHERE المستوى LIKE ?", 
                               (order, f"%{level}%"))
            
            conn.commit(); conn.close()
            self.load_levels()  # إعادة تحميل المستويات لعكس الترتيب الجديد
            
            # رسالة نجاح
            success_dialog = QDialog(self)
            success_dialog.setWindowTitle("نجاح العملية")
            success_dialog.setFixedSize(400, 200)
            success_dialog.setLayoutDirection(Qt.RightToLeft)
            success_dialog.setStyleSheet("""
                QDialog { background-color: white; border: 1px solid #cccccc; border-radius: 5px; }
            """)

            success_layout = QVBoxLayout(success_dialog)
            success_layout.setContentsMargins(15, 15, 15, 15)
            
            # عنوان مع أيقونة نجاح
            success_title_layout = QHBoxLayout()
            
            success_icon = QLabel("✓")
            success_icon.setFixedSize(40, 40)
            success_icon.setStyleSheet("""
                QLabel { 
                    background-color: #2ecc71; 
                    color: white; 
                    font-size: 20pt; 
                    border-radius: 20px; 
                    qproperty-alignment: AlignCenter;
                }
            """)
            
            # خط العنوان Calibri 14 أزرق غامق
            success_title = QLabel("تم تحديث ترتيب المستويات بنجاح")
            title_font = QFont("Calibri", 14)
            title_font.setBold(True)
            success_title.setFont(title_font)
            success_title.setStyleSheet("color: #0D47A1;")
            
            success_title_layout.addWidget(success_title)
            success_title_layout.addStretch()
            success_title_layout.addWidget(success_icon)
            
            success_layout.addLayout(success_title_layout)
            
            # رسالة النجاح
            success_msg = QLabel("تم تحديث ترتيب المستويات في قاعدة البيانات وإعادة تحميل البيانات بنجاح.")
            details_font = QFont("Calibri", 13)
            details_font.setBold(True)
            success_msg.setFont(details_font)
            success_msg.setStyleSheet("color: black;")
            success_msg.setWordWrap(True)
            success_layout.addWidget(success_msg)
            
            # زر إغلاق
            close_btn = QPushButton("إغلاق")
            close_btn.setFont(details_font)
            close_btn.setStyleSheet("""
                QPushButton { 
                    background-color: #1976d2; 
                    color: white; 
                    border: none; 
                    min-width: 120px;
                    min-height: 40px;
                    border-radius: 4px;
                }
                QPushButton:hover { 
                    background-color: #1565c0; 
                }
            """)
            close_btn.clicked.connect(success_dialog.accept)
            
            close_layout = QHBoxLayout()
            close_layout.addStretch()
            close_layout.addWidget(close_btn)
            
            success_layout.addLayout(close_layout)
            success_dialog.exec_()
            
        except Exception as e:
            print(f"خطأ في تحديث ترتيب المستويات: {e}")
            QMessageBox.critical(self, "خطأ في العملية", f"حدث خطأ أثناء تحديث ترتيب المستويات:\n{str(e)}")

    def get_current_levels_order(self):
        """استرجاع ترتيب المستويات الحالي من قاعدة البيانات"""
        levels_data = []
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # التأكد من وجود العمود ترتيب_المستويات
            try:
                cursor.execute("ALTER TABLE البنية_التربوية ADD COLUMN ترتيب_المستويات INTEGER DEFAULT 99")
                conn.commit()
            except:
                pass
            
            # جلب المستويات الفريدة مع قيمة الترتيب الخاصة بكل منها
            query = """
                SELECT المستوى, ترتيب_المستويات 
                FROM (
                    SELECT المستوى, ترتيب_المستويات, COUNT(*) as ظهور
                    FROM البنية_التربوية
                    GROUP BY المستوى
                )
                ORDER BY ترتيب_المستويات, المستوى
            """
            cursor.execute(query)
            results = cursor.fetchall()
            
            # تحويل النتائج إلى قائمة
            levels_data = [(level, order) for level, order in results]
            conn.close()
            return levels_data
        except Exception as e:
            print(f"خطأ في استرجاع ترتيب المستويات: {e}")
            return [
                ["جذع مشترك", 1],
                ["أولى بكالوريا", 2],
                ["ثانية بكالوريا", 3],
                ["الأولى", 10],
                ["الثانية", 11],
                ["الثالثة", 12]
            ]  # قيم افتراضية

    def show_help(self):
        """عرض نافذة التعليمات للمستخدم"""
        help_dialog = QDialog(self)
        help_dialog.setWindowTitle("تعليمات استخدام النافذة")
        help_dialog.setMinimumSize(900, 700)
        help_dialog.setLayoutDirection(Qt.RightToLeft)

        # تطبيق التصميم على النافذة
        help_dialog.setStyleSheet("""
            QDialog { background-color: white; }
            QTextBrowser {
                border: 1px solid #E0E0E0;
                border-radius: 5px;
                padding: 10px;
                font-family: Calibri;
                font-size: 14pt;
                line-height: 1.5;
            }
        """)

        layout = QVBoxLayout(help_dialog)

        text_browser = QTextBrowser()
        text_browser.setOpenExternalLinks(True)

        # محتوى التعليمات
        help_text = """
        <h2 style="color: #1976d2; text-align: center;">تعليمات استخدام نافذة إدارة الأقسام والحراسة</h2>

        <h3 style="color: #2196f3;">نظرة عامة</h3>
        <p>هذه النافذة تساعدك على إدارة الأقسام وتعيينها للحراسات المختلفة بطريقة سهلة ومنظمة.</p>

        <h3 style="color: #2196f3;">مكونات النافذة</h3>
        <ul>
            <li><b>القائمة العلوية:</b> تحتوي على أدوات التحكم الرئيسية.</li>
            <li><b>جدول المستويات:</b> يعرض المستويات الدراسية المتوفرة.</li>
            <li><b>جدول الأقسام:</b> يعرض أقسام المستوى المحدد.</li>
            <li><b>جدول الأقسام المسندة:</b> يعرض الأقسام المسندة للحراسة المحددة.</li>
        </ul>

        <h3 style="color: #2196f3;">خطوات الاستخدام</h3>
        <ol>
            <li><b>اختيار السنة الدراسية:</b> استخدم القائمة المنسدلة "السنة الدراسية" لاختيار السنة المطلوبة.</li>
            <li><b>تصفح المستويات:</b> ستظهر المستويات الدراسية في الجدول الأول.</li>
            <li><b>عرض أقسام المستوى:</b> انقر على أي مستوى لعرض أقسامه في الجدول الأوسط.</li>
            <li><b>تعيين قسم لحراسة:</b> اختر الحراسة المطلوبة من قائمة "الحراسة العامة" ثم انقر على القسم المراد تعيينه.</li>
            <li><b>عرض الأقسام المسندة:</b> سيتم عرض الأقسام المسندة للحراسة المحددة في الجدول الثالث.</li>
        </ol>

        <h3 style="color: #2196f3;">ميزات إضافية</h3>
        <ul>
            <li><b>تعيين الكل لحراسة 1:</b> يقوم بتعيين جميع أقسام السنة الدراسية المحددة إلى حراسة رقم 1.</li>
            <li><b>تحديث ترتيب المستويات:</b> يقوم بتحديث ترتيب عرض المستويات بطريقة منطقية (جذع مشترك، أولى باك، ثانية باك).</li>
            <li><b>تغيير حجم الجداول:</b> يمكنك سحب الفواصل بين الجداول لتغيير حجمها.</li>
            <li><b>تغيير عرض الأعمدة:</b> يمكنك سحب حدود عناوين الأعمدة لتغيير عرضها.</li>
        </ul>

        <h3 style="color: #2196f3;">ملاحظات هامة</h3>
        <ul>
            <li>للتراجع عن تعيين قسم، يمكنك النقر عليه مجدداً واختيار حراسة أخرى.</li>
            <li>يرجى التأكد من اختيار السنة الدراسية قبل محاولة استخدام الأزرار.</li>
            <li>يمكنك تصفية الأقسام المسندة حسب الحراسة والسنة الدراسية.</li>
        </ul>
        """

        text_browser.setHtml(help_text)
        layout.addWidget(text_browser)

        # زر إغلاق
        close_btn = QPushButton("إغلاق")
        close_btn.setFixedHeight(35)
        close_btn.setMinimumWidth(120)
        close_btn.clicked.connect(help_dialog.accept)
        close_btn.setStyleSheet("""
            QPushButton { background-color: #1976d2; color: white; border: none; border-radius: 4px;
                          padding: 0 15px; font-family: Calibri; font-size: 13pt; font-weight: bold; }
            QPushButton:hover { background-color: #1565c0; }
        """)

        btn_layout = QHBoxLayout()
        btn_layout.addStretch()
        btn_layout.addWidget(close_btn)
        layout.addLayout(btn_layout)

        help_dialog.setLayout(layout)
        help_dialog.exec_()


if __name__ == '__main__':
    app = QApplication(sys.argv)
    app.setLayoutDirection(Qt.RightToLeft)
    window = Sub3Window()
    window.show()
    sys.exit(app.exec_())

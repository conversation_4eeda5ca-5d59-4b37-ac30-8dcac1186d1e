import sys
import os
from PyQt5.QtWidgets import (QApplication, QMainWindow, QWidget, QVBoxLayout,
                            QHBoxLayout, QLabel, QPushButton, QTableWidget,
                            QTableWidgetItem, QHeaderView, QMessageBox, QStyledItemDelegate,
                            QStyle, QStyleOptionButton, QDialog, QTextBrowser)
from PyQt5.QtGui import QFont, QColor, QClipboard, QIcon, QPixmap
from PyQt5.QtCore import Qt, QSize, QDate
from PyQt5.QtSql import QSqlQuery
import sqlite3
from print9 import print_certificates_requests

# استيراد وحدة الرسائل المميزة من sub100_window
try:
    from sub100_window import ConfirmationDialogs
    # فحص وجود الدوال المطلوبة للتأكد من توافقها
    required_methods = ['show_custom_success_message', 'show_custom_warning_message', 'show_custom_error_message', 'show_custom_confirmation_dialog']
    CUSTOM_DIALOGS_IMPORTED = all(hasattr(ConfirmationDialogs, method) for method in required_methods)
    if not CUSTOM_DIALOGS_IMPORTED:
        print("تم استيراد الفئة ConfirmationDialogs ولكن بعض الدوال المطلوبة غير متوفرة")
        # إضافة الدوال المفقودة محليًا للفئة إذا لم تكن موجودة
        if not hasattr(ConfirmationDialogs, "show_custom_success_message"):
            # إضافة دالة محلية بديلة للنجاح
            setattr(ConfirmationDialogs, "show_custom_success_message",
                    staticmethod(lambda parent, message, title="نجاح": QMessageBox.information(parent, title, message)))
        if not hasattr(ConfirmationDialogs, "show_custom_warning_message"):
            # إضافة دالة محلية بديلة للتحذير
            setattr(ConfirmationDialogs, "show_custom_warning_message",
                    staticmethod(lambda parent, message, title="تنبيه": QMessageBox.warning(parent, title, message)))
        if not hasattr(ConfirmationDialogs, "show_custom_error_message"):
            # إضافة دالة محلية بديلة للخطأ
            setattr(ConfirmationDialogs, "show_custom_error_message",
                    staticmethod(lambda parent, message, title="خطأ": QMessageBox.critical(parent, title, message)))
        if not hasattr(ConfirmationDialogs, "show_custom_confirmation_dialog"):
            # إضافة دالة محلية بديلة للتأكيد
            setattr(ConfirmationDialogs, "show_custom_confirmation_dialog",
                    staticmethod(lambda parent, message, title="تأكيد":
                                QMessageBox.question(parent, title, message, QMessageBox.Yes | QMessageBox.No) == QMessageBox.Yes))
        # تعيين المتغير إلى True بعد إضافة الدوال المفقودة
        CUSTOM_DIALOGS_IMPORTED = True
except ImportError:
    CUSTOM_DIALOGS_IMPORTED = False
    print("تعذر استيراد وحدة الرسائل المميزة، سيتم استخدام الرسائل القياسية")
    # إنشاء فئة محلية كبديل
    class ConfirmationDialogs:
        @staticmethod
        def show_custom_success_message(parent, message, title="نجاح"):
            return QMessageBox.information(parent, title, message)

        @staticmethod
        def show_custom_warning_message(parent, message, title="تنبيه"):
            return QMessageBox.warning(parent, title, message)

        @staticmethod
        def show_custom_error_message(parent, message, title="خطأ"):
            return QMessageBox.critical(parent, title, message)

        @staticmethod
        def show_custom_confirmation_dialog(parent, message, title="تأكيد"):
            return QMessageBox.question(parent, title, message, QMessageBox.Yes | QMessageBox.No) == QMessageBox.Yes

# استيراد دالة الطباعة الجديدة
try:
    # محاولة استيراد دالة الطباعة من arabic_pdf_report.py (الدالة الأفضل مع دعم النصوص العربية)
    from arabic_pdf_report import print_certificates_requests as arabic_print_function
    ARABIC_PDF_AVAILABLE = True
    print("تم استيراد دالة الطباعة من arabic_pdf_report.py بنجاح")
except ImportError:
    # الاعتماد على الدالة الأصلية من print9.py إذا لم تتوفر الدالة الجديدة
    from print9 import print_certificates_requests
    ARABIC_PDF_AVAILABLE = False
    print("تم استيراد دالة الطباعة من print9.py")

class CheckBoxDelegate(QStyledItemDelegate):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.checked_items = set()
        # تعيين مؤشر اليد عند المرور فوق مربعات الاختيار
        if parent:
            parent.viewport().setCursor(Qt.PointingHandCursor)

    def createEditor(self, parent, option, index):
        return None

    def paint(self, painter, option, index):
        opt = QStyleOptionButton()
        opt.rect = option.rect
        opt.state |= QStyle.State_Enabled

        row_key = index.row()
        if row_key in self.checked_items:
            opt.state |= QStyle.State_On
        else:
            opt.state |= QStyle.State_Off

        if option.state & QStyle.State_Selected:
            painter.fillRect(option.rect, option.palette.highlight())

        checkbox_rect = QStyle.alignedRect(
            Qt.RightToLeft,
            Qt.AlignCenter,
            QSize(20, 20),
            option.rect
        )
        opt.rect = checkbox_rect
        QApplication.style().drawControl(QStyle.CE_CheckBox, opt, painter)

    def editorEvent(self, event, model, option, index):
        if event.type() == event.MouseButtonRelease:
            row_key = index.row()
            if row_key in self.checked_items:
                self.checked_items.remove(row_key)
            else:
                self.checked_items.add(row_key)
            model.dataChanged.emit(index, index)
            return True
        return False

    def get_checked_rows(self):
        return list(self.checked_items)

class SchoolCertificateWindow(QMainWindow):
    def __init__(self, parent=None, db=None):
        super().__init__(parent)
        self.db = db
        self.db_path = os.path.join(os.path.dirname(__file__), "data.db")
        self.using_qsql = bool(db and hasattr(db, 'isOpen') and db.isOpen())

        self.setWindowTitle("إدارة طلبات الشهادات المدرسية")
        self.setMinimumSize(1000, 600)
        self.setup_ui()  # Move this first
        self.checkbox_delegate = CheckBoxDelegate(self.table)  # Then create delegate
        self.setup_table_connections()  # إضافة هذا السطر
        self.load_data()  # Finally load data
        self.load_data_signal = None  # إضافة متغير للإشارة

    def setup_ui(self):
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        layout = QVBoxLayout(central_widget)

        # العنوان
        title_label = QLabel("طلبات الشهادات المدرسية")
        title_label.setFont(QFont("Calibri", 14, QFont.Bold))
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setCursor(Qt.ArrowCursor)  # تعيين المؤشر الافتراضي للعنوان
        layout.addWidget(title_label)

        # أزرار التحكم
        buttons_layout = QHBoxLayout()

        # إضافة زر عرض/إخفاء السجلات المسلمة
        self.toggle_delivered_btn = QPushButton("عرض السجلات المسلمة")
        self.toggle_delivered_btn.setFont(QFont("Calibri", 13, QFont.Bold))
        self.toggle_delivered_btn.setCheckable(True)  # جعل الزر قابل للتبديل
        self.toggle_delivered_btn.setStyleSheet("""
            QPushButton {
                background-color: #9b59b6;
                color: white;
                padding: 8px 16px;
                border-radius: 4px;
                min-width: 200px;
            }
            QPushButton:hover {
                background-color: #8e44ad;
            }
            QPushButton:checked {
                background-color: #8e44ad;
            }
        """)
        self.toggle_delivered_btn.setCursor(Qt.PointingHandCursor)  # تغيير شكل المؤشر إلى يد
        self.toggle_delivered_btn.clicked.connect(self.toggle_delivered_records)
        buttons_layout.addWidget(self.toggle_delivered_btn)

        # إضافة زر تحديد السجلات غير المسلمة
        self.select_undelivered_btn = QPushButton("تحديد السجلات غير المسلمة")
        self.select_undelivered_btn.setFont(QFont("Calibri", 13, QFont.Bold))
        self.select_undelivered_btn.setStyleSheet("""
            QPushButton {
                background-color: #f39c12;
                color: white;
                padding: 8px 16px;
                border-radius: 4px;
                min-width: 200px;
            }
            QPushButton:hover {
                background-color: #e67e22;
            }
        """)
        self.select_undelivered_btn.setCursor(Qt.PointingHandCursor)  # تغيير شكل المؤشر إلى يد
        self.select_undelivered_btn.clicked.connect(self.select_undelivered_records)
        buttons_layout.addWidget(self.select_undelivered_btn)

        self.deliver_btn = QPushButton("تسليم طلبات الشهادات المدرسية")
        self.deliver_btn.setFont(QFont("Calibri",  13, QFont.Bold))
        self.deliver_btn.setStyleSheet("""
            QPushButton {
                background-color: #2ecc71;
                color: white;
                padding: 8px 16px;
                border-radius: 4px;
                min-width: 200px;
            }
            QPushButton:hover {
                background-color: #27ae60;
            }
        """)
        self.deliver_btn.setCursor(Qt.PointingHandCursor)  # تغيير شكل المؤشر إلى يد
        self.deliver_btn.clicked.connect(self.deliver_certificates)

        self.print_btn = QPushButton("معاينة وطباعة طلبات الشهادات المدرسية")
        self.print_btn.setFont(QFont("Calibri", 13, QFont.Bold))
        self.print_btn.setStyleSheet("""
            QPushButton {
                background-color: #3498db;
                color: white;
                padding: 8px 16px;
                border-radius: 4px;
                min-width: 200px;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
        """)
        self.print_btn.setCursor(Qt.PointingHandCursor)  # تغيير شكل المؤشر إلى يد
        self.print_btn.clicked.connect(self.preview_and_print)

        buttons_layout.addWidget(self.deliver_btn)
        buttons_layout.addWidget(self.print_btn)
        layout.addLayout(buttons_layout)

        # جدول الطلبات
        self.table = QTableWidget()
        self.table.setFont(QFont("Calibri", 13, QFont.Bold))
        self.table.setColumnCount(10)  # تقليل عدد الأعمدة
        self.table.setHorizontalHeaderLabels([
            "اختيار",
            "الرمز",
            "السنة",
            "الرقم",
            "القسم",
            "الاسم والنسب",
            "تاريخ الطلب",
            "تاريخ التسليم",
            "ملاحظات",
            "مكتب الحراسة"
        ])
        self.table.setCursor(Qt.PointingHandCursor)  # تغيير شكل المؤشر إلى يد عند المرور فوق الجدول

        # تحديد عرض الأعمدة
        column_widths = [50, 110, 100, 60, 100, 200, 100, 100, 150, 100]
        header = self.table.horizontalHeader()
        header.setFont(QFont("Calibri", 12, QFont.Bold))
        header.setCursor(Qt.PointingHandCursor)  # تغيير شكل المؤشر إلى يد عند المرور فوق رأس الجدول

        # تطبيق العرض المخصص لكل عمود
        for i, width in enumerate(column_widths):
            self.table.setColumnWidth(i, width)
            if i == 5:  # عمود الاسم والنسب
                header.setSectionResizeMode(i, QHeaderView.Stretch)
            else:
                header.setSectionResizeMode(i, QHeaderView.Fixed)

        layout.addWidget(self.table)

    def setup_table_connections(self):
        """إعداد الاتصالات للجدول"""
        self.table.cellClicked.connect(self.on_cell_clicked)

        # تعيين مؤشر اليد للعناصر التفاعلية في الجدول
        self.table.horizontalHeader().setCursor(Qt.PointingHandCursor)
        self.table.verticalHeader().setCursor(Qt.PointingHandCursor)

    def on_cell_clicked(self, row, column):
        """معالجة النقر على خلايا الجدول"""
        if column == 1:  # عمود الرمز
            code = self.table.item(row, column).text()
            clipboard = QApplication.clipboard()
            clipboard.setText(code)

            # استخدام الرسائل المميزة دائمًا
            ConfirmationDialogs.show_custom_success_message(
                self,
                f"تم نسخ الرمز: {code} إلى الحافظة",
                "نسخ الرمز"
            )

    def load_data(self):
        try:
            # تعديل الاستعلام ليأخذ في الاعتبار حالة زر التبديل
            show_delivered = self.toggle_delivered_btn.isChecked()

            query_str = """
                SELECT الرمز, السنة, الرقم, القسم, الاسم_والنسب,
                       تاريخ_الطلب, تاريخ_التسليم, ملاحظات, مكتب_الحراسة, اختيار
                FROM الشهادة_المدرسية
                WHERE تاريخ_التسليم IS {}
                ORDER BY الرقم DESC
            """.format("NOT NULL" if show_delivered else "NULL")

            if self.using_qsql:
                query = QSqlQuery(self.db)
                if not query.exec_(query_str):
                    raise Exception(f"خطأ في تنفيذ الاستعلام: {query.lastError().text()}")

                records = []
                while query.next():
                    record = []
                    for i in range(10):
                        record.append(query.value(i))
                    records.append(record)
            else:
                conn = sqlite3.connect(self.db_path)
                cursor = conn.cursor()
                cursor.execute(query_str)
                records = cursor.fetchall()
                conn.close()

            self.table.setRowCount(len(records))
            for row, record in enumerate(records):
                # إضافة خلية مربع الاختيار
                checkbox_item = QTableWidgetItem()
                checkbox_item.setFlags(Qt.ItemIsEnabled | Qt.ItemIsSelectable)
                self.table.setItem(row, 0, checkbox_item)

                for col, value in enumerate(record):
                    item = QTableWidgetItem(str(value if value is not None else ''))
                    item.setTextAlignment(Qt.AlignCenter)
                    # تعيين مؤشر اليد للخلايا القابلة للنقر
                    if col == 0:  # عمود الرمز
                        item.setToolTip("انقر للنسخ إلى الحافظة")
                    self.table.setItem(row, col + 1, item)  # +1 لمراعاة عمود الاختيار

            # تعيين مندوب مربع الاختيار للعمود الأول
            self.table.setItemDelegateForColumn(0, self.checkbox_delegate)

            # تعيين مؤشر اليد للخلايا القابلة للنقر
            for row in range(self.table.rowCount()):
                for col in range(1, self.table.columnCount()):  # بدءًا من العمود 1 (بعد مربع الاختيار)
                    if self.table.item(row, col):
                        self.table.item(row, col).setFlags(self.table.item(row, col).flags() | Qt.ItemIsEnabled)

            # تحديث نص الزر
            self.toggle_delivered_btn.setText(
                "إخفاء السجلات المسلمة" if show_delivered else "عرض السجلات المسلمة"
            )

        except Exception as e:
            # استخدام الرسائل المميزة دائمًا
            ConfirmationDialogs.show_custom_error_message(
                self,
                f"حدث خطأ أثناء تحميل البيانات:\n{str(e)}",
                "خطأ في تحميل البيانات"
            )

    def deliver_certificates(self):
        """تسليم طلبات الشهادات المدرسية المحددة"""
        try:
            checked_rows = self.checkbox_delegate.get_checked_rows()
            if not checked_rows:
                # استخدام الرسائل المميزة دائمًا
                ConfirmationDialogs.show_custom_warning_message(
                    self,
                    "الرجاء تحديد طلب واحد على الأقل لتسليمه",
                    "تحديد الشهادات المدرسية"
                )
                return

            # عرض مربع حوار تأكيد باستخدام الرسائل المميزة دائمًا
            confirm_delivery = ConfirmationDialogs.show_custom_confirmation_dialog(
                self,
                f"هل تريد تسليم {len(checked_rows)} شهادة مدرسية؟\n\nسيتم تسجيل تاريخ التسليم وتحديث حالة الشهادات",
                "تأكيد تسليم الشهادات المدرسية"
            )

            if confirm_delivery:
                current_date = QDate.currentDate().toString("yyyy-MM-dd")

                # متغير لتتبع نجاح العمليات
                success_count = 0
                error_messages = []

                for row in checked_rows:
                    try:
                        # التحقق من وجود العنصر قبل محاولة الوصول إليه
                        if self.table.item(row, 1) is None:
                            error_messages.append(f"خطأ: لا توجد بيانات في الصف {row+1}")
                            continue

                        reg_number = self.table.item(row, 1).text()  # عمود الرمز في العمود 1

                        if not reg_number:
                            error_messages.append(f"خطأ: الرمز فارغ في الصف {row+1}")
                            continue

                        if self.using_qsql:
                            query = QSqlQuery(self.db)
                            query.prepare("""
                                UPDATE الشهادة_المدرسية
                                SET اختيار = 1,
                                    تاريخ_التسليم = ?,
                                    ملاحظات = 'تم التسليم'
                                WHERE الرمز = ?
                            """)
                            query.addBindValue(current_date)
                            query.addBindValue(reg_number)
                            if not query.exec_():
                                error_messages.append(f"خطأ في تحديث السجل رقم {reg_number}: {query.lastError().text()}")
                                continue
                        else:
                            try:
                                conn = sqlite3.connect(self.db_path)
                                cursor = conn.cursor()
                                cursor.execute("""
                                    UPDATE الشهادة_المدرسية
                                    SET اختيار = 1,
                                        تاريخ_التسليم = ?,
                                        ملاحظات = 'تم التسليم'
                                    WHERE الرمز = ?
                                """, (current_date, reg_number))
                                conn.commit()
                                conn.close()
                            except sqlite3.Error as sql_error:
                                error_messages.append(f"خطأ في تحديث السجل رقم {reg_number}: {str(sql_error)}")
                                if 'conn' in locals() and conn:
                                    conn.close()
                                continue

                        success_count += 1
                    except Exception as row_error:
                        error_messages.append(f"خطأ في معالجة الصف {row+1}: {str(row_error)}")

                # عرض رسالة مخصصة عند النجاح/الفشل
                if success_count > 0:
                    success_message = f"تم تسليم {success_count} شهادة مدرسية بنجاح"

                    if error_messages:
                        success_message += f"\n\nمع وجود {len(error_messages)} أخطاء"

                    # استخدام رسالة النجاح المميزة دائمًا
                    ConfirmationDialogs.show_custom_success_message(
                        self,
                        success_message,
                        "تسليم الشهادات المدرسية"
                    )

                    self.checkbox_delegate.checked_items.clear()
                    self.load_data()

                    # استدعاء إشارة التحديث إذا كانت موجودة
                    if self.load_data_signal:
                        self.load_data_signal()

                # إذا كانت هناك أخطاء فقط، عرض تفاصيلها
                elif error_messages:
                    error_details = "\n".join(error_messages[:5])
                    if len(error_messages) > 5:
                        error_details += f"\n\n... و{len(error_messages) - 5} أخطاء أخرى"

                    # استخدام رسالة الخطأ المميزة دائمًا
                    ConfirmationDialogs.show_custom_error_message(
                        self,
                        f"لم يتم تسليم أي شهادة مدرسية\n\nالأخطاء:\n{error_details}",
                        "خطأ في تسليم الشهادات"
                    )

        except Exception as e:
            # معالجة الأخطاء الشاملة للدالة
            print(f"خطأ عام: {str(e)}")

            # استخدام رسالة الخطأ المميزة دائمًا
            ConfirmationDialogs.show_custom_error_message(
                self,
                f"حدث خطأ أثناء تسليم الشهادات المدرسية:\n{str(e)}",
                "خطأ في تسليم الشهادات"
            )

    def preview_and_print(self):
        """طباعة سجلات طلبات الشهادات المدرسية"""
        try:
            # إنشاء المجلد الرئيسي على سطح المكتب
            desktop_path = os.path.join(os.path.expanduser("~"), "Desktop")
            main_folder = os.path.join(desktop_path, "تقارير برنامج المعين في الحراسة العامة")
            if not os.path.exists(main_folder):
                os.makedirs(main_folder)

            # إنشاء المجلد الفرعي لتقارير طلبات الشواهد المدرسية
            reports_dir = os.path.join(main_folder, "تقارير طلبات الشواهد المدرسية")
            if not os.path.exists(reports_dir):
                os.makedirs(reports_dir)

            # عرض رسالة انتظار
            QApplication.setOverrideCursor(Qt.WaitCursor)

            # استخدام دالة الطباعة المناسبة
            if ARABIC_PDF_AVAILABLE:
                # استخدام دالة الطباعة الجديدة التي تدعم اللغة العربية
                result, filepath, certificates_dir = arabic_print_function(self)
            else:
                # استخدام دالة الطباعة القديمة
                result, filepath, certificates_dir = print_certificates_requests(self)

            # إعادة المؤشر إلى الوضع الطبيعي
            QApplication.restoreOverrideCursor()

            if result and os.path.exists(filepath):
                # استخدام الرسائل المميزة دائمًا لإظهار رسالة نجاح وخيار فتح الملف
                confirm_open = ConfirmationDialogs.show_custom_confirmation_dialog(
                    self,
                    f"تم إنشاء التقرير بنجاح وحفظه في المسار:\n{filepath}\n\nهل ترغب في فتح التقرير الآن؟",
                    "تم إنشاء التقرير بنجاح"
                )
                if confirm_open:
                    try:
                        # نظام ويندوز فقط لأن التطبيق يعمل على ويندوز
                        os.startfile(filepath)
                    except Exception as open_error:
                        ConfirmationDialogs.show_custom_error_message(
                            self,
                            f"حدث خطأ أثناء فتح الملف:\n{str(open_error)}",
                            "خطأ في فتح الملف"
                        )
            else:
                # في حالة إلغاء العملية أو حدوث خطأ (إذا لم يتم عرض رسالة بالفعل)
                if not filepath and not certificates_dir:  # إذا كان الإلغاء من المستخدم
                    pass  # لا نعرض أي رسالة عند الإلغاء المتعمد
                elif not os.path.exists(filepath):
                    # استخدام الرسائل المميزة دائمًا
                    ConfirmationDialogs.show_custom_warning_message(
                        self,
                        "حدث خطأ أثناء طباعة سجلات الشهادات المدرسية\nلم يتم إنشاء الملف بشكل صحيح",
                        "خطأ في الطباعة"
                    )
                else:
                    # استخدام الرسائل المميزة دائمًا
                    ConfirmationDialogs.show_custom_warning_message(
                        self,
                        "حدث خطأ أثناء طباعة سجلات الشهادات المدرسية\nالرجاء التأكد من اتصال الطابعة وتوفر كافة البيانات",
                        "خطأ في الطباعة"
                    )
        except Exception as e:
            # استخدام الرسائل المميزة دائمًا
            ConfirmationDialogs.show_custom_error_message(
                self,
                f"حدث خطأ أثناء طباعة سجلات الشهادات المدرسية:\n{str(e)}",
                "خطأ في الطباعة"
            )

    def toggle_delivered_records(self):
        """دالة للتبديل بين عرض السجلات المسلمة وغير المسلمة"""
        self.load_data()  # إعادة تحميل البيانات بناءً على حالة الزر الجديدة

    def select_undelivered_records(self):
        """تحديد جميع السجلات التي لم يتم تسليمها بعد"""
        try:
            # التأكد من أن الزر غير مفعل (لعرض السجلات غير المسلمة فقط)
            if self.toggle_delivered_btn.isChecked():
                # إذا كان الزر مفعل (يعرض السجلات المسلمة)، قم بإلغاء تفعيله وإعادة تحميل البيانات
                self.toggle_delivered_btn.setChecked(False)
                self.load_data()

            # مسح التحديدات السابقة
            self.checkbox_delegate.checked_items.clear()

            # تحديد جميع الصفوف المعروضة (وهي الآن السجلات غير المسلمة فقط)
            for row in range(self.table.rowCount()):
                self.checkbox_delegate.checked_items.add(row)

            # تحديث عرض الجدول
            self.table.viewport().update()

            # عرض رسالة تأكيد
            count = len(self.checkbox_delegate.checked_items)
            if count > 0:
                ConfirmationDialogs.show_custom_success_message(
                    self,
                    f"تم تحديد {count} سجل غير مسلم بنجاح",
                    "تحديد السجلات"
                )
            else:
                ConfirmationDialogs.show_custom_warning_message(
                    self,
                    "لا توجد سجلات غير مسلمة للتحديد",
                    "تحديد السجلات"
                )

        except Exception as e:
            print(f"خطأ في تحديد السجلات غير المسلمة: {e}")
            ConfirmationDialogs.show_custom_error_message(
                self,
                f"حدث خطأ أثناء تحديد السجلات غير المسلمة:\n{str(e)}",
                "خطأ في تحديد السجلات"
            )

if __name__ == "__main__":
    app = QApplication(sys.argv)
    app.setLayoutDirection(Qt.RightToLeft)
    window = SchoolCertificateWindow()
    window.show()
    sys.exit(app.exec_())
